﻿@if (IsLoading)
{
	<FormSectionComponent Title="Описание роли">
		<MudSkeleton Width="60%"
					 Height="52px" />

		<MudSkeleton Width="60%"
					 Height="52px" />
	</FormSectionComponent>
	<FormSectionComponent Title="Список пользователей с данной ролью">
		@for (var i = 0; i < _rnd.Next(1, 5); i++)
		{
			<MudSkeleton SkeletonType="SkeletonType.Rectangle"
						 Class="skeleton-camera"
						 @key="i" />
		}
	</FormSectionComponent>

	<div>
		<MudStack>
			<MudText Typo="Typo.h6">Права доступа</MudText>
			<MudText Typo="Typo.body2">
				<MudSkeleton Width="60%"
							 Height="52px" />
				<MudSkeleton Width="60%" />
				<MudSkeleton Width="60%" />
			</MudText>
		</MudStack>

		<MudSkeleton Width="60%"
					 Height="52px" />
		<FormSectionComponent>
			@for (var i = 0; i < _rnd.Next(1, 5); i++)
			{
				<MudSkeleton SkeletonType="SkeletonType.Rectangle"
							 Class="skeleton-camera"
							 @key="i" />
			}
		</FormSectionComponent>
	</div>
}