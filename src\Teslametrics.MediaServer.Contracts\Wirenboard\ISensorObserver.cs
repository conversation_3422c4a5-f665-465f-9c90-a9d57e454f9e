using Orleans;

namespace Teslametrics.MediaServer.Orleans.Wirenboard;

public interface ISensorObserver : IGrainObserver
{
    Task ReceiveData(ISensorData SensorData);
}

public interface ISensorData
{
}

[GenerateSerializer]
public record SensorIntData(string Topic, int Value) : ISensorData;

[GenerateSerializer]
public record SensorStringData(string Topic, string Value) : ISensorData;

[GenerateSerializer]
public record SensorDoubleData(string Topic, double Value) : ISensorData;

[GenerateSerializer]
public record SensorBoolData(string Topic, bool Value) : ISensorData;