namespace FFMpegNET;

/// <summary>
/// Информация о медиапотоке
/// </summary>
public record StreamInfo
{
    /// <summary>
    /// Ширина видео в пикселях
    /// </summary>
    public int Width { get; set; }

    /// <summary>
    /// Высота видео в пикселях
    /// </summary>
    public int Height { get; set; }

    /// <summary>
    /// Средняя частота кадров
    /// </summary>
    public double FrameRate { get; set; }

    /// <summary>
    /// Битрейт видео в битах в секунду
    /// </summary>
    public long BitRate { get; set; }

    /// <summary>
    /// Название видеокодека
    /// </summary>
    public string VideoCodec { get; set; } = string.Empty;

    /// <summary>
    /// Название аудиокодека
    /// </summary>
    public string? AudioCodec { get; set; }

    /// <summary>
    /// Частота дискретизации аудио в Гц
    /// </summary>
    public int AudioSampleRate { get; set; }

    /// <summary>
    /// Количество аудиоканалов
    /// </summary>
    public int AudioChannels { get; set; }

    /// <summary>
    /// Битрейт аудио в битах в секунду
    /// </summary>
    public long AudioBitRate { get; set; }

    /// <summary>
    /// Наличие аудиопотока
    /// </summary>
    public bool HasAudio { get; set; }
}
