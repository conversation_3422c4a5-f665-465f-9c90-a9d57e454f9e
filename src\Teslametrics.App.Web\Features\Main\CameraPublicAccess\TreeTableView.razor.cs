using System.Reactive;
using Microsoft.AspNetCore.Components;
using MudBlazor;
using Teslametrics.App.Web.Data.Sql;
using Teslametrics.App.Web.Events.CameraPublicAccess;
using Teslametrics.App.Web.Extensions;

namespace Teslametrics.App.Web.Features.Main.CameraPublicAccess;

// TODO: оптимизировать это в будущем
public partial class TreeTableView
{
    private string _orderBy = "Name";
    private OrderDirection _orderDirection = OrderDirection.Ascending;

    private DateTime _lastRefreshTime = DateTime.Now;
    private GetTreeNodeUseCase.Response? _response;

    private bool _subscribing;
    private TreeNodeSubscribeUseCase.Response? _subscriptionResult;

    private List<FlatTreeItem> FlattenedItems { get; set; } = new();

    #region Parameters
    [Parameter]
    public int Offset { get; set; } = 0;
    [Parameter]
    public EventCallback<int> OffsetChanged { get; set; }

    [Parameter]
    public int Limit { get; set; } = 25;
    [Parameter]
    public EventCallback<int> LimitChanged { get; set; }
    #endregion [Parameter]

    protected int CurrentPage => Offset / Limit;

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();

        await FetchAsync();
    }

    private async Task FetchAsync()
    {
        if (IsLoading) return;

        try
        {
            //if (Items.Count() == 0) await SetLoadingAsync(true);
            var userId = await GetCurrentUserIdAsync() ?? throw new UnauthorizedAccessException();
            _response = await ScopeFactory.MediatorSend(new GetTreeNodeUseCase.Query(userId, 0, 100, _orderBy, _orderDirection));
            switch (_response.Result)
            {
                case GetTreeNodeUseCase.Result.Success:
                    FlattenItems(_response.Items);

                    await SubscribeAsync();
                    _lastRefreshTime = DateTime.Now;

                    break;
                case GetTreeNodeUseCase.Result.ValidationError:
                    Snackbar.Add("Не удалось получить список организаций. Повторите попытку", Severity.Error);
                    break;
                case GetTreeNodeUseCase.Result.Unknown:
                default:
                    Snackbar.Add("Не удалось получить список организаций. Повторите попытку", Severity.Error);
                    break;
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add("Не удалось получить список организаций. Повторите попытку", Severity.Error);
            Logger.LogError(ex, ex.Message);
        }
        finally
        {
            await SetLoadingAsync(false);
        }
    }

    private async Task SubscribeAsync()
    {
        try
        {
            Unsubscribe();

            await SetSubscribingAsync(true);
            var orgIds = FlattenedItems.Select(x => x.Item.Id).ToList();
            if (orgIds.Count == 0)
            {
                Snackbar.Add("Нет доступных для просмотра организаций. Невозможно подписаться на обновления.", Severity.Error);
                return;
            }
            _subscriptionResult = await ScopeFactory.MediatorSend(new TreeNodeSubscribeUseCase.Request(Observer.Create<object>(OnAppEventHandler, OnError), orgIds));
            await SetSubscribingAsync(false);
            switch (_subscriptionResult.Result)
            {
                case TreeNodeSubscribeUseCase.Result.Success:
                    CompositeDisposable.Add(_subscriptionResult.Subscription!);
                    break;
                case TreeNodeSubscribeUseCase.Result.ValidationError:
                    Snackbar.Add("Ошибка валидации при подписке на события", Severity.Error);
                    break;
                case TreeNodeSubscribeUseCase.Result.Unknown:
                default:
                    throw new Exception($"Unexpected result in {nameof(TreeNodeSubscribeUseCase)}: {_subscriptionResult.Result}");
            }
        }
        catch (Exception ex)
        {
            await SetSubscribingAsync(false);
            Snackbar.Add("Не удалось получить подписку на события дерева из-за непредвиденной ошибки. Повторите попытку", Severity.Error);
            Logger.LogError(ex, ex.Message);
        }
    }
    private void Unsubscribe()
    {
        if (_subscriptionResult?.Subscription is not null)
        {
            CompositeDisposable.Remove(_subscriptionResult.Subscription);
            _subscriptionResult.Subscription.Dispose();
        }
    }
    private Task SetSubscribingAsync(bool isLoading = true) => UpdateViewAsync(() =>
    {
        _subscribing = isLoading;
    });

    #region [Actions]
    private void OnRowClick(TableRowClickEventArgs<FlatTreeItem> args)
    {
        if (args.Item is null) return;

        if (args.Item.Item.ElementType == ElementType.Link)
        {
            EventSystem.Publish(new CameraPublicAccessSelectEto(args.Item.Item.OrganizationId, args.Item.Parent!.Item.Id, args.Item.Item.Id));
        }
    }
    private Task RefreshAsync() => FetchAsync();
    private void ReissueUrl(Guid organizationId, Guid cameraId, Guid accessId) => EventSystem.Publish(new CameraPublicAccessReissueEto(organizationId, cameraId, accessId));
    private void CreateUrl(Guid organizationId, Guid cameraId) => EventSystem.Publish(new CameraPublicAccessCreateEto(organizationId, cameraId));
    private void SelectUrl(Guid organizationId, Guid cameraId, Guid accessId) => EventSystem.Publish(new CameraPublicAccessSelectEto(organizationId, cameraId, accessId));
    private void EditUrl(Guid organizationId, Guid cameraId, Guid accessId) => EventSystem.Publish(new CameraPublicAccessEditEto(organizationId, cameraId, accessId));
    private void DeleteUrl(Guid organizationId, Guid cameraId, Guid accessId) => EventSystem.Publish(new CameraPublicAccessDeleteEto(organizationId, cameraId, accessId));
    private void ToggleExpand(FlatTreeItem item)
    {
        item.IsExpanded = !item.IsExpanded;

        // Если сворачиваем элемент, то сворачиваем все дочерние
        if (!item.IsExpanded)
        {
            var currentIndex = FlattenedItems.IndexOf(item);
            var children = FlattenedItems
                .Skip(currentIndex + 1)
                .TakeWhile(x => x.Level > item.Level)
                .ToList();

            foreach (var child in children)
            {
                child.IsExpanded = false;
            }
        }
    }

    private Task RowsPerPageChanged(int limit)
    {
        Limit = limit;
        if (LimitChanged.HasDelegate)
        {
            return LimitChanged.InvokeAsync(limit);
        }
        return Task.CompletedTask;
    }
    #endregion

    #region [Events]
    private async void OnAppEventHandler(object appEvent)
    {
        await RefreshAsync();
        await UpdateViewAsync();
    }

    private void OnError(Exception exc)
    {
        Snackbar.Add("Ошибка при подписке на события", Severity.Error);
        Logger.LogError(exc, exc.Message);
    }
    #endregion

    #region [Helpers]
    private TreeItemData<Element>? FindTreeItem(List<TreeItemData<Element>> items, Element target)
    {
        foreach (var item in items)
        {
            if (item.Value == null) continue;

            if (item.Value.Equals(target)) return item;

            if (item.Children != null)
            {
                var found = FindTreeItem(item.Children, target);
                if (found != null) return found;
            }
        }
        return null;
    }

    private void FlattenItems(List<GetTreeNodeUseCase.Response.Item> items)
    {
        // Сохраняем текущие состояния IsExpanded
        var expandedStates = FlattenedItems.ToDictionary(
            item => item.Item.Id,
            item => item.IsExpanded
        );

        FlattenedItems.Clear();

        foreach (var organization in items)
        {

            var organizationElement = new Element(organization.Id, organization.Id, organization.Name, ElementType.Organization)
            {
                CameraCount = organization.CameraCount,
                PublicAccessCount = organization.PublicAccessCount
            };
            // Add organization
            var parentOrganization = new FlatTreeItem(organizationElement, 0, organization.Folders.Count > 0, expandedStates.GetValueOrDefault(organization.Id, false));
            FlattenedItems.Add(parentOrganization);

            // Add folders
            foreach (var folder in organization.Folders)
            {
                var folderElement = new Element(folder.Id, organization.Id, folder.Name, ElementType.Folder)
                {
                    CameraCount = folder.CameraCount,
                    PublicAccessCount = folder.PublicAccessCount
                };
                var parentFolder = new FlatTreeItem(folderElement, 1, folder.Cameras.Count > 0, expandedStates.GetValueOrDefault(folder.Id, false), parentOrganization);
                FlattenedItems.Add(parentFolder);

                // Add cameras
                foreach (var camera in folder.Cameras)
                {
                    var cameraElement = new Element(camera.Id, organization.Id, camera.Name, ElementType.Camera)
                    {
                        CameraCount = 1,
                        PublicAccessCount = camera.PublicAccessCount
                    };
                    var parentCamera = new FlatTreeItem(cameraElement, 2, camera.PublicAccesses.Count > 0, expandedStates.GetValueOrDefault(camera.Id, false), parentFolder);
                    FlattenedItems.Add(parentCamera);

                    // Add public access links
                    foreach (var link in camera.PublicAccesses)
                    {
                        var linkElement = new Element(link.Id, organization.Id, link.Name, ElementType.Link)
                        {
                            CameraCount = 0,
                            PublicAccessCount = 1
                        };
                        var parentLink = new FlatTreeItem(linkElement, 3, false, expandedStates.GetValueOrDefault(link.Id, false), parentCamera);
                        FlattenedItems.Add(parentLink);
                    }
                }
            }
        }
    }

    private bool ShouldShowVerticalLine(FlatTreeItem item, int level)
    {
        // Если это корневой элемент и проверяем уровень 0 - линия не нужна
        if (item.Parent is null && level == 0) return false;

        if (item.Level == level) return true;

        // Находим индекс текущего элемента
        var currentIndex = FlattenedItems.IndexOf(item);

        if (level >= item.Level)
        {
            var childrens = FlattenedItems
                .Skip(currentIndex + 1)
                .TakeWhile(x => x.Level > item.Level)
                .ToList();
            return childrens.Count > 0;
        }

        var parents = FlattenedItems
            .Skip(currentIndex + 1)
            .TakeWhile(x => x.Level <= item.Level && x.Level != 0)
            .ToList();

        return parents.Where(x => x.Level == level).Any();
    }
    #endregion
}

#region [Type Definitions]
public class FlatTreeItem(Element element, int level, bool hasChildren, bool isExpanded, FlatTreeItem? parent = null)
{
    public Element Item { get; set; } = element;
    public int Level { get; set; } = level;
    public bool HasChildren { get; set; } = hasChildren;
    public bool IsExpanded { get; set; } = isExpanded;
    public FlatTreeItem? Parent { get; set; } = parent;
}

public class Element(Guid id, Guid organizationId, string name, ElementType elementType)
{
    public string Icon => ElementType switch
    {
        ElementType.Organization => Icons.Material.Outlined.Business,
        ElementType.Folder => Icons.Material.Outlined.Folder,
        ElementType.Camera => Icons.Material.Outlined.Camera,
        ElementType.Link => Icons.Material.Outlined.Link,
        _ => Icons.Material.Outlined.QuestionMark
    };

    public Guid Id { get; set; } = id;
    public Guid OrganizationId { get; set; } = organizationId;
    public string Name { get; set; } = name;
    public ElementType ElementType { get; set; } = elementType;
    public int CameraCount { get; set; }
    public int PublicAccessCount { get; set; }
}

public enum ElementType
{
    Organization,
    Folder,
    Camera,
    Link,
}
#endregion