@using Teslametrics.App.Web.Features.Main.AccessControl.Roles.Drawer.Create.CameraViews
@using Teslametrics.App.Web.Features.Main.AccessControl.Roles.Drawer.Create.CamerasPermissions
@inherits InteractiveBaseComponent<Teslametrics.App.Web.Locales.Features.Main.AccessControl.Roles.Drawer.Create.RoleCreateComponent>
<DrawerHeader>
	<MudText Typo="Typo.h1">@L["Title"]</MudText>
</DrawerHeader>
<MudForm Model="_model"
		 Validation="Validator.ValidateValue"
		 Class="flex-1"
		 ValidationDelay="0"
		 OverrideFieldValidation="true"
		 Spacing="8">
	<div>
		<MudStack Spacing="2"
				  Class="mb-2">
			<MudText Typo="Typo.h6">@L["RoleDescription"]</MudText>
		</MudStack>
		<MudCard>
			<MudCardContent>
				<MudStack Spacing="4">
					<MudTextField @bind-Value="_model.Name"
								  For="() => _model.Name"
								  Clearable="true"
								  InputType="InputType.Text"
								  Immediate="true"
								  Label="@L["RoleName"]"
								  Required="true"
								  RequiredError="@L["RoleNameRequired"]"
								  Class="field_login"
								  HelperText="@L["RoleNameHelper"]" />

					<MudCheckBox T="bool"
								 Value="_model.IsAdmin"
								 ValueChanged="IsAdminChanged"
								 Label="@L["AdminRole"]"
								 Color="Color.Primary"
								 Class="ml-n4" />
				</MudStack>
			</MudCardContent>
		</MudCard>
	</div>
	<div>
		<MudStack>
			<MudText Typo="Typo.h6">@L["AccessRights"]</MudText>
			<MudText Typo="Typo.body2">
				@L["AccessRightsDescription"]<br />
				@if (OrganizationId == SystemConsts.RootOrganizationId)
				{
					<MudText>@L["GlobalRightsRoot"]</MudText>
					<MudText>@L["ResourceRightsRoot"]</MudText>
				}
				else
				{
					<MudText>@L["GlobalRightsOrg"]</MudText>
					<MudText>@L["ResourceRightsOrg"]</MudText>
				}
			</MudText>
		</MudStack>
		<MudTabs ApplyEffectsToContainer="true"
				 PanelClass="tab_banel pt-6 d-flex flex-column gap-5"
				 TabHeaderClass="tabs"
				 KeepPanelsAlive="false">
			@if (_model.IsAdmin)
			{
				<MudTabPanel Text="@L["TabAccessControl"]"
							 Icon="@Icons.Material.Outlined.Key">
					<FormSectionComponent Subtitle="@(OrganizationId == SystemConsts.RootOrganizationId ? L["GlobalRights"] : L["CommonRights"])">
						@if (OrganizationId == SystemConsts.RootOrganizationId)
						{
							<WildcardPermissionComponents TEnum="AppPermissions.Main.AccessControl.Organizations"
														  Title="@L["Organizations"]"
														  @bind-Selected="_model.Permissions"
														  ShowAdminTaggedValues="_model.IsAdmin" />
						}
						<WildcardPermissionComponents TEnum="AppPermissions.Main.AccessControl.Users"
													  Title="@L["Users"]"
													  @bind-Selected="_model.Permissions"
													  ShowAdminTaggedValues="_model.IsAdmin" />
						<WildcardPermissionComponents TEnum="AppPermissions.Main.AccessControl.Roles"
													  Title="@L["Roles"]"
													  @bind-Selected="_model.Permissions"
													  ShowAdminTaggedValues="_model.IsAdmin" />
					</FormSectionComponent>

					@if (OrganizationId == SystemConsts.RootOrganizationId)
					{
						<FormSectionComponent Subtitle="@L["ResourceRights"]"
											  CardContentClass="pa-0">
							<OrganizationsConcretePermissionsComponent @bind-Selected="_model.Permissions" />
						</FormSectionComponent>
					}
				</MudTabPanel>
			}
			<MudTabPanel Text="@L["TabCameras"]"
						 Icon="@Icons.Material.Outlined.Camera">
				<FormSectionComponent Subtitle="@(OrganizationId == SystemConsts.RootOrganizationId ? L["GlobalRights"] : L["CommonRights"])">
					<WildcardPermissionComponents TEnum="AppPermissions.Main.Cameras"
												  Title="@L["Cameras"]"
												  @bind-Selected="_model.Permissions"
												  ShowAdminTaggedValues="_model.IsAdmin" />
					<WildcardPermissionComponents TEnum="AppPermissions.Main.Folders"
												  Title="@L["Folders"]"
												  @bind-Selected="_model.Permissions"
												  ShowAdminTaggedValues="_model.IsAdmin" />
				</FormSectionComponent>

				<FormSectionComponent Subtitle="@L["ResourceRights"]"
									  CardContentClass="pa-0">
					<CamerasConcretePermissionsComponent @bind-Selected="_model.Permissions"
														 OrganizationId="OrganizationId"
														 ShowAdminTaggedValues="_model.IsAdmin" />
				</FormSectionComponent>
			</MudTabPanel>
			@if (OrganizationId == SystemConsts.RootOrganizationId && _model.IsAdmin)
			{
				<MudTabPanel Text="@L["TabPresets"]"
							 Icon="@Icons.Material.Outlined.Camera">
					<FormSectionComponent Subtitle="@(OrganizationId == SystemConsts.RootOrganizationId ? L["GlobalRights"] : L["CommonRights"])">
						<WildcardPermissionComponents TEnum="AppPermissions.Main.CameraPresets"
													  Title="@L["CameraPresets"]"
													  @bind-Selected="_model.Permissions"
													  ShowAdminTaggedValues="_model.IsAdmin" />
					</FormSectionComponent>
				</MudTabPanel>
			}

			@if (OrganizationId == SystemConsts.RootOrganizationId && _model.IsAdmin)
			{
				<MudTabPanel Text="@L["TabQuotas"]"
							 Icon="@Icons.Material.Outlined.Dataset">
					<FormSectionComponent Subtitle="@L["GlobalRights"]">
						<WildcardPermissionComponents TEnum="AppPermissions.Main.CameraQuotas"
													  Title="@L["CameraQuotas"]"
													  @bind-Selected="_model.Permissions"
													  ShowAdminTaggedValues="@_model.IsAdmin" />
					</FormSectionComponent>
				</MudTabPanel>
			}
			@if (OrganizationId == SystemConsts.RootOrganizationId && _model.IsAdmin)
			{
				<MudTabPanel Text="@L["TabPublicAccess"]"
							 Icon="@Icons.Material.Outlined.Dataset">
					<FormSectionComponent Subtitle="@L["GlobalRights"]">
						<WildcardPermissionComponents TEnum="AppPermissions.Main.CameraPublicAccess"
													  Title="@L["CameraPublicAccess"]"
													  @bind-Selected="_model.Permissions"
													  ShowAdminTaggedValues="@_model.IsAdmin" />
					</FormSectionComponent>
				</MudTabPanel>
			}

			<MudTabPanel Text="@L["TabViews"]"
						 Icon="@Icons.Material.Filled.ViewModule">
				<FormSectionComponent Subtitle="@(OrganizationId == SystemConsts.RootOrganizationId ? L["GlobalRights"] : L["CommonRights"])">
					<WildcardPermissionComponents TEnum="AppPermissions.Main.CameraViews"
												  Title="@L["CameraViews"]"
												  @bind-Selected="_model.Permissions"
												  ShowAdminTaggedValues="@_model.IsAdmin" />
				</FormSectionComponent>

				<FormSectionComponent Subtitle="@L["ResourceRights"]">
					<CameraViewsConcretePermissionsComponent @bind-Selected="_model.Permissions"
															 OrganizationId="OrganizationId"
															 ShowAdminTaggedValues="_model.IsAdmin" />
				</FormSectionComponent>
			</MudTabPanel>
		</MudTabs>
	</div>
</MudForm>
<DrawerActions>
	<MudSpacer />
	<MudButton OnClick="CancelAsync"
			   Variant="Variant.Outlined"
			   StartIcon="@Icons.Material.Outlined.Close">Закрыть</MudButton>
	<AuthorizeView Policy="@AppPermissions.Main.AccessControl.Roles.Create.GetEnumPermissionString()"
				   Context="innerContext">
		<MudButton OnClick="SubmitAsync"
				   Disabled="@(!_isValid)"
				   Color="Color.Secondary"
				   Variant="Variant.Outlined">@L["Save"]</MudButton>
	</AuthorizeView>
</DrawerActions>