using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Rendering;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;


namespace Teslametrics.App.Web.Features.Main.Presets.List;

public partial class ErrorReceivedComponent
{
	[Parameter]
	public GetCameraPresetListUseCase.Result? Result { get; set; }

	[Parameter]
	public DateTime LastRefreshTime { get; set; }

	[Parameter]
	public EventCallback RefreshAsync { get; set; }
}
