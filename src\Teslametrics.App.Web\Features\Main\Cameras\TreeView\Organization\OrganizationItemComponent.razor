@using Teslametrics.Shared
@inherits InteractiveBaseComponent
<MudTreeViewItem @bind-Expanded="@Item.Expanded"
				 CanExpand="@Item.Expandable"
				 Items="@Item.Children"
				 Value="@Item.Value"
				 Selected="@Item.Selected"
				 OnClick="SelectHandler">
	<Content>
		<MudTreeViewItemToggleButton Expanded="@Item.Expanded"
									 ExpandedChanged="(newExpanded) => OnExpandChanged.InvokeAsync((newExpanded, Item.Value))"
									 Visible="@Item.HasChildren" />
		<div class="d-flex flex-row align-center mud-width-full">
			<div class="py-2 px-1 d-flex flex-row align-center overflow-auto">
				<MudIcon Icon="@Item.Icon"
						 Class="ml-0 mr-2"
						 Color="@Color.Default" />
				<MudText>@Item.Text</MudText>
			</div>
			<MudSpacer />
			<MudStack Row="true"
					  AlignItems="AlignItems.Center">
				<CameraListStatusComponent DisconnectedCount="@_presenter.StoppedCameraCount"
										   ConnectingCount="@_presenter.StartingCameraCount"
										   ConnectedCount="@_presenter.ConnectedCameraCount"
										   ProblemCount="@_presenter.ProblemCameraCount"
										   TotalCount="@_presenter.CameraCount" />
				<MudMenu Icon="@Icons.Material.Filled.MoreVert"
						 Variant="Variant.Outlined"
						 Color="Color.Secondary">
					<AuthorizeView Policy="@AppPermissions.Main.Cameras.Create.GetEnumPermissionString()"
								   Resource="new PolicyRequirementResource(Item.Value, null)"
								   Context="innerContext">
						<MudMenuItem OnClick="Create"
									 Icon="@Icons.Material.Outlined.Folder">Создать директорию</MudMenuItem>
					</AuthorizeView>
					<AuthorizeView Policy="@AppPermissions.Main.Cameras.Disconnect.GetEnumPermissionString()"
								   Resource="new PolicyRequirementResource(Item.Value, null)"
								   Context="innerContext">
						<MudMenuItem OnClick="DisconnectAsync"
									 Icon="@Icons.Material.Filled.VisibilityOff">Отключить все</MudMenuItem>
					</AuthorizeView>
					<AuthorizeView Policy="@AppPermissions.Main.Cameras.Connect.GetEnumPermissionString()"
								   Resource="new PolicyRequirementResource(Item.Value, null)"
								   Context="innerContext">
						<MudMenuItem OnClick="ConnectAsync"
									 Icon="@Icons.Material.Filled.Visibility">Подключить все</MudMenuItem>
					</AuthorizeView>
				</MudMenu>
			</MudStack>
		</div>
	</Content>
</MudTreeViewItem>
