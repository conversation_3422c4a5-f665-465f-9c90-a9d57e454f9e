@using System
@implements IDisposable

@if (string.IsNullOrEmpty(TooltipText))
{
    <MudTooltip Arrow="true"
                Placement="Placement.Start"
                Text="@InputTime.ToLocalTime().ToString("F")">
        <span>@_displayText</span>
    </MudTooltip>
}
else
{
    <MudTooltip Arrow="true"
                Placement="Placement.Start"
                Text="@TooltipText">
        <span>@_displayText</span>
    </MudTooltip>
}

@code {
    [Parameter, EditorRequired]
    public DateTime InputTime { get; set; }

    [Parameter]
    public string? TooltipText { get; set; }

    private Timer? _timer;
    private string _displayText = string.Empty;

    protected override void OnInitialized()
    {
        UpdateDisplayText();
        _timer = new Timer(UpdateTimer, null, 0, 1000); // Update every second
    }

    private void UpdateTimer(object? state)
    {
        InvokeAsync(() =>
        {
            UpdateDisplayText();
            StateHasChanged();
        });
    }

    private void UpdateDisplayText()
    {
        var now = DateTime.Now;
        var diff = now - InputTime;

        _displayText = FormatTimeSpan(diff);
    }

    private string FormatTimeSpan(TimeSpan timeSpan)
    {
        if (timeSpan.TotalSeconds < 60)
        {
            var seconds = (int)timeSpan.TotalSeconds;
            return FormatUnit(seconds, "секунда", "секунды", "секунд");
        }
        if (timeSpan.TotalMinutes < 60)
        {
            var minutes = (int)timeSpan.TotalMinutes;
            return FormatUnit(minutes, "минута", "минуты", "минут");
        }
        if (timeSpan.TotalHours < 24)
        {
            var hours = (int)timeSpan.TotalHours;
            return FormatUnit(hours, "час", "часа", "часов");
        }
        if (timeSpan.TotalDays < 30)
        {
            var days = (int)timeSpan.TotalDays;
            return FormatUnit(days, "день", "дня", "дней");
        }
        if (timeSpan.TotalDays < 365)
        {
            var months = (int)(timeSpan.TotalDays / 30);
            return FormatUnit(months, "месяц", "месяца", "месяцев");
        }

        var years = (int)(timeSpan.TotalDays / 365);
        return FormatUnit(years, "год", "года", "лет");
    }

    private string FormatUnit(int number, string form1, string form2, string form5)
    {
        var lastDigit = number % 10;
        var lastTwoDigits = number % 100;

        if (lastTwoDigits >= 11 && lastTwoDigits <= 19)
            return $"{number} {form5} назад";

        if (lastDigit == 1)
            return $"{number} {form1} назад";

        if (lastDigit >= 2 && lastDigit <= 4)
            return $"{number} {form2} назад";

        return $"{number} {form5} назад";
    }

    public void Dispose()
    {
        _timer?.Dispose();
    }
}