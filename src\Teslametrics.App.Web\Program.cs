using FluentValidation;
using Microsoft.AspNetCore.Localization;
using Microsoft.AspNetCore.SignalR;
using Serilog;
using System.Globalization;
using Teslametrics.App.Web.Behaviors;
using Teslametrics.App.Web.Domain;
using Teslametrics.App.Web.Extensions;
using Teslametrics.App.Web.Middleware;
using Teslametrics.App.Web.Orleans;
using Teslametrics.App.Web.Services.Authentication;
using Teslametrics.App.Web.Services.Authorization;
using Teslametrics.App.Web.Services.BlzEventSystem;
using Teslametrics.App.Web.Services.DomainEventBus;
using Teslametrics.App.Web.Services.FileStorage;
using Teslametrics.App.Web.Services.OpenTelemetry;
using Teslametrics.App.Web.Services.Outbox;
using Teslametrics.App.Web.Services.Persistence;
using Teslametrics.App.Web.Services.TransactionManager;
using Teslametrics.App.Web.Services.UserSession;

namespace Teslametrics.App.Web;

public class Program
{
    public static void Main(string[] args)
    {
        var environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");

        environment = ValidateEnvironment(environment);

        var config = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
            .AddJsonFile($"appsettings.{environment}.json", optional: true, reloadOnChange: true)
            .Build();

        Log.Logger = new LoggerConfiguration()
            .ReadFrom.Configuration(config)
            .CreateBootstrapLogger();

        AppDomain currentDomain = AppDomain.CurrentDomain;
        currentDomain.UnhandledException += new UnhandledExceptionEventHandler((object sender, UnhandledExceptionEventArgs args) =>
        {
            Log.Logger.Error((Exception)args.ExceptionObject, "Unhandled exception");
        });

        try
        {
            Log.Information($"Starting application in {environment} environment...");

            var builder = WebApplication.CreateBuilder(args);

            builder.Host.UseSerilog();

            builder.Services.AddLocalization();

            builder.Services.AddMediatR(cfg =>
            {
                cfg.RegisterServicesFromAssembly(typeof(Program).Assembly);
                cfg.AddOpenBehavior(typeof(ExceptionBehavior<,>));
                cfg.AddOpenBehavior(typeof(ResilenceBehavior<,>));
            });

            builder.Services.AddValidatorsFromAssembly(typeof(Program).Assembly);

            DomainModule.Install(builder.Services);

            PostgresDomainPersistenceModule.Install(builder.Services, builder.Configuration);

            AuthenticationModule.Install(builder.Services);

            AuthorizationModule.Install(builder.Services);

            OutboxModule.Install(builder.Services);

            DomainEventBusModule.Install(builder.Services);

            TransactionManagerModule.Install(builder.Services);

            IoTModule.Install(builder);

            SessionModule.Install(builder.Services);

            MinioFileStorageModule.Install(builder.Services, builder.Configuration);

            OpenTelemetryModule.Install(builder);

            builder.Services.AddDatabaseDeveloperPageExceptionFilter();

            builder.Services.AddHttpContextAccessor();

            #region Blazor
            builder.WebHost.UseStaticWebAssets();

            builder.Services.AddRazorComponents()
                .AddInteractiveServerComponents();

            builder.Services.AddScoped<IBlzEventSystem, DefaultBlzEventSystem>();

            builder.Services.AddBlazorDownloadFile();
            builder.Services.AddMudBlazor();

            #endregion
            builder.Services.AddAntiforgery(options =>
            {
                options.Cookie.HttpOnly = true;
                options.Cookie.SecurePolicy = CookieSecurePolicy.SameAsRequest;
                options.SuppressXFrameOptionsHeader = false;
            });

            builder.Services.AddControllers();
            builder.Services.AddSignalR(o =>
            {
                o.MaximumReceiveMessageSize = 1024 * 1024 * 10; // 10 MB
                o.KeepAliveInterval = TimeSpan.FromSeconds(15); // ping the proxy
                o.ClientTimeoutInterval = TimeSpan.FromSeconds(60); // client waits this long for a ping
            })
            .AddMessagePackProtocol(options =>
            {
                // Настройка MessagePack для эффективной передачи бинарных данных
                options.SerializerOptions = MessagePack.MessagePackSerializerOptions.Standard;
            })
            .AddHubOptions<Hubs.VideoHub>(options =>
            {
                options.EnableDetailedErrors = true; // TODO: Удалить после тестов
            });

            IoTModule.Initialize();

            var app = builder.Build();

            // Локализация
            string[] supportedCultures = ["ru-RU"];//, "en-US"
            var russianCulture = new System.Globalization.CultureInfo("ru-RU");

            // Принудительно устанавливаем русскую локаль как стандартную
            System.Globalization.CultureInfo.DefaultThreadCurrentCulture = russianCulture;
            System.Globalization.CultureInfo.DefaultThreadCurrentUICulture = russianCulture;

            var localizationOptions = new RequestLocalizationOptions()
                .SetDefaultCulture(supportedCultures[0])
                .AddSupportedCultures(supportedCultures)
                .AddSupportedUICultures(supportedCultures);
            //System.Globalization.CultureInfo.DefaultThreadCurrentCulture = new System.Globalization.CultureInfo(supportedCultures[0]);

            // Настраиваем провайдеры определения культуры, чтобы принудительно использовать русскую локаль
            localizationOptions.RequestCultureProviders.Clear();
            localizationOptions.RequestCultureProviders.Add(new CustomRequestCultureProvider(context => Task.FromResult<ProviderCultureResult?>(new("ru-RU"))));

            app.UseRequestLocalization(localizationOptions);

            // Configure the HTTP request pipeline.
            if (app.Environment.IsDevelopment())
            {
                app.UseMigrationsEndPoint();
            }
            else
            {
                app.UseExceptionHandler("/Error");
                // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
                app.UseHsts();
            }

            //app.UseHttpsRedirection();

            //TODO нужен ли этот заголовок для всего приложения или только для плеера?
            //Зачем нам разрешать встраивать всё приложение в iframe?
            app.Use(async (context, next) =>
            {
                context.Response.Headers["X-Frame-Options"] = "ALLOW-FROM *";
                await next();
            });

            app.UseStaticFiles();                     // wwwroot + _content
                                                      // отдельная папка /components, но ТОЛЬКО JS-файлы, нужно для модульности импорта JS-модулей
                                                      // Да, это грязно, но StaticWebAssets с автоматическим импортом не сработает из-за того, что в папке компонентов Blazor любой .js считает частью компонента и просто так его импортнуть уже не выйдет
                                                      // TODO: Проверить возможность подключения без этого на .net9.0+
            var jsOnly = new Microsoft.AspNetCore.StaticFiles.FileExtensionContentTypeProvider(mapping: new Dictionary<string, string>() { { ".js", "application/javascript" } });
            app.UseStaticFiles(new StaticFileOptions
            {
                FileProvider = new Microsoft.Extensions.FileProviders.PhysicalFileProvider(Path.Combine(app.Environment.ContentRootPath, "Components")),
                RequestPath = "/components",
                ContentTypeProvider = jsOnly,                //-→ отдаём только .js
                ServeUnknownFileTypes = false                // (значение по умолчанию, оставляем)
            });

            app.UseAntiforgery();
            app.UseMiddleware<CustomAuthenticationMiddleware>();
            app.UseAuthorization();

            #region Blazor
            app.MapRazorComponents<App>()
                .AddInteractiveServerRenderMode();
            #endregion
            app.UseStatusCodePagesWithRedirects("/Error");
            app.MapControllerRoute("default", "{controller=Home}/{action=Index}/{id?}");
            app.MapPrometheusScrapingEndpoint();

            // Регистрируем хаб для MSE плеера
            app.MapHub<Hubs.VideoHub>("/videoHub");

            app.Run();

            IoTModule.Dispose();
            IoTModule.DisposeServices(app.Services);
        }
        catch (Exception ex) when (ex is not HostAbortedException && ex.Source != "Microsoft.EntityFrameworkCore.Design")
        {
            Log.Fatal(ex, "Host terminated unexpectedly!");
        }
        finally
        {
            Log.CloseAndFlush();
        }
    }

    private static string ValidateEnvironment(string? environment)
    {
        if (string.IsNullOrEmpty(environment))
        {
            throw new ArgumentOutOfRangeException(nameof(environment), "Environment variable ASPNETCORE_ENVIRONMENT is not set");
        }

        return environment switch
        {
            "Local" => environment,
            "Development" => environment,
            "Perftest" => environment,
            "Caviardev" => environment,
            "Stage" => environment,
            "Production" => environment,
            _ => throw new ArgumentOutOfRangeException(nameof(environment))
        };
    }
}

public static class StreamNames
{
    public const string PersistentStream = nameof(PersistentStream);
    public const string VideoLiveStream = nameof(VideoLiveStream);
}

public static class StreamNamespaces
{
    public const string CameraStreams = nameof(CameraStreams);
}

public static class StreamTopicNames
{
    public const string MinioEvents = "minio-events";
}