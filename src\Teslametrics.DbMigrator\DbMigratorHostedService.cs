using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Teslametrics.DbMigrator.Data;

namespace Teslametrics.DbMigrator;

public class DbMigratorHostedService : IHostedService
{
    public class Options
    {
        public bool RecreateDatabase { get; set; }
    }

    private readonly IHostApplicationLifetime _hostApplicationLifetime;
    private readonly ILogger<DbMigratorHostedService> _logger;
    private readonly TeslametricsDbMigrationService _dbMigrator;
    private readonly Options _options;

    public DbMigratorHostedService(
        IHostApplicationLifetime hostApplicationLifetime,
        ILogger<DbMigratorHostedService> logger,
        TeslametricsDbMigrationService dbMigrator,
        IOptions<Options> options)
    {
        _hostApplicationLifetime = hostApplicationLifetime;
        _logger = logger;
        _dbMigrator = dbMigrator;
        _options = options.Value;
    }

    public async Task StartAsync(CancellationToken cancellationToken)
    {
        await _dbMigrator.MigrateAsync(_options.RecreateDatabase);
        _hostApplicationLifetime.StopApplication();
    }

    public Task StopAsync(CancellationToken cancellationToken) => Task.CompletedTask;
}
