using Microsoft.EntityFrameworkCore;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Domain.CameraPresets;

namespace Teslametrics.App.Web.Services.Persistence;

public class PresetRepository : BaseRepository<PresetAggregate>, IPresetRepository
{
    public PresetRepository(CommandAppDbContext dbContext)
        : base(dbContext)
    {
    }

    public Task<bool> IsCameraPresetExistsAsync(Guid presetId, CancellationToken cancellationToken = default) =>
        DbContext.Set<PresetAggregate>()
            .AsNoTracking()
            .AnyAsync(entity => entity.Id == presetId, cancellationToken);

    public Task<bool> IsCameraPresetNameExistsAsync(string name, CancellationToken cancellationToken = default) =>
        DbContext.Set<PresetAggregate>()
            .AsNoTracking()
            .AnyAsync(entity => entity.Name == name, cancellationToken);
}
