using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.Core.Domain.AccessControl.Organizations;
using Teslametrics.Core.Domain.AccessControl.Users;
using Teslametrics.Core.Services.Outbox;
using Teslametrics.Core.Services.TransactionManager;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Roles.DeleteDialog;

public static class DeleteRoleUseCase
{
    public record Command(Guid OrganizationId, Guid RoleId) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public Result Result { get; init; }
        public bool IsSuccess => Result == Result.Success;

        public Response(Result result)
        {
            Result = result;
        }
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        OrganizationNotFound,
        CannotDeleteSystemRole,
        CannotDeleteRoleAssignedToUser
    }

    public class Validator : AbstractValidator<Command>
    {
        public Validator()
        {
            RuleFor(c => c.OrganizationId).NotEmpty();
            RuleFor(c => c.RoleId).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Command, Response>
    {
        private readonly IValidator<Command> _validator;
        private readonly IOrganizationRepository _organizationRepository;
        private readonly IUserRepository _userRepository;
        private readonly ITransactionManager _transactionManager;
        private readonly IPublisher _publisher;
        private readonly IOutbox _outbox;

        public Handler(IValidator<Command> validator,
                       IOrganizationRepository organizationRepository,
                       IUserRepository userRepository,
                       ITransactionManager transactionManager,
                       IPublisher publisher,
                       IOutbox outbox)
        {
            _validator = validator;
            _organizationRepository = organizationRepository;
            _userRepository = userRepository;
            _transactionManager = transactionManager;
            _publisher = publisher;
            _outbox = outbox;
        }

        public async Task<Response> Handle(Command request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            using var transaction = await _transactionManager.CreateTransactionAsync();

            var organization = await _organizationRepository.FindAsync(request.OrganizationId, cancellationToken);

            if (organization == null)
            {
                return new Response(Result.OrganizationNotFound);
            }

            var role = organization.Roles.FirstOrDefault(r => r.Id == request.RoleId);
            //if (role is null)
            //{
            //    return new Response(Result.RoleAlreadyDeleted);
            //}

            if (role is not null)
            {
                if (role.IsSystem)
                {
                    return new Response(Result.CannotDeleteSystemRole);
                }

                if ((await _userRepository.GetUsersInRoleAsync(role.Id, cancellationToken)).Count != 0)
                {
                    return new Response(Result.CannotDeleteRoleAssignedToUser);
                }

                var events = organization.DeleteRole(request.RoleId);

                await _organizationRepository.SaveChangesAsync(cancellationToken);

                foreach (var @event in events)
                {
                    await _publisher.Publish(@event, cancellationToken);
                }

                await _outbox.AddRangeAsync(events);

                await transaction.CommitAsync();
            }

            return new Response(Result.Success);
        }
    }
}