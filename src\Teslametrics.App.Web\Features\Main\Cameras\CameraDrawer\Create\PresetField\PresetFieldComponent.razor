@inherits InteractiveBaseComponent

<MudStack Spacing="4"
		  Row="true">
	<MudAutocomplete T="Preset"
					 Label="Квота"
					 Value="Selected"
					 DebounceInterval="500"
					 ValueChanged="OnSelectedValudeChanged"
					 SearchFunc="@SearchAsync"
					 ToStringFunc="item => item?.Title ?? string.Empty"
					 HelperText="Введите название пресета"
					 MaxItems="25"
					 Required="true"
					 AdornmentIcon="@Icons.Material.Filled.Search"
					 Clearable="true"
					 ListItemClass="pa-0"
					 RequiredError="Поле обязательно к заполнению"
					 SelectOnActivation="false"
					 ShowProgressIndicator="true"
					 ProgressIndicatorColor="Color.Primary">
		<NoItemsTemplate>
			<NoItemsFoundComponent HasItems="false" />
		</NoItemsTemplate>
		<ItemTemplate Context="e">
			<MudTooltip Placement="Placement.Start"
						Arrow="true"
						VisibleChanged="(bool visible) => OnTooltipVisibleChanged(visible, e.Id)"
						RootClass="mud-width-full pa-2"
						Class="mud-elevation-1 pa-0 quota_select_item_tooltip">
				<ChildContent>
					<MudText class="mud-width-full">
						@e.Title
					</MudText>
				</ChildContent>
				<TooltipContent>
					@if (_streamConfigResponse is not null)
					{
						<PresetInfoComponent Archive="@(_streamConfigResponse.ArchiveStreamConfig is not null ? new(_streamConfigResponse.ArchiveStreamConfig.Resolution, _streamConfigResponse.ArchiveStreamConfig.VideoCodec, _streamConfigResponse.ArchiveStreamConfig.FrameRate, _streamConfigResponse.ArchiveStreamConfig.SceneDynamic, _streamConfigResponse.ArchiveStreamConfig.AudioCodec) : null)"
											 View="@(_streamConfigResponse.ViewStreamConfig is not null ? new(_streamConfigResponse.ViewStreamConfig.Resolution, _streamConfigResponse.ViewStreamConfig.VideoCodec, _streamConfigResponse.ViewStreamConfig.FrameRate, _streamConfigResponse.ViewStreamConfig.SceneDynamic, _streamConfigResponse.ViewStreamConfig.AudioCodec) : null)"
											 Public="@(_streamConfigResponse.PublicStreamConfig is not null ? new(_streamConfigResponse.PublicStreamConfig.Resolution, _streamConfigResponse.PublicStreamConfig.VideoCodec, _streamConfigResponse.PublicStreamConfig.FrameRate, _streamConfigResponse.PublicStreamConfig.SceneDynamic, _streamConfigResponse.PublicStreamConfig.AudioCodec) : null)" />
					}
					else
					{
						<MudProgressCircular Color="Color.Primary"
											 Indeterminate="true" />
					}
				</TooltipContent>
			</MudTooltip>
		</ItemTemplate>
		<ItemSelectedTemplate Context="e">
			<MudTooltip Placement="Placement.Start"
						Arrow="true"
						VisibleChanged="(bool visible) => OnTooltipVisibleChanged(visible, e.Id)"
						RootClass="mud-width-full pa-2"
						Class="mud-elevation-1 pa-0 quota_select_item_tooltip">
				<ChildContent>
					<MudText class="mud-width-full">
						@e.Title
					</MudText>
				</ChildContent>
				<TooltipContent>
					@if (_streamConfigResponse is not null)
					{
						<PresetInfoComponent Archive="@(_streamConfigResponse.ArchiveStreamConfig is not null ? new(_streamConfigResponse.ArchiveStreamConfig.Resolution, _streamConfigResponse.ArchiveStreamConfig.VideoCodec, _streamConfigResponse.ArchiveStreamConfig.FrameRate, _streamConfigResponse.ArchiveStreamConfig.SceneDynamic, _streamConfigResponse.ArchiveStreamConfig.AudioCodec) : null)"
											 View="@(_streamConfigResponse.ViewStreamConfig is not null ? new(_streamConfigResponse.ViewStreamConfig.Resolution, _streamConfigResponse.ViewStreamConfig.VideoCodec, _streamConfigResponse.ViewStreamConfig.FrameRate, _streamConfigResponse.ViewStreamConfig.SceneDynamic, _streamConfigResponse.ViewStreamConfig.AudioCodec) : null)"
											 Public="@(_streamConfigResponse.PublicStreamConfig is not null ? new(_streamConfigResponse.PublicStreamConfig.Resolution, _streamConfigResponse.PublicStreamConfig.VideoCodec, _streamConfigResponse.PublicStreamConfig.FrameRate, _streamConfigResponse.PublicStreamConfig.SceneDynamic, _streamConfigResponse.PublicStreamConfig.AudioCodec) : null)" />
					}
					else
					{
						<MudProgressCircular Color="Color.Primary"
											 Indeterminate="true" />
					}
				</TooltipContent>
			</MudTooltip>
		</ItemSelectedTemplate>
	</MudAutocomplete>
	<MudTooltip Placement="Placement.Start"
				Arrow="true"
				VisibleChanged="(bool visible) => Selected is not null ? OnTooltipVisibleChanged(visible, Selected.Id) : Task.CompletedTask"
				Class="mud-elevation-1 pa-0 quota_select_item_tooltip">
		<ChildContent>
			<MudIconButton Icon="@Icons.Material.Filled.Info"
						   Color="Color.Info" />
		</ChildContent>
		<TooltipContent>
			@if (Selected is not null)
			{
				if (_streamConfigResponse is not null)
				{
					<PresetInfoComponent Archive="@(_streamConfigResponse.ArchiveStreamConfig is not null ? new(_streamConfigResponse.ArchiveStreamConfig.Resolution, _streamConfigResponse.ArchiveStreamConfig.VideoCodec, _streamConfigResponse.ArchiveStreamConfig.FrameRate, _streamConfigResponse.ArchiveStreamConfig.SceneDynamic, _streamConfigResponse.ArchiveStreamConfig.AudioCodec) : null)"
										 View="@(_streamConfigResponse.ViewStreamConfig is not null ? new(_streamConfigResponse.ViewStreamConfig.Resolution, _streamConfigResponse.ViewStreamConfig.VideoCodec, _streamConfigResponse.ViewStreamConfig.FrameRate, _streamConfigResponse.ViewStreamConfig.SceneDynamic, _streamConfigResponse.ViewStreamConfig.AudioCodec) : null)"
										 Public="@(_streamConfigResponse.PublicStreamConfig is not null ? new(_streamConfigResponse.PublicStreamConfig.Resolution, _streamConfigResponse.PublicStreamConfig.VideoCodec, _streamConfigResponse.PublicStreamConfig.FrameRate, _streamConfigResponse.PublicStreamConfig.SceneDynamic, _streamConfigResponse.PublicStreamConfig.AudioCodec) : null)" />
				}
				else
				{
					<MudProgressCircular Color="Color.Primary"
										 Indeterminate="true" />
				}
			}
			else
			{
				<MudText>выберите пресет для потока</MudText>
			}
		</TooltipContent>
	</MudTooltip>
</MudStack>