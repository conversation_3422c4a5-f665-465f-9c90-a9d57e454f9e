using System.Data;
using Dapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Teslametrics.App.Web.Extensions;
using Teslametrics.MediaServer.Orleans.Camera;
using Teslametrics.App.Web.Services.Authorization;
using Teslametrics.Core.Services.Persistence;
using Teslametrics.Shared;
using Teslametrics.Core.Services.FileStorage;

namespace Teslametrics.App.Web.Features.Main.Cameras;

[ApiController]
[Route("streams")]
public class StreamsController : ControllerBase
{
    private readonly ILogger<StreamsController> _logger;
    private readonly IDbConnection _dbConnection;
    private readonly IFileStorage _fileStorage;
    private readonly IClusterClient _clusterClient;
    private readonly IAuthorizationService _authorizationService;

    public StreamsController(IAuthorizationService authorizationService,
                             ILogger<StreamsController> logger,
                             IDbConnection dbConnection,
                             IFileStorage fileStorage,
                             IClusterClient clusterClient)
    {
        _authorizationService = authorizationService;
        _logger = logger;
        _dbConnection = dbConnection;
        _fileStorage = fileStorage;
        _clusterClient = clusterClient;
    }

    //[Route("{cameraId}/stream.m3u8")]
    //[HttpGet]
    //[Authorize]
    //public async Task<string?> GetStream(Guid cameraId, [FromQuery] DateTimeOffset start)
    //{
    //    var organizationId = GetOrganizationId(cameraId);

    //    var authResult = await _authorizationService.AuthorizeAsync(HttpContext.User, new PolicyRequirementResource(organizationId, cameraId), AppPermissions.Main.Cameras.Read.GetEnumPermissionString());
    //    if (!authResult.Succeeded)
    //    {
    //        return null;
    //    }

    //    return await new M3u8Generator(_dbConnection, _streamCache).GetAsync(cameraId, start);
    //}

    //[Route("{cameraId}/online/stream.m3u8")]
    //[HttpGet]
    //[Authorize]
    //public async Task<string?> GetOnlineStream(Guid cameraId)
    //{
    //    var organizationId = GetOrganizationId(cameraId);

    //    var authResult = await _authorizationService.AuthorizeAsync(HttpContext.User, new PolicyRequirementResource(organizationId, cameraId), AppPermissions.Main.Cameras.Read.GetEnumPermissionString());
    //    if (!authResult.Succeeded)
    //    {
    //        return null;
    //    }

    //    return await new M3u8Generator(_dbConnection, _streamCache).GetAsync(cameraId, StreamType.Archive);
    //}

    //[Route("{cameraId}/view/stream.m3u8")]
    //[HttpGet]
    //[Authorize]
    //public async Task<string?> GetViewStream(Guid cameraId)
    //{
    //    var organizationId = GetOrganizationId(cameraId);

    //    var authResult = await _authorizationService.AuthorizeAsync(HttpContext.User, new PolicyRequirementResource(organizationId, cameraId), AppPermissions.Main.Cameras.Read.GetEnumPermissionString());
    //    if (!authResult.Succeeded)
    //    {
    //        return null;
    //    }

    //    return await new M3u8Generator(_dbConnection, _streamCache).GetAsync(cameraId, StreamType.View);
    //}

    [Route("{cameraId}/gettimeline")]
    [HttpGet]
    [Authorize]
    public async Task<List<TimelineRange>?> GetTimeline(Guid cameraId, [FromQuery] DateTimeOffset start, [FromQuery] DateTimeOffset end)
    {
        var organizationId = GetOrganizationId(cameraId);

        var authResult = await _authorizationService.AuthorizeAsync(HttpContext.User, new PolicyRequirementResource(organizationId, cameraId), AppPermissions.Main.Cameras.Read.GetEnumPermissionString());
        if (!authResult.Succeeded)
        {
            return null;
        }

        // TODO: Добавить время камеры

        var table = $"{Db.StreamSegments.Table}_{cameraId.ToString("N")}";

        var template = SqlQueryBuilder.Create()
            .Select(Db.StreamSegments.Columns.StartTime)
            .Select(Db.StreamSegments.Columns.EndTime)
            .Where(Db.StreamSegments.Columns.StartTime, ":End", SqlOperator.LessThan, new { end })
            .Where(Db.StreamSegments.Columns.EndTime, ":Start", SqlOperator.GreaterThan, new { start })
            .OrderBy(Db.StreamSegments.Columns.StartTime, OrderDirection.Ascending)
            .Build(QueryType.Standard, table, RowSelection.AllRows);

        var ranges = await _dbConnection.QueryAsync<TimelineRange>(template.RawSql, template.Parameters);

        // Используем эффективный LINQ для сортировки и оптимизируем память
        return ranges
            .OrderBy(r => r.StartTime)
            .Aggregate(
                new List<TimelineRange>(),
                (mergedRanges, current) =>
                {
                    if (!mergedRanges.Any())
                    {
                        mergedRanges.Add(current);
                        return mergedRanges;
                    }

                    var lastRange = mergedRanges[^1];
                    if (Math.Abs((current.StartTime - lastRange.EndTime).TotalSeconds) <= 1)
                    {
                        // Обновляем последний диапазон вместо создания нового
                        mergedRanges[^1] = lastRange with { EndTime = current.EndTime };
                    }
                    else
                    {
                        mergedRanges.Add(current);
                    }

                    return mergedRanges;
                });
    }

    [Route("{cameraId}/getplaybackrange")]
    [HttpGet]
    [Authorize]
    public async Task<List<TimelineRange>?> GetPlaybackRange(Guid cameraId, DateTimeOffset fromTime)
    {
        var organizationId = GetOrganizationId(cameraId);

        var authResult = await _authorizationService.AuthorizeAsync(HttpContext.User, new PolicyRequirementResource(organizationId, cameraId), AppPermissions.Main.Cameras.Read.GetEnumPermissionString());
        if (!authResult.Succeeded)
        {
            return null;
        }

        var table = $"{Db.StreamSegments.Table}_{cameraId.ToString("N")}";

        var template = SqlQueryBuilder.Create()
            .Select(Db.StreamSegments.Columns.StartTime)
            .Select(Db.StreamSegments.Columns.EndTime)
            .Where(Db.StreamSegments.Columns.EndTime, ":FromTime", SqlOperator.GreaterThanOrEqual, new { fromTime })
            .OrderBy(Db.StreamSegments.Columns.StartTime, OrderDirection.Ascending)
            .Build(QueryType.Standard, table, RowSelection.AllRows);

        var sql = template.RawSql + $"LIMIT 2";

        return (await _dbConnection.QueryAsync<TimelineRange>(sql, new { fromTime })).ToList();
    }

    [HttpGet]
    [Route("{cameraId}/{fileName}")]
    [Authorize]
    public async Task<IActionResult> GetArchiveFile(Guid cameraId, string fileName)
    {
        try
        {
            var organizationId = GetOrganizationId(cameraId);

            var authResult = await _authorizationService.AuthorizeAsync(HttpContext.User, new PolicyRequirementResource(organizationId, cameraId), AppPermissions.Main.Cameras.Read.GetEnumPermissionString());
            if (!authResult.Succeeded)
            {
                return Unauthorized();
            }

            if (string.IsNullOrWhiteSpace(fileName))
            {
                return BadRequest("Invalid file name.");
            }

            var stream = await _fileStorage.GetFileAsync(cameraId.ToString("N"), fileName);

            return File(stream, GetContentType(fileName), fileName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, ex.Message);
            return Problem();
        }
    }

    //[HttpGet]
    //[Route("{cameraId}/online/{fileName}")]
    //[Authorize]
    //public async Task<IActionResult> GetLiveFile(Guid cameraId, string fileName)
    //{
    //    try
    //    {
    //        var organizationId = GetOrganizationId(cameraId);

    //        var authResult = await _authorizationService.AuthorizeAsync(HttpContext.User, new PolicyRequirementResource(organizationId, cameraId), AppPermissions.Main.Cameras.Read.GetEnumPermissionString());
    //        if (!authResult.Succeeded)
    //        {
    //            return Unauthorized();
    //        }

    //        if (string.IsNullOrWhiteSpace(fileName))
    //        {
    //            return BadRequest("Invalid segment name.");
    //        }

    //        var data = _streamCache.GetSegment(cameraId, StreamType.Archive, int.Parse(Path.GetFileNameWithoutExtension(fileName)));

    //        return File(data, GetContentType(fileName), fileName);
    //    }
    //    catch (Exception ex)
    //    {
    //        _logger.LogError(ex, ex.Message);
    //        return Problem();
    //    }
    //}

    [HttpGet]
    [Route("{cameraId}/{cameraStreamId}/online/preview")]
    [Authorize]
    public async Task<IActionResult> GetLivePreviewFile(Guid cameraId, Guid cameraStreamId)
    {
        try
        {
            var organizationId = GetOrganizationId(cameraId);

            var authResult = await _authorizationService.AuthorizeAsync(HttpContext.User, new PolicyRequirementResource(organizationId, cameraId), AppPermissions.Main.Cameras.Read.GetEnumPermissionString());
            if (!authResult.Succeeded)
            {
                return Unauthorized();
            }

            var data = await _clusterClient.GetGrain<ICameraStreamPreviewGrain>(cameraStreamId).GetPreviewAsync();

            if (data == null || data.Length == 0)
            {
                return NotFound("Preview image not found.");
            }

            return File(data, "image/jpeg");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, ex.Message);
            return Problem();
        }
    }

    [HttpGet]
    [Route("{cameraId}/{cameraStreamId}/view/preview")]
    [Authorize]
    public async Task<IActionResult> GetViewPreviewFile(Guid cameraId, Guid cameraStreamId)
    {
        try
        {
            var organizationId = GetOrganizationId(cameraId);

            var authResult = await _authorizationService.AuthorizeAsync(HttpContext.User, new PolicyRequirementResource(organizationId, cameraId), AppPermissions.Main.Cameras.Read.GetEnumPermissionString());
            if (!authResult.Succeeded)
            {
                return Unauthorized();
            }

            var data = await _clusterClient.GetGrain<ICameraStreamPreviewGrain>(cameraStreamId).GetPreviewAsync();

            if (data == null || data.Length == 0)
            {
                return NotFound("Preview image not found.");
            }

            return File(data, "image/jpeg");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, ex.Message);
            return Problem();
        }
    }

    //[HttpGet]
    //[Route("{cameraId}/view/{fileName}")]
    //[Authorize]
    //public async Task<IActionResult> GetViewFile(Guid cameraId, string fileName)
    //{
    //    try
    //    {
    //        var organizationId = GetOrganizationId(cameraId);

    //        var authResult = await _authorizationService.AuthorizeAsync(HttpContext.User, new PolicyRequirementResource(organizationId, cameraId), AppPermissions.Main.Cameras.Read.GetEnumPermissionString());
    //        if (!authResult.Succeeded)
    //        {
    //            return Unauthorized();
    //        }

    //        if (string.IsNullOrWhiteSpace(fileName))
    //        {
    //            return BadRequest("Invalid segment name.");
    //        }

    //        var data = _streamCache.GetSegment(cameraId, StreamType.View, int.Parse(Path.GetFileNameWithoutExtension(fileName)));

    //        return File(data, GetContentType(fileName), fileName);
    //    }
    //    catch (Exception ex)
    //    {
    //        _logger.LogError(ex, ex.Message);
    //        return Problem();
    //    }
    //}

    // Метод для определения типа контента (например, .jpg -> image/jpeg)
    private static string GetContentType(string fileName)
    {
        var fileExtension = Path.GetExtension(fileName).ToLower();
        return fileExtension switch
        {
            ".jpg" => "image/jpeg",
            ".png" => "image/png",
            ".mp4" => "video/mp4",
            ".m3u8" => "application/vnd.apple.mpegurl",
            ".ts" => "video/mp2t",
            _ => "application/octet-stream", // Стандартный тип для неизвестных файлов
        };
    }

    private Guid GetOrganizationId(Guid cameraId)
    {
        var template = SqlQueryBuilder.Create()
            .Select(Db.Cameras.Props.OrganizationId)
            .Where(Db.Cameras.Columns.Id, ":CameraId", SqlOperator.Equals, new { cameraId })
            .Build(QueryType.Standard, Db.Cameras.Table, RowSelection.AllRows);

        var organizationId = _dbConnection.QuerySingle<Guid>(template.RawSql, template.Parameters);
        return organizationId;
    }

    public record TimelineRange(DateTimeOffset StartTime, DateTimeOffset EndTime);
}