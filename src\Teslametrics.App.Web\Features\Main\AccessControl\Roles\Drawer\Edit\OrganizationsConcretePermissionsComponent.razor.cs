using Microsoft.AspNetCore.Components;
using MudBlazor;
using System.Reactive;
using System.Text;
using Teslametrics.App.Web.Domain.AccessControl;
using Teslametrics.App.Web.Exceptions;
using Teslametrics.App.Web.Extensions;
using Teslametrics.App.Web.Shared;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Roles.Drawer.Edit;

public partial class OrganizationsConcretePermissionsComponent
{
	private MudDataGrid<Organization>? _gridRef;
	private IEnumerable<ResourcePermission> _wildcardPermissions => Selected.Where(x => x.ResourceId.IsWildcard);

	private record Organization(Guid Id, string Name);

	[Parameter]
	[EditorRequired]
	public IEnumerable<ResourcePermission> Selected { get; set; } = null!;

	[Parameter]
	public EventCallback<IEnumerable<ResourcePermission>> SelectedChanged { get; set; }

	protected override async Task OnInitializedAsync()
	{
		await base.OnInitializedAsync();
		await SubscribeAsync();
	}

	private async Task OnChangedHandler(bool isChecked, AppPermissions.Main.AccessControl.Organizations value, Guid id)
	{
		string permissionName = GetPermissionString(value);
		if (isChecked)
		{
			Selected = Selected.Append(new ResourcePermission(new(id), new(permissionName)));
		}
		else
		{
			var toRemove = Selected.Where(x => x.Permission.Value == permissionName && x.ResourceId.Value == id);
			Selected = Selected.Except(toRemove);

		}

		if (SelectedChanged.HasDelegate)
			await SelectedChanged.InvokeAsync(Selected);
	}

	private async Task<GridData<Organization>> ServerDataFunc(GridStateVirtualize<Organization> gridState, CancellationToken token)
	{
		try
		{
			await SetLoadingAsync();
			var userId = await GetCurrentUserIdAsync() ?? throw new NotAuthorizedException();
			var _response = await ScopeFactory.MediatorSend(new GetOrganizationListUseCase.Query(userId, gridState.StartIndex, gridState.Count, string.Empty));
			switch (_response.Result)
			{
				case GetOrganizationListUseCase.Result.Success:
					return new GridData<Organization>
					{
						Items = _response.Items.Select(x => new Organization(x.Id, x.Name)),
						TotalItems = _response.TotalCount
					};

				case GetOrganizationListUseCase.Result.ValidationError:
					Snackbar.Add("Ошибка валидации при получении списка организаций", Severity.Error);
					return new GridData<Organization>
					{
						Items = [],
						TotalItems = 0
					};

				default:
				case GetOrganizationListUseCase.Result.Unknown:
					Snackbar.Add("Не удалось получить список организаций повторите попытку. Если проблема сохраняется - обратитесь к администратору", Severity.Error);
					return new GridData<Organization>
					{
						Items = [],
						TotalItems = 0
					};
			}
		}
		catch (TaskCanceledException)
		{
			return new GridData<Organization>
			{
				Items = [],
				TotalItems = 0
			};
		}
		catch (Exception ex)
		{
			Snackbar.Add("Не удалось получить список организаций повторите попытку. Если проблема сохраняется - обратитесь к администратору", Severity.Error);
			Logger.LogError(ex, ex.Message);

			return new GridData<Organization>
			{
				Items = [],
				TotalItems = 0
			};
		}
	}

	private async Task SubscribeAsync()
	{
		var userId = await GetCurrentUserIdAsync() ?? throw new NotAuthorizedException();
		var result = await ScopeFactory.MediatorSend(new SubscribeOrganizationListUseCase.Request(Observer.Create<object>(OnAppEventHandler, OnError), userId));
		switch (result.Result)
		{
			case SubscribeOrganizationListUseCase.Result.Success:
				CompositeDisposable.Add(result.Subscription!);
				break;
			case SubscribeOrganizationListUseCase.Result.ValidationError:
				Snackbar.Add("Ошибка валидации при подписке на события", Severity.Error);
				break;

			case SubscribeOrganizationListUseCase.Result.Unknown:
			default:
				Snackbar.Add("Не удалось получить подписку на обновления из-за непредвиденной ошибки:" + result.Result.ToString(), Severity.Error);
				break;
		}
	}

	private string GetPermissionString(AppPermissions.Main.AccessControl.Organizations value)
	{
		return new StringBuilder("Main.AccessControl.Organizations.").Append(value).ToString();
	}

	private bool ContainsPermission(IEnumerable<ResourcePermission> permissions, AppPermissions.Main.AccessControl.Organizations value)
	{
		return permissions.Any(x => x.Permission.Value == GetPermissionString(value));
	}

	private bool ContainsPermission(IEnumerable<ResourcePermission> permissions, AppPermissions.Main.AccessControl.Organizations value, Guid resourceId)
	{
		return permissions.Any(x => x.ResourceId.Value == resourceId && x.Permission.Value == GetPermissionString(value));
	}

	#region [EventHandlers]
	private async void OnAppEventHandler(object appEvent)
	{
		if (appEvent is SubscribeOrganizationListUseCase.DeletedEvent deletedEvent)
		{
			var toRemove = Selected.Where(x => x.ResourceId.Value == deletedEvent.Id);
			Selected = Selected.Except(toRemove);

			if (SelectedChanged.HasDelegate)
				await SelectedChanged.InvokeAsync(Selected);
		}
		if (_gridRef is not null)
		{
			await _gridRef.ReloadServerData();
			await UpdateViewAsync();
		}
	}

	private void OnError(Exception exc)
	{
		Logger.LogError(exc, exc.Message);
		Snackbar.Add("Ошибка валидации при подписке на события", Severity.Error);
	}
	#endregion

}
