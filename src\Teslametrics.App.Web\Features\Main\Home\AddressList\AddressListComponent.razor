@page "/address-list"
@using Teslametrics.App.Web.Features.Main.Home.AddressList
@using static Teslametrics.App.Web.Features.Main.Home.AddressList.GetAddressListUseCase.Response
@using static Teslametrics.App.Web.Features.Main.Home.AddressList.SubscribeAddressListUseCase
@inherits InteractiveBaseComponent
<div class="mt-4">
    <MudText Typo="Typo.subtitle1"
             Class="mb-4">Город</MudText>

    @if (IsLoading)
    {
        <MudProgressCircular Indeterminate="true" />
    }
    @if (!IsLoading && _response is not null && _response.Cities.Count == 0)
    {
        <MudAlert Severity="Severity.Info">Нет доступных адресов</MudAlert>
    }
    @if (_response is not null && _response.Cities.Count > 0)
    {
        <MudStack Spacing="2">
            @foreach (var city in _response.Cities)
            {
                <MudPaper Outlined="true"
                          Class="pa-4"
                          @key="city">
                    <MudStack AlignItems="AlignItems.Center"
                              Row="true"
                              Justify="Justify.SpaceBetween">
                        <MudCheckBox T="bool?"
                                     Value="@(city.Buildings.Any(b => _selected.Contains(b)))"
                                     ValueChanged="() => OnCitySelected(city)"
                                     Color="Color.Primary"
                                     Label="@city.Name"
                                     Class="city_checkbox" />
                        <MudText Class="building_count">@city.Buildings.Count зданий</MudText>
                    </MudStack>
                    <MudList T="GetAddressListUseCase.Response.Building"
                             SelectedValues="@_selected"
                             SelectedValuesChanged="OnBuildingSelected"
                             SelectionMode="SelectionMode.MultiSelection"
                             ReadOnly="false"
                             CheckBoxColor="Color.Primary"
                             Class="gap-2 d-flex flex-column">
                        @foreach (var building in city.Buildings)
                        {
                            <MudListItem Value="@building"
                                         Class="@(_selected.Contains(building) ? "list_item selected" : "list_item")"
                                         @key="building">
                                <div class="d-flex justify-space-between align-center">
                                    <MudStack Spacing="2">
                                        <MudText Class="building_address">@building.Address</MudText>
                                        <MudText class="incident_count">@building.IncidentCount происшествия</MudText>
                                    </MudStack>
                                    <MudIconButton OnClick="() => NavigateToDeviceList(city, building)"
                                                   Icon="@Icons.Material.Filled.ArrowForwardIos"
                                                   Class="to_device_icon" />
                                </div>
                            </MudListItem>
                        }
                    </MudList>
                </MudPaper>
            }
        </MudStack>
    }
</div>