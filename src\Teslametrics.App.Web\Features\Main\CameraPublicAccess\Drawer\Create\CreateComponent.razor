﻿@inherits InteractiveBaseComponent
<DrawerHeader>
	<MudStack Spacing="0">
		<MudText Typo="Typo.h1">Создание ссылки публичного доступа для камеры</MudText>
		@if (IsLoading)
		{
			<MudSkeleton Width="60%"></MudSkeleton>
		}
		else
		{
			<MudText Typo="Typo.subtitle1">@_cameraModel?.CameraName</MudText>
		}
	</MudStack>
	<MudSpacer />
	@if (!_subscribing && (_subscriptionResult is null || !_subscriptionResult.IsSuccess))
	{
		<MudTooltip Arrow="true"
					Placement="Placement.Start"
					Text="Ошибка подписки на события">
			<MudIconButton OnClick="SubscribeAsync"
						   Icon="@Icons.Material.Filled.ErrorOutline"
						   Color="Color.Error" />
		</MudTooltip>
		<MudIconButton OnClick="RefreshAsync"
					   Icon="@Icons.Material.Filled.Refresh"
					   Color="Color.Primary" />
	}
</DrawerHeader>
<MudForm Model="_model"
		 Validation="_validator.ValidateValue"
		 @bind-IsValid="_isValid"
		 Class="flex-1"
		 OverrideFieldValidation="true"
		 UserAttributes="@(new Dictionary<string, object>() {{"autocomplete", "off"}, {"aria-autocomplete", "none"}, {"role", "presentation"} })"
		 Spacing="8">
	<FormSectionComponent title="Описание доступа к камере"
						  Subtitle="Настройки, которые влияют только на восприятие человеком">
		<MudTextField @bind-Value="_model.Name"
					  For="@(() => _model.Name)"
					  Clearable="true"
					  InputType="InputType.Text"
					  Immediate="true"
					  Label="Наименование"
					  RequiredError="Данное поле обязательно"
					  Required="true" />
	</FormSectionComponent>
</MudForm>
<DrawerActions>
	<MudSpacer />
	<MudButton OnClick="CancelAsync"
			   Variant="Variant.Outlined"
			   StartIcon="@Icons.Material.Outlined.Close">Закрыть</MudButton>
	<MudButton OnClick="SubmitAsync"
			   Disabled="@(!_isValid)"
			   Color="Color.Secondary"
			   Variant="Variant.Outlined">Сохранить</MudButton>
</DrawerActions>