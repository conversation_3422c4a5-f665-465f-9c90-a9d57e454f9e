using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.WebUtilities;

namespace Teslametrics.App.Web.Features.DetailView;

public partial class DetailViewPage
{
    [Inject]
    private NavigationManager NavigationManager { get; set; } = default!;

    [SupplyParameterFromQuery(Name = "OrganizationId")]
    public Guid? OrganizationId { get; set; }

    [SupplyParameterFromQuery(Name = "ViewId")]
    public Guid? ViewId { get; set; }

    private void OnTreeParametersChanged((Guid? OrganizationId, Guid? ViewId) parameters)
    {
        OrganizationId = parameters.OrganizationId;
        ViewId = parameters.ViewId;

        var uri = NavigationManager.Uri;
        var uriWithoutQuery = uri.Split('?')[0];
        var queryParameters = new Dictionary<string, string?>();

        if (parameters.OrganizationId.HasValue)
            queryParameters.Add("OrganizationId", parameters.OrganizationId.Value.ToString());
        if (parameters.ViewId.HasValue)
            queryParameters.Add("ViewId", parameters.ViewId.Value.ToString());

        var newUri = QueryHelpers.AddQueryString(uriWithoutQuery, queryParameters);
        NavigationManager.NavigateTo(newUri, replace: true);
    }

}
