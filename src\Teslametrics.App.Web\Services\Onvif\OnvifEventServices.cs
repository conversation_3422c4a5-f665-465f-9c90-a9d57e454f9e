﻿//------------------------------------------------------------------------------
// <auto-generated>
//     Этот код создан программой.
//
//     Изменения в этом файле могут привести к неправильной работе и будут потеряны в случае
//     повторного создания кода.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Teslametrics.App.Web.Services.OnvifProxy;

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.onvif.org/ver10/events/wsdl")]
public partial class PullMessagesFaultResponse
{

    private string maxTimeoutField;

    private int maxMessageLimitField;

    private System.Xml.XmlElement[] anyField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(DataType="duration", Order=0)]
    public string MaxTimeout
    {
        get
        {
            return this.maxTimeoutField;
        }
        set
        {
            this.maxTimeoutField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public int MaxMessageLimit
    {
        get
        {
            return this.maxMessageLimitField;
        }
        set
        {
            this.maxMessageLimitField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlAnyElementAttribute(Order=2)]
    public System.Xml.XmlElement[] Any
    {
        get
        {
            return this.anyField;
        }
        set
        {
            this.anyField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://docs.oasis-open.org/wsn/b-2")]
public partial class TopicExpressionType
{

    private System.Xml.XmlNode[] anyField;

    private string dialectField;

    /// <remarks/>
    [System.Xml.Serialization.XmlTextAttribute()]
    [System.Xml.Serialization.XmlAnyElementAttribute(Order=0)]
    public System.Xml.XmlNode[] Any
    {
        get
        {
            return this.anyField;
        }
        set
        {
            this.anyField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlAttributeAttribute(DataType="anyURI")]
    public string Dialect
    {
        get
        {
            return this.dialectField;
        }
        set
        {
            this.dialectField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.w3.org/2005/08/addressing")]
public partial class MetadataType
{

    private System.Xml.XmlElement[] anyField;

    /// <remarks/>
    [System.Xml.Serialization.XmlAnyElementAttribute(Order=0)]
    public System.Xml.XmlElement[] Any
    {
        get
        {
            return this.anyField;
        }
        set
        {
            this.anyField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.w3.org/2005/08/addressing")]
public partial class ReferenceParametersType
{

    private System.Xml.XmlElement[] anyField;

    /// <remarks/>
    [System.Xml.Serialization.XmlAnyElementAttribute(Order=0)]
    public System.Xml.XmlElement[] Any
    {
        get
        {
            return this.anyField;
        }
        set
        {
            this.anyField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.w3.org/2005/08/addressing")]
public partial class AttributedURIType
{

    private string valueField;

    /// <remarks/>
    [System.Xml.Serialization.XmlTextAttribute(DataType="anyURI")]
    public string Value
    {
        get
        {
            return this.valueField;
        }
        set
        {
            this.valueField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.w3.org/2005/08/addressing")]
public partial class EndpointReferenceType
{

    private AttributedURIType addressField;

    private ReferenceParametersType referenceParametersField;

    private MetadataType metadataField;

    private System.Xml.XmlElement[] anyField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public AttributedURIType Address
    {
        get
        {
            return this.addressField;
        }
        set
        {
            this.addressField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public ReferenceParametersType ReferenceParameters
    {
        get
        {
            return this.referenceParametersField;
        }
        set
        {
            this.referenceParametersField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=2)]
    public MetadataType Metadata
    {
        get
        {
            return this.metadataField;
        }
        set
        {
            this.metadataField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlAnyElementAttribute(Order=3)]
    public System.Xml.XmlElement[] Any
    {
        get
        {
            return this.anyField;
        }
        set
        {
            this.anyField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://docs.oasis-open.org/wsn/b-2")]
public partial class NotificationMessageHolderType
{

    private EndpointReferenceType subscriptionReferenceField;

    private TopicExpressionType topicField;

    private EndpointReferenceType producerReferenceField;

    private System.Xml.XmlElement messageField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public EndpointReferenceType SubscriptionReference
    {
        get
        {
            return this.subscriptionReferenceField;
        }
        set
        {
            this.subscriptionReferenceField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public TopicExpressionType Topic
    {
        get
        {
            return this.topicField;
        }
        set
        {
            this.topicField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=2)]
    public EndpointReferenceType ProducerReference
    {
        get
        {
            return this.producerReferenceField;
        }
        set
        {
            this.producerReferenceField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=3)]
    public System.Xml.XmlElement Message
    {
        get
        {
            return this.messageField;
        }
        set
        {
            this.messageField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://docs.oasis-open.org/wsrf/r-2")]
public partial class ResourceUnknownFaultType : BaseFaultType
{
}

/// <remarks/>
[System.Xml.Serialization.XmlIncludeAttribute(typeof(ResourceUnavailableFaultType))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(ResourceUnknownFaultType))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(ResumeFailedFaultType))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(PauseFailedFaultType))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(UnableToDestroySubscriptionFaultType))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(UnacceptableTerminationTimeFaultType))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(UnableToCreatePullPointFaultType))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(UnableToDestroyPullPointFaultType))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(UnableToGetMessagesFaultType))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(NoCurrentMessageOnTopicFaultType))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(UnacceptableInitialTerminationTimeFaultType))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(NotifyMessageNotSupportedFaultType))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(UnsupportedPolicyRequestFaultType))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(UnrecognizedPolicyRequestFaultType))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(InvalidMessageContentExpressionFaultType))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(InvalidProducerPropertiesExpressionFaultType))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(MultipleTopicsSpecifiedFaultType))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(TopicNotSupportedFaultType))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(InvalidTopicExpressionFaultType))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(TopicExpressionDialectUnknownFaultType))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(InvalidFilterFaultType))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(SubscribeCreationFailedFaultType))]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://docs.oasis-open.org/wsrf/bf-2")]
public partial class BaseFaultType
{

    private System.Xml.XmlElement[] anyField;

    private System.DateTime timestampField;

    private EndpointReferenceType originatorField;

    private BaseFaultTypeErrorCode errorCodeField;

    private BaseFaultTypeDescription[] descriptionField;

    private System.Xml.XmlElement faultCauseField;

    /// <remarks/>
    [System.Xml.Serialization.XmlAnyElementAttribute(Order=0)]
    public System.Xml.XmlElement[] Any
    {
        get
        {
            return this.anyField;
        }
        set
        {
            this.anyField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public System.DateTime Timestamp
    {
        get
        {
            return this.timestampField;
        }
        set
        {
            this.timestampField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=2)]
    public EndpointReferenceType Originator
    {
        get
        {
            return this.originatorField;
        }
        set
        {
            this.originatorField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=3)]
    public BaseFaultTypeErrorCode ErrorCode
    {
        get
        {
            return this.errorCodeField;
        }
        set
        {
            this.errorCodeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("Description", Order=4)]
    public BaseFaultTypeDescription[] Description
    {
        get
        {
            return this.descriptionField;
        }
        set
        {
            this.descriptionField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=5)]
    public System.Xml.XmlElement FaultCause
    {
        get
        {
            return this.faultCauseField;
        }
        set
        {
            this.faultCauseField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://docs.oasis-open.org/wsrf/bf-2")]
public partial class BaseFaultTypeErrorCode
{

    private string dialectField;

    private string[] textField;

    /// <remarks/>
    [System.Xml.Serialization.XmlAttributeAttribute(DataType="anyURI")]
    public string dialect
    {
        get
        {
            return this.dialectField;
        }
        set
        {
            this.dialectField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlTextAttribute()]
    public string[] Text
    {
        get
        {
            return this.textField;
        }
        set
        {
            this.textField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://docs.oasis-open.org/wsrf/bf-2")]
public partial class BaseFaultTypeDescription
{

    private string langField;

    private string valueField;

    /// <remarks/>
    [System.Xml.Serialization.XmlAttributeAttribute(Form=System.Xml.Schema.XmlSchemaForm.Qualified, Namespace="http://www.w3.org/XML/1998/namespace")]
    public string lang
    {
        get
        {
            return this.langField;
        }
        set
        {
            this.langField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlTextAttribute()]
    public string Value
    {
        get
        {
            return this.valueField;
        }
        set
        {
            this.valueField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://docs.oasis-open.org/wsrf/r-2")]
public partial class ResourceUnavailableFaultType : BaseFaultType
{
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://docs.oasis-open.org/wsn/b-2")]
public partial class ResumeFailedFaultType : BaseFaultType
{
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://docs.oasis-open.org/wsn/b-2")]
public partial class PauseFailedFaultType : BaseFaultType
{
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://docs.oasis-open.org/wsn/b-2")]
public partial class UnableToDestroySubscriptionFaultType : BaseFaultType
{
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://docs.oasis-open.org/wsn/b-2")]
public partial class UnacceptableTerminationTimeFaultType : BaseFaultType
{

    private System.DateTime minimumTimeField;

    private System.DateTime maximumTimeField;

    private bool maximumTimeFieldSpecified;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public System.DateTime MinimumTime
    {
        get
        {
            return this.minimumTimeField;
        }
        set
        {
            this.minimumTimeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public System.DateTime MaximumTime
    {
        get
        {
            return this.maximumTimeField;
        }
        set
        {
            this.maximumTimeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool MaximumTimeSpecified
    {
        get
        {
            return this.maximumTimeFieldSpecified;
        }
        set
        {
            this.maximumTimeFieldSpecified = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://docs.oasis-open.org/wsn/b-2")]
public partial class UnableToCreatePullPointFaultType : BaseFaultType
{
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://docs.oasis-open.org/wsn/b-2")]
public partial class UnableToDestroyPullPointFaultType : BaseFaultType
{
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://docs.oasis-open.org/wsn/b-2")]
public partial class UnableToGetMessagesFaultType : BaseFaultType
{
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://docs.oasis-open.org/wsn/b-2")]
public partial class NoCurrentMessageOnTopicFaultType : BaseFaultType
{
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://docs.oasis-open.org/wsn/b-2")]
public partial class UnacceptableInitialTerminationTimeFaultType : BaseFaultType
{

    private System.DateTime minimumTimeField;

    private System.DateTime maximumTimeField;

    private bool maximumTimeFieldSpecified;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public System.DateTime MinimumTime
    {
        get
        {
            return this.minimumTimeField;
        }
        set
        {
            this.minimumTimeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public System.DateTime MaximumTime
    {
        get
        {
            return this.maximumTimeField;
        }
        set
        {
            this.maximumTimeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool MaximumTimeSpecified
    {
        get
        {
            return this.maximumTimeFieldSpecified;
        }
        set
        {
            this.maximumTimeFieldSpecified = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://docs.oasis-open.org/wsn/b-2")]
public partial class NotifyMessageNotSupportedFaultType : BaseFaultType
{
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://docs.oasis-open.org/wsn/b-2")]
public partial class UnsupportedPolicyRequestFaultType : BaseFaultType
{

    private System.Xml.XmlQualifiedName[] unsupportedPolicyField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("UnsupportedPolicy", Order=0)]
    public System.Xml.XmlQualifiedName[] UnsupportedPolicy
    {
        get
        {
            return this.unsupportedPolicyField;
        }
        set
        {
            this.unsupportedPolicyField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://docs.oasis-open.org/wsn/b-2")]
public partial class UnrecognizedPolicyRequestFaultType : BaseFaultType
{

    private System.Xml.XmlQualifiedName[] unrecognizedPolicyField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("UnrecognizedPolicy", Order=0)]
    public System.Xml.XmlQualifiedName[] UnrecognizedPolicy
    {
        get
        {
            return this.unrecognizedPolicyField;
        }
        set
        {
            this.unrecognizedPolicyField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://docs.oasis-open.org/wsn/b-2")]
public partial class InvalidMessageContentExpressionFaultType : BaseFaultType
{
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://docs.oasis-open.org/wsn/b-2")]
public partial class InvalidProducerPropertiesExpressionFaultType : BaseFaultType
{
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://docs.oasis-open.org/wsn/b-2")]
public partial class MultipleTopicsSpecifiedFaultType : BaseFaultType
{
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://docs.oasis-open.org/wsn/b-2")]
public partial class TopicNotSupportedFaultType : BaseFaultType
{
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://docs.oasis-open.org/wsn/b-2")]
public partial class InvalidTopicExpressionFaultType : BaseFaultType
{
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://docs.oasis-open.org/wsn/b-2")]
public partial class TopicExpressionDialectUnknownFaultType : BaseFaultType
{
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://docs.oasis-open.org/wsn/b-2")]
public partial class InvalidFilterFaultType : BaseFaultType
{

    private System.Xml.XmlQualifiedName[] unknownFilterField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("UnknownFilter", Order=0)]
    public System.Xml.XmlQualifiedName[] UnknownFilter
    {
        get
        {
            return this.unknownFilterField;
        }
        set
        {
            this.unknownFilterField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://docs.oasis-open.org/wsn/b-2")]
public partial class SubscribeCreationFailedFaultType : BaseFaultType
{
}

[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.ServiceModel.ServiceContractAttribute(Namespace="http://docs.oasis-open.org/wsn/bw-2", ConfigurationName="OnvifProxy.NotificationConsumer")]
public interface NotificationConsumer
{

    [System.ServiceModel.OperationContractAttribute(IsOneWay=true, Action="http://docs.oasis-open.org/wsn/bw-2/NotificationConsumer/Notify")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    System.Threading.Tasks.Task NotifyAsync(OnvifProxy.Notify1 request);
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://docs.oasis-open.org/wsn/b-2")]
public partial class Notify
{

    private NotificationMessageHolderType[] notificationMessageField;

    private System.Xml.XmlElement[] anyField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("NotificationMessage", Order=0)]
    public NotificationMessageHolderType[] NotificationMessage
    {
        get
        {
            return this.notificationMessageField;
        }
        set
        {
            this.notificationMessageField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlAnyElementAttribute(Order=1)]
    public System.Xml.XmlElement[] Any
    {
        get
        {
            return this.anyField;
        }
        set
        {
            this.anyField = value;
        }
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
public partial class Notify1
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://docs.oasis-open.org/wsn/b-2", Order=0)]
    public OnvifProxy.Notify Notify;

    public Notify1()
    {
    }

    public Notify1(OnvifProxy.Notify Notify)
    {
        this.Notify = Notify;
    }
}

[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
public interface NotificationConsumerChannel : OnvifProxy.NotificationConsumer, System.ServiceModel.IClientChannel
{
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
public partial class NotificationConsumerClient : System.ServiceModel.ClientBase<OnvifProxy.NotificationConsumer>, OnvifProxy.NotificationConsumer
{

    public NotificationConsumerClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) :
            base(binding, remoteAddress)
    {
    }

    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    System.Threading.Tasks.Task OnvifProxy.NotificationConsumer.NotifyAsync(OnvifProxy.Notify1 request)
    {
        return base.Channel.NotifyAsync(request);
    }

    public System.Threading.Tasks.Task NotifyAsync(OnvifProxy.Notify Notify)
    {
        OnvifProxy.Notify1 inValue = new OnvifProxy.Notify1();
        inValue.Notify = Notify;
        return ((OnvifProxy.NotificationConsumer)(this)).NotifyAsync(inValue);
    }

    public virtual System.Threading.Tasks.Task OpenAsync()
    {
        return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginOpen(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndOpen));
    }
}

[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.ServiceModel.ServiceContractAttribute(Namespace="http://docs.oasis-open.org/wsn/bw-2", ConfigurationName="OnvifProxy.NotificationProducer")]
public interface NotificationProducer
{

    [System.ServiceModel.OperationContractAttribute(Action="http://docs.oasis-open.org/wsn/bw-2/NotificationProducer/SubscribeRequest", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(OnvifProxy.ResourceUnknownFaultType), Action="http://docs.oasis-open.org/wsn/bw-2/NotificationProducer/SubscribeRequest", Name="ResourceUnknownFault", Namespace="http://docs.oasis-open.org/wsrf/r-2")]
    [System.ServiceModel.FaultContractAttribute(typeof(OnvifProxy.InvalidFilterFaultType), Action="http://docs.oasis-open.org/wsn/bw-2/NotificationProducer/SubscribeRequest", Name="InvalidFilterFault", Namespace="http://docs.oasis-open.org/wsn/b-2")]
    [System.ServiceModel.FaultContractAttribute(typeof(OnvifProxy.TopicExpressionDialectUnknownFaultType), Action="http://docs.oasis-open.org/wsn/bw-2/NotificationProducer/SubscribeRequest", Name="TopicExpressionDialectUnknownFault", Namespace="http://docs.oasis-open.org/wsn/b-2")]
    [System.ServiceModel.FaultContractAttribute(typeof(OnvifProxy.InvalidTopicExpressionFaultType), Action="http://docs.oasis-open.org/wsn/bw-2/NotificationProducer/SubscribeRequest", Name="InvalidTopicExpressionFault", Namespace="http://docs.oasis-open.org/wsn/b-2")]
    [System.ServiceModel.FaultContractAttribute(typeof(OnvifProxy.TopicNotSupportedFaultType), Action="http://docs.oasis-open.org/wsn/bw-2/NotificationProducer/SubscribeRequest", Name="TopicNotSupportedFault", Namespace="http://docs.oasis-open.org/wsn/b-2")]
    [System.ServiceModel.FaultContractAttribute(typeof(OnvifProxy.InvalidProducerPropertiesExpressionFaultType), Action="http://docs.oasis-open.org/wsn/bw-2/NotificationProducer/SubscribeRequest", Name="InvalidProducerPropertiesExpressionFault", Namespace="http://docs.oasis-open.org/wsn/b-2")]
    [System.ServiceModel.FaultContractAttribute(typeof(OnvifProxy.InvalidMessageContentExpressionFaultType), Action="http://docs.oasis-open.org/wsn/bw-2/NotificationProducer/SubscribeRequest", Name="InvalidMessageContentExpressionFault", Namespace="http://docs.oasis-open.org/wsn/b-2")]
    [System.ServiceModel.FaultContractAttribute(typeof(OnvifProxy.UnacceptableInitialTerminationTimeFaultType), Action="http://docs.oasis-open.org/wsn/bw-2/NotificationProducer/SubscribeRequest", Name="UnacceptableInitialTerminationTimeFault", Namespace="http://docs.oasis-open.org/wsn/b-2")]
    [System.ServiceModel.FaultContractAttribute(typeof(OnvifProxy.UnrecognizedPolicyRequestFaultType), Action="http://docs.oasis-open.org/wsn/bw-2/NotificationProducer/SubscribeRequest", Name="UnrecognizedPolicyRequestFault", Namespace="http://docs.oasis-open.org/wsn/b-2")]
    [System.ServiceModel.FaultContractAttribute(typeof(OnvifProxy.UnsupportedPolicyRequestFaultType), Action="http://docs.oasis-open.org/wsn/bw-2/NotificationProducer/SubscribeRequest", Name="UnsupportedPolicyRequestFault", Namespace="http://docs.oasis-open.org/wsn/b-2")]
    [System.ServiceModel.FaultContractAttribute(typeof(OnvifProxy.NotifyMessageNotSupportedFaultType), Action="http://docs.oasis-open.org/wsn/bw-2/NotificationProducer/SubscribeRequest", Name="NotifyMessageNotSupportedFault", Namespace="http://docs.oasis-open.org/wsn/b-2")]
    [System.ServiceModel.FaultContractAttribute(typeof(OnvifProxy.SubscribeCreationFailedFaultType), Action="http://docs.oasis-open.org/wsn/bw-2/NotificationProducer/SubscribeRequest", Name="SubscribeCreationFailedFault", Namespace="http://docs.oasis-open.org/wsn/b-2")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    System.Threading.Tasks.Task<OnvifProxy.SubscribeResponse1> SubscribeAsync(OnvifProxy.SubscribeRequest request);

    [System.ServiceModel.OperationContractAttribute(Action="http://docs.oasis-open.org/wsn/bw-2/NotificationProducer/GetCurrentMessageRequest" +
        "", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(OnvifProxy.ResourceUnknownFaultType), Action="http://docs.oasis-open.org/wsn/bw-2/NotificationProducer/GetCurrentMessageRequest" +
        "", Name="ResourceUnknownFault", Namespace="http://docs.oasis-open.org/wsrf/r-2")]
    [System.ServiceModel.FaultContractAttribute(typeof(OnvifProxy.TopicExpressionDialectUnknownFaultType), Action="http://docs.oasis-open.org/wsn/bw-2/NotificationProducer/GetCurrentMessageRequest" +
        "", Name="TopicExpressionDialectUnknownFault", Namespace="http://docs.oasis-open.org/wsn/b-2")]
    [System.ServiceModel.FaultContractAttribute(typeof(OnvifProxy.InvalidTopicExpressionFaultType), Action="http://docs.oasis-open.org/wsn/bw-2/NotificationProducer/GetCurrentMessageRequest" +
        "", Name="InvalidTopicExpressionFault", Namespace="http://docs.oasis-open.org/wsn/b-2")]
    [System.ServiceModel.FaultContractAttribute(typeof(OnvifProxy.TopicNotSupportedFaultType), Action="http://docs.oasis-open.org/wsn/bw-2/NotificationProducer/GetCurrentMessageRequest" +
        "", Name="TopicNotSupportedFault", Namespace="http://docs.oasis-open.org/wsn/b-2")]
    [System.ServiceModel.FaultContractAttribute(typeof(OnvifProxy.NoCurrentMessageOnTopicFaultType), Action="http://docs.oasis-open.org/wsn/bw-2/NotificationProducer/GetCurrentMessageRequest" +
        "", Name="NoCurrentMessageOnTopicFault", Namespace="http://docs.oasis-open.org/wsn/b-2")]
    [System.ServiceModel.FaultContractAttribute(typeof(OnvifProxy.MultipleTopicsSpecifiedFaultType), Action="http://docs.oasis-open.org/wsn/bw-2/NotificationProducer/GetCurrentMessageRequest" +
        "", Name="MultipleTopicsSpecifiedFault", Namespace="http://docs.oasis-open.org/wsn/b-2")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    System.Threading.Tasks.Task<OnvifProxy.GetCurrentMessageResponse1> GetCurrentMessageAsync(OnvifProxy.GetCurrentMessageRequest request);
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://docs.oasis-open.org/wsn/b-2")]
public partial class Subscribe
{

    private EndpointReferenceType consumerReferenceField;

    private FilterType filterField;

    private string initialTerminationTimeField;

    private SubscribeSubscriptionPolicy subscriptionPolicyField;

    private System.Xml.XmlElement[] anyField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public EndpointReferenceType ConsumerReference
    {
        get
        {
            return this.consumerReferenceField;
        }
        set
        {
            this.consumerReferenceField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public FilterType Filter
    {
        get
        {
            return this.filterField;
        }
        set
        {
            this.filterField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=2)]
    public string InitialTerminationTime
    {
        get
        {
            return this.initialTerminationTimeField;
        }
        set
        {
            this.initialTerminationTimeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=3)]
    public SubscribeSubscriptionPolicy SubscriptionPolicy
    {
        get
        {
            return this.subscriptionPolicyField;
        }
        set
        {
            this.subscriptionPolicyField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlAnyElementAttribute(Order=4)]
    public System.Xml.XmlElement[] Any
    {
        get
        {
            return this.anyField;
        }
        set
        {
            this.anyField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://docs.oasis-open.org/wsn/b-2")]
public partial class FilterType
{

    private System.Xml.XmlElement[] anyField;

    /// <remarks/>
    [System.Xml.Serialization.XmlAnyElementAttribute(Order=0)]
    public System.Xml.XmlElement[] Any
    {
        get
        {
            return this.anyField;
        }
        set
        {
            this.anyField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://docs.oasis-open.org/wsn/b-2")]
public partial class SubscribeSubscriptionPolicy
{

    private System.Xml.XmlElement[] anyField;

    /// <remarks/>
    [System.Xml.Serialization.XmlAnyElementAttribute(Order=0)]
    public System.Xml.XmlElement[] Any
    {
        get
        {
            return this.anyField;
        }
        set
        {
            this.anyField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://docs.oasis-open.org/wsn/b-2")]
public partial class SubscribeResponse
{

    private EndpointReferenceType subscriptionReferenceField;

    private System.DateTime currentTimeField;

    private bool currentTimeFieldSpecified;

    private System.Nullable<System.DateTime> terminationTimeField;

    private bool terminationTimeFieldSpecified;

    private System.Xml.XmlElement[] anyField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public EndpointReferenceType SubscriptionReference
    {
        get
        {
            return this.subscriptionReferenceField;
        }
        set
        {
            this.subscriptionReferenceField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public System.DateTime CurrentTime
    {
        get
        {
            return this.currentTimeField;
        }
        set
        {
            this.currentTimeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool CurrentTimeSpecified
    {
        get
        {
            return this.currentTimeFieldSpecified;
        }
        set
        {
            this.currentTimeFieldSpecified = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=2)]
    public System.Nullable<System.DateTime> TerminationTime
    {
        get
        {
            return this.terminationTimeField;
        }
        set
        {
            this.terminationTimeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool TerminationTimeSpecified
    {
        get
        {
            return this.terminationTimeFieldSpecified;
        }
        set
        {
            this.terminationTimeFieldSpecified = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlAnyElementAttribute(Order=3)]
    public System.Xml.XmlElement[] Any
    {
        get
        {
            return this.anyField;
        }
        set
        {
            this.anyField = value;
        }
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
public partial class SubscribeRequest
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://docs.oasis-open.org/wsn/b-2", Order=0)]
    public OnvifProxy.Subscribe Subscribe;

    public SubscribeRequest()
    {
    }

    public SubscribeRequest(OnvifProxy.Subscribe Subscribe)
    {
        this.Subscribe = Subscribe;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
public partial class SubscribeResponse1
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://docs.oasis-open.org/wsn/b-2", Order=0)]
    public OnvifProxy.SubscribeResponse SubscribeResponse;

    public SubscribeResponse1()
    {
    }

    public SubscribeResponse1(OnvifProxy.SubscribeResponse SubscribeResponse)
    {
        this.SubscribeResponse = SubscribeResponse;
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://docs.oasis-open.org/wsn/b-2")]
public partial class GetCurrentMessage
{

    private TopicExpressionType topicField;

    private System.Xml.XmlElement[] anyField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public TopicExpressionType Topic
    {
        get
        {
            return this.topicField;
        }
        set
        {
            this.topicField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlAnyElementAttribute(Order=1)]
    public System.Xml.XmlElement[] Any
    {
        get
        {
            return this.anyField;
        }
        set
        {
            this.anyField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://docs.oasis-open.org/wsn/b-2")]
public partial class GetCurrentMessageResponse
{

    private System.Xml.XmlElement[] anyField;

    /// <remarks/>
    [System.Xml.Serialization.XmlAnyElementAttribute(Order=0)]
    public System.Xml.XmlElement[] Any
    {
        get
        {
            return this.anyField;
        }
        set
        {
            this.anyField = value;
        }
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
public partial class GetCurrentMessageRequest
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://docs.oasis-open.org/wsn/b-2", Order=0)]
    public OnvifProxy.GetCurrentMessage GetCurrentMessage;

    public GetCurrentMessageRequest()
    {
    }

    public GetCurrentMessageRequest(OnvifProxy.GetCurrentMessage GetCurrentMessage)
    {
        this.GetCurrentMessage = GetCurrentMessage;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
public partial class GetCurrentMessageResponse1
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://docs.oasis-open.org/wsn/b-2", Order=0)]
    public OnvifProxy.GetCurrentMessageResponse GetCurrentMessageResponse;

    public GetCurrentMessageResponse1()
    {
    }

    public GetCurrentMessageResponse1(OnvifProxy.GetCurrentMessageResponse GetCurrentMessageResponse)
    {
        this.GetCurrentMessageResponse = GetCurrentMessageResponse;
    }
}

[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
public interface NotificationProducerChannel : OnvifProxy.NotificationProducer, System.ServiceModel.IClientChannel
{
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
public partial class NotificationProducerClient : System.ServiceModel.ClientBase<OnvifProxy.NotificationProducer>, OnvifProxy.NotificationProducer
{

    public NotificationProducerClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) :
            base(binding, remoteAddress)
    {
    }

    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    System.Threading.Tasks.Task<OnvifProxy.SubscribeResponse1> OnvifProxy.NotificationProducer.SubscribeAsync(OnvifProxy.SubscribeRequest request)
    {
        return base.Channel.SubscribeAsync(request);
    }

    public System.Threading.Tasks.Task<OnvifProxy.SubscribeResponse1> SubscribeAsync(OnvifProxy.Subscribe Subscribe)
    {
        OnvifProxy.SubscribeRequest inValue = new OnvifProxy.SubscribeRequest();
        inValue.Subscribe = Subscribe;
        return ((OnvifProxy.NotificationProducer)(this)).SubscribeAsync(inValue);
    }

    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    System.Threading.Tasks.Task<OnvifProxy.GetCurrentMessageResponse1> OnvifProxy.NotificationProducer.GetCurrentMessageAsync(OnvifProxy.GetCurrentMessageRequest request)
    {
        return base.Channel.GetCurrentMessageAsync(request);
    }

    public System.Threading.Tasks.Task<OnvifProxy.GetCurrentMessageResponse1> GetCurrentMessageAsync(OnvifProxy.GetCurrentMessage GetCurrentMessage)
    {
        OnvifProxy.GetCurrentMessageRequest inValue = new OnvifProxy.GetCurrentMessageRequest();
        inValue.GetCurrentMessage = GetCurrentMessage;
        return ((OnvifProxy.NotificationProducer)(this)).GetCurrentMessageAsync(inValue);
    }

    public virtual System.Threading.Tasks.Task OpenAsync()
    {
        return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginOpen(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndOpen));
    }
}

[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.ServiceModel.ServiceContractAttribute(Namespace="http://docs.oasis-open.org/wsn/bw-2", ConfigurationName="OnvifProxy.PullPoint")]
public interface PullPoint
{

    [System.ServiceModel.OperationContractAttribute(Action="http://docs.oasis-open.org/wsn/bw-2/PullPoint/GetMessagesRequest", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(OnvifProxy.ResourceUnknownFaultType), Action="http://docs.oasis-open.org/wsn/bw-2/PullPoint/GetMessagesRequest", Name="ResourceUnknownFault", Namespace="http://docs.oasis-open.org/wsrf/r-2")]
    [System.ServiceModel.FaultContractAttribute(typeof(OnvifProxy.UnableToGetMessagesFaultType), Action="http://docs.oasis-open.org/wsn/bw-2/PullPoint/GetMessagesRequest", Name="UnableToGetMessagesFault", Namespace="http://docs.oasis-open.org/wsn/b-2")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    System.Threading.Tasks.Task<OnvifProxy.GetMessagesResponse1> GetMessagesAsync(OnvifProxy.GetMessagesRequest request);

    [System.ServiceModel.OperationContractAttribute(Action="http://docs.oasis-open.org/wsn/bw-2/PullPoint/DestroyPullPointRequest", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(OnvifProxy.ResourceUnknownFaultType), Action="http://docs.oasis-open.org/wsn/bw-2/PullPoint/DestroyPullPointRequest", Name="ResourceUnknownFault", Namespace="http://docs.oasis-open.org/wsrf/r-2")]
    [System.ServiceModel.FaultContractAttribute(typeof(OnvifProxy.UnableToDestroyPullPointFaultType), Action="http://docs.oasis-open.org/wsn/bw-2/PullPoint/DestroyPullPointRequest", Name="UnableToDestroyPullPointFault", Namespace="http://docs.oasis-open.org/wsn/b-2")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    System.Threading.Tasks.Task<OnvifProxy.DestroyPullPointResponse1> DestroyPullPointAsync(OnvifProxy.DestroyPullPointRequest request);

    [System.ServiceModel.OperationContractAttribute(IsOneWay=true, Action="http://docs.oasis-open.org/wsn/bw-2/PullPoint/Notify")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    System.Threading.Tasks.Task NotifyAsync(OnvifProxy.Notify1 request);
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://docs.oasis-open.org/wsn/b-2")]
public partial class GetMessages
{

    private string maximumNumberField;

    private System.Xml.XmlElement[] anyField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(DataType="nonNegativeInteger", Order=0)]
    public string MaximumNumber
    {
        get
        {
            return this.maximumNumberField;
        }
        set
        {
            this.maximumNumberField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlAnyElementAttribute(Order=1)]
    public System.Xml.XmlElement[] Any
    {
        get
        {
            return this.anyField;
        }
        set
        {
            this.anyField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://docs.oasis-open.org/wsn/b-2")]
public partial class GetMessagesResponse
{

    private NotificationMessageHolderType[] notificationMessageField;

    private System.Xml.XmlElement[] anyField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("NotificationMessage", Order=0)]
    public NotificationMessageHolderType[] NotificationMessage
    {
        get
        {
            return this.notificationMessageField;
        }
        set
        {
            this.notificationMessageField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlAnyElementAttribute(Order=1)]
    public System.Xml.XmlElement[] Any
    {
        get
        {
            return this.anyField;
        }
        set
        {
            this.anyField = value;
        }
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
public partial class GetMessagesRequest
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://docs.oasis-open.org/wsn/b-2", Order=0)]
    public OnvifProxy.GetMessages GetMessages;

    public GetMessagesRequest()
    {
    }

    public GetMessagesRequest(OnvifProxy.GetMessages GetMessages)
    {
        this.GetMessages = GetMessages;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
public partial class GetMessagesResponse1
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://docs.oasis-open.org/wsn/b-2", Order=0)]
    public OnvifProxy.GetMessagesResponse GetMessagesResponse;

    public GetMessagesResponse1()
    {
    }

    public GetMessagesResponse1(OnvifProxy.GetMessagesResponse GetMessagesResponse)
    {
        this.GetMessagesResponse = GetMessagesResponse;
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://docs.oasis-open.org/wsn/b-2")]
public partial class DestroyPullPoint
{

    private System.Xml.XmlElement[] anyField;

    /// <remarks/>
    [System.Xml.Serialization.XmlAnyElementAttribute(Order=0)]
    public System.Xml.XmlElement[] Any
    {
        get
        {
            return this.anyField;
        }
        set
        {
            this.anyField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://docs.oasis-open.org/wsn/b-2")]
public partial class DestroyPullPointResponse
{

    private System.Xml.XmlElement[] anyField;

    /// <remarks/>
    [System.Xml.Serialization.XmlAnyElementAttribute(Order=0)]
    public System.Xml.XmlElement[] Any
    {
        get
        {
            return this.anyField;
        }
        set
        {
            this.anyField = value;
        }
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
public partial class DestroyPullPointRequest
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://docs.oasis-open.org/wsn/b-2", Order=0)]
    public OnvifProxy.DestroyPullPoint DestroyPullPoint;

    public DestroyPullPointRequest()
    {
    }

    public DestroyPullPointRequest(OnvifProxy.DestroyPullPoint DestroyPullPoint)
    {
        this.DestroyPullPoint = DestroyPullPoint;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
public partial class DestroyPullPointResponse1
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://docs.oasis-open.org/wsn/b-2", Order=0)]
    public OnvifProxy.DestroyPullPointResponse DestroyPullPointResponse;

    public DestroyPullPointResponse1()
    {
    }

    public DestroyPullPointResponse1(OnvifProxy.DestroyPullPointResponse DestroyPullPointResponse)
    {
        this.DestroyPullPointResponse = DestroyPullPointResponse;
    }
}

[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
public interface PullPointChannel : OnvifProxy.PullPoint, System.ServiceModel.IClientChannel
{
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
public partial class PullPointClient : System.ServiceModel.ClientBase<OnvifProxy.PullPoint>, OnvifProxy.PullPoint
{

    public PullPointClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) :
            base(binding, remoteAddress)
    {
    }

    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    System.Threading.Tasks.Task<OnvifProxy.GetMessagesResponse1> OnvifProxy.PullPoint.GetMessagesAsync(OnvifProxy.GetMessagesRequest request)
    {
        return base.Channel.GetMessagesAsync(request);
    }

    public System.Threading.Tasks.Task<OnvifProxy.GetMessagesResponse1> GetMessagesAsync(OnvifProxy.GetMessages GetMessages)
    {
        OnvifProxy.GetMessagesRequest inValue = new OnvifProxy.GetMessagesRequest();
        inValue.GetMessages = GetMessages;
        return ((OnvifProxy.PullPoint)(this)).GetMessagesAsync(inValue);
    }

    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    System.Threading.Tasks.Task<OnvifProxy.DestroyPullPointResponse1> OnvifProxy.PullPoint.DestroyPullPointAsync(OnvifProxy.DestroyPullPointRequest request)
    {
        return base.Channel.DestroyPullPointAsync(request);
    }

    public System.Threading.Tasks.Task<OnvifProxy.DestroyPullPointResponse1> DestroyPullPointAsync(OnvifProxy.DestroyPullPoint DestroyPullPoint)
    {
        OnvifProxy.DestroyPullPointRequest inValue = new OnvifProxy.DestroyPullPointRequest();
        inValue.DestroyPullPoint = DestroyPullPoint;
        return ((OnvifProxy.PullPoint)(this)).DestroyPullPointAsync(inValue);
    }

    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    System.Threading.Tasks.Task OnvifProxy.PullPoint.NotifyAsync(OnvifProxy.Notify1 request)
    {
        return base.Channel.NotifyAsync(request);
    }

    public System.Threading.Tasks.Task NotifyAsync(OnvifProxy.Notify Notify)
    {
        OnvifProxy.Notify1 inValue = new OnvifProxy.Notify1();
        inValue.Notify = Notify;
        return ((OnvifProxy.PullPoint)(this)).NotifyAsync(inValue);
    }

    public virtual System.Threading.Tasks.Task OpenAsync()
    {
        return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginOpen(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndOpen));
    }
}

[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.ServiceModel.ServiceContractAttribute(Namespace="http://docs.oasis-open.org/wsn/bw-2", ConfigurationName="OnvifProxy.CreatePullPoint")]
public interface CreatePullPoint
{

    [System.ServiceModel.OperationContractAttribute(Action="http://docs.oasis-open.org/wsn/bw-2/CreatePullPoint/CreatePullPointRequest", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(OnvifProxy.UnableToCreatePullPointFaultType), Action="http://docs.oasis-open.org/wsn/bw-2/CreatePullPoint/CreatePullPointRequest", Name="UnableToCreatePullPointFault", Namespace="http://docs.oasis-open.org/wsn/b-2")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    System.Threading.Tasks.Task<OnvifProxy.CreatePullPointResponse1> CreatePullPointAsync(OnvifProxy.CreatePullPointRequest request);
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://docs.oasis-open.org/wsn/b-2")]
public partial class CreatePullPoint1
{

    private System.Xml.XmlElement[] anyField;

    /// <remarks/>
    [System.Xml.Serialization.XmlAnyElementAttribute(Order=0)]
    public System.Xml.XmlElement[] Any
    {
        get
        {
            return this.anyField;
        }
        set
        {
            this.anyField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://docs.oasis-open.org/wsn/b-2")]
public partial class CreatePullPointResponse
{

    private EndpointReferenceType pullPointField;

    private System.Xml.XmlElement[] anyField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public EndpointReferenceType PullPoint
    {
        get
        {
            return this.pullPointField;
        }
        set
        {
            this.pullPointField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlAnyElementAttribute(Order=1)]
    public System.Xml.XmlElement[] Any
    {
        get
        {
            return this.anyField;
        }
        set
        {
            this.anyField = value;
        }
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
public partial class CreatePullPointRequest
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://docs.oasis-open.org/wsn/b-2", Order=0)]
    public OnvifProxy.CreatePullPoint CreatePullPoint;

    public CreatePullPointRequest()
    {
    }

    public CreatePullPointRequest(OnvifProxy.CreatePullPoint CreatePullPoint)
    {
        this.CreatePullPoint = CreatePullPoint;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
public partial class CreatePullPointResponse1
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://docs.oasis-open.org/wsn/b-2", Order=0)]
    public OnvifProxy.CreatePullPointResponse CreatePullPointResponse;

    public CreatePullPointResponse1()
    {
    }

    public CreatePullPointResponse1(OnvifProxy.CreatePullPointResponse CreatePullPointResponse)
    {
        this.CreatePullPointResponse = CreatePullPointResponse;
    }
}

[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
public interface CreatePullPointChannel : OnvifProxy.CreatePullPoint, System.ServiceModel.IClientChannel
{
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
public partial class CreatePullPointClient : System.ServiceModel.ClientBase<OnvifProxy.CreatePullPoint>, OnvifProxy.CreatePullPoint
{

    public CreatePullPointClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) :
            base(binding, remoteAddress)
    {
    }

    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    System.Threading.Tasks.Task<OnvifProxy.CreatePullPointResponse1> OnvifProxy.CreatePullPoint.CreatePullPointAsync(OnvifProxy.CreatePullPointRequest request)
    {
        return base.Channel.CreatePullPointAsync(request);
    }

    public System.Threading.Tasks.Task<OnvifProxy.CreatePullPointResponse1> CreatePullPointAsync(OnvifProxy.CreatePullPoint CreatePullPoint)
    {
        OnvifProxy.CreatePullPointRequest inValue = new OnvifProxy.CreatePullPointRequest();
        inValue.CreatePullPoint = CreatePullPoint;
        return ((OnvifProxy.CreatePullPoint)(this)).CreatePullPointAsync(inValue);
    }

    public virtual System.Threading.Tasks.Task OpenAsync()
    {
        return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginOpen(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndOpen));
    }
}

[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.ServiceModel.ServiceContractAttribute(Namespace="http://docs.oasis-open.org/wsn/bw-2", ConfigurationName="OnvifProxy.SubscriptionManager")]
public interface SubscriptionManager
{

    [System.ServiceModel.OperationContractAttribute(Action="http://docs.oasis-open.org/wsn/bw-2/SubscriptionManager/RenewRequest", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(OnvifProxy.ResourceUnknownFaultType), Action="http://docs.oasis-open.org/wsn/bw-2/SubscriptionManager/RenewRequest", Name="ResourceUnknownFault", Namespace="http://docs.oasis-open.org/wsrf/r-2")]
    [System.ServiceModel.FaultContractAttribute(typeof(OnvifProxy.UnacceptableTerminationTimeFaultType), Action="http://docs.oasis-open.org/wsn/bw-2/SubscriptionManager/RenewRequest", Name="UnacceptableTerminationTimeFault", Namespace="http://docs.oasis-open.org/wsn/b-2")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    System.Threading.Tasks.Task<OnvifProxy.RenewResponse1> RenewAsync(OnvifProxy.RenewRequest request);

    [System.ServiceModel.OperationContractAttribute(Action="http://docs.oasis-open.org/wsn/bw-2/SubscriptionManager/UnsubscribeRequest", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(OnvifProxy.ResourceUnknownFaultType), Action="http://docs.oasis-open.org/wsn/bw-2/SubscriptionManager/UnsubscribeRequest", Name="ResourceUnknownFault", Namespace="http://docs.oasis-open.org/wsrf/r-2")]
    [System.ServiceModel.FaultContractAttribute(typeof(OnvifProxy.UnableToDestroySubscriptionFaultType), Action="http://docs.oasis-open.org/wsn/bw-2/SubscriptionManager/UnsubscribeRequest", Name="UnableToDestroySubscriptionFault", Namespace="http://docs.oasis-open.org/wsn/b-2")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    System.Threading.Tasks.Task<OnvifProxy.UnsubscribeResponse1> UnsubscribeAsync(OnvifProxy.UnsubscribeRequest request);
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://docs.oasis-open.org/wsn/b-2")]
public partial class Renew
{

    private string terminationTimeField;

    private System.Xml.XmlElement[] anyField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
    public string TerminationTime
    {
        get
        {
            return this.terminationTimeField;
        }
        set
        {
            this.terminationTimeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlAnyElementAttribute(Order=1)]
    public System.Xml.XmlElement[] Any
    {
        get
        {
            return this.anyField;
        }
        set
        {
            this.anyField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://docs.oasis-open.org/wsn/b-2")]
public partial class RenewResponse
{

    private System.Nullable<System.DateTime> terminationTimeField;

    private System.DateTime currentTimeField;

    private bool currentTimeFieldSpecified;

    private System.Xml.XmlElement[] anyField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
    public System.Nullable<System.DateTime> TerminationTime
    {
        get
        {
            return this.terminationTimeField;
        }
        set
        {
            this.terminationTimeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public System.DateTime CurrentTime
    {
        get
        {
            return this.currentTimeField;
        }
        set
        {
            this.currentTimeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool CurrentTimeSpecified
    {
        get
        {
            return this.currentTimeFieldSpecified;
        }
        set
        {
            this.currentTimeFieldSpecified = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlAnyElementAttribute(Order=2)]
    public System.Xml.XmlElement[] Any
    {
        get
        {
            return this.anyField;
        }
        set
        {
            this.anyField = value;
        }
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
public partial class RenewRequest
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://docs.oasis-open.org/wsn/b-2", Order=0)]
    public OnvifProxy.Renew Renew;

    public RenewRequest()
    {
    }

    public RenewRequest(OnvifProxy.Renew Renew)
    {
        this.Renew = Renew;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
public partial class RenewResponse1
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://docs.oasis-open.org/wsn/b-2", Order=0)]
    public OnvifProxy.RenewResponse RenewResponse;

    public RenewResponse1()
    {
    }

    public RenewResponse1(OnvifProxy.RenewResponse RenewResponse)
    {
        this.RenewResponse = RenewResponse;
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://docs.oasis-open.org/wsn/b-2")]
public partial class Unsubscribe
{

    private System.Xml.XmlElement[] anyField;

    /// <remarks/>
    [System.Xml.Serialization.XmlAnyElementAttribute(Order=0)]
    public System.Xml.XmlElement[] Any
    {
        get
        {
            return this.anyField;
        }
        set
        {
            this.anyField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://docs.oasis-open.org/wsn/b-2")]
public partial class UnsubscribeResponse
{

    private System.Xml.XmlElement[] anyField;

    /// <remarks/>
    [System.Xml.Serialization.XmlAnyElementAttribute(Order=0)]
    public System.Xml.XmlElement[] Any
    {
        get
        {
            return this.anyField;
        }
        set
        {
            this.anyField = value;
        }
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
public partial class UnsubscribeRequest
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://docs.oasis-open.org/wsn/b-2", Order=0)]
    public OnvifProxy.Unsubscribe Unsubscribe;

    public UnsubscribeRequest()
    {
    }

    public UnsubscribeRequest(OnvifProxy.Unsubscribe Unsubscribe)
    {
        this.Unsubscribe = Unsubscribe;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
public partial class UnsubscribeResponse1
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://docs.oasis-open.org/wsn/b-2", Order=0)]
    public OnvifProxy.UnsubscribeResponse UnsubscribeResponse;

    public UnsubscribeResponse1()
    {
    }

    public UnsubscribeResponse1(OnvifProxy.UnsubscribeResponse UnsubscribeResponse)
    {
        this.UnsubscribeResponse = UnsubscribeResponse;
    }
}

[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
public interface SubscriptionManagerChannel : OnvifProxy.SubscriptionManager, System.ServiceModel.IClientChannel
{
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
public partial class SubscriptionManagerClient : System.ServiceModel.ClientBase<OnvifProxy.SubscriptionManager>, OnvifProxy.SubscriptionManager
{

    public SubscriptionManagerClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) :
            base(binding, remoteAddress)
    {
    }

    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    System.Threading.Tasks.Task<OnvifProxy.RenewResponse1> OnvifProxy.SubscriptionManager.RenewAsync(OnvifProxy.RenewRequest request)
    {
        return base.Channel.RenewAsync(request);
    }

    public System.Threading.Tasks.Task<OnvifProxy.RenewResponse1> RenewAsync(OnvifProxy.Renew Renew)
    {
        OnvifProxy.RenewRequest inValue = new OnvifProxy.RenewRequest();
        inValue.Renew = Renew;
        return ((OnvifProxy.SubscriptionManager)(this)).RenewAsync(inValue);
    }

    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    System.Threading.Tasks.Task<OnvifProxy.UnsubscribeResponse1> OnvifProxy.SubscriptionManager.UnsubscribeAsync(OnvifProxy.UnsubscribeRequest request)
    {
        return base.Channel.UnsubscribeAsync(request);
    }

    public System.Threading.Tasks.Task<OnvifProxy.UnsubscribeResponse1> UnsubscribeAsync(OnvifProxy.Unsubscribe Unsubscribe)
    {
        OnvifProxy.UnsubscribeRequest inValue = new OnvifProxy.UnsubscribeRequest();
        inValue.Unsubscribe = Unsubscribe;
        return ((OnvifProxy.SubscriptionManager)(this)).UnsubscribeAsync(inValue);
    }

    public virtual System.Threading.Tasks.Task OpenAsync()
    {
        return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginOpen(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndOpen));
    }
}

[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.ServiceModel.ServiceContractAttribute(Namespace="http://docs.oasis-open.org/wsn/bw-2", ConfigurationName="OnvifProxy.PausableSubscriptionManager")]
public interface PausableSubscriptionManager
{

    [System.ServiceModel.OperationContractAttribute(Action="http://docs.oasis-open.org/wsn/bw-2/PausableSubscriptionManager/RenewRequest", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(OnvifProxy.ResourceUnknownFaultType), Action="http://docs.oasis-open.org/wsn/bw-2/PausableSubscriptionManager/RenewRequest", Name="ResourceUnknownFault", Namespace="http://docs.oasis-open.org/wsrf/r-2")]
    [System.ServiceModel.FaultContractAttribute(typeof(OnvifProxy.UnacceptableTerminationTimeFaultType), Action="http://docs.oasis-open.org/wsn/bw-2/PausableSubscriptionManager/RenewRequest", Name="UnacceptableTerminationTimeFault", Namespace="http://docs.oasis-open.org/wsn/b-2")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    System.Threading.Tasks.Task<OnvifProxy.RenewResponse1> RenewAsync(OnvifProxy.RenewRequest request);

    [System.ServiceModel.OperationContractAttribute(Action="http://docs.oasis-open.org/wsn/bw-2/PausableSubscriptionManager/UnsubscribeReques" +
        "t", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(OnvifProxy.ResourceUnknownFaultType), Action="http://docs.oasis-open.org/wsn/bw-2/PausableSubscriptionManager/UnsubscribeReques" +
        "t", Name="ResourceUnknownFault", Namespace="http://docs.oasis-open.org/wsrf/r-2")]
    [System.ServiceModel.FaultContractAttribute(typeof(OnvifProxy.UnableToDestroySubscriptionFaultType), Action="http://docs.oasis-open.org/wsn/bw-2/PausableSubscriptionManager/UnsubscribeReques" +
        "t", Name="UnableToDestroySubscriptionFault", Namespace="http://docs.oasis-open.org/wsn/b-2")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    System.Threading.Tasks.Task<OnvifProxy.UnsubscribeResponse1> UnsubscribeAsync(OnvifProxy.UnsubscribeRequest request);

    [System.ServiceModel.OperationContractAttribute(Action="http://docs.oasis-open.org/wsn/bw-2/PausableSubscriptionManager/PauseSubscription" +
        "Request", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(OnvifProxy.ResourceUnknownFaultType), Action="http://docs.oasis-open.org/wsn/bw-2/PausableSubscriptionManager/PauseSubscription" +
        "Request", Name="ResourceUnknownFault", Namespace="http://docs.oasis-open.org/wsrf/r-2")]
    [System.ServiceModel.FaultContractAttribute(typeof(OnvifProxy.PauseFailedFaultType), Action="http://docs.oasis-open.org/wsn/bw-2/PausableSubscriptionManager/PauseSubscription" +
        "Request", Name="PauseFailedFault", Namespace="http://docs.oasis-open.org/wsn/b-2")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    System.Threading.Tasks.Task<OnvifProxy.PauseSubscriptionResponse1> PauseSubscriptionAsync(OnvifProxy.PauseSubscriptionRequest request);

    [System.ServiceModel.OperationContractAttribute(Action="http://docs.oasis-open.org/wsn/bw-2/PausableSubscriptionManager/ResumeSubscriptio" +
        "nRequest", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(OnvifProxy.ResourceUnknownFaultType), Action="http://docs.oasis-open.org/wsn/bw-2/PausableSubscriptionManager/ResumeSubscriptio" +
        "nRequest", Name="ResourceUnknownFault", Namespace="http://docs.oasis-open.org/wsrf/r-2")]
    [System.ServiceModel.FaultContractAttribute(typeof(OnvifProxy.ResumeFailedFaultType), Action="http://docs.oasis-open.org/wsn/bw-2/PausableSubscriptionManager/ResumeSubscriptio" +
        "nRequest", Name="ResumeFailedFault", Namespace="http://docs.oasis-open.org/wsn/b-2")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    System.Threading.Tasks.Task<OnvifProxy.ResumeSubscriptionResponse1> ResumeSubscriptionAsync(OnvifProxy.ResumeSubscriptionRequest request);
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://docs.oasis-open.org/wsn/b-2")]
public partial class PauseSubscription
{

    private System.Xml.XmlElement[] anyField;

    /// <remarks/>
    [System.Xml.Serialization.XmlAnyElementAttribute(Order=0)]
    public System.Xml.XmlElement[] Any
    {
        get
        {
            return this.anyField;
        }
        set
        {
            this.anyField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://docs.oasis-open.org/wsn/b-2")]
public partial class PauseSubscriptionResponse
{

    private System.Xml.XmlElement[] anyField;

    /// <remarks/>
    [System.Xml.Serialization.XmlAnyElementAttribute(Order=0)]
    public System.Xml.XmlElement[] Any
    {
        get
        {
            return this.anyField;
        }
        set
        {
            this.anyField = value;
        }
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
public partial class PauseSubscriptionRequest
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://docs.oasis-open.org/wsn/b-2", Order=0)]
    public OnvifProxy.PauseSubscription PauseSubscription;

    public PauseSubscriptionRequest()
    {
    }

    public PauseSubscriptionRequest(OnvifProxy.PauseSubscription PauseSubscription)
    {
        this.PauseSubscription = PauseSubscription;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
public partial class PauseSubscriptionResponse1
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://docs.oasis-open.org/wsn/b-2", Order=0)]
    public OnvifProxy.PauseSubscriptionResponse PauseSubscriptionResponse;

    public PauseSubscriptionResponse1()
    {
    }

    public PauseSubscriptionResponse1(OnvifProxy.PauseSubscriptionResponse PauseSubscriptionResponse)
    {
        this.PauseSubscriptionResponse = PauseSubscriptionResponse;
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://docs.oasis-open.org/wsn/b-2")]
public partial class ResumeSubscription
{

    private System.Xml.XmlElement[] anyField;

    /// <remarks/>
    [System.Xml.Serialization.XmlAnyElementAttribute(Order=0)]
    public System.Xml.XmlElement[] Any
    {
        get
        {
            return this.anyField;
        }
        set
        {
            this.anyField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://docs.oasis-open.org/wsn/b-2")]
public partial class ResumeSubscriptionResponse
{

    private System.Xml.XmlElement[] anyField;

    /// <remarks/>
    [System.Xml.Serialization.XmlAnyElementAttribute(Order=0)]
    public System.Xml.XmlElement[] Any
    {
        get
        {
            return this.anyField;
        }
        set
        {
            this.anyField = value;
        }
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
public partial class ResumeSubscriptionRequest
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://docs.oasis-open.org/wsn/b-2", Order=0)]
    public OnvifProxy.ResumeSubscription ResumeSubscription;

    public ResumeSubscriptionRequest()
    {
    }

    public ResumeSubscriptionRequest(OnvifProxy.ResumeSubscription ResumeSubscription)
    {
        this.ResumeSubscription = ResumeSubscription;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
public partial class ResumeSubscriptionResponse1
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://docs.oasis-open.org/wsn/b-2", Order=0)]
    public OnvifProxy.ResumeSubscriptionResponse ResumeSubscriptionResponse;

    public ResumeSubscriptionResponse1()
    {
    }

    public ResumeSubscriptionResponse1(OnvifProxy.ResumeSubscriptionResponse ResumeSubscriptionResponse)
    {
        this.ResumeSubscriptionResponse = ResumeSubscriptionResponse;
    }
}

[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
public interface PausableSubscriptionManagerChannel : OnvifProxy.PausableSubscriptionManager, System.ServiceModel.IClientChannel
{
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
public partial class PausableSubscriptionManagerClient : System.ServiceModel.ClientBase<OnvifProxy.PausableSubscriptionManager>, OnvifProxy.PausableSubscriptionManager
{

    public PausableSubscriptionManagerClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) :
            base(binding, remoteAddress)
    {
    }

    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    System.Threading.Tasks.Task<OnvifProxy.RenewResponse1> OnvifProxy.PausableSubscriptionManager.RenewAsync(OnvifProxy.RenewRequest request)
    {
        return base.Channel.RenewAsync(request);
    }

    public System.Threading.Tasks.Task<OnvifProxy.RenewResponse1> RenewAsync(OnvifProxy.Renew Renew)
    {
        OnvifProxy.RenewRequest inValue = new OnvifProxy.RenewRequest();
        inValue.Renew = Renew;
        return ((OnvifProxy.PausableSubscriptionManager)(this)).RenewAsync(inValue);
    }

    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    System.Threading.Tasks.Task<OnvifProxy.UnsubscribeResponse1> OnvifProxy.PausableSubscriptionManager.UnsubscribeAsync(OnvifProxy.UnsubscribeRequest request)
    {
        return base.Channel.UnsubscribeAsync(request);
    }

    public System.Threading.Tasks.Task<OnvifProxy.UnsubscribeResponse1> UnsubscribeAsync(OnvifProxy.Unsubscribe Unsubscribe)
    {
        OnvifProxy.UnsubscribeRequest inValue = new OnvifProxy.UnsubscribeRequest();
        inValue.Unsubscribe = Unsubscribe;
        return ((OnvifProxy.PausableSubscriptionManager)(this)).UnsubscribeAsync(inValue);
    }

    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    System.Threading.Tasks.Task<OnvifProxy.PauseSubscriptionResponse1> OnvifProxy.PausableSubscriptionManager.PauseSubscriptionAsync(OnvifProxy.PauseSubscriptionRequest request)
    {
        return base.Channel.PauseSubscriptionAsync(request);
    }

    public System.Threading.Tasks.Task<OnvifProxy.PauseSubscriptionResponse1> PauseSubscriptionAsync(OnvifProxy.PauseSubscription PauseSubscription)
    {
        OnvifProxy.PauseSubscriptionRequest inValue = new OnvifProxy.PauseSubscriptionRequest();
        inValue.PauseSubscription = PauseSubscription;
        return ((OnvifProxy.PausableSubscriptionManager)(this)).PauseSubscriptionAsync(inValue);
    }

    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    System.Threading.Tasks.Task<OnvifProxy.ResumeSubscriptionResponse1> OnvifProxy.PausableSubscriptionManager.ResumeSubscriptionAsync(OnvifProxy.ResumeSubscriptionRequest request)
    {
        return base.Channel.ResumeSubscriptionAsync(request);
    }

    public System.Threading.Tasks.Task<OnvifProxy.ResumeSubscriptionResponse1> ResumeSubscriptionAsync(OnvifProxy.ResumeSubscription ResumeSubscription)
    {
        OnvifProxy.ResumeSubscriptionRequest inValue = new OnvifProxy.ResumeSubscriptionRequest();
        inValue.ResumeSubscription = ResumeSubscription;
        return ((OnvifProxy.PausableSubscriptionManager)(this)).ResumeSubscriptionAsync(inValue);
    }

    public virtual System.Threading.Tasks.Task OpenAsync()
    {
        return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginOpen(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndOpen));
    }
}

[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.ServiceModel.ServiceContractAttribute(Namespace="http://www.onvif.org/ver10/events/wsdl", ConfigurationName="OnvifProxy.EventPortType")]
public interface EventPortType
{

    [System.ServiceModel.OperationContractAttribute(Action="http://www.onvif.org/ver10/events/wsdl/EventPortType/GetServiceCapabilitiesReques" +
        "t", ReplyAction="http://www.onvif.org/ver10/events/wsdl/EventPortType/GetServiceCapabilitiesRespon" +
        "se")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    [return: System.ServiceModel.MessageParameterAttribute(Name="Capabilities")]
    System.Threading.Tasks.Task<OnvifProxy.Capabilities> GetServiceCapabilitiesAsync();

    // CODEGEN: Создается контракт сообщения, так как операция имеет много возвращаемых значений.
    [System.ServiceModel.OperationContractAttribute(Action="http://www.onvif.org/ver10/events/wsdl/EventPortType/CreatePullPointSubscriptionR" +
        "equest", ReplyAction="http://www.onvif.org/ver10/events/wsdl/EventPortType/CreatePullPointSubscriptionR" +
        "esponse")]
    [System.ServiceModel.FaultContractAttribute(typeof(OnvifProxy.ResourceUnknownFaultType), Action="http://www.onvif.org/ver10/events/wsdl/EventPortType/CreatePullPointSubscriptionR" +
        "equest", Name="ResourceUnknownFault", Namespace="http://docs.oasis-open.org/wsrf/r-2")]
    [System.ServiceModel.FaultContractAttribute(typeof(OnvifProxy.InvalidFilterFaultType), Action="http://www.onvif.org/ver10/events/wsdl/EventPortType/CreatePullPointSubscriptionR" +
        "equest", Name="InvalidFilterFault", Namespace="http://docs.oasis-open.org/wsn/b-2")]
    [System.ServiceModel.FaultContractAttribute(typeof(OnvifProxy.TopicExpressionDialectUnknownFaultType), Action="http://www.onvif.org/ver10/events/wsdl/EventPortType/CreatePullPointSubscriptionR" +
        "equest", Name="TopicExpressionDialectUnknownFault", Namespace="http://docs.oasis-open.org/wsn/b-2")]
    [System.ServiceModel.FaultContractAttribute(typeof(OnvifProxy.InvalidTopicExpressionFaultType), Action="http://www.onvif.org/ver10/events/wsdl/EventPortType/CreatePullPointSubscriptionR" +
        "equest", Name="InvalidTopicExpressionFault", Namespace="http://docs.oasis-open.org/wsn/b-2")]
    [System.ServiceModel.FaultContractAttribute(typeof(OnvifProxy.TopicNotSupportedFaultType), Action="http://www.onvif.org/ver10/events/wsdl/EventPortType/CreatePullPointSubscriptionR" +
        "equest", Name="TopicNotSupportedFault", Namespace="http://docs.oasis-open.org/wsn/b-2")]
    [System.ServiceModel.FaultContractAttribute(typeof(OnvifProxy.InvalidProducerPropertiesExpressionFaultType), Action="http://www.onvif.org/ver10/events/wsdl/EventPortType/CreatePullPointSubscriptionR" +
        "equest", Name="InvalidProducerPropertiesExpressionFault", Namespace="http://docs.oasis-open.org/wsn/b-2")]
    [System.ServiceModel.FaultContractAttribute(typeof(OnvifProxy.InvalidMessageContentExpressionFaultType), Action="http://www.onvif.org/ver10/events/wsdl/EventPortType/CreatePullPointSubscriptionR" +
        "equest", Name="InvalidMessageContentExpressionFault", Namespace="http://docs.oasis-open.org/wsn/b-2")]
    [System.ServiceModel.FaultContractAttribute(typeof(OnvifProxy.UnacceptableInitialTerminationTimeFaultType), Action="http://www.onvif.org/ver10/events/wsdl/EventPortType/CreatePullPointSubscriptionR" +
        "equest", Name="UnacceptableInitialTerminationTimeFault", Namespace="http://docs.oasis-open.org/wsn/b-2")]
    [System.ServiceModel.FaultContractAttribute(typeof(OnvifProxy.UnrecognizedPolicyRequestFaultType), Action="http://www.onvif.org/ver10/events/wsdl/EventPortType/CreatePullPointSubscriptionR" +
        "equest", Name="UnrecognizedPolicyRequestFault", Namespace="http://docs.oasis-open.org/wsn/b-2")]
    [System.ServiceModel.FaultContractAttribute(typeof(OnvifProxy.UnsupportedPolicyRequestFaultType), Action="http://www.onvif.org/ver10/events/wsdl/EventPortType/CreatePullPointSubscriptionR" +
        "equest", Name="UnsupportedPolicyRequestFault", Namespace="http://docs.oasis-open.org/wsn/b-2")]
    [System.ServiceModel.FaultContractAttribute(typeof(OnvifProxy.NotifyMessageNotSupportedFaultType), Action="http://www.onvif.org/ver10/events/wsdl/EventPortType/CreatePullPointSubscriptionR" +
        "equest", Name="NotifyMessageNotSupportedFault", Namespace="http://docs.oasis-open.org/wsn/b-2")]
    [System.ServiceModel.FaultContractAttribute(typeof(OnvifProxy.SubscribeCreationFailedFaultType), Action="http://www.onvif.org/ver10/events/wsdl/EventPortType/CreatePullPointSubscriptionR" +
        "equest", Name="SubscribeCreationFailedFault", Namespace="http://docs.oasis-open.org/wsn/b-2")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    System.Threading.Tasks.Task<OnvifProxy.CreatePullPointSubscriptionResponse> CreatePullPointSubscriptionAsync(OnvifProxy.CreatePullPointSubscriptionRequest request);

    // CODEGEN: Создается контракт сообщения, так как операция имеет много возвращаемых значений.
    [System.ServiceModel.OperationContractAttribute(Action="http://www.onvif.org/ver10/events/wsdl/EventPortType/GetEventPropertiesRequest", ReplyAction="http://www.onvif.org/ver10/events/wsdl/EventPortType/GetEventPropertiesResponse")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    System.Threading.Tasks.Task<OnvifProxy.GetEventPropertiesResponse> GetEventPropertiesAsync(OnvifProxy.GetEventPropertiesRequest request);

    [System.ServiceModel.OperationContractAttribute(Action="http://www.onvif.org/ver10/events/wsdl/EventPortType/AddEventBrokerRequest", ReplyAction="*")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    System.Threading.Tasks.Task AddEventBrokerAsync(OnvifProxy.EventBrokerConfig EventBroker);

    [System.ServiceModel.OperationContractAttribute(Action="http://www.onvif.org/ver10/events/wsdl/EventPortType/DeleteEventBrokerRequest", ReplyAction="*")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    System.Threading.Tasks.Task<OnvifProxy.DeleteEventBrokerResponse> DeleteEventBrokerAsync(OnvifProxy.DeleteEventBrokerRequest request);

    [System.ServiceModel.OperationContractAttribute(Action="http://www.onvif.org/ver10/events/wsdl/EventPortType/GetEventBrokersRequest", ReplyAction="*")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    System.Threading.Tasks.Task<OnvifProxy.GetEventBrokersResponse> GetEventBrokersAsync(OnvifProxy.GetEventBrokersRequest request);
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.onvif.org/ver10/events/wsdl")]
public partial class Capabilities
{

    private System.Xml.XmlElement[] anyField;

    private bool wSSubscriptionPolicySupportField;

    private bool wSSubscriptionPolicySupportFieldSpecified;

    private bool wSPausableSubscriptionManagerInterfaceSupportField;

    private bool wSPausableSubscriptionManagerInterfaceSupportFieldSpecified;

    private int maxNotificationProducersField;

    private bool maxNotificationProducersFieldSpecified;

    private int maxPullPointsField;

    private bool maxPullPointsFieldSpecified;

    private bool persistentNotificationStorageField;

    private bool persistentNotificationStorageFieldSpecified;

    private string eventBrokerProtocolsField;

    private int maxEventBrokersField;

    private bool maxEventBrokersFieldSpecified;

    private bool metadataOverMQTTField;

    private bool metadataOverMQTTFieldSpecified;

    /// <remarks/>
    [System.Xml.Serialization.XmlAnyElementAttribute(Order=0)]
    public System.Xml.XmlElement[] Any
    {
        get
        {
            return this.anyField;
        }
        set
        {
            this.anyField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlAttributeAttribute()]
    public bool WSSubscriptionPolicySupport
    {
        get
        {
            return this.wSSubscriptionPolicySupportField;
        }
        set
        {
            this.wSSubscriptionPolicySupportField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool WSSubscriptionPolicySupportSpecified
    {
        get
        {
            return this.wSSubscriptionPolicySupportFieldSpecified;
        }
        set
        {
            this.wSSubscriptionPolicySupportFieldSpecified = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlAttributeAttribute()]
    public bool WSPausableSubscriptionManagerInterfaceSupport
    {
        get
        {
            return this.wSPausableSubscriptionManagerInterfaceSupportField;
        }
        set
        {
            this.wSPausableSubscriptionManagerInterfaceSupportField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool WSPausableSubscriptionManagerInterfaceSupportSpecified
    {
        get
        {
            return this.wSPausableSubscriptionManagerInterfaceSupportFieldSpecified;
        }
        set
        {
            this.wSPausableSubscriptionManagerInterfaceSupportFieldSpecified = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlAttributeAttribute()]
    public int MaxNotificationProducers
    {
        get
        {
            return this.maxNotificationProducersField;
        }
        set
        {
            this.maxNotificationProducersField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool MaxNotificationProducersSpecified
    {
        get
        {
            return this.maxNotificationProducersFieldSpecified;
        }
        set
        {
            this.maxNotificationProducersFieldSpecified = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlAttributeAttribute()]
    public int MaxPullPoints
    {
        get
        {
            return this.maxPullPointsField;
        }
        set
        {
            this.maxPullPointsField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool MaxPullPointsSpecified
    {
        get
        {
            return this.maxPullPointsFieldSpecified;
        }
        set
        {
            this.maxPullPointsFieldSpecified = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlAttributeAttribute()]
    public bool PersistentNotificationStorage
    {
        get
        {
            return this.persistentNotificationStorageField;
        }
        set
        {
            this.persistentNotificationStorageField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool PersistentNotificationStorageSpecified
    {
        get
        {
            return this.persistentNotificationStorageFieldSpecified;
        }
        set
        {
            this.persistentNotificationStorageFieldSpecified = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlAttributeAttribute()]
    public string EventBrokerProtocols
    {
        get
        {
            return this.eventBrokerProtocolsField;
        }
        set
        {
            this.eventBrokerProtocolsField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlAttributeAttribute()]
    public int MaxEventBrokers
    {
        get
        {
            return this.maxEventBrokersField;
        }
        set
        {
            this.maxEventBrokersField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool MaxEventBrokersSpecified
    {
        get
        {
            return this.maxEventBrokersFieldSpecified;
        }
        set
        {
            this.maxEventBrokersFieldSpecified = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlAttributeAttribute()]
    public bool MetadataOverMQTT
    {
        get
        {
            return this.metadataOverMQTTField;
        }
        set
        {
            this.metadataOverMQTTField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool MetadataOverMQTTSpecified
    {
        get
        {
            return this.metadataOverMQTTFieldSpecified;
        }
        set
        {
            this.metadataOverMQTTFieldSpecified = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://www.onvif.org/ver10/events/wsdl")]
public partial class CreatePullPointSubscriptionSubscriptionPolicy
{

    private System.Xml.XmlElement[] anyField;

    /// <remarks/>
    [System.Xml.Serialization.XmlAnyElementAttribute(Order=0)]
    public System.Xml.XmlElement[] Any
    {
        get
        {
            return this.anyField;
        }
        set
        {
            this.anyField = value;
        }
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.ServiceModel.MessageContractAttribute(WrapperName="CreatePullPointSubscription", WrapperNamespace="http://www.onvif.org/ver10/events/wsdl", IsWrapped=true)]
public partial class CreatePullPointSubscriptionRequest
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.onvif.org/ver10/events/wsdl", Order=0)]
    public OnvifProxy.FilterType Filter;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.onvif.org/ver10/events/wsdl", Order=1)]
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
    public string InitialTerminationTime;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.onvif.org/ver10/events/wsdl", Order=2)]
    public OnvifProxy.CreatePullPointSubscriptionSubscriptionPolicy SubscriptionPolicy;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="", Order=3)]
    [System.Xml.Serialization.XmlAnyElementAttribute()]
    public System.Xml.XmlElement[] Any;

    public CreatePullPointSubscriptionRequest()
    {
    }

    public CreatePullPointSubscriptionRequest(OnvifProxy.FilterType Filter, string InitialTerminationTime, OnvifProxy.CreatePullPointSubscriptionSubscriptionPolicy SubscriptionPolicy, System.Xml.XmlElement[] Any)
    {
        this.Filter = Filter;
        this.InitialTerminationTime = InitialTerminationTime;
        this.SubscriptionPolicy = SubscriptionPolicy;
        this.Any = Any;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.ServiceModel.MessageContractAttribute(WrapperName="CreatePullPointSubscriptionResponse", WrapperNamespace="http://www.onvif.org/ver10/events/wsdl", IsWrapped=true)]
public partial class CreatePullPointSubscriptionResponse
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.onvif.org/ver10/events/wsdl", Order=0)]
    public OnvifProxy.EndpointReferenceType SubscriptionReference;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://docs.oasis-open.org/wsn/b-2", Order=1)]
    [System.Xml.Serialization.XmlElementAttribute(Namespace="http://docs.oasis-open.org/wsn/b-2")]
    public System.DateTime CurrentTime;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://docs.oasis-open.org/wsn/b-2", Order=2)]
    [System.Xml.Serialization.XmlElementAttribute(Namespace="http://docs.oasis-open.org/wsn/b-2", IsNullable=true)]
    public System.Nullable<System.DateTime> TerminationTime;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="", Order=3)]
    [System.Xml.Serialization.XmlAnyElementAttribute()]
    public System.Xml.XmlElement[] Any;

    public CreatePullPointSubscriptionResponse()
    {
    }

    public CreatePullPointSubscriptionResponse(OnvifProxy.EndpointReferenceType SubscriptionReference, System.DateTime CurrentTime, System.Nullable<System.DateTime> TerminationTime, System.Xml.XmlElement[] Any)
    {
        this.SubscriptionReference = SubscriptionReference;
        this.CurrentTime = CurrentTime;
        this.TerminationTime = TerminationTime;
        this.Any = Any;
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://docs.oasis-open.org/wsn/t-1")]
public partial class TopicSetType : ExtensibleDocumented
{

    private System.Xml.XmlElement[] anyField;

    /// <remarks/>
    [System.Xml.Serialization.XmlAnyElementAttribute(Order=0)]
    public System.Xml.XmlElement[] Any
    {
        get
        {
            return this.anyField;
        }
        set
        {
            this.anyField = value;
        }
    }
}

/// <remarks/>
[System.Xml.Serialization.XmlIncludeAttribute(typeof(TopicSetType))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(TopicType))]
[System.Xml.Serialization.XmlIncludeAttribute(typeof(TopicNamespaceType))]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://docs.oasis-open.org/wsn/t-1")]
public abstract partial class ExtensibleDocumented
{

    private Documentation documentationField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public Documentation documentation
    {
        get
        {
            return this.documentationField;
        }
        set
        {
            this.documentationField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://docs.oasis-open.org/wsn/t-1")]
public partial class Documentation
{

    private System.Xml.XmlNode[] anyField;

    /// <remarks/>
    [System.Xml.Serialization.XmlTextAttribute()]
    [System.Xml.Serialization.XmlAnyElementAttribute(Order=0)]
    public System.Xml.XmlNode[] Any
    {
        get
        {
            return this.anyField;
        }
        set
        {
            this.anyField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://docs.oasis-open.org/wsn/t-1")]
public partial class TopicType : ExtensibleDocumented
{

    private QueryExpressionType1 messagePatternField;

    private TopicType[] topicField;

    private System.Xml.XmlElement[] anyField;

    private string nameField;

    private System.Xml.XmlQualifiedName[] messageTypesField;

    private bool finalField;

    public TopicType()
    {
        this.finalField = false;
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public QueryExpressionType1 MessagePattern
    {
        get
        {
            return this.messagePatternField;
        }
        set
        {
            this.messagePatternField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("Topic", Order=1)]
    public TopicType[] Topic
    {
        get
        {
            return this.topicField;
        }
        set
        {
            this.topicField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlAnyElementAttribute(Order=2)]
    public System.Xml.XmlElement[] Any
    {
        get
        {
            return this.anyField;
        }
        set
        {
            this.anyField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlAttributeAttribute(DataType="NCName")]
    public string name
    {
        get
        {
            return this.nameField;
        }
        set
        {
            this.nameField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlAttributeAttribute()]
    public System.Xml.XmlQualifiedName[] messageTypes
    {
        get
        {
            return this.messageTypesField;
        }
        set
        {
            this.messageTypesField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlAttributeAttribute()]
    [System.ComponentModel.DefaultValueAttribute(false)]
    public bool final
    {
        get
        {
            return this.finalField;
        }
        set
        {
            this.finalField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(TypeName="QueryExpressionType", Namespace="http://docs.oasis-open.org/wsn/t-1")]
public partial class QueryExpressionType1
{

    private System.Xml.XmlNode[] anyField;

    private string dialectField;

    /// <remarks/>
    [System.Xml.Serialization.XmlTextAttribute()]
    [System.Xml.Serialization.XmlAnyElementAttribute(Order=0)]
    public System.Xml.XmlNode[] Any
    {
        get
        {
            return this.anyField;
        }
        set
        {
            this.anyField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlAttributeAttribute(DataType="anyURI")]
    public string Dialect
    {
        get
        {
            return this.dialectField;
        }
        set
        {
            this.dialectField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://docs.oasis-open.org/wsn/t-1")]
public partial class TopicNamespaceType : ExtensibleDocumented
{

    private TopicNamespaceTypeTopic[] topicField;

    private System.Xml.XmlElement[] anyField;

    private string nameField;

    private string targetNamespaceField;

    private bool finalField;

    public TopicNamespaceType()
    {
        this.finalField = false;
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute("Topic", Order=0)]
    public TopicNamespaceTypeTopic[] Topic
    {
        get
        {
            return this.topicField;
        }
        set
        {
            this.topicField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlAnyElementAttribute(Order=1)]
    public System.Xml.XmlElement[] Any
    {
        get
        {
            return this.anyField;
        }
        set
        {
            this.anyField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlAttributeAttribute(DataType="NCName")]
    public string name
    {
        get
        {
            return this.nameField;
        }
        set
        {
            this.nameField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlAttributeAttribute(DataType="anyURI")]
    public string targetNamespace
    {
        get
        {
            return this.targetNamespaceField;
        }
        set
        {
            this.targetNamespaceField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlAttributeAttribute()]
    [System.ComponentModel.DefaultValueAttribute(false)]
    public bool final
    {
        get
        {
            return this.finalField;
        }
        set
        {
            this.finalField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://docs.oasis-open.org/wsn/t-1")]
public partial class TopicNamespaceTypeTopic : TopicType
{

    private string parentField;

    /// <remarks/>
    [System.Xml.Serialization.XmlAttributeAttribute(DataType="token")]
    public string parent
    {
        get
        {
            return this.parentField;
        }
        set
        {
            this.parentField = value;
        }
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.ServiceModel.MessageContractAttribute(WrapperName="GetEventProperties", WrapperNamespace="http://www.onvif.org/ver10/events/wsdl", IsWrapped=true)]
public partial class GetEventPropertiesRequest
{

    public GetEventPropertiesRequest()
    {
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.ServiceModel.MessageContractAttribute(WrapperName="GetEventPropertiesResponse", WrapperNamespace="http://www.onvif.org/ver10/events/wsdl", IsWrapped=true)]
public partial class GetEventPropertiesResponse
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.onvif.org/ver10/events/wsdl", Order=0)]
    [System.Xml.Serialization.XmlElementAttribute("TopicNamespaceLocation", DataType="anyURI")]
    public string[] TopicNamespaceLocation;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://docs.oasis-open.org/wsn/b-2", Order=1)]
    [System.Xml.Serialization.XmlElementAttribute(Namespace="http://docs.oasis-open.org/wsn/b-2")]
    public bool FixedTopicSet;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://docs.oasis-open.org/wsn/t-1", Order=2)]
    [System.Xml.Serialization.XmlElementAttribute(Namespace="http://docs.oasis-open.org/wsn/t-1")]
    public OnvifProxy.TopicSetType TopicSet;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://docs.oasis-open.org/wsn/b-2", Order=3)]
    [System.Xml.Serialization.XmlElementAttribute("TopicExpressionDialect", Namespace="http://docs.oasis-open.org/wsn/b-2", DataType="anyURI")]
    public string[] TopicExpressionDialect;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.onvif.org/ver10/events/wsdl", Order=4)]
    [System.Xml.Serialization.XmlElementAttribute("MessageContentFilterDialect", DataType="anyURI")]
    public string[] MessageContentFilterDialect;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.onvif.org/ver10/events/wsdl", Order=5)]
    [System.Xml.Serialization.XmlElementAttribute("ProducerPropertiesFilterDialect", DataType="anyURI")]
    public string[] ProducerPropertiesFilterDialect;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.onvif.org/ver10/events/wsdl", Order=6)]
    [System.Xml.Serialization.XmlElementAttribute("MessageContentSchemaLocation", DataType="anyURI")]
    public string[] MessageContentSchemaLocation;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="", Order=7)]
    [System.Xml.Serialization.XmlAnyElementAttribute()]
    public System.Xml.XmlElement[] Any;

    public GetEventPropertiesResponse()
    {
    }

    public GetEventPropertiesResponse(string[] TopicNamespaceLocation, bool FixedTopicSet, OnvifProxy.TopicSetType TopicSet, string[] TopicExpressionDialect, string[] MessageContentFilterDialect, string[] ProducerPropertiesFilterDialect, string[] MessageContentSchemaLocation, System.Xml.XmlElement[] Any)
    {
        this.TopicNamespaceLocation = TopicNamespaceLocation;
        this.FixedTopicSet = FixedTopicSet;
        this.TopicSet = TopicSet;
        this.TopicExpressionDialect = TopicExpressionDialect;
        this.MessageContentFilterDialect = MessageContentFilterDialect;
        this.ProducerPropertiesFilterDialect = ProducerPropertiesFilterDialect;
        this.MessageContentSchemaLocation = MessageContentSchemaLocation;
        this.Any = Any;
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://www.onvif.org/ver10/events/wsdl")]
public partial class EventBrokerConfig
{

    private string addressField;

    private string topicPrefixField;

    private string userNameField;

    private string passwordField;

    private string certificateIDField;

    private FilterType publishFilterField;

    private int qoSField;

    private bool qoSFieldSpecified;

    private string statusField;

    private string certPathValidationPolicyIDField;

    private FilterType metadataFilterField;

    private System.Xml.XmlElement[] anyField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(DataType="anyURI", Order=0)]
    public string Address
    {
        get
        {
            return this.addressField;
        }
        set
        {
            this.addressField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public string TopicPrefix
    {
        get
        {
            return this.topicPrefixField;
        }
        set
        {
            this.topicPrefixField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=2)]
    public string UserName
    {
        get
        {
            return this.userNameField;
        }
        set
        {
            this.userNameField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=3)]
    public string Password
    {
        get
        {
            return this.passwordField;
        }
        set
        {
            this.passwordField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(DataType="token", Order=4)]
    public string CertificateID
    {
        get
        {
            return this.certificateIDField;
        }
        set
        {
            this.certificateIDField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=5)]
    public FilterType PublishFilter
    {
        get
        {
            return this.publishFilterField;
        }
        set
        {
            this.publishFilterField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=6)]
    public int QoS
    {
        get
        {
            return this.qoSField;
        }
        set
        {
            this.qoSField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool QoSSpecified
    {
        get
        {
            return this.qoSFieldSpecified;
        }
        set
        {
            this.qoSFieldSpecified = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=7)]
    public string Status
    {
        get
        {
            return this.statusField;
        }
        set
        {
            this.statusField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=8)]
    public string CertPathValidationPolicyID
    {
        get
        {
            return this.certPathValidationPolicyIDField;
        }
        set
        {
            this.certPathValidationPolicyIDField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=9)]
    public FilterType MetadataFilter
    {
        get
        {
            return this.metadataFilterField;
        }
        set
        {
            this.metadataFilterField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlAnyElementAttribute(Order=10)]
    public System.Xml.XmlElement[] Any
    {
        get
        {
            return this.anyField;
        }
        set
        {
            this.anyField = value;
        }
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="DeleteEventBroker", WrapperNamespace="http://www.onvif.org/ver10/events/wsdl", IsWrapped=true)]
public partial class DeleteEventBrokerRequest
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.onvif.org/ver10/events/wsdl", Order=0)]
    [System.Xml.Serialization.XmlElementAttribute(DataType="anyURI")]
    public string Address;

    public DeleteEventBrokerRequest()
    {
    }

    public DeleteEventBrokerRequest(string Address)
    {
        this.Address = Address;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="DeleteEventBrokerResponse", WrapperNamespace="http://www.onvif.org/ver10/events/wsdl", IsWrapped=true)]
public partial class DeleteEventBrokerResponse
{

    public DeleteEventBrokerResponse()
    {
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="GetEventBrokers", WrapperNamespace="http://www.onvif.org/ver10/events/wsdl", IsWrapped=true)]
public partial class GetEventBrokersRequest
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.onvif.org/ver10/events/wsdl", Order=0)]
    [System.Xml.Serialization.XmlElementAttribute(DataType="anyURI")]
    public string Address;

    public GetEventBrokersRequest()
    {
    }

    public GetEventBrokersRequest(string Address)
    {
        this.Address = Address;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="GetEventBrokersResponse", WrapperNamespace="http://www.onvif.org/ver10/events/wsdl", IsWrapped=true)]
public partial class GetEventBrokersResponse
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.onvif.org/ver10/events/wsdl", Order=0)]
    [System.Xml.Serialization.XmlElementAttribute("EventBroker")]
    public OnvifProxy.EventBrokerConfig[] EventBroker;

    public GetEventBrokersResponse()
    {
    }

    public GetEventBrokersResponse(OnvifProxy.EventBrokerConfig[] EventBroker)
    {
        this.EventBroker = EventBroker;
    }
}

[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
public interface EventPortTypeChannel : OnvifProxy.EventPortType, System.ServiceModel.IClientChannel
{
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
public partial class EventPortTypeClient : System.ServiceModel.ClientBase<OnvifProxy.EventPortType>, OnvifProxy.EventPortType
{

    public EventPortTypeClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) :
            base(binding, remoteAddress)
    {
    }

    public System.Threading.Tasks.Task<OnvifProxy.Capabilities> GetServiceCapabilitiesAsync()
    {
        return base.Channel.GetServiceCapabilitiesAsync();
    }

    public System.Threading.Tasks.Task<OnvifProxy.CreatePullPointSubscriptionResponse> CreatePullPointSubscriptionAsync(OnvifProxy.CreatePullPointSubscriptionRequest request)
    {
        return base.Channel.CreatePullPointSubscriptionAsync(request);
    }

    public System.Threading.Tasks.Task<OnvifProxy.GetEventPropertiesResponse> GetEventPropertiesAsync(OnvifProxy.GetEventPropertiesRequest request)
    {
        return base.Channel.GetEventPropertiesAsync(request);
    }

    public System.Threading.Tasks.Task AddEventBrokerAsync(OnvifProxy.EventBrokerConfig EventBroker)
    {
        return base.Channel.AddEventBrokerAsync(EventBroker);
    }

    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    System.Threading.Tasks.Task<OnvifProxy.DeleteEventBrokerResponse> OnvifProxy.EventPortType.DeleteEventBrokerAsync(OnvifProxy.DeleteEventBrokerRequest request)
    {
        return base.Channel.DeleteEventBrokerAsync(request);
    }

    public System.Threading.Tasks.Task<OnvifProxy.DeleteEventBrokerResponse> DeleteEventBrokerAsync(string Address)
    {
        OnvifProxy.DeleteEventBrokerRequest inValue = new OnvifProxy.DeleteEventBrokerRequest();
        inValue.Address = Address;
        return ((OnvifProxy.EventPortType)(this)).DeleteEventBrokerAsync(inValue);
    }

    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    System.Threading.Tasks.Task<OnvifProxy.GetEventBrokersResponse> OnvifProxy.EventPortType.GetEventBrokersAsync(OnvifProxy.GetEventBrokersRequest request)
    {
        return base.Channel.GetEventBrokersAsync(request);
    }

    public System.Threading.Tasks.Task<OnvifProxy.GetEventBrokersResponse> GetEventBrokersAsync(string Address)
    {
        OnvifProxy.GetEventBrokersRequest inValue = new OnvifProxy.GetEventBrokersRequest();
        inValue.Address = Address;
        return ((OnvifProxy.EventPortType)(this)).GetEventBrokersAsync(inValue);
    }

    public virtual System.Threading.Tasks.Task OpenAsync()
    {
        return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginOpen(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndOpen));
    }
}

[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.ServiceModel.ServiceContractAttribute(Namespace="http://www.onvif.org/ver10/events/wsdl", ConfigurationName="OnvifProxy.PullPointSubscription")]
public interface PullPointSubscription
{

    // CODEGEN: Создается контракт сообщения, так как операция имеет много возвращаемых значений.
    [System.ServiceModel.OperationContractAttribute(Action="http://www.onvif.org/ver10/events/wsdl/PullPointSubscription/PullMessagesRequest", ReplyAction="http://www.onvif.org/ver10/events/wsdl/PullPointSubscription/PullMessagesResponse" +
        "")]
    [System.ServiceModel.FaultContractAttribute(typeof(OnvifProxy.PullMessagesFaultResponse), Action="http://www.onvif.org/ver10/events/wsdl/PullPointSubscription/PullMessages/Fault/P" +
        "ullMessagesFaultResponse", Name="PullMessagesFaultResponse")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    System.Threading.Tasks.Task<OnvifProxy.PullMessagesResponse> PullMessagesAsync(OnvifProxy.PullMessagesRequest request);

    [System.ServiceModel.OperationContractAttribute(Action="http://www.onvif.org/ver10/events/wsdl/PullPointSubscription/SeekRequest", ReplyAction="http://www.onvif.org/ver10/events/wsdl/PullPointSubscription/SeekResponse")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    System.Threading.Tasks.Task<OnvifProxy.SeekResponse> SeekAsync(OnvifProxy.SeekRequest request);

    [System.ServiceModel.OperationContractAttribute(Action="http://www.onvif.org/ver10/events/wsdl/PullPointSubscription/SetSynchronizationPo" +
        "intRequest", ReplyAction="http://www.onvif.org/ver10/events/wsdl/PullPointSubscription/SetSynchronizationPo" +
        "intResponse")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    System.Threading.Tasks.Task SetSynchronizationPointAsync();

    [System.ServiceModel.OperationContractAttribute(Action="http://docs.oasis-open.org/wsn/bw-2/SubscriptionManager/UnsubscribeRequest", ReplyAction="*")]
    [System.ServiceModel.FaultContractAttribute(typeof(OnvifProxy.ResourceUnknownFaultType), Action="http://docs.oasis-open.org/wsn/bw-2/SubscriptionManager/UnsubscribeRequest", Name="ResourceUnknownFault", Namespace="http://docs.oasis-open.org/wsrf/r-2")]
    [System.ServiceModel.FaultContractAttribute(typeof(OnvifProxy.UnableToDestroySubscriptionFaultType), Action="http://docs.oasis-open.org/wsn/bw-2/SubscriptionManager/UnsubscribeRequest", Name="UnableToDestroySubscriptionFault", Namespace="http://docs.oasis-open.org/wsn/b-2")]
    [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
    System.Threading.Tasks.Task<OnvifProxy.UnsubscribeResponse1> UnsubscribeAsync(OnvifProxy.UnsubscribeRequest request);
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.ServiceModel.MessageContractAttribute(WrapperName="PullMessages", WrapperNamespace="http://www.onvif.org/ver10/events/wsdl", IsWrapped=true)]
public partial class PullMessagesRequest
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.onvif.org/ver10/events/wsdl", Order=0)]
    [System.Xml.Serialization.XmlElementAttribute(DataType="duration")]
    public string Timeout;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.onvif.org/ver10/events/wsdl", Order=1)]
    public int MessageLimit;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="", Order=2)]
    [System.Xml.Serialization.XmlAnyElementAttribute()]
    public System.Xml.XmlElement[] Any;

    public PullMessagesRequest()
    {
    }

    public PullMessagesRequest(string Timeout, int MessageLimit, System.Xml.XmlElement[] Any)
    {
        this.Timeout = Timeout;
        this.MessageLimit = MessageLimit;
        this.Any = Any;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.ServiceModel.MessageContractAttribute(WrapperName="PullMessagesResponse", WrapperNamespace="http://www.onvif.org/ver10/events/wsdl", IsWrapped=true)]
public partial class PullMessagesResponse
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.onvif.org/ver10/events/wsdl", Order=0)]
    public System.DateTime CurrentTime;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.onvif.org/ver10/events/wsdl", Order=1)]
    public System.DateTime TerminationTime;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://docs.oasis-open.org/wsn/b-2", Order=2)]
    [System.Xml.Serialization.XmlElementAttribute("NotificationMessage", Namespace="http://docs.oasis-open.org/wsn/b-2")]
    public OnvifProxy.NotificationMessageHolderType[] NotificationMessage;

    public PullMessagesResponse()
    {
    }

    public PullMessagesResponse(System.DateTime CurrentTime, System.DateTime TerminationTime, OnvifProxy.NotificationMessageHolderType[] NotificationMessage)
    {
        this.CurrentTime = CurrentTime;
        this.TerminationTime = TerminationTime;
        this.NotificationMessage = NotificationMessage;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="Seek", WrapperNamespace="http://www.onvif.org/ver10/events/wsdl", IsWrapped=true)]
public partial class SeekRequest
{

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.onvif.org/ver10/events/wsdl", Order=0)]
    public System.DateTime UtcTime;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://www.onvif.org/ver10/events/wsdl", Order=1)]
    public bool Reverse;

    [System.ServiceModel.MessageBodyMemberAttribute(Namespace="", Order=2)]
    [System.Xml.Serialization.XmlAnyElementAttribute()]
    public System.Xml.XmlElement[] Any;

    public SeekRequest()
    {
    }

    public SeekRequest(System.DateTime UtcTime, bool Reverse, System.Xml.XmlElement[] Any)
    {
        this.UtcTime = UtcTime;
        this.Reverse = Reverse;
        this.Any = Any;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
[System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
[System.ServiceModel.MessageContractAttribute(WrapperName="SeekResponse", WrapperNamespace="http://www.onvif.org/ver10/events/wsdl", IsWrapped=true)]
public partial class SeekResponse
{

    public SeekResponse()
    {
    }
}

[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
public interface PullPointSubscriptionChannel : OnvifProxy.PullPointSubscription, System.ServiceModel.IClientChannel
{
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.1.0")]
public partial class PullPointSubscriptionClient : System.ServiceModel.ClientBase<OnvifProxy.PullPointSubscription>, OnvifProxy.PullPointSubscription
{

    public PullPointSubscriptionClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) :
            base(binding, remoteAddress)
    {
    }

    public System.Threading.Tasks.Task<OnvifProxy.PullMessagesResponse> PullMessagesAsync(OnvifProxy.PullMessagesRequest request)
    {
        return base.Channel.PullMessagesAsync(request);
    }

    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    System.Threading.Tasks.Task<OnvifProxy.SeekResponse> OnvifProxy.PullPointSubscription.SeekAsync(OnvifProxy.SeekRequest request)
    {
        return base.Channel.SeekAsync(request);
    }

    public System.Threading.Tasks.Task<OnvifProxy.SeekResponse> SeekAsync(System.DateTime UtcTime, bool Reverse, System.Xml.XmlElement[] Any)
    {
        OnvifProxy.SeekRequest inValue = new OnvifProxy.SeekRequest();
        inValue.UtcTime = UtcTime;
        inValue.Reverse = Reverse;
        inValue.Any = Any;
        return ((OnvifProxy.PullPointSubscription)(this)).SeekAsync(inValue);
    }

    public System.Threading.Tasks.Task SetSynchronizationPointAsync()
    {
        return base.Channel.SetSynchronizationPointAsync();
    }

    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    System.Threading.Tasks.Task<OnvifProxy.UnsubscribeResponse1> OnvifProxy.PullPointSubscription.UnsubscribeAsync(OnvifProxy.UnsubscribeRequest request)
    {
        return base.Channel.UnsubscribeAsync(request);
    }

    public System.Threading.Tasks.Task<OnvifProxy.UnsubscribeResponse1> UnsubscribeAsync(OnvifProxy.Unsubscribe Unsubscribe)
    {
        OnvifProxy.UnsubscribeRequest inValue = new OnvifProxy.UnsubscribeRequest();
        inValue.Unsubscribe = Unsubscribe;
        return ((OnvifProxy.PullPointSubscription)(this)).UnsubscribeAsync(inValue);
    }

    public virtual System.Threading.Tasks.Task OpenAsync()
    {
        return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginOpen(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndOpen));
    }
}
