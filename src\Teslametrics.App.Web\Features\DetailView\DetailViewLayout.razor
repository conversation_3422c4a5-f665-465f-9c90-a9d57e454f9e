@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.Extensions.Localization
@using Teslametrics.App.Web.Features.DetailView.AuthToolbarComponent
@using Teslametrics.App.Web.Features.Authentication
@inherits LayoutComponentBase
<MudThemeProvider @ref="@_mudThemeProvider"
				  @bind-IsDarkMode="@_isDarkMode"
				  Theme="CustomTheme" />
<div class="d_contents">
	<CascadingValue Value="_mudThemeProvider">
		<AuthorizeView Context="layoutContext">
			<Authorized>
				<!-- Зоны наведения -->
				<div class="hover-zone left"
					 @onmouseenter="() => OpenDrawer(Anchor.Left)"
					 @onmouseleave="() => CloseDrawerDelayed()"></div>

				<div class="hover-zone right"
					 @onmouseenter="() => OpenDrawer(Anchor.Right)"
					 @onmouseleave="() => CloseDrawerDelayed()"></div>

				<MudLayout UserAttributes="@(new Dictionary<string, object>() { {"id", "main_layout"} })"
						   Style="height: 100vh;"
						   class="pa-0">
					<MudDrawer @bind-Open="@_open"
							   Anchor="@_anchor"
							   Elevation="1"
							   Variant="@DrawerVariant.Temporary"
							   ClipMode="DrawerClipMode.Never"
							   Overlay="false"
							   @onmouseenter="() => _isMouseOverDrawer = true"
							   @onmouseleave="() => OnDrawerMouseLeaveAsync()">
						<MudNavMenu Rounded="true"
									Margin="Margin.Normal"
									Color="Color.Primary"
									Class="nav_menu mud-height-full overflow-auto px-4">
							@if (OrganizationId is not null && ViewId is not null)
							{
								<MudStack Row="true"
										  Spacing="0">
									<MudNavLink Href="@($"{RouteConstants.CameraViews}?OrganizationId={OrganizationId}&ViewId={ViewId}")"
												Icon="@Icons.Material.Filled.ArrowBack"
												Match="NavLinkMatch.All"
												Class="nav_link"
												IconColor="Color.Inherit">
										Назад
									</MudNavLink>
									<div class="mr-n3">
										<FullscreenButton />
									</div>
								</MudStack>

								<MudDivider Style="width: calc(100% + 32px)"
											Class="mx-n4" />
							}
							<AuthorizeView Policy="@AppPermissions.Main.CameraViews.Read.GetEnumPermissionString()">
								<MudNavLink Href="@RouteConstants.CameraViews"
											Icon="@Icons.Material.Filled.Public"
											Match="NavLinkMatch.All"
											Class="nav_link"
											IconColor="Color.Inherit">
									Виды
								</MudNavLink>
							</AuthorizeView>
							<AuthorizeView Policy="@($"{AppPermissions.Main.Cameras.Read.GetEnumPermissionString()},{AppPermissions.Main.Folders.Read.GetEnumPermissionString()}")">
								<MudNavLink Href="@RouteConstants.Cameras"
											Icon="@Icons.Material.Outlined.Camera"
											Match="NavLinkMatch.All"
											Class="nav_link"
											IconColor="Color.Inherit"
											title="Камеры">
									Камеры
								</MudNavLink>
							</AuthorizeView>
							<MudNavLink Href="@RouteConstants.Organizations"
										Icon="@Icons.Material.Filled.Business"
										Match="NavLinkMatch.All"
										Class="nav_link"
										IconColor="Color.Inherit"
										title="Организации">
								Организации
							</MudNavLink>
							<AuthorizeView Policy="@(Fqdn<AppPermissions>.GetNames(AppPermissions.Main.AccessControl.Organizations.Read).Last())">
								<MudNavLink Href="@RouteConstants.AccessControl"
											Icon="@Icons.Material.Outlined.Key"
											Match="NavLinkMatch.All"
											Class="nav_link"
											IconColor="Color.Inherit"
											title="Контроль доступа">
									Контроль доступа
								</MudNavLink>
							</AuthorizeView>
							<AuthorizeView Policy="@AppPermissions.Main.CameraPresets.Read.GetEnumPermissionString()">
								<MudNavLink Href="@RouteConstants.CameraPresets"
											Icon="@Icons.Material.Filled.Pattern"
											Match="NavLinkMatch.All"
											Class="nav_link"
											IconColor="Color.Inherit"
											title="Пресеты">
									Пресеты
								</MudNavLink>
							</AuthorizeView>
							<AuthorizeView Policy="@AppPermissions.Main.CameraPublicAccess.Read.GetEnumPermissionString()">
								<MudNavLink Href="@RouteConstants.CameraPublicAccess"
											Icon="@Icons.Material.Filled.Public"
											Match="NavLinkMatch.All"
											Class="nav_link"
											IconColor="Color.Inherit"
											title="Публичный доступ">
									Публичный доступ
								</MudNavLink>
							</AuthorizeView>
						</MudNavMenu>
						<MudDivider />
						<AuthToolbarComponent />
					</MudDrawer>
					@Body
				</MudLayout>
			</Authorized>
			<NotAuthorized>
				<MudLayout Style="height: 100vh;"
						   class="pa-0">
					<div class="pa-4 h-100 d-flex align-center justify-center mud-height-full mud-width-full">
						@Body
					</div>
				</MudLayout>
			</NotAuthorized>
		</AuthorizeView>
	</CascadingValue>
</div>