﻿::deep {
  --items_conteiner_max_width: 31.25rem;
}

::deep .container {
  display: flex;
  flex-direction: column;
}

::deep .items_container {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

::deep .main_card {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

::deep footer {
  color: #B2C2D2;
  align-items: end;
  display: flex;
}

::deep footer .links {
  padding-top: var(--footer_links_pt);
}

.ad {
  width: fit-content;
  height: 100%;
  display: none;
}

.ad.laptop {
  display: none;
}

header {
  color: #e0e0e0;
}

header .logo {
  font-size: 40px;
  height: 58px;
  background: var(--mud-palette-dark);
  padding: 2px 4px;
  border-radius: 4px;
  display: flex;
}

::deep header>p {
  font: italic 1.2rem "Fira Sans", serif;
  font-size: 40px;
}

@media (min-width: 576px) {
  ::deep .items_container {
    max-width: 100%;
  }

  ::deep .items_container header {
    max-width: 100%;
    justify-content: center;
  }

  ::deep .ad {
    display: none;
  }

  ::deep .ad.laptop {
    width: 100%;
    height: auto;
    display: block;
  }

  ::deep .container {
    align-items: center;
    display: flex;
    justify-content: center;
    gap: 96px;
  }
}

@media (min-width: 1200px) {
  ::deep .container {
    display: grid;
    grid-template-columns: minmax(0, 25%) auto;
    align-items: center;
    justify-items: center;
    width: 100%;
    max-width: 100%;
    overflow: hidden;
  }

  ::deep .container>* {
    min-width: 0;
  }

  ::deep .ad {
    display: block !important;
  }

  ::deep .ad.laptop {
    display: none !important;
  }

  header {
    justify-content: start !important;
  }
}