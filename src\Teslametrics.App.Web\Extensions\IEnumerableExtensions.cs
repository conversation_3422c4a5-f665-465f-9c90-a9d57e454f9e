namespace Teslametrics.App.Web.Extensions;

public static class IEnumerableExtensions
{
	private static IEnumerable<T> GetAllDescendants<T>(IEnumerable<T> rootNodes, Func<T, IEnumerable<T>> childrenPropertySelector)
	{
		var descendants = rootNodes.SelectMany(_ => GetAllDescendants(childrenPropertySelector.Invoke(_), childrenPropertySelector));
		return rootNodes.Concat(descendants);
	}

	public static T? GetParent<T>(this IEnumerable<T> rootList, Func<T, bool> childSelector, Func<T, IEnumerable<T>> childrenPropertySelector)
	{
		IEnumerable<T> allNodes = GetAllDescendants(rootList, childrenPropertySelector);
		var parentsOfSelectedChildren = allNodes.Where(node => childrenPropertySelector.Invoke(node).Any(childSelector));

		if (parentsOfSelectedChildren.Any())
			return parentsOfSelectedChildren.Single();
		else
			return default(T);
	}

	public static T GetParent<T>(this T rootNode, Func<T, bool> childSelector, Func<T, IEnumerable<T>> childrenPropertySelector)
	{
		IEnumerable<T> allNodes = GetAllDescendants([rootNode], childrenPropertySelector);
		var parentsOfSelectedChildren = allNodes.Where(node => childrenPropertySelector.Invoke(node).Any(childSelector));

		return parentsOfSelectedChildren.Single();
	}

	public static bool ContainsChildren<T>(this T root, T target, Func<T, IEnumerable<T>> childrenPropertySelector)
	{
		if (root is null) return false;

		// Если текущий элемент содержит искомый Id
		if (root.Equals(target)) return true;

		// Рекурсивно проверяем всех детей текущего узла
		foreach (var child in childrenPropertySelector.Invoke(root))
		{
			if (ContainsChildren(child, target, childrenPropertySelector)) return true;
		}

		// Если элемент не найден
		return false;
	}

	public static bool ContainsChildren<T>(this T root, Func<T, bool> selector, Func<T, IEnumerable<T>> childrenPropertySelector)
	{
		if (root is null) return false;

		// Если текущий элемент содержит искомый Id
		if (selector.Invoke(root)) return true;

		// Рекурсивно проверяем всех детей текущего узла
		foreach (var child in childrenPropertySelector.Invoke(root))
		{
			if (ContainsChildren(child, selector, childrenPropertySelector)) return true;
		}

		// Если элемент не найден
		return false;
	}

	public static T? FindChildren<T>(this IEnumerable<T> root, Func<T, bool> selector, Func<T, IEnumerable<T>> childrenPropertySelector)
	{
		if (root is null) return default;

		// Рекурсивно проверяем всех детей текущего узла
		foreach (var child in root)
		{
			var found = FindChildren(child, selector, childrenPropertySelector);
			if (found is not null) return found;
		}

		// Если элемент не найден
		return default;
	}

	public static T? FindChildren<T>(this T root, Func<T, bool> selector, Func<T, IEnumerable<T>> childrenPropertySelector)
	{
		if (root is null) return default;

		// Если текущий элемент содержит искомый Id
		if (selector.Invoke(root)) return root;

		// Рекурсивно проверяем всех детей текущего узла
		foreach (var child in childrenPropertySelector.Invoke(root))
		{
			var found = FindChildren(child, selector, childrenPropertySelector);
			if (found is not null) return found;
		}

		// Если элемент не найден
		return default;
	}
}
