using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;

namespace Teslametrics.App.Web.Features.DetailView;

public partial class FullscreenButton : IAsyncDisposable
{
	private IJSObjectReference? _playerJsModule;

	private bool _jsModuleLoaded = false;
	private bool _isFullscreen = false;

	[Inject]
	public IJSRuntime Js { get; set; } = null!;

	public async ValueTask DisposeAsync()
	{
		try
		{
			if (_playerJsModule is not null)
			{
				await _playerJsModule.DisposeAsync();
			}
		}
		catch (JSDisconnectedException) // А всё. Соединения нету. https://learn.microsoft.com/en-us/aspnet/core/blazor/javascript-interoperability/?view=aspnetcore-9.0
		{
		}
		catch (Exception exc)
		{
			Logger.LogError(exc, exc.Message);
		}
	}

	protected override async Task OnAfterRenderAsync(bool firstRender)
	{
		if (firstRender)
		{
			try
			{
				_playerJsModule = await Js.InvokeAsync<IJSObjectReference>("import", "./Features/DetailView/FullscreenButton.razor.js");
				_isFullscreen = await _playerJsModule.InvokeAsync<bool>("isFullscreen");
				_jsModuleLoaded = true;
				StateHasChanged();
			}
			catch (JSDisconnectedException) // А всё. Соединения нету. https://learn.microsoft.com/en-us/aspnet/core/blazor/javascript-interoperability/?view=aspnetcore-9.0
			{
				_jsModuleLoaded = false;
			}
			catch (Exception exc)
			{
				_jsModuleLoaded = false;
				Logger.LogError(exc, exc.Message);
			}
		}
		await base.OnAfterRenderAsync(firstRender);
	}

	private async Task OnToggleAsync()
	{
		if (_playerJsModule is null) return;
		_isFullscreen = await _playerJsModule.InvokeAsync<bool>("toggleFullscreen");
	}
}
