<?xml version="1.0" encoding="utf-8"?>
<root>
  <!--
    Microsoft ResX Schema

    Version 2.0

    The primary goals of this format is to allow a simple XML format
    that is mostly human readable. The generation and parsing of the
    various data types are done through the TypeConverter classes
    associated with the data types.

    Example:

    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>

    There are any number of "resheader" rows that contain simple
    name/value pairs.

    Each data row contains a name, and value. The row also contains a
    type or mimetype. Type corresponds to a .NET class that support
    text/value conversion through the TypeConverter architecture.
    Classes that don't support this are serialized and stored with the
    mimetype set.

    The mimetype is used for serialized objects, and tells the
    ResXResourceReader how to depersist the object. This is currently not
    extensible. For a given mimetype the value must be set accordingly:

    Note - application/x-microsoft.net.object.binary.base64 is the format
    that the ResXResourceWriter will generate, however the reader can
    read any of the formats listed below.

    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Main.AccessControl.Users.Update" xml:space="preserve">
    <value>Edit user</value>
  </data>
  <data name="Main.AccessControl.Users.Create" xml:space="preserve">
    <value>Create user</value>
  </data>
  <data name="Description:Main.AccessControl.Users.Create" xml:space="preserve">
    <value>Allows a user to create a user. This right is inherited by viewing users.</value>
  </data>
  <data name="Description:Main.AccessControl.Users.Update" xml:space="preserve">
    <value>Allows the user to edit created users. This right is inherited by viewing users.</value>
  </data>
  <data name="Main.AccessControl.Organizations.Read" xml:space="preserve">
    <value>Read organizations</value>
  </data>
  <data name="Description:Main.AccessControl.Organizations.Read" xml:space="preserve">
    <value>Allows the user to view information about organizations.</value>
  </data>
  <data name="Main.AccessControl.Organizations.Create" xml:space="preserve">
    <value>Create organizations</value>
  </data>
  <data name="Description:Main.AccessControl.Organizations.Create" xml:space="preserve">
    <value>Allows the user to create organizations.  This right is inherited by viewing organizations.</value>
  </data>
  <data name="Main.AccessControl.Organizations.Update" xml:space="preserve">
    <value>Edit organization</value>
  </data>
  <data name="Description:Main.AccessControl.Organizations.Update" xml:space="preserve">
    <value>Allows the user to edit organizations.  This right is inherited by viewing organizations.</value>
  </data>
  <data name="Main.AccessControl.Organizations.Delete" xml:space="preserve">
    <value>Delete organizations</value>
  </data>
  <data name="Description:Main.AccessControl.Organizations.Delete" xml:space="preserve">
    <value>Allows the user to delete organizations. This right is inherited by viewing organizations.</value>
  </data>
  <data name="Main.AccessControl.Users.Read" xml:space="preserve">
    <value>Read users</value>
  </data>
  <data name="Description:Main.AccessControl.Users.Read" xml:space="preserve">
    <value>Allows the user to view users.</value>
  </data>
  <data name="Main.AccessControl.Users.Delete" xml:space="preserve">
    <value>Delete users</value>
  </data>
  <data name="Description:Main.AccessControl.Users.Delete" xml:space="preserve">
    <value>Allows the user to delete users. This right is inherited by viewing users.</value>
  </data>
  <data name="Main.AccessControl.Users.Lock" xml:space="preserve">
    <value>Lock users</value>
  </data>
  <data name="Main.AccessControl.Users.Unlock" xml:space="preserve">
    <value>Unlock users</value>
  </data>
  <data name="Main.AccessControl.Users.ForceChangePassword" xml:space="preserve">
    <value>Force the user to change their password upon login</value>
  </data>
  <data name="Main.AccessControl.Roles.Read" xml:space="preserve">
    <value>Read roles</value>
  </data>
  <data name="Main.AccessControl.Roles.Create" xml:space="preserve">
    <value>Create roles</value>
  </data>
  <data name="Main.AccessControl.Roles.Update" xml:space="preserve">
    <value>Edit roles</value>
  </data>
  <data name="Main.AccessControl.Roles.Delete" xml:space="preserve">
    <value>Delete roles</value>
  </data>
  <data name="Description:Main.AccessControl.Users.Lock" xml:space="preserve">
    <value>Allows a user to block other users. This right inherits the user's view right.
</value>
  </data>
  <data name="Description:Main.AccessControl.Users.Unlock" xml:space="preserve">
    <value>Allows the user to unblock other users. This right inherits the user's view right.</value>
  </data>
  <data name="Description:Main.AccessControl.Users.ForceChangePassword" xml:space="preserve">
    <value>Allows a user to force another user to change their password the next time they log into the application. This right inherits the user's view right</value>
  </data>
  <data name="Description:Main.AccessControl.Roles.Read" xml:space="preserve">
    <value>Allows the user to view role information.</value>
  </data>
  <data name="Description:Main.AccessControl.Roles.Create" xml:space="preserve">
    <value>Allows the user to create new roles. This right inherits the user's view right.</value>
  </data>
  <data name="Description:Main.AccessControl.Roles.Update" xml:space="preserve">
    <value>Allows the user to edit roles. This right inherits the user's view right.</value>
  </data>
  <data name="Description:Main.AccessControl.Roles.Delete" xml:space="preserve">
    <value>Allows the user to delete roles. This right inherits the user's view right.</value>
  </data>
  <data name="Description:Main.Cameras.Read" xml:space="preserve">
    <value>Allows a user with this right to view cameras</value>
  </data>
  <data name="Main.Cameras.Read" xml:space="preserve">
    <value>Viewing cameras</value>
  </data>
  <data name="Main.Cameras.Create" xml:space="preserve">
    <value>Creating cameras</value>
  </data>
  <data name="Main.Cameras.Update" xml:space="preserve">
    <value>Editing cameras</value>
  </data>
  <data name="Description:Main.Cameras.Create" xml:space="preserve">
    <value>Access to add new cameras to the system.</value>
  </data>
  <data name=" Main.Cameras.Delete" xml:space="preserve">
    <value>Delete cameras</value>
  </data>
  <data name="Main.Cameras.Connect" xml:space="preserve">
    <value>Connect camera</value>
  </data>
  <data name="Main.Cameras.Disconnect" xml:space="preserve">
    <value>Disconnect cameras</value>
  </data>
  <data name="Main.Cameras.Move" xml:space="preserve">
    <value>Move cameras to another folder</value>
  </data>
  <data name="Main.Folders.Read" xml:space="preserve">
    <value>Read folders</value>
  </data>
  <data name="Main.Folders.Create" xml:space="preserve">
    <value>Create folders</value>
  </data>
  <data name="Main.Cameras.Delete" xml:space="preserve">
    <value>Delete cameras</value>
  </data>
  <data name="Main.Folders.Update" xml:space="preserve">
    <value>Edit folders</value>
  </data>
  <data name="Main.Folders.Delete" xml:space="preserve">
    <value>Delete folders</value>
  </data>
  <data name="Main.Folders.Move" xml:space="preserve">
    <value>Move folders</value>
  </data>
  <data name="Description:Main.Cameras.Update" xml:space="preserve">
    <value>Allows the user to edit created cameras. This right is inherited by viewing cameras.</value>
  </data>
  <data name="Description:Main.Cameras.Delete" xml:space="preserve">
    <value>Allows the user to delete created cameras. This right is inherited by viewing cameras.</value>
  </data>
  <data name="Description:Main.Cameras.Connect" xml:space="preserve">
    <value>Allows the user to connect cameras. This right is inherited by viewing cameras.</value>
  </data>
  <data name="Description:Main.Cameras.Disconnect" xml:space="preserve">
    <value>Allows the user to disconnect cameras. This right is inherited by viewing cameras.</value>
  </data>
  <data name="Description:Main.Cameras.Move" xml:space="preserve">
    <value>allows the user to move cameras between directories. This right inherits the right to view cameras.</value>
  </data>
  <data name="Description:Main.Folders.Read" xml:space="preserve">
    <value>allows the user to move directories to other directories. This right inherits the right to view directories.</value>
  </data>
  <data name="Description:Main.Folders.Create" xml:space="preserve">
    <value>Allows the user to create directories. This right inherits the right to view directories.</value>
  </data>
  <data name="Description:Main.Folders.Update" xml:space="preserve">
    <value>Allows the user to edit directories. This right inherits the right to view directories.</value>
  </data>
  <data name="Description:Main.Folders.Delete" xml:space="preserve">
    <value>Allows the user to delete directories. This right inherits the right to view directories.</value>
  </data>
  <data name="Description:Main.Folders.Move" xml:space="preserve">
    <value>Allows the user to move directories to other directories. This right inherits the right to view directories.</value>
  </data>
  <data name="Description:Main.Groups.Read" xml:space="preserve">
    <value>Allows the user to view groups.</value>
  </data>
  <data name="Description:Main.Groups.Create" xml:space="preserve">
    <value>Allows the user to create groups. This right inherits the right to view groups.</value>
  </data>
  <data name="Description:Main.Groups.Update" xml:space="preserve">
    <value>Allows the user to edit groups. This right inherits the right to view groups.</value>
  </data>
  <data name="Description:Main.Groups.Delete" xml:space="preserve">
    <value>Allows the user to delete groups. This right inherits the right to view groups.</value>
  </data>
  <data name="Description:Main.Groups.Move" xml:space="preserve">
    <value>Allows the user to move groups. This right inherits the right to view groups.</value>
  </data>
  <data name="Main.Groups.Read" xml:space="preserve">
    <value>View groups</value>
  </data>
  <data name="Main.Groups.Create" xml:space="preserve">
    <value>Creating groups</value>
  </data>
  <data name="Main.Groups.Update" xml:space="preserve">
    <value>Editing groups</value>
  </data>
  <data name="Main.Groups.Delete" xml:space="preserve">
    <value>Deleting groups</value>
  </data>
  <data name="Main.Groups.Move" xml:space="preserve">
    <value>Moving groups</value>
  </data>
  <data name="Main.CameraPresets.Create" xml:space="preserve">
    <value>Create camera presets</value>
  </data>
  <data name="Main.CameraPresets.Delete" xml:space="preserve">
    <value>Delete camera presets</value>
  </data>
  <data name="Main.CameraPresets.Read" xml:space="preserve">
    <value>Read cameras presets</value>
  </data>
  <data name="Main.CameraPresets.Update" xml:space="preserve">
    <value>Edit camera presets</value>
  </data>
  <data name="Description:Main.CameraPresets.Read" xml:space="preserve">
    <value>Allows the user to view created camera presets, and their settings.</value>
  </data>
  <data name="Description:Main.CameraPresets.Delete" xml:space="preserve">
    <value>Allows the user to delete created camera presets. This right is inherited by viewing camera presets.</value>
  </data>
  <data name="Description:Main.CameraPresets.Update" xml:space="preserve">
    <value>Allows the user to edit created camera presets. This right is inherited by viewing camera presets.</value>
  </data>
  <data name="Description:Main.CameraPresets.Create" xml:space="preserve">
    <value>Allows the user to create camera presets. This right is inherited by viewing camera presets.</value>
  </data>
  <data name="Description:Main.CameraQuotas.Read" xml:space="preserve">
    <value>Allows the user to view created camera quotas, and their settings.</value>
  </data>
  <data name="Description:Main.CameraQuotas.Update" xml:space="preserve">
    <value>Allows the user to edit created camera quotas. This right is inherited by viewing camera quotas.</value>
  </data>
  <data name="Description:Main.CameraQuotas.Create" xml:space="preserve">
    <value>Allows the user to create camera quotas. This right is inherited by viewing camera quotas.</value>
  </data>
  <data name="Description:Main.CameraQuotas.Delete" xml:space="preserve">
    <value>Allows the user to delete created camera quotas. This right is inherited by viewing camera quotas.</value>
  </data>
  <data name="Main.CameraQuotas.Read" xml:space="preserve">
    <value>View camera quotas</value>
  </data>
  <data name="Main.CameraQuotas.Update" xml:space="preserve">
    <value>Edit camera quotas</value>
  </data>
  <data name="Main.CameraQuotas.Create" xml:space="preserve">
    <value>Create camera quotas</value>
  </data>
  <data name="Main.CameraQuotas.Delete" xml:space="preserve">
    <value>Delete camera quotas</value>
  </data>
  <data name="Description:Main.CameraPublicAccess.Read" xml:space="preserve">
    <value>Allows the user to view created camera public access, and their settings.</value>
  </data>
  <data name="Main.CameraPublicAccess.Read" xml:space="preserve">
    <value>View camera public access</value>
  </data>
  <data name="Description:Main.CameraPublicAccess.Update" xml:space="preserve">
    <value>Allows the user to edit created camera public access. This right is inherited by viewing camera public access.</value>
  </data>
  <data name="Main.CameraPublicAccess.Update" xml:space="preserve">
    <value>Edit camera public access</value>
  </data>
  <data name="Description:Main.CameraPublicAccess.Create" xml:space="preserve">
    <value>Allows the user to create camera public access. This right is inherited by viewing camera public access.</value>
  </data>
  <data name="Main.CameraPublicAccess.Create" xml:space="preserve">
    <value>Create camera public access</value>
  </data>
  <data name="Description:Main.CameraPublicAccess.Delete" xml:space="preserve">
    <value>Allows the user to delete created camera public access. This right is inherited by viewing camera public access.</value>
  </data>
  <data name="Main.CameraPublicAccess.Delete" xml:space="preserve">
    <value>Delete camera public access</value>
  </data>
  <data name="Main.CameraViews" xml:space="preserve">
    <value>Camera views</value>
  </data>
  <data name="Description:Main.CameraViews" xml:space="preserve">
    <value>Allows the user to view cameras.</value>
  </data>
  <data name="Main.CameraViews.Read" xml:space="preserve">
    <value>View cameras</value>
  </data>
  <data name="Description:Main.CameraViews.Read" xml:space="preserve">
    <value>Allows viewing cameras in the system.</value>
  </data>
  <data name="Main.CameraViews.Create" xml:space="preserve">
    <value>Create cameras</value>
  </data>
  <data name="Description:Main.CameraViews.Create" xml:space="preserve">
    <value>Allows creating new cameras. This right inherits camera viewing rights.</value>
  </data>
  <data name="Main.CameraViews.Update" xml:space="preserve">
    <value>Edit cameras</value>
  </data>
  <data name="Description:Main.CameraViews.Update" xml:space="preserve">
    <value>Allows editing existing cameras. This right inherits camera viewing rights.</value>
  </data>
  <data name="Main.CameraViews.Delete" xml:space="preserve">
    <value>Delete cameras</value>
  </data>
  <data name="Description:Main.CameraViews.Delete" xml:space="preserve">
    <value>Allows deleting cameras. This right inherits camera viewing rights.</value>
  </data>
</root>