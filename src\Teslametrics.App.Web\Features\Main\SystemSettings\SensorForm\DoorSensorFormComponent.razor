@if (_model is not null)
{
	<MudTextField @bind-Value="_model.DisplayName" Label="Наименование датчика" Clearable="true" />
	<MudTextField @bind-Value="_model.Name" Label="Наименование топика" />
	<MudNumericField @bind-Value="_model.AvailableOpeningTime" Label="Максимальное время открытия двери, секунд" />
}

@code {
	private DoorModel? _model => SensorModel as DoorModel;

	[Parameter]
	public ISensorModel? SensorModel { get; set; }
}
