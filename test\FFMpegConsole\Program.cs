using FFMpegNET;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

/// <summary>
/// Основной класс программы для обработки RTSP потока
/// </summary>
class Program
{
    // URI камеры для подключения
    //private static string _cameraUri = "rtsp://admin:veresk13@77.37.206.139:5543/Streaming/Channels/101";
    private static string _cameraUri = "rtsp://115.74.224.136:554";
    // Длительность сегмента видео в секундах

    /// <summary>
    /// Точка входа в программу
    /// </summary>
    private static async Task Main(string[] args)
    {
        var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Debug));
        var logger = loggerFactory.CreateLogger<RtspMicroSegmenter>();

        FFMpegSetup.InitializeFFMpeg(logger);

        if (Directory.Exists("stream"))
        {
            Directory.Delete("stream", true);
        }

        Directory.CreateDirectory("stream");

        if (Directory.Exists("segments"))
        {
            Directory.Delete("segments", true);
        }

        Directory.CreateDirectory("segments");

        var cts = new CancellationTokenSource();

        var segmenterOptions = RtspMicroSegmenter.Options.Default;

        var segmenter = new RtspMicroSegmenter(logger, Options.Create(segmenterOptions));

        var accumulatorOptions = new MicroSegmentAccumulator.Options
        {
            SegmentDuration = 15,
            StrictTimestampValidation = true // Отключаем строгую проверку для работы с поврежденными потоками
        };

        var accumulator = new MicroSegmentAccumulator(loggerFactory.CreateLogger<MicroSegmentAccumulator>(), Options.Create(accumulatorOptions));
        _accumulator = accumulator;

        _previewGenerator = new MicroSegmentPreviewGenerator(loggerFactory.CreateLogger<MicroSegmentPreviewGenerator>(), Options.Create(new MicroSegmentPreviewGenerator.Options
        {
            PreviewSegmentInterval = 1,
            PreviewWidth = 320,
            PreviewHeight = 240,
            PreviewQuality = 100
        }));

        try
        {
            var segmenterTask = segmenter.Run(_cameraUri, true, OnNextMicroSegment, cts.Token);
            var accumulatorTask = accumulator.Run(OnNextSegment, cts.Token);

            await Task.Delay(100000); // Запускаем на 10 секунд

            segmenter.RequestGracefulStop();
            accumulator.RequestGracefulStop();

            await Task.WhenAll(segmenterTask, accumulatorTask);
        }
        catch (Exception ex)
        {
            logger.LogError("Error: {Message}", ex.Message);
        }
        finally
        {
            segmenter.Dispose();
            cts.Dispose();
        }

        logger.LogInformation("Segmentation completed.");

        FFMpegSetup.FinalizeFFMpeg();
    }

    private static MicroSegmentAccumulator? _accumulator;
    private static MicroSegmentPreviewGenerator? _previewGenerator;

    private static async Task OnNextMicroSegment(MicroSegment microSegment)
    {
        try
        {
            // Сохраняем микросегмент в файл
            using var microSegmentStream = new FileStream($"stream/{microSegment.StartTime:yyyy-MM-dd_HH-mm-ss-fff}.ts", FileMode.Create);
            await microSegmentStream.WriteAsync(microSegment.Payload);

            Console.WriteLine($"Adding micro segment: Duration: {microSegment.Duration:F3} sec, Size: {microSegment.Payload!.Length} bytes");
            // Передаем микросегмент в аккумулятор
            _accumulator?.AddMicroSegment(microSegment);

            var preview = _previewGenerator?.GeneratePreview(microSegment);

            if (preview is not null)
            {
                using var previewStream = new FileStream($"preview.jpg", FileMode.Create);
                await previewStream.WriteAsync(preview);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error processing micro segment: {ex.Message}");
        }
    }

    private static async Task OnNextSegment(Segment segment)
    {
        try
        {
            // Сохраняем длинный сегмент в файл
            using var fileStream = new FileStream($"segments/{segment.StartTime:yyyy-MM-dd_HH-mm-ss-fff}_segment.ts", FileMode.Create);
            segment.Stream.Position = 0;
            await segment.Stream.CopyToAsync(fileStream);

            Console.WriteLine($"Saved segment: {segment.StartTime:yyyy-MM-dd HH:mm:ss.fff}, Duration: {segment.Duration:F3} sec, Size: {segment.Stream.Length} bytes");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error saving segment: {ex.Message}");
        }
    }

    private static async Task OnNextPreview(byte[] jpegData)
    {
        using var fileStream = new FileStream($"preview.jpg", FileMode.Create);
        await fileStream.WriteAsync(jpegData);
    }
}