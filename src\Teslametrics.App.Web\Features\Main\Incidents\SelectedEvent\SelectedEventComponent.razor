@using Teslametrics.App.Web.Features.Main.Incidents.SelectedEvent.SensorData
@using Teslametrics.App.Web.Features.Main.Incidents.SelectedEvent.Player
@inherits InteractiveBaseComponent
<div class="d_contents">
	@if (IsLoading && (_response is null || !_response.IsSuccess))
	{
		<EventLoaderComponent />
	}
	@if (_response is not null && _response.IsSuccess && _response.Fridge is not null && _response.Incident is not null)
	{
			<MudStack Spacing="6"
					  Class="px-4 pt-2 overflow-auto">
				<MudStack Spacing="2">
					<MudText Typo="Typo.subtitle1"
							 Class="pa-2">Общая информация</MudText>
					<MudPaper Elevation="0"
							  Outlined="true"
							  Class="pa-4">
						<MudText>Оборудование: @_response.Fridge.Name</MudText>
						<MudText>Тип происшествия: @_response!.Incident.IncidentType!.Value.GetName()</MudText>
						<MudText>Дата: @_response.Incident.FiredAt.Date.ToLongDateString()</MudText>
						<MudText>Время: @_response.Incident.FiredAt.DateTime.ToLongTimeString()</MudText>
					</MudPaper>
					<MudPaper Elevation="0"
							  Class="@(_response.Incident.IResolved ? "error_description pa-4" : "error_description error_description--resolved pa-4")">
						@switch (_response.Incident.IncidentType)
						{
							case Teslametrics.Shared.IncidentType.Door:
								<MudText>Превышено допустимое время с открытой дверью</MudText>
								break;
							case Teslametrics.Shared.IncidentType.Temperature:
								<MudText>Температура вышла за пределы допустимого диапазона</MudText>
								break;
							case Teslametrics.Shared.IncidentType.Humidity:
								<MudText>Влажность вышла за пределы допустимого диапазона</MudText>
								break;
							case Teslametrics.Shared.IncidentType.Leak:
								<MudText>Завиксирована протечка</MudText>
								break;
							case Teslametrics.Shared.IncidentType.Power:
								<MudText>Пропало питание</MudText>
								break;
						}
					</MudPaper>
				</MudStack>

				<MudStack Spacing=" 2">
					<MudText Typo="Typo.subtitle1"
							 Class="pa-2">Информация о холодильнике</MudText>
					<MudGrid Class="pa-4"
							 Spacing="2">
						@foreach (var card in _doubledCards)
						{
							<MudItem xs="12">
								<MudPaper Elevation="0"
										  Outlined="true"
										  Class="pa-4">
									<MudGrid>
										<MudItem xs="6">
											@if (card.Temperature is not null)
											{
												<SensorDataComponent TopicName="@card.Temperature.Name"
																	 Name="@card.Temperature.DisplayName"
																	 ValueProcessor="@((object value) => value is double doubleValue ? Math.Round(doubleValue, 2) + "°C" : value.ToString())"
																	 Icon="@TeslaIcons.Sensors.Temperature" />
											}
										</MudItem>
										<MudItem xs="6">
											@if (card.Humidity is not null)
											{
												<SensorDataComponent TopicName="@card.Humidity.Name"
																	 Name="@card.Humidity.DisplayName"
																	 ValueProcessor="@((object value) => value.ToString() + "%")"
																	 Icon="@TeslaIcons.Sensors.Humidity" />
											}
										</MudItem>
									</MudGrid>
								</MudPaper>
							</MudItem>
						}
						@foreach (var item in _door ?? [])
						{
							<MudItem xs="12"
									 md="6">
								<MudPaper Elevation="0"
										  Outlined="true"
										  Class="pa-4">
									<SensorDataComponent TopicName="@item.Name"
														 Name="@item.DisplayName"
														 ValueProcessor="@((object value) => value is bool boolValue ? (boolValue ? "Открыта" : "Закрыта") : value.ToString())"
														 Icon="@TeslaIcons.Sensors.Door" />
								</MudPaper>
							</MudItem>
						}
						@foreach (var item in _power ?? [])
						{
							<MudItem xs="12"
									 md="6">
								<MudPaper Elevation="0"
										  Outlined="true"
										  Class="pa-4">
									<SensorDataComponent TopicName="@item.Name"
														 Name="@item.DisplayName"
														 ValueProcessor="@((object value) => value is bool boolValue ? (boolValue ? "Подключен" : "Не подключен") : value.ToString())"
														 Icon="@TeslaIcons.Sensors.Power" />
								</MudPaper>
							</MudItem>
						}
						@foreach (var item in _leak ?? [])
						{
							<MudItem xs="12">
								<MudPaper Elevation="0"
										  Outlined="true"
										  Class="pa-4">
									<SensorDataComponent TopicName="@item.Name"
														 Name="@item.DisplayName"
														 ValueProcessor="@((object value) => value is bool boolValue ? (boolValue ? "Есть протечка" : "Нет протечек") : value.ToString())"
														 Icon="@TeslaIcons.Sensors.Leak" />
								</MudPaper>
							</MudItem>
						}
					</MudGrid>
				</MudStack>

				@if (_response.Cameras.Any())
				{
					<MudStack Spacing="2">
						<MudText Typo="Typo.subtitle1"
								 Class="pa-2">Данные с камеры</MudText>
						<MudGrid Class="pa-4"
								 Spacing="2">
							@foreach (var camera in _response.Cameras)
							{
								<MudItem xs="12"
										 @key="camera">
									<Player Id="@($"camera_{camera}")"
											PathToFile="@($"streams/{camera}/online/stream.m3u8")" />
								</MudItem>
							}
						</MudGrid>
					</MudStack>
				}
        </MudStack>
	}
</div>