using Microsoft.AspNetCore.Components;
using MudBlazor;
using System.Reactive;
using System.Reactive.Linq;
using Teslametrics.App.Web.Extensions;

namespace Teslametrics.App.Web.Features.Main.Devices.RoomContent;

public partial class RoomContentComponent : IDisposable
{
	private GetRoomContentUseCase.Response? _response;
	private SubscribeRoomContentUseCase.Response? _subscribeResponse;
	private bool _subscribing;

	[Inject]
	protected NavigationManager NavigationManager { get; set; } = null!;

	[Parameter]
	[EditorRequired]
	public Guid RoomId { get; set; }

	protected override async Task OnParametersSetAsync()
	{
		await base.OnParametersSetAsync();
		await FetchAsync();
		await SubscribeAsync();
	}

	private async Task FetchAsync()
	{
		if (IsLoading) return;
		try
		{
			await SetLoadingAsync(true);
			_response = await ScopeFactory.MediatorSend(new GetRoomContentUseCase.Query(RoomId));
		}
		catch (Exception ex)
		{
			_response = null;
			Logger.LogError(ex, ex.Message);
			Snackbar.Add($"Не удалось получить данные комнаты из-за ошибки сообщения с сервером. Повторите попытку позже.", Severity.Error);
		}

		await SetLoadingAsync(false);
		if (_response is null) return;

		switch (_response.Result)
		{
			case GetRoomContentUseCase.Result.Success:
				break;
			case GetRoomContentUseCase.Result.RoomNotFound:
				Snackbar.Add("Комната не найдена", Severity.Error);
				NavigationManager.NavigateTo("/devices");
				break;
			case GetRoomContentUseCase.Result.PlanNotFound:
				Snackbar.Add("Комната не найдена", Severity.Error);
				NavigationManager.NavigateTo("/devices");
				break;
			case GetRoomContentUseCase.Result.Unknown:
				Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(RoomContentComponent), nameof(GetRoomContentUseCase));
				Snackbar.Add($"Не удалось получить данные комнаты из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", Severity.Error);
				break;
			default:
				Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(RoomContentComponent), nameof(GetRoomContentUseCase), _response.Result);
				Snackbar.Add($"Не удалось получить данные комнаты из-за ошибки: {_response.Result}", Severity.Error);
				break;
		}
	}

	private async Task SubscribeAsync()
	{
		Unsubscribe();

		if (_response is null || !_response.IsSuccess || _subscribing) return;
		if (RoomId == Guid.Empty || _response is null || !_response.IsSuccess) return;
		try
		{
			await SetSubscribingAsync(true);
			_subscribeResponse = await ScopeFactory.MediatorSend(new SubscribeRoomContentUseCase.Request(Observer.Create<object>(OnAppEventHandler, OnError), RoomId, _response.Cameras));
		}
		catch (Exception ex)
		{
			_subscribeResponse = null;
			Snackbar.Add($"Не удалось подписаться на события комнаты из-за ошибки сообщения с сервером. Повторите попытку позже.", MudBlazor.Severity.Error);
			Logger.LogError(ex, ex.Message);
		}

		await SetSubscribingAsync(false);
		if (_subscribeResponse is null) return;
		switch (_subscribeResponse.Result)
		{
			case SubscribeRoomContentUseCase.Result.Success:
				break;
			case SubscribeRoomContentUseCase.Result.ValidationError:
				Snackbar.Add("Ошибка валидации при подписке на события комнаты", MudBlazor.Severity.Error);
				break;
			case SubscribeRoomContentUseCase.Result.Unknown:
				Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(RoomContentComponent), nameof(SubscribeRoomContentUseCase));
				Snackbar.Add($"Не удалось подписаться на события комнаты из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
				break;
			default:
				Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(RoomContentComponent), nameof(SubscribeRoomContentUseCase), _subscribeResponse.Result);
				Snackbar.Add($"Не удалось подписаться на события комнаты из-за ошибки: {_subscribeResponse.Result}", MudBlazor.Severity.Error);
				break;
		}
	}

	private void Unsubscribe()
	{
		if (_subscribeResponse?.Subscription is not null)
		{
			CompositeDisposable.Remove(_subscribeResponse.Subscription);
			_subscribeResponse.Subscription.Dispose();
		}
	}

	protected Task SetSubscribingAsync(bool isLoading = true) => UpdateViewAsync(() =>
	{
		_subscribing = isLoading;
	});

	#region [Event Handlers]
	private async void OnAppEventHandler(object appEvent)
	{
		await FetchAsync();
		await UpdateViewAsync();
	}

	private void OnError(Exception exc)
	{
		Snackbar.Add("Ошибка при подписке на события", MudBlazor.Severity.Error);
		Logger.LogError(exc, exc.Message);
	}
	#endregion [Event Handlers]
}
