using System.Data;
using Dapper;
using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Data.Sql;
using Teslametrics.App.Web.Services.Persistence;
using Teslametrics.App.Web.Shared;

namespace Teslametrics.App.Web.Features.Main.Presets.List;

public static class GetCameraPresetListUseCase
{
	public record Query(Guid UserId, int Offset, int Limit, string Filter) : BaseRequest<Response>;

	public record Response : BaseResponse
	{
		public List<Item> Items { get; init; }

		public int TotalCount { get; init; }

		public Result Result { get; init; }

		public bool IsSuccess => Result == Result.Success;

		public Response(List<Item> items, int totalCount)
		{
			Items = items;
			TotalCount = totalCount;
			Result = Result.Success;
		}

		public Response(Result result)
		{
			if (result == Result.Success)
			{
				throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
			}

			Result = result;

			Items = [];
			TotalCount = 0;
		}

		public record Item(Guid Id, string Name);
	}
	public enum Result
	{
		Unknown = 0,
		Success,
		ValidationError
	}

	public class Validator : AbstractValidator<Query>
	{
		public Validator()
		{
			RuleFor(q => q.UserId).NotEmpty();
			RuleFor(q => q.Offset).GreaterThanOrEqualTo(0);
			RuleFor(q => q.Limit).GreaterThan(0);
			RuleFor(q => q.Filter).MaximumLength(60);
		}
	}

	public class Handler : IRequestHandler<Query, Response>
	{
		private readonly IValidator<Query> _validator;
		private readonly IDbConnection _dbConnection;

		public Handler(IValidator<Query> validator,
					   IDbConnection dbConnection)
		{
			_validator = validator;
			_dbConnection = dbConnection;
		}

		public async Task<Response> Handle(Query request, CancellationToken cancellationToken)
		{
			if (!_validator.Validate(request).IsValid)
			{
				return new Response(Result.ValidationError);
			}

			var hasPermission = request.UserId == SystemConsts.RootUserId
				|| await HasPermissionAsync(request.UserId);

			if (!hasPermission)
			{
				return new Response([], 0);
			}

			var builder = SqlQueryBuilder.Create()
				.WhereIf(!string.IsNullOrEmpty(request.Filter), Db.Presets.Props.Name, "CONCAT('%', :Filter, '%')", SqlOperator.Like, new { request.Filter });

			var countTemplate = builder.Build(QueryType.Standard, Db.Presets.Table, RowSelection.AllRows, ["COUNT(*)"]);
			var totalCount = await _dbConnection.ExecuteScalarAsync<int>(countTemplate.RawSql, countTemplate.Parameters);

			var selectTemplate = builder.Build(QueryType.Paginated,
											   Db.Presets.Table,
											   RowSelection.AllRows,
											   [
												   Db.Presets.Props.Id,
												   Db.Presets.Props.Name
											   ],
											   new { request.Limit, request.Offset });
			var presets = await _dbConnection.QueryAsync<CameraPresetModel>(selectTemplate.RawSql, selectTemplate.Parameters);

			return new Response(presets.Select(u => new Response.Item(u.Id, u.Name)).ToList(), totalCount);
		}

		private Task<bool> HasPermissionAsync(Guid userId)
		{
			var template = SqlQueryBuilder.Create()
				.Select($"COUNT(DISTINCT {Db.RolePermissions.Props.ResourceId}) > 0")
				.InnerJoin(Db.UserRoles.Table, Db.UserRoles.Props.RoleId, Db.RolePermissions.Props.RoleId, SqlOperator.Equals)
				.Where(Db.RolePermissions.Props.Permission, ":Permission", SqlOperator.Equals, new { Permission = Fqdn<AppPermissions>.GetName(AppPermissions.Main.CameraPresets.Read) })
				.Where(Db.UserRoles.Props.UserId, ":UserId", SqlOperator.Equals, new { userId })
				.Build(QueryType.Standard, Db.RolePermissions.Table, RowSelection.AllRows);

			return _dbConnection.ExecuteScalarAsync<bool>(template.RawSql, template.Parameters);
		}
	}

	public record CameraPresetModel(Guid Id, string Name);
}