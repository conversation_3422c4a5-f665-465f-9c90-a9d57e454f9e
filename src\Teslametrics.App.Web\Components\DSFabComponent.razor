﻿@using Microsoft.AspNetCore.Components.Web

@inherits MudFab

<MudElement @bind-Ref="@_elementReference"
            HtmlTag="@HtmlTag"
            Class="@Classname"
            Style="@Style"
            @attributes="UserAttributes"
            @onclick="OnClickHandler"
            type="@ButtonType.ToDescriptionString()"
            href="@Href"
            target="@Target"
            rel="@GetRel()"
            disabled="@isDisabled"
            id="@Id">
    @if (IsLoading)
    {
        <MudProgressCircular Size="IconSize"
                             Indeterminate="true" />
    }
    @if (!IsLoading && !string.IsNullOrWhiteSpace(StartIcon))
    {
        <MudIcon Icon="@StartIcon"
                 Color="@IconColor"
                 Size="@IconSize" />
    }
    <span class="mud-fab-label">
        @Label
    </span>
    @if (!string.IsNullOrWhiteSpace(EndIcon))
    {
        <MudIcon Icon="@EndIcon"
                 Color="@IconColor"
                 Size="@IconSize" />
    }
</MudElement>

@code {
    private bool isDisabled => GetDisabledState() || IsLoading;

    [Parameter]
    public bool IsLoading { get; set; } = false;

    [Parameter]
    public string Id { get; set; } = Guid.NewGuid().ToString();
}