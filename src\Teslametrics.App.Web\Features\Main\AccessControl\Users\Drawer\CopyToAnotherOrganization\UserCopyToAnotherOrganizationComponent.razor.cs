using FluentValidation;
using Microsoft.AspNetCore.Components;
using Teslametrics.App.Web.Abstractions;


namespace Teslametrics.App.Web.Features.Main.AccessControl.Users.Drawer.CopyToAnotherOrganization;

public partial class UserCopyToAnotherOrganizationComponent
{
	private class RoleModel(Guid id, string name)
	{
		public Guid Id { get; set; } = id;
		public string Username { get; init; } = name;
		public IEnumerable<RoleModel> Roles { get; init; } = [];
	}
	private class CopyUserModel
	{
		public IEnumerable<RoleModel> Roles { get; set; } = [];
	}

	private class CreateUserValidator : BaseFluentValidator<CopyUserModel>
	{
		public CreateUserValidator()
		{
			RuleFor(model => model.Roles)
				.NotEmpty()
				.WithMessage("Добавьте ххотя бы 1 роль");
		}
	}

	private bool _isValid;
	private CopyUserModel _model = new();
	private List<RoleModel> _roles = new();

	private CreateUserValidator _validator = new();

	#region Parameters
	[Parameter]
	public Guid OrganizationId { get; set; } // TODO:: | UI impelemnt Organization Id
	#endregion

	protected override async Task OnInitializedAsync()
	{
		await FetchRolesAsync();
		await base.OnInitializedAsync();
	}

	private async Task FetchRolesAsync()
	{
		if (IsLoading) return;

		try
		{
			//await SetLoadingAsync();
			//_roles.Clear();
			//int offset = 0;
			//int limit = 25;
			//do
			//{
			//	var result = await ScopeFactory.MidiatorSend(new GetRoleListUseCase.Query(offset, limit, string.Empty));
			//	_roles.AddRange(result.Items.Select(item => new RoleModel(item.Id, item.Name)));
			//	offset += limit;
			//	if (offset >= result.TotalCount)
			//	{
			//		break;
			//	}
			//} while (true);
		}
		catch (Exception ex)
		{
			Snackbar.Add("Не удалось получить выбранную роль. Повторите попытку", MudBlazor.Severity.Error);
			Logger.LogError(ex, ex.Message);
		}
		finally
		{
			await SetLoadingAsync(false);
		}
	}

	#region [Actions]
	#endregion
}
