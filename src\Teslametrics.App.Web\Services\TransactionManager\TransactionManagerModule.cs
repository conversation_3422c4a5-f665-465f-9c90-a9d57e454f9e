﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;
using Teslametrics.App.Web.Services.Persistence;

namespace Teslametrics.App.Web.Services.TransactionManager;

public static class TransactionManagerModule
{
    public static void Install(IServiceCollection services)
    {
        services.AddScoped<ITransactionManager, DefaultManager<CommandAppDbContext>>();
    }

    public class DefaultManager<TDbContext> : ITransactionManager
        where TDbContext : DbContext
    {
        private readonly TDbContext _dbContext;

        public DefaultManager(TDbContext dbContext)
        {
            _dbContext = dbContext;
        }

        public async Task<ITransaction> CreateTransactionAsync()
        {
            return new Transaction(await _dbContext.Database.BeginTransactionAsync());
        }
    }

    public class Transaction : ITransaction
    {
        private readonly IDbContextTransaction _dbContextTransaction;

        public Transaction(IDbContextTransaction dbContextTransaction)
        {
            _dbContextTransaction = dbContextTransaction;
        }

        public Task CommitAsync()
        {
            return _dbContextTransaction.CommitAsync();
        }

        public Task RollbackAsync()
        {
            return _dbContextTransaction.RollbackAsync();
        }

        public void Dispose()
        {
            _dbContextTransaction.Dispose();
        }

        public ValueTask DisposeAsync()
        {
            return _dbContextTransaction.DisposeAsync();
        }
    }
}