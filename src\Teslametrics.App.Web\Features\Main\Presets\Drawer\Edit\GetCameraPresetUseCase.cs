using System.Data;
using System.Text.Json;
using Dapper;
using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Services;
using Teslametrics.Core.Services.Persistence;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Features.Main.Presets.Drawer.Edit;

public static class GetCameraPresetUseCase
{
	public record Query(Guid Id) : BaseRequest<Response>;

	public record Response : BaseResponse
	{
		public Guid Id { get; init; }

		public string Name { get; init; }

		public StreamConfig? ArchiveStreamConfig { get; init; }

		public StreamConfig? ViewStreamConfig { get; init; }

		public StreamConfig? PublicStreamConfig { get; init; }

		public Result Result { get; init; }

		public bool IsSuccess => Result == Result.Success;

		public Response(Guid id, string name, StreamConfig archiveStreamConfig, StreamConfig viewStreamConfig, StreamConfig publicStreamConfig)
		{
			Id = id;
			Name = name;
			ArchiveStreamConfig = archiveStreamConfig;
			ViewStreamConfig = viewStreamConfig;
			PublicStreamConfig = publicStreamConfig;
			Result = Result.Success;
		}

		public Response(Result result)
		{
			if (result == Result.Success)
			{
				throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
			}

			Result = result;

			Id = Guid.Empty;
			Name = string.Empty;
		}

		public record StreamConfig(Resolution Resolution, VideoCodec VideoCodec, FrameRate FrameRate, SceneDynamic SceneDynamic, AudioCodec AudioCodec, double Bitrate);
	}

	public enum Result
	{
		Unknown = 0,
		Success,
		ValidationError,
		CameraPresetNotFound
	}

	public class Validator : AbstractValidator<Query>
	{
		public Validator()
		{
			RuleFor(x => x.Id).NotEmpty();
		}
	}

	public class Handler : IRequestHandler<Query, Response>
	{
		private readonly IValidator<Query> _validator;
		private readonly IDbConnection _dbConnection;

		public Handler(IValidator<Query> validator,
					   IDbConnection dbConnection)
		{
			_validator = validator;
			_dbConnection = dbConnection;
		}

		public async Task<Response> Handle(Query request, CancellationToken cancellationToken)
		{
			if (!_validator.Validate(request).IsValid)
			{
				return new Response(Result.ValidationError);
			}

			var template = SqlQueryBuilder.Create()
				.Select(Db.Presets.Props.Id)
				.Select(Db.Presets.Props.Name)
				.Select(Db.Presets.Props.ArchiveStreamConfig)
				.Select(Db.Presets.Props.ViewStreamConfig)
				.Select(Db.Presets.Props.PublicStreamConfig)
				.Where(Db.Presets.Props.Id, ":Id", SqlOperator.Equals, new { request.Id })
				.Build(QueryType.Standard, Db.Presets.Table, RowSelection.AllRows);

			var preset = await _dbConnection.QuerySingleOrDefaultAsync<CameraPresetModel>(template.RawSql, template.Parameters);

			if (preset is null)
			{
				return new Response(Result.CameraPresetNotFound);
			}

			var archiveStreamConfig = JsonSerializer.Deserialize<StreamConfigModel>(preset.ArchiveStreamConfig);
			var viewStreamConfig = JsonSerializer.Deserialize<StreamConfigModel>(preset.ViewStreamConfig);
			var publicStreamConfig = JsonSerializer.Deserialize<StreamConfigModel>(preset.PublicStreamConfig);

			return new Response(preset.Id,
								preset.Name,
								ToStreamConfigResponse(archiveStreamConfig!),
								ToStreamConfigResponse(viewStreamConfig!),
								ToStreamConfigResponse(publicStreamConfig!));
		}

		private static Response.StreamConfig ToStreamConfigResponse(StreamConfigModel streamConfig)
		{
			var bitrate = BitrateCalculator.CalculateBitrate(streamConfig.Resolution, streamConfig.VideoCodec, streamConfig.FrameRate, streamConfig.SceneDynamic, streamConfig.AudioCodec);
			return new Response.StreamConfig(streamConfig.Resolution, streamConfig.VideoCodec, streamConfig.FrameRate, streamConfig.SceneDynamic, streamConfig.AudioCodec, bitrate);
		}
	}

	public record CameraPresetModel(Guid Id, string Name, string ArchiveStreamConfig, string ViewStreamConfig, string PublicStreamConfig);

	public record StreamConfigModel(Resolution Resolution, VideoCodec VideoCodec, FrameRate FrameRate, SceneDynamic SceneDynamic, AudioCodec AudioCodec);
}