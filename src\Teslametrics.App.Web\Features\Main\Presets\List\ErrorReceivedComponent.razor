@if (Result  is null || Result == GetCameraPresetListUseCase.Result.Success)
{
	return;
}
<MudStack Row>
	<MudIcon Icon="@Icons.Material.Filled.Error" Style="font-size: 8rem;" Color="Color.Error" />
	<MudStack AlignItems="AlignItems.Start" Justify="Justify.FlexStart" Class="mud-height-full mt-5">
		<MudText Typo="Typo.body1">Произошла ошибка при получении списка пресетов</MudText>
		<MudText Typo="Typo.subtitle1">Попробуйте снова позднее</MudText>
		<MudText Typo="Typo.subtitle2">Дата последнего обновления: <MudText Inline="true" Color="Color.Warning">@LastRefreshTime.ToLocalTime()</MudText></MudText>
		<MudButton OnClick="@RefreshAsync" Color="Color.Primary" Variant="Variant.Outlined">Обновить</MudButton>
	</MudStack>
</MudStack>