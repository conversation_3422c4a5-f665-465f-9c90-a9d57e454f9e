@page "/cameras/archive/{OrganizationId:guid}/{CameraId:guid}"
@attribute [AppAuthorize(AppPermissions.Main.Cameras.Read)]
@inherits InteractiveBaseComponent
<PageTitle>Multimonitor | Просмотр архива камеры</PageTitle>
<AuthorizeView Policy="@AppPermissions.Main.Cameras.Read.GetEnumPermissionString()"
			   Resource="new PolicyRequirementResource(OrganizationId, CameraId)"
			   Context="resourceContext">
	<Authorized>
		<CameraViewComponent CameraId="CameraId" />
	</Authorized>
	<NotAuthorized>
		<NotAuthorizedView />
	</NotAuthorized>
</AuthorizeView>
@code {
	[Parameter]
	public Guid OrganizationId { get; set; }

	[Parameter]
	public Guid CameraId { get; set; }
}