@rendermode InteractiveServer

@attribute [Route("/403")]
@inherits InteractiveBaseComponent
@inject NavigationManager NavigationManager
@inject AuthenticationStateProvider AuthStateProvider

<PageTitle>Multimonitor | Нет доступа</PageTitle>

<MudContainer MaxWidth="MaxWidth.Medium"
			  Class="mud-height-full">
	<MudStack Spacing="2"
			  Class="py-3 mud-width-full mud-height-full">
		<MudStack Spacing="4">
			<h1 data-h1="403"
				class="grid-first">403</h1>
			<p data-p="Доступ запрещён"
			   class="grid-first">Доступ запрещён</p>
			<p class="subtitle">К сожалению, но у вас недостаточно прав для просмотра данной страницы. Ошиблись?
				Обратитесь к администратору.</p>
			<MudBlazor.MudFab Label="Вернуться на главную страницу"
							  Color="MudBlazor.Color.Primary"
							  Href="/" />
		</MudStack>
	</MudStack>
</MudContainer>

@code {
	[CascadingParameter]
	private HttpContext HttpContext { get; set; } = default!;

	protected override async Task OnInitializedAsync()
	{
		var authState = await AuthStateProvider.GetAuthenticationStateAsync();
		if (!authState.User.Identity?.IsAuthenticated ?? true)
		{
			var returnUri = NavigationManager.ToBaseRelativePath(NavigationManager.Uri);
			if (!string.IsNullOrWhiteSpace(returnUri))
			{
				returnUri = $"?returnUrl={returnUri}";
			}
			if (HttpContext is not null && !HttpContext.Response.HasStarted)
			{
				HttpContext.Response.Redirect($"/account/login{returnUri}");
				return;
			}

			NavigationManager.NavigateTo($"/account/login{returnUri}", forceLoad: false);
			return;
		}

		await base.OnInitializedAsync();
	}
}
