﻿@inherits InteractiveBaseComponent
<MudDialog ActionsClass="mx-2"
		   ContentClass="pt-8 pb-12"
		   Visible="_isVisible"
		   VisibleChanged="VisibilityChanged"
		   Options="_dialogOptions">
	<TitleContent>
		<MudStack Row=true>
			<MudIcon Icon="@Icons.Material.Filled.Key"
					 Class="mt-1" />
			<MudStack Spacing="0">
				<MudText Typo="Typo.h6">Смена пароля текущего пользователя</MudText>
			</MudStack>
		</MudStack>
	</TitleContent>
	<DialogContent>
		<MudStack AlignItems="AlignItems.Center"
				  Justify="Justify.Center"
				  Spacing="0"
				  Class="mud-height-full">
			<MudIcon Icon="@Icons.Material.Outlined.Key"
					 Color="Color.Warning"
					 Style="font-size: 4rem;"
					 Class="mb-2" />
			<MudForm @ref="_formRef"
					 Model="_model"
					 Validation="Validator.ValidateValue"
					 @bind-IsValid="_isValid"
					 Class="flex-1 mud-width-full"
					 OverrideFieldValidation="true">
				<PasswordFieldComponent @bind-Value="@_model.Password"
										For="@(() => _model.Password)"
										Label="Текущий пароль"
										HelperText="Введите пароль, который Вы использовали для входа"
										Required="true"
										RequiredError="Введите пароль, который использовался для входа в систему"
										Immediate="true" />

				<PasswordFieldComponent Value="@_model.NewPassword"
										ValueChanged="OnPwdChange"
										For="@(() => _model.NewPassword)"
										Label="Пароль"
										Required="true"
										RequiredError="Поле должно быть заполнено"
										HelperText="Введите новый пароль"
										Immediate="true"
										@ref="pwdFieldRef" />

				<PasswordFieldComponent Value="@_model.NewPasswordConfirmation"
										ValueChanged="OnPwdConfirmChange"
										For="@(() => _model.NewPasswordConfirmation)"
										Label="Подтверждение пароля"
										RequiredError="Поле должно быть заполнено"
										HelperText="Повторите введёный ранее пароль"
										Required="true"
										Immediate="true"
										@ref="pwdConfirmFieldRef" />
			</MudForm>
		</MudStack>
	</DialogContent>
	<DialogActions>
		<MudButton OnClick="CancelAsync">Отменить</MudButton>
		<MudButton OnClick="SubmitAsync"
				   Color="Color.Warning"
				   Disabled="@(!_isValid)">Подвердить</MudButton>
	</DialogActions>
</MudDialog>