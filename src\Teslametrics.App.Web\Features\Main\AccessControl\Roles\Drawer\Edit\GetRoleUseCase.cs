using Dapper;
using FluentValidation;
using MediatR;
using System.Data;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.Shared;
using Teslametrics.App.Web.Domain.AccessControl;
using Teslametrics.App.Web.Services.Persistence;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Roles.Drawer.Edit;

public static class GetRoleUseCase
{
    public record Query(Guid Id) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public Guid Id { get; init; }

        public string Name { get; init; }

        public bool IsAdmin { get; init; }

        public List<ResourcePermission> Permissions { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(Guid id, string name, bool isAdmin, List<ResourcePermission> permissions)
        {
            Id = id;
            Name = name;
            IsAdmin = isAdmin;
            Permissions = permissions;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Result = result;

            Id = Guid.Empty;
            Name = string.Empty;
            Permissions = [];
        }
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        RoleNotFound
    }

    public class Validator : AbstractValidator<Query>
    {
        public Validator()
        {
            RuleFor(q => q.Id).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Query, Response>
    {
        private readonly IValidator<Query> _validator;
        private readonly IDbConnection _dbConnection;

        public Handler(IValidator<Query> validator,
                       IDbConnection dbConnection)
        {
            _validator = validator;
            _dbConnection = dbConnection;
        }

        public async Task<Response> Handle(Query request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            var template = SqlQueryBuilder.Create()
                .Select(Db.Roles.Props.Id)
                .Select(Db.Roles.Props.Name)
                .Select(Db.Roles.Props.IsAdmin)
                .Select(Db.RolePermissions.Props.Permission)
                .Select(Db.RolePermissions.Props.ResourceId)
                .LeftJoin(Db.RolePermissions.Table, Db.RolePermissions.Props.RoleId, Db.Roles.Props.Id, SqlOperator.Equals)
                .Where(Db.Roles.Props.Id, ":Id", SqlOperator.Equals, new { request.Id })
                .Build(QueryType.Standard, Db.Roles.Table, RowSelection.AllRows);

            var roles = await _dbConnection.QueryAsync<RoleModel, PermissionModel, RoleModel>(template.RawSql, (role, permission) =>
            {
                if (permission is not null)
                {
                    role.Permissions.Add(permission);
                }

                return role;
            },
            template.Parameters,
            splitOn: "Permission");

            if (!roles.Any())
            {
                return new Response(Result.RoleNotFound);
            }

            var result = roles.GroupBy(r => r.Id)
                .Select(g =>
                {
                    var groupedRole = g.First();
                    groupedRole.Permissions = g.SelectMany(r => r.Permissions).Distinct().ToList();
                    return groupedRole;
                });

            var role = result.Single();

            return new Response(role.Id,
                                role.Name,
                                role.IsAdmin,
                                role.Permissions.Select(p => new ResourcePermission(new ResourceId(p.ResourceId), new Permission(p.Permission))).ToList());
        }
    }

    public record RoleModel(Guid Id, string Name, bool IsAdmin)
    {
        public List<PermissionModel> Permissions { get; set; } = [];
    }

    public record PermissionModel(string Permission, Guid ResourceId);
}