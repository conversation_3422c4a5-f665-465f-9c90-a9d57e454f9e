
const methodNames_ = {
	error: 'ShowError'
};

function _formatErrorMessage(error) {
	if (error instanceof Error) {
		return error.message || 'Неизвестная ошибка';
	}
	return error?.toString() || 'Неизвестная ошибка';
}

async function isClipboardAvailable() {
	return navigator.clipboard != null;
}

async function copyToClipboard(text) {
	// Поддержка современных браузеров
	if (navigator.clipboard) {
		try {
			await navigator.clipboard.writeText(text);
			return true;
		} catch (err) {
			await sendErrorAsync(err);
			return false;
		}
	}

	// Fallback для старых браузеров
	const textarea = document.createElement("textarea");
	textarea.value = text;
	textarea.style.position = "fixed";
	document.body.appendChild(textarea);
	textarea.select();
	try {
		document.execCommand("copy");
		return true;
	} catch (err) {
		await sendErrorAsync(err);
		return false;
	} finally {
		document.body.removeChild(textarea);
	}
}

async function sendErrorAsync(blazorObjectReference, error) {
	try {
		if (blazorObjectReference == null) {
			console.error('Blazor object reference is null!');
			return;
		}
		const errorMessage = _formatErrorMessage(error);
		await blazorObjectReference.invokeMethodAsync(methodNames_.error, errorMessage);
	} catch (err) {
		console.error('Failed to send error to Blazor:', err);
	}
}

export {
	isClipboardAvailable,
	copyToClipboard,
	sendErrorAsync
};