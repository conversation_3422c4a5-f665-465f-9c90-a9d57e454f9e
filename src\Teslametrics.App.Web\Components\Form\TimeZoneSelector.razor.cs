using Microsoft.AspNetCore.Components;

namespace Teslametrics.App.Web.Components.Form;

public partial class TimeZoneSelector
{
	private readonly TimeSpan[] _availableTimeZones = new[]
	{
			TimeSpan.FromHours(-12),
			TimeSpan.FromHours(-11),
			TimeSpan.FromHours(-10),
			TimeSpan.FromHours(-9),
			TimeSpan.FromHours(-8),
			TimeSpan.FromHours(-7),
			TimeSpan.FromHours(-6),
			TimeSpan.FromHours(-5),
			TimeSpan.FromHours(-4),
			TimeSpan.FromHours(-3),
			TimeSpan.FromHours(-2),
			TimeSpan.FromHours(-1),
			TimeSpan.Zero,
			TimeSpan.FromHours(1),
			TimeSpan.FromHours(2),
			TimeSpan.FromHours(3),
			TimeSpan.FromHours(4),
			TimeSpan.FromHours(5),
			TimeSpan.FromHours(6),
			TimeSpan.FromHours(7),
			TimeSpan.FromHours(8),
			TimeSpan.FromHours(9),
			TimeSpan.FromHours(10),
			TimeSpan.FromHours(11),
			TimeSpan.FromHours(12)
	};

	[Parameter]
	public string Label { get; set; } = string.Empty;

	[Parameter]
	[EditorRequired]
	public TimeSpan TimeZone { get; set; }

	[Parameter]
	public Action<TimeSpan> TimeZoneChanged { get; set; } = default!;

	[Parameter]
	public string HelperText { get; set; } = string.Empty;

	[Parameter]
	public bool ReadOnly { get; set; } = false;
}
