using System.Reactive.Linq;
using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Domain.Incidents.Events;
using Teslametrics.App.Web.Exceptions;
using Teslametrics.App.Web.Services.DomainEventBus;
using Teslametrics.App.Web.Shared;

namespace Teslametrics.App.Web.Features.Main.Incidents.IncidentsList;

public static class SubscribeIncidentListUseCase
{
    public record Request(IObserver<UpdatedEvent> Observer,
                          Guid UserId,
                          DateTimeOffset DateFrom,
                          DateTimeOffset DateTo,
                          Guid? CityId,
                          Guid? BuildingId,
                          Guid? FloorId,
                          Guid? RoomId,
                          Guid? FridgeId,
                          IncidentType? IncidentType,
                          bool? IsResolved) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public IDisposable? Subscription { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(IDisposable subscription)
        {
            Subscription = subscription;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Subscription = null;
            Result = result;
        }
    }

    // События, на которые можно подписаться
    public record UpdatedEvent;

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError
    }

    public class Validator : AbstractValidator<Request>
    {
        public Validator()
        {
            RuleFor(r => r.Observer).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Request, Response>
    {
        private readonly IValidator<Request> _validator;
        private readonly IDomainEventBus _domainEventBus;

        public Handler(IValidator<Request> validator,
                       IDomainEventBus domainEventBus)
        {
            _validator = validator;
            _domainEventBus = domainEventBus;
        }

        public async Task<Response> Handle(Request request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            var eventStream = await _domainEventBus.GetEventStreamAsync();

            // Подписываемся на события, связанные с инцидентами
            var subscription = eventStream
                .Where(e => e switch
                {
                    IncidentCreatedEvent @event => @event.CreatedAt >= request.DateFrom && @event.CreatedAt <= request.DateTo &&
                        (request.CityId is null || @event.CityId == request.CityId) &&
                        (request.BuildingId is null || @event.BuildingId == request.BuildingId) &&
                        (request.FloorId is null || @event.FloorId == request.FloorId) &&
                        (request.RoomId is null || @event.RoomId == request.RoomId) &&
                        (request.FridgeId is null || @event.DeviceId == request.FridgeId) &&
                        (request.IncidentType is null || @event.IncidentType == request.IncidentType) &&
                        (request.IsResolved is null || !request.IsResolved.Value),
                    IncidentResolvedEvent @event => @event.CreatedAt >= request.DateFrom && @event.CreatedAt <= request.DateTo &&
                        (request.CityId is null || @event.CityId == request.CityId) &&
                        (request.BuildingId is null || @event.BuildingId == request.BuildingId) &&
                        (request.FloorId is null || @event.FloorId == request.FloorId) &&
                        (request.RoomId is null || @event.RoomId == request.RoomId) &&
                        (request.FridgeId is null || @event.DeviceId == request.FridgeId) &&
                        (request.IncidentType is null || @event.IncidentType == request.IncidentType),
                    IncidentReadedEvent @event => @event.UserId == request.UserId,
                    _ => false
                })
                .Select(e => e switch
                {
                    IncidentCreatedEvent @event => new UpdatedEvent(),
                    IncidentResolvedEvent @event => new UpdatedEvent(),
                    IncidentReadedEvent @event => new UpdatedEvent(),
                    _ => throw new AppException("Invalid event type")
                })
                .Subscribe(request.Observer);

            return new Response(subscription);
        }
    }
}