﻿@inherits InteractiveBaseComponent
<div class="d_contents">
    <MudStack Spacing="5"
              Row="true"
              Class="relative">
        @if (IsLoading && _response is not null && _response.IsSuccess)
        {
            <MudProgressLinear Class="progress absolute" />
        }
        @if (IsLoading && (_response is null || !_response.IsSuccess))
        {
            <IncidentsCardSkeleton />
            <IncidentsCardSkeleton />
            <IncidentsCardSkeleton />
        }
        @if (_response is not null && _response.IsSuccess)
        {
            <MudPaper Elevation="0"
                      Outlined="true"
                      Class="mud-width-full pa-6 d-flex flex-column gap-6">
                <MudText Typo="Typo.subtitle1"
                         Class="card_title">Происшествия с холодильниками</MudText>
                <MudStack Spacing="4">
                    <CounterComponent Count="@_response.FridgeIncidents!.Incidents"
                                      TotalCount="@_response.FridgeIncidents!.Total" />
                    <MudProgressLinear Color="Color.Primary"
                                       Size="Size.Small"
                                       Value="_response.FridgeIncidents!.Incidents"
                                       Min="0"
                                       Max="_response.FridgeIncidents!.Total" />
                    <MudStack Spacing="1">
                        <MudText Class="dotted primary">@_response.FridgeIncidents!.Incidents холодильника с происшествием</MudText>
                        <MudText Typo="Typo.body2"
                                 Class="dotted">@(_response.FridgeIncidents!.Total - _response.FridgeIncidents.Incidents) холодильников в норме
                        </MudText>
                    </MudStack>
                </MudStack>
            </MudPaper>
            <MudPaper Elevation="0"
                      Outlined="true"
                      Class="mud-width-full pa-6 d-flex flex-column gap-6">
                <MudText Typo="Typo.subtitle1"
                         Class="card_title">Камеры не на связи</MudText>
                <MudStack Spacing="4">
                    <CounterComponent Count="_response.CameraIncidents!.Incidents"
                                      TotalCount="@_response.CameraIncidents!.Total" />
                    <MudProgressLinear Color="Color.Primary"
                                       Size="Size.Small"
                                       Value="_response.CameraIncidents!.Incidents"
                                       Min="0"
                                       Max="_response.CameraIncidents!.Total" />
                    <MudStack Spacing="1">
                        <MudText Class="dotted primary">@_response.CameraIncidents!.Incidents камеры не на связи</MudText>
                        <MudText Typo="Typo.body2"
                                 Class="dotted">@(_response.CameraIncidents!.Total - _response.CameraIncidents.Incidents) камеры на связи
                        </MudText>
                    </MudStack>
                </MudStack>
            </MudPaper>
            <MudPaper Elevation="0"
                      Outlined="true"
                      Class="mud-width-full pa-6 d-flex flex-column gap-6">
                <MudStack Row="true"
                          Justify="Justify.SpaceBetween">
                    <MudText Typo="Typo.subtitle1"
                             Class="card_title">Происшествия</MudText>
                    <MudText Typo="Typo.subtitle2"
                             Class="incident_time">24ч</MudText>
                </MudStack>
                <MudStack Spacing="4">
                    <CounterComponent Count="@_response.TotalIncidents!.Current"
                                      TotalCount="@_response.TotalIncidents!.Total" />
                    <MudProgressLinear Color="Color.Primary"
                                       Size="Size.Small"
                                       Value="@_response.TotalIncidents.Current"
                                       Min="0"
                                       Max="@_response.TotalIncidents!.Total" />
                    <MudStack Spacing="1">
                        <MudText Class="dotted primary">@_response.TotalIncidents.Current не устранено</MudText>
                        <MudText Typo="Typo.body2"
                                 Class="dotted">@_response.TotalIncidents!.Total устранено</MudText>
                    </MudStack>
                </MudStack>
            </MudPaper>
        }
    </MudStack>
</div>