﻿@inherits InteractiveBaseComponent
<MudDialog @bind-Visible="_isVisible"
           ActionsClass="mx-2"
           ContentClass="mb-12"
           Options="_dialogOptions">
    <DialogContent>
        <MudStack Row=true>
            <MudIcon Icon="@Icons.Material.Filled.DeleteForever"
                     Class="mt-1" />
            <MudStack Spacing="0">
                <MudText Typo="Typo.h6">Удаление ссылки публичного доступа</MudText>
                @if (IsLoading)
                {
                    <MudSkeleton Width="30%"
                                 Height="42px" />
                }
                @if (!IsLoading && _camera is null)
                {
                    <MudText Typo="Typo.subtitle2">Ссылка публичного доступа не найдена</MudText>
                }
                @if (!IsLoading && _camera is not null)
                {
                    <MudText Typo="Typo.subtitle2">@_camera?.AccessName, привязанная к камере @_camera?.CameraName</MudText>
                }
            </MudStack>
            <MudSpacer />
            <div>
                @if (!_subscribing && (_subscriptionResult is null || !_subscriptionResult.IsSuccess))
                {
                    <MudTooltip Arrow="true"
                                Placement="Placement.Start"
                                Text="Ошибка подписки на события">
                        <MudIconButton OnClick="SubscribeAsync"
                                       Icon="@Icons.Material.Filled.ErrorOutline"
                                       Color="Color.Error" />
                    </MudTooltip>
                    <MudIconButton OnClick="RefreshAsync"
                                   Icon="@Icons.Material.Filled.Refresh"
                                   Color="Color.Primary" />
                }
                <MudIconButton OnClick="Cancel"
                               Icon="@Icons.Material.Outlined.Close" />
            </div>
        </MudStack>
        @if (IsLoading)
        {
            <MudSkeleton Width="30%" />
            <MudSkeleton Width="30%" />
            <MudSkeleton Width="30%" />
            <MudSkeleton Width="30%" />
            <MudSkeleton Width="30%" />
        }
        @if (!IsLoading && _camera is null)
        {
            <MudStack AlignItems="AlignItems.Center"
                      Justify="Justify.Center"
                      Spacing="0"
                      Class="mud-height-full">
                <MudIcon Icon="@Icons.Material.Outlined.WarningAmber"
                         Color="Color.Warning"
                         Style="font-size: 8rem;"
                         Class="mb-2" />
                <MudText Typo="Typo.subtitle1"
                         Color="Color.Warning">Ошибка получения данных</MudText>
                <MudText Typo="Typo.body2">Не удалось получить даные о публичном доступе или камере.</MudText>
                <MudButton OnClick="RefreshAsync">Повторить попытку</MudButton>
            </MudStack>
        }
        @if (!IsLoading && _camera is not null)
        {
            <MudStack AlignItems="AlignItems.Center"
                      Justify="Justify.Center"
                      Spacing="0"
                      Class="mud-height-full">
                <MudIcon Icon="@Icons.Material.Outlined.WarningAmber"
                         Color="Color.Warning"
                         Style="font-size: 8rem;"
                         Class="mb-2" />
                <MudText Typo="Typo.subtitle1"
                         Color="Color.Warning">Удаление публичного доступа!</MudText>
                <MudText Typo="Typo.body1">Вы уверены, что вы хотите удалить публичный доступ <b>@_camera?.AccessName</b>?
                </MudText>
                <MudText Typo="Typo.body2">Данное изменение необратимо.</MudText>
            </MudStack>
        }
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="Cancel">Отменить</MudButton>
        @if (IsLoading)
        {
            <MudSkeleton Width="160px"
                         Height="52px" />
        }
        @if (!IsLoading && _camera is not null)
        {
            <MudButton OnClick="SubmitAsync"
                       Color="Color.Warning">Подвердить</MudButton>
        }
    </DialogActions>
</MudDialog>