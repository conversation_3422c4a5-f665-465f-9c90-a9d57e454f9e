using System.Data;
using System.Text;
using Dapper;
using Teslametrics.Shared;
using Teslametrics.Core.Services.Persistence;
using Teslametrics.MediaServer.Orleans.Camera;

namespace Teslametrics.App.Web.Services;

//public class M3u8Generator
//{
//    private readonly IDbConnection _dbConnection;

//    public M3u8Generator(IDbConnection dbConnection, StreamCache streamCache)
//    {
//        _dbConnection = dbConnection;
//        _streamCache = streamCache;
//    }

//    public async Task<string> GetAsync(Guid cameraId, DateTimeOffset start)
//    {
//        var segmentCount = 5;

//        var table = $"{Db.StreamSegments.Table}_{cameraId.ToString("N")}";

//        var template = SqlQueryBuilder.Create()
//            .Select(Db.StreamSegments.Columns.SegmentIndex)
//            .Select(Db.StreamSegments.Columns.FileName)
//            .Select(Db.StreamSegments.Columns.StartTime)
//            .Select(Db.StreamSegments.Columns.EndTime)
//            .Where(Db.StreamSegments.Columns.EndTime, ":Start", SqlOperator.GreaterThan, new { start })
//            .OrderBy(Db.StreamSegments.Columns.StartTime, OrderDirection.Ascending)
//            .Build(QueryType.Standard, $"{table}", RowSelection.AllRows);

//        var sql = template.RawSql + $"LIMIT {segmentCount}";

//        var segments = await _dbConnection.QueryAsync<StreamSegment>(sql, template.Parameters);

//        return BuildM3u8File(segments);
//    }

//    public Task<string> GetAsync(Guid cameraId, StreamType streamType)
//    {
//        var segments = _streamCache.GetTwoLastIndices(cameraId, streamType);

//        return Task.FromResult(BuildM3u8File(segments.Select(s => new StreamSegment(s.Index, $"{s.Index}.ts", s.StartTime, s.EndTime))));
//    }

//    public string BuildM3u8File(IEnumerable<StreamSegment> segments)
//    {
//        var targetDuration = segments.Any() ? (int)Math.Ceiling(segments.Max(s => (s.EndTime - s.StartTime).TotalSeconds)) : 2;

//        var stringBuilder = new StringBuilder();
//        stringBuilder.AppendLine("#EXTM3U");
//        stringBuilder.AppendLine("#EXT-X-VERSION:3");
//        stringBuilder.AppendLine($"#EXT-X-TARGETDURATION:{targetDuration}");

//        if (segments.Any())
//        {
//            stringBuilder.AppendLine($"#EXT-X-MEDIA-SEQUENCE:{segments.Min(s => s.SegmentIndex - 1)}");

//            stringBuilder.AppendLine("#EXT-X-DISCONTINUITY"); // первые сегменты не включены в список

//            StreamSegment? prevSegment = null;
//            foreach (var segment in segments.OrderBy(s => s.StartTime))
//            {
//                // https://datatracker.ietf.org/doc/html/rfc8216#section-4.3.2.3
//                if (prevSegment != null) // Был пропуск сегмента. Указываем разрыв для плеера
//                {
//                    var currentTime = segment.StartTime;
//                    var prevTime = prevSegment.StartTime;

//                    // Если разрыв больше 2 секунд (с небольшим запасом для погрешности)
//                    if ((currentTime - prevTime).TotalSeconds > 2.1)
//                    {
//                        stringBuilder.AppendLine("#EXT-X-DISCONTINUITY");
//                    }
//                }

//                var duration = (segment.EndTime - segment.StartTime).TotalSeconds.ToString().Replace(",", ".");

//                stringBuilder.AppendLine($"#EXTINF:{duration},");
//                stringBuilder.AppendLine(segment.FileName);
//                prevSegment = segment;
//            }
//        }

//        return stringBuilder.ToString();
//    }

//    public record StreamSegment(long SegmentIndex, string FileName, DateTimeOffset StartTime, DateTimeOffset EndTime);
//}