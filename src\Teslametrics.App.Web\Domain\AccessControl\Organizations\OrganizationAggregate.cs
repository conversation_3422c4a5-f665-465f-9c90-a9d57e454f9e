﻿using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Domain.AccessControl.Organizations.Events;
using Teslametrics.App.Web.Shared;

namespace Teslametrics.App.Web.Domain.AccessControl.Organizations;

public class OrganizationAggregate : IEntity
{
    private readonly List<OrganizationRoleEntity> _roles;
    private readonly List<CameraQuotaEntity> _cameraQuotas;

    public Guid Id { get; private set; }

    public Guid OwnerId { get; private set; }

    public string Name { get; private set; }

    public IReadOnlyCollection<OrganizationRoleEntity> Roles => _roles;

    public IReadOnlyCollection<CameraQuotaEntity> CameraQuotas => _cameraQuotas;

    public bool IsSystem => Id == SystemConsts.RootOrganizationId;

    public static (OrganizationAggregate Organization, List<object> events) Create(Guid id, Guid ownerId, string name)
    {
        return (new OrganizationAggregate(id, ownerId, name), [new OrganizationCreatedEvent(id)]);
    }

    private OrganizationAggregate()
    {
        Name = string.Empty;
        _roles = [];
        _cameraQuotas = [];
    }

    private OrganizationAggregate(Guid id, Guid ownerId, string name)
    {
        Id = id;
        OwnerId = ownerId;
        Name = name;
        _roles = [];
        _cameraQuotas = [];
    }

    public List<object> Update(string name)
    {
        if (Name != name)
        {
            Name = name;
            return [new OrganizationUpdatedEvent(Id)];
        }

        return [];
    }

    public List<object> ChangeOwner(Guid ownerId)
    {
        if (OwnerId != ownerId)
        {
            OwnerId = ownerId;
            return [new OrganizationOwnerChangedEvent(Id)];
        }

        return [];
    }

    public (Guid Id, List<object> Events) CreateRole(Guid id, string name, bool isAdmin, IEnumerable<(Guid Id, ResourcePermission ResourcePermission)> permissions)
    {
        var role = OrganizationRoleEntity.Create(id, name, isAdmin, permissions.Select(p => (p.Id, Id, p.ResourcePermission)));

        _roles.Add(role);
        return (role.Id, [new RoleCreatedEvent(id)]);
    }

    public List<object> UpdateRole(Guid roleId, string name, bool isAdmin, IEnumerable<(Guid Id, ResourcePermission ResourcePermission)> permissions)
    {
        var role = Roles.SingleOrDefault(r => r.Id == roleId) ?? throw new Exception("Role not found");

        var isUpdated = role.Update(name, isAdmin, permissions.Select(p => (p.Id, Id, p.ResourcePermission)));

        return isUpdated ? [new RoleUpdatedEvent(roleId)] : [];
    }

    public List<object> DeleteRole(Guid roleId)
    {
        var role = _roles.SingleOrDefault(r => r.Id == roleId);
        if (role is null)
        {
            return [];
        }

        _roles.Remove(role);

        return [new RoleDeletedEvent(roleId)];
    }

    public List<object> AddCameraQuotas(IEnumerable<(Guid QuotaId, Guid? PresetId, string Name, int Limit, int RetentionPeriodDays, int StorageLimitMb)> quotas)
    {
        bool isUpdated = false;

        foreach (var quota in quotas)
        {
            _cameraQuotas.Add(CameraQuotaEntity.Create(quota.QuotaId, Id, quota.PresetId, quota.Name, quota.Limit, quota.RetentionPeriodDays, quota.StorageLimitMb));
            isUpdated = true;
        }

        return isUpdated ? [new CameraQuotaCreatedEvent(Id)] : [];
    }

    public List<object> RemoveCameraQuota(Guid quotaId)
    {
        var quota = _cameraQuotas.SingleOrDefault(p => p.Id == quotaId);

        if (quota is not null)
        {
            _cameraQuotas.Remove(quota);
            return [new CameraQuotaDeletedEvent(quotaId, Id)];
        }

        return [];
    }

    public List<object> UpdateCameraQuota(Guid quotaId, string name, int limit, int retentionPeriodDays, int storageLimitMb)
    {
        var isUpdated = _cameraQuotas.Single(p => p.Id == quotaId).Update(name, limit, retentionPeriodDays, storageLimitMb);
        return isUpdated ? [new CameraQuotaUpdatedEvent(quotaId, Id)] : [];
    }
}