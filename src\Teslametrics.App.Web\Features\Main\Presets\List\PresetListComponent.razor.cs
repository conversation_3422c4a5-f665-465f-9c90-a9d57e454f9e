using Microsoft.AspNetCore.Components;
using MudBlazor;
using System.Reactive;
using Teslametrics.App.Web.Events.Presets;
using Teslametrics.App.Web.Exceptions;
using Teslametrics.App.Web.Extensions;

namespace Teslametrics.App.Web.Features.Main.Presets.List;

public partial class PresetListComponent
{
	private GetCameraPresetListUseCase.Response? _response;
	private SubscribeCameraPresetListUseCase.Response? _subResponse;
	private DateTime _lastRefreshTime = DateTime.Now;
	private string _searchString = string.Empty;
	protected int CurrentPage => Offset / Limit;

	#region Parameters
	[Parameter]
	public int Offset { get; set; } = 0;
	[Parameter]
	public EventCallback<int> OffsetChanged { get; set; }

	[Parameter]
	public int Limit { get; set; } = 25;
	[Parameter]
	public EventCallback<int> LimitChanged { get; set; }
	#endregion [Parameter]

	protected override async Task OnInitializedAsync()
	{
		await LoadDataAsync();
		await SubscribeAsync();
	}

	private async Task LoadDataAsync()
	{
		try
		{
			if (IsLoading) return;

			await SetLoadingAsync();

			var userId = await GetCurrentUserIdAsync() ?? throw new NotAuthorizedException();
			_response = await ScopeFactory.MediatorSend(new GetCameraPresetListUseCase.Query(userId, Offset, Limit, _searchString));
			_lastRefreshTime = DateTime.Now;
			switch (_response.Result)
			{
				case GetCameraPresetListUseCase.Result.Success:
					break;

				case GetCameraPresetListUseCase.Result.ValidationError:
					Snackbar.Add("Ошибка валидации при получении списка пресетов", MudBlazor.Severity.Error);
					break;

				case GetCameraPresetListUseCase.Result.Unknown:
				default:
					Snackbar.Add("Не удалось получить список пресетов из-за непредвиденной ошибки:" + _response.Result.ToString(), MudBlazor.Severity.Error);
					break;
			}
		}
		catch (Exception ex)
		{
			Logger.LogError(ex, ex.Message);
		}
		finally
		{
			await SetLoadingAsync(false);
		}
	}

	private async Task SubscribeAsync()
	{
		_subResponse = await ScopeFactory.MediatorSend(new SubscribeCameraPresetListUseCase.Request(Observer.Create<SubscribeCameraPresetListUseCase.UpdatedEvent>(OnAppEventHandler, OnError)));
		switch (_subResponse.Result)
		{
			case SubscribeCameraPresetListUseCase.Result.Success:
				CompositeDisposable.Add(_subResponse.Subscription!);
				break;

			case SubscribeCameraPresetListUseCase.Result.ValidationError:
				Snackbar.Add("Ошибка валидации при подписке на события", Severity.Error);
				break;

			case SubscribeCameraPresetListUseCase.Result.Unknown:
			default:
				Snackbar.Add("Не удалось получить подписку на обновления из-за непредвиденной ошибки:" + _subResponse.Result.ToString(), Severity.Error);
				break;
		}
	}

	#region [Actions]
	private Task RefreshAsync() => LoadDataAsync();

	private void Create() => EventSystem.Publish(new PresetCreateEto());
	private void Select(DataGridRowClickEventArgs<GetCameraPresetListUseCase.Response.Item> args) => EventSystem.Publish(new PresetSelectEto(args.Item.Id));
	private void Select(Guid id) => EventSystem.Publish(new PresetSelectEto(id));
	private void Edit(Guid id) => EventSystem.Publish(new PresetEditEto(id));
	private void Delete(Guid id) => EventSystem.Publish(new PresetDeleteEto(id));
	#endregion [Actions]

	#region [Event Handlers]
	private Task OnSearchAsync(string text)
	{
		_searchString = text;
		return LoadDataAsync();
	}

	protected async void OnAppEventHandler(SubscribeCameraPresetListUseCase.UpdatedEvent appEvent)
	{
		await RefreshAsync();
		await UpdateViewAsync();
	}

	private Task RowsPerPageChanged(int limit)
	{
		Limit = limit;
		if (LimitChanged.HasDelegate)
		{
			return LimitChanged.InvokeAsync(limit);
		}
		return Task.CompletedTask;
	}

	protected void OnError(Exception exc)
	{
		Logger.LogError(exc, exc.Message);
		Snackbar.Add("Ошибка валидации при подписке на события", MudBlazor.Severity.Error);
	}
	#endregion [Event Handlers]
}