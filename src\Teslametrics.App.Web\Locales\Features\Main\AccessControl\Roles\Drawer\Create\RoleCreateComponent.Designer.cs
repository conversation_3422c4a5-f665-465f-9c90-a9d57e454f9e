﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Teslametrics.App.Web.Locales.Features.Main.AccessControl.Roles.Drawer.Create {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class RoleCreateComponent {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal RoleCreateComponent() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Teslametrics.App.Web.Locales.Features.Main.AccessControl.Roles.Drawer.Create.Role" +
                            "CreateComponent", typeof(RoleCreateComponent).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Access rights.
        /// </summary>
        public static string AccessRights {
            get {
                return ResourceManager.GetString("AccessRights", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User access rights are of two types:.
        /// </summary>
        public static string AccessRightsDescription {
            get {
                return ResourceManager.GetString("AccessRightsDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Administrator role.
        /// </summary>
        public static string AdminRole {
            get {
                return ResourceManager.GetString("AdminRole", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Camera presets.
        /// </summary>
        public static string CameraPresets {
            get {
                return ResourceManager.GetString("CameraPresets", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Camera public access.
        /// </summary>
        public static string CameraPublicAccess {
            get {
                return ResourceManager.GetString("CameraPublicAccess", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Camera quotas.
        /// </summary>
        public static string CameraQuotas {
            get {
                return ResourceManager.GetString("CameraQuotas", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cameras.
        /// </summary>
        public static string Cameras {
            get {
                return ResourceManager.GetString("Cameras", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Common.
        /// </summary>
        public static string CommonRights {
            get {
                return ResourceManager.GetString("CommonRights", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Folders.
        /// </summary>
        public static string Folders {
            get {
                return ResourceManager.GetString("Folders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Global.
        /// </summary>
        public static string GlobalRights {
            get {
                return ResourceManager.GetString("GlobalRights", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 1. Common - to all organization resources.
        /// </summary>
        public static string GlobalRightsOrg {
            get {
                return ResourceManager.GetString("GlobalRightsOrg", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 1. Global - to all resources of all organizations.
        /// </summary>
        public static string GlobalRightsRoot {
            get {
                return ResourceManager.GetString("GlobalRightsRoot", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Organizations.
        /// </summary>
        public static string Organizations {
            get {
                return ResourceManager.GetString("Organizations", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resource.
        /// </summary>
        public static string ResourceRights {
            get {
                return ResourceManager.GetString("ResourceRights", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 2. Resource - to a specific object in the organization.
        /// </summary>
        public static string ResourceRightsOrg {
            get {
                return ResourceManager.GetString("ResourceRightsOrg", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 2. Resource - to a specific object in a certain area.
        /// </summary>
        public static string ResourceRightsRoot {
            get {
                return ResourceManager.GetString("ResourceRightsRoot", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Role description.
        /// </summary>
        public static string RoleDescription {
            get {
                return ResourceManager.GetString("RoleDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Role name.
        /// </summary>
        public static string RoleName {
            get {
                return ResourceManager.GetString("RoleName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Specify the role name in Latin characters.
        /// </summary>
        public static string RoleNameHelper {
            get {
                return ResourceManager.GetString("RoleNameHelper", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This field is required.
        /// </summary>
        public static string RoleNameRequired {
            get {
                return ResourceManager.GetString("RoleNameRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Roles.
        /// </summary>
        public static string Roles {
            get {
                return ResourceManager.GetString("Roles", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save.
        /// </summary>
        public static string Save {
            get {
                return ResourceManager.GetString("Save", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Access control.
        /// </summary>
        public static string TabAccessControl {
            get {
                return ResourceManager.GetString("TabAccessControl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cameras.
        /// </summary>
        public static string TabCameras {
            get {
                return ResourceManager.GetString("TabCameras", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Presets.
        /// </summary>
        public static string TabPresets {
            get {
                return ResourceManager.GetString("TabPresets", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Camera public access.
        /// </summary>
        public static string TabPublicAccess {
            get {
                return ResourceManager.GetString("TabPublicAccess", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quota.
        /// </summary>
        public static string TabQuotas {
            get {
                return ResourceManager.GetString("TabQuotas", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Views.
        /// </summary>
        public static string TabViews {
            get {
                return ResourceManager.GetString("TabViews", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create role.
        /// </summary>
        public static string Title {
            get {
                return ResourceManager.GetString("Title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Users.
        /// </summary>
        public static string Users {
            get {
                return ResourceManager.GetString("Users", resourceCulture);
            }
        }
    }
}
