using System.Data;
using BenchmarkDotNet.Attributes;
using BenchmarkDotNet.Running;
using Dapper;
using Npgsql;
using Teslametrics.App.Web;
using Teslametrics.App.Web.Data.Sql;
using Teslametrics.App.Web.Services;
using Teslametrics.App.Web.Services.Persistence;
using static Teslametrics.App.Web.Features.Main.Cameras.StreamsController;

public class Program
{
    private static void Main(string[] args)
    {
        BenchmarkRunner.Run<StreamSegments>();
    }
}

public class StreamSegments
{
    private readonly string _tableNamePostgres = $"{Db.StreamSegments.Table}_test";
    private readonly string _tableNameTimescale = $"{Db.StreamSegments.Table}_timescale_test";
    private readonly IDbConnection _dbConnection;

    public StreamSegments()
    {
        SqlMapper.AddTypeHandler(typeof(DateTimeOffset), new DateTimeOffsetTypeHandler());
        DefaultTypeMap.MatchNamesWithUnderscores = true;
        _dbConnection = new NpgsqlConnection("Host=localhost;Port=5432;Database=teslametrics;Username=********;Password=********");
    }

    // [GlobalSetup(Targets = new[] { nameof(InsertPostgres) })]
    // public void PostgresSetup()
    // {
    //     _dbConnection.Open();

    //     _dbConnection.Execute($"CREATE TABLE IF NOT EXISTS {_tableNamePostgres} " +
    //                             $"({Db.StreamSegments.Columns.SegmentIndex} BIGSERIAL PRIMARY KEY, " +
    //                             $"{Db.StreamSegments.Columns.FileName} VARCHAR(255), " +
    //                             $"{Db.StreamSegments.Columns.StartTime} TIMESTAMP(6), " +
    //                             $"{Db.StreamSegments.Columns.EndTime} TIMESTAMP(6))");

    //     _dbConnection.Close();
    // }

    // [GlobalSetup(Targets = new[] { nameof(InsertTimescale) })]
    // public void TimescaleSetup()
    // {
    //     _dbConnection.Open();

    //     _dbConnection.Execute($"CREATE TABLE IF NOT EXISTS {_tableNameTimescale} " +
    //                             $"({Db.StreamSegments.Columns.FileName} VARCHAR(255), " +
    //                             $"{Db.StreamSegments.Columns.StartTime} TIMESTAMP(6) PRIMARY KEY, " +
    //                             $"{Db.StreamSegments.Columns.EndTime} TIMESTAMP(6))");

    //     //_dbConnection.Execute($"CREATE INDEX ix_{Db.StreamSegments.Columns.StartTime} ON {_tableNameTimescale} ({Db.StreamSegments.Columns.StartTime} ASC)");

    //     _dbConnection.Execute($"SELECT create_hypertable('{_tableNameTimescale}', '{Db.StreamSegments.Columns.StartTime}')");

    //     _dbConnection.Close();
    // }

    // [Benchmark]
    // public int InsertTimescale()
    // {
    //     _dbConnection.Open();

    //     var sql = $"INSERT INTO {_tableNameTimescale} ({Db.StreamSegments.Columns.FileName}, {Db.StreamSegments.Columns.StartTime}, {Db.StreamSegments.Columns.EndTime}) " +
    //               $"VALUES (:FileName, :Start, :End);";

    //     var result = _dbConnection.Execute(sql, new { FileName = "testfile.ts", Start = DateTime.UtcNow, End = DateTime.UtcNow.AddSeconds(2) });

    //     _dbConnection.Close();

    //     return result;
    // }

    [Benchmark]
    public List<TimelineRange> SelectTimescale()
    {
        _dbConnection.Open();

        var start = DateTimeOffset.Parse("2025-02-06 01:42:57.000Z");
        var end = start.AddHours(2);

		var template = SqlQueryBuilder.Create()
			.Select(Db.StreamSegments.Columns.StartTime)
			.Select(Db.StreamSegments.Columns.EndTime)
			.Where(Db.StreamSegments.Columns.StartTime, ":End", SqlOperator.LessThan, new { end })
			.Where(Db.StreamSegments.Columns.EndTime, ":Start", SqlOperator.GreaterThan, new { start })
			.Build(QueryType.Standard, _tableNameTimescale, RowSelection.AllRows);

		var ranges = _dbConnection.Query<TimelineRange>(template.RawSql, template.Parameters).ToList();

        _dbConnection.Close();

        return ranges;
    }

    // [Benchmark(Baseline = true)]
    // public int InsertPostgres()
    // {
    //     _dbConnection.Open();

    //     var sql = $"INSERT INTO {_tableNamePostgres} ({Db.StreamSegments.Columns.FileName}, {Db.StreamSegments.Columns.StartTime}, {Db.StreamSegments.Columns.EndTime}) " +
    //               $"VALUES (:FileName, :Start, :End)";

    //     var result = _dbConnection.Execute(sql, new { FileName = "testfile.ts", Start = DateTime.UtcNow, End = DateTime.UtcNow.AddSeconds(2) });

    //     _dbConnection.Close();

    //     return result;
    // }

    [Benchmark(Baseline = true)]
    public List<TimelineRange> SelectPostgres()
    {
        _dbConnection.Open();

        var start = DateTimeOffset.Parse("2025-02-06 01:42:57.000Z");
        var end = start.AddHours(2);

		var template = SqlQueryBuilder.Create()
			.Select(Db.StreamSegments.Columns.StartTime)
			.Select(Db.StreamSegments.Columns.EndTime)
			.Where(Db.StreamSegments.Columns.StartTime, ":End", SqlOperator.LessThan, new { end })
			.Where(Db.StreamSegments.Columns.EndTime, ":Start", SqlOperator.GreaterThan, new { start })
			.Build(QueryType.Standard, _tableNamePostgres, RowSelection.AllRows);

		var ranges = _dbConnection.Query<TimelineRange>(template.RawSql, template.Parameters).ToList();

        _dbConnection.Close();

        return ranges;
    }

    // [GlobalCleanup(Targets = new[] { nameof(SelectTimescale) })]
    // public void TimescaleCleanup()
    // {
    //     _dbConnection.Open();

    //     _dbConnection.Execute($"DROP TABLE {_tableNameTimescale}");

    //     _dbConnection.Close();
    // }

    // [GlobalCleanup(Targets = new[] { nameof(SelectPostgres) })]
    // public void PostgresCleanup()
    // {
    //     _dbConnection.Open();

    //     _dbConnection.Execute($"DROP TABLE {_tableNamePostgres}");

    //     _dbConnection.Close();
    // }
}

public record TimescaleStreamSegment(string FileName, DateTime StartTime, DateTime EndTime);
