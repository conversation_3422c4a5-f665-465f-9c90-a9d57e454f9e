using Microsoft.AspNetCore.Components;

namespace Teslametrics.App.Web.Features.Main.IncidentsDashboard;

public partial class IncidentsDashboardPage
{
    [Inject] private NavigationManager NavigationManager { get; set; } = null!;

    [Parameter, SupplyParameterFromQuery(Name = "start")]
    public DateTime? Start { get; set; }

    [Parameter, SupplyParameterFromQuery(Name = "end")]
    public DateTime? End { get; set; }

    [Parameter, SupplyParameterFromQuery(Name = "cityId")]
    public Guid? CityId { get; set; }

    [Parameter, SupplyParameterFromQuery(Name = "buildingId")]
    public Guid? BuildingId { get; set; }

    [Parameter, SupplyParameterFromQuery(Name = "floorId")]
    public Guid? FloorId { get; set; }

    [Parameter, SupplyParameterFromQuery(Name = "roomId")]
    public Guid? RoomId { get; set; }

    [Parameter, SupplyParameterFromQuery(Name = "deviceId")]
    public Guid? DeviceId { get; set; }

    public MudBlazor.DateRange DateRange
    {
        get => new(Start ?? DateTime.Today, End ?? DateTime.Today.AddDays(1).AddMilliseconds(-1));
        set
        {
            Start = value.Start;
            End = value.End;
            UpdateQueryString();
        }
    }

    protected override void OnInitialized()
    {
        if (Start == null)
        {
            Start = DateTime.Today;
        }
        if (End == null)
        {
            End = DateTime.Today.AddDays(1).AddMilliseconds(-1);
        }
        base.OnInitialized();
    }

    // protected override async Task OnParametersSetAsync()
    // {
    //     await LoadChartsAsync();
    //     await base.OnParametersSetAsync();
    // }

    // protected override async Task OnAfterRenderAsync(bool firstRender)
    // {
    //     if (firstRender)
    //     {
    //         _jsModule = await JSRuntime.InvokeAsync<IJSObjectReference>(
    //             "import", "./Features/Main/IncidentsDashboard/IncidentsDashboardPage.razor.js");
    //     }
    // }

    // private async Task LoadChartsAsync()
    // {
    //     if (_jsModule == null) return;

    //     try
    //     {
    //         await SetLoadingAsync(true);

    //         var humidityData = await FetchHumidityDataAsync();

    //         await _jsModule.InvokeVoidAsync("initHumidityChart", humidityData);
    //     }
    //     catch (Exception ex)
    //     {
    //         Logger.LogError(ex, "Error loading charts data");
    //         Snackbar.Add("Не удалось загрузить данные для графиков", MudBlazor.Severity.Error);
    //     }
    //     finally
    //     {
    //         await SetLoadingAsync(false);
    //     }
    // }

    // private async Task<HumidityChartData> FetchHumidityDataAsync()
    // {
    //     return new HumidityChartData
    //     {
    //         Categories = new[] { "Пн", "Вт", "Ср", "Чт", "Пт", "Сб", "Вс" },
    //         Series = new[]
    //         {
    //             new SeriesData { Name = "За период", Data = new[] { -80.0, 90, 30, 10, -10, -40, 40 } },
    //             new SeriesData { Name = "За прошлый период", Data = new[] { -20.0, 30, 20, 10, 5, -10, 0 } },
    //             new SeriesData { Name = "Референсное значение", Data = new[] { 50.0, 50, 50, 50, 50, 50, 50 } }
    //         }
    //     };
    // }

    private void UpdateQueryString()
    {
        var query = new Dictionary<string, object?>
        {
            ["start"] = Start?.ToString("o"),
            ["end"] = End?.ToString("o"),
            ["cityId"] = CityId,
            ["buildingId"] = BuildingId,
            ["floorId"] = FloorId,
            ["roomId"] = RoomId,
            ["deviceId"] = DeviceId
        };

        var newUri = NavigationManager.GetUriWithQueryParameters(query);
        NavigationManager.NavigateTo(newUri);
    }
}
