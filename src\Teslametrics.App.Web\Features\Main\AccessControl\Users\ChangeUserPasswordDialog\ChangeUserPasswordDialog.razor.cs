using FluentValidation;
using MudBlazor;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Components.Form;
using Teslametrics.App.Web.Eto.Users;
using Teslametrics.App.Web.Extensions;
using Severity = MudBlazor.Severity;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Users.ChangeUserPasswordDialog;

public partial class ChangeUserPasswordDialog
{
	private class PwdChange
	{
		public string Password { get; set; } = string.Empty;
		public string PasswordConfirm { get; set; } = string.Empty;
	}
	private class PasswordChangeValidator : BaseFluentValidator<PwdChange>
	{
		public PasswordChangeValidator()
		{
			RuleFor(model => model.Password)
				.NotEmpty().WithMessage("Поле должно быть заполнено");

			When(model => !string.IsNullOrWhiteSpace(model.Password) && !string.IsNullOrWhiteSpace(model.PasswordConfirm), () =>
			{
				RuleFor(model => model.Password).Equal(model => model.PasswordConfirm).WithMessage("Пароли не совпадают");
				RuleFor(model => model.PasswordConfirm).Equal(model => model.Password).WithMessage("Пароли не совпадают");
			});

			RuleFor(model => model.PasswordConfirm)
				.NotEmpty().WithMessage("Поле должно быть заполнено");
		}
	}

	private DialogOptions _dialogOptions = new() { CloseOnEscapeKey = true, FullWidth = true, MaxWidth = MaxWidth.Medium, CloseButton = true };
	private bool _isValid;
	private bool _isVisible;
	private PasswordChangeValidator Validator = new();
	private GetUserUseCase.Response? _user;
	private PwdChange _model = new();

	private MudForm? _formRef;
	private PasswordFieldComponent? pwdFieldRef;
	private PasswordFieldComponent? pwdConfirmFieldRef;
	protected override void OnInitialized()
	{
		CompositeDisposable.Add(EventSystem.Subscribe<ChangeUserPasswordEto>(ShowHandler));

		base.OnInitialized();
	}

	private async Task FetchAsync(Guid id)
	{
		if (IsLoading) return;

		try
		{
			await SetLoadingAsync();
			var response = await ScopeFactory.MediatorSend(new GetUserUseCase.Query(id));
			if (response.IsSuccess)
			{
				_user = response;
				return;
			}

			switch (response.Result)
			{
				case GetUserUseCase.Result.ValidationError:
					Snackbar.Add("Ошибка валидации данных", Severity.Error);
					break;
				case GetUserUseCase.Result.UserNotFound:
					Snackbar.Add("Пользователь не найден", Severity.Error);
					break;
				case GetUserUseCase.Result.Unknown:
				default:
					Snackbar.Add("Не удалось получить пользователя из-за непредвиденной ошибки:" + response.Result.ToString(), Severity.Error);
					break;
			}
		}
		catch (Exception ex)
		{
			Snackbar.Add("Не удалось получить выбранного пользователя. Повторите попытку", Severity.Error);
			Logger.LogError(ex, ex.Message);
		}
		finally
		{
			await SetLoadingAsync(false);
		}
	}

	#region [Actions]
	private void Cancel()
	{
		VisibilityChanged(false);
		StateHasChanged();
	}

	private async Task SubmitAsync()
	{
		if (IsLoading || _user is null) return;

		try
		{
			await SetLoadingAsync();
			var result = await ScopeFactory.MediatorSend(new ChangeUserPasswordUseCase.Command(_user.Id, _model.Password));
			if (result.IsSuccess)
			{
				Snackbar.Add("Пользователю успешно изменён пароль", Severity.Success);
				Cancel();
				return;
			}

			switch (result.Result)
			{
				case ChangeUserPasswordUseCase.Result.ValidationError:
					Snackbar.Add("Ошибка валидации данных", Severity.Error);
					break;
				case ChangeUserPasswordUseCase.Result.UserNotFound:
					Snackbar.Add("Пользователь не найден", Severity.Error);
					break;
				case ChangeUserPasswordUseCase.Result.Unknown:
				default:
					Snackbar.Add("Не удалось получить пользователя из-за непредвиденной ошибки:" + result.Result.ToString(), Severity.Error);
					break;
			}
		}
		catch (Exception ex)
		{
			Snackbar.Add("Произошла ошибка при удалении пользователя", Severity.Error);
			Logger.LogError(ex, ex.Message);
		}
		finally
		{
			await SetLoadingAsync(false);
		}
	}
	#endregion

	#region [Event Handlers]
	private async Task ShowHandler(ChangeUserPasswordEto eto)
	{
		if (_user is not null && eto.UserId == _user.Id)
		{
			return;
		}

		_model.Password = string.Empty;
		_model.PasswordConfirm = string.Empty;
		await UpdateViewAsync(() =>
		{
			_isVisible = true;
		});

		await FetchAsync(eto.UserId);
	}
	private async Task OnPwdChange(string pwd)
	{
		_model.Password = pwd;
		if (!string.IsNullOrWhiteSpace(_model.PasswordConfirm))
		{
			pwdConfirmFieldRef?.ResetValidation();
		}
		if (_model.PasswordConfirm == _model.Password && _formRef is not null)
		{
			await _formRef.Validate();
		}
	}

	private async Task OnPwdConfirmChange(string pwd)
	{
		_model.PasswordConfirm = pwd;
		if (!string.IsNullOrWhiteSpace(_model.Password))
		{
			pwdFieldRef?.ResetValidation();
		}
		if (_model.PasswordConfirm == _model.Password && _formRef is not null)
		{
			await _formRef.Validate();
		}
	}

	private void VisibilityChanged(bool isVisible)
	{
		if (!isVisible)
		{
			_model.Password = string.Empty;
			_model.PasswordConfirm = string.Empty;
			_user = null;
		}
		_isVisible = isVisible;
	}
	#endregion
}
