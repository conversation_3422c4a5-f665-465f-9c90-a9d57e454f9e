﻿@inherits InteractiveBaseComponent
<MudAutocomplete T="GetRoomListUseCase.Response.Item"
                 SearchFunc="@SearchAsync"
                 ToStringFunc="@(e => e == null ? null : e.Name)"
                 Value="@_selected"
                 ValueChanged="@ValueChanged"
                 Label="Помещение"
                 Clearable="true"
                 Margin="Margin.Dense"
                 ResetValueOnEmptyText="true"
                 Variant="Variant.Outlined"
                 Disabled="BuildingId is null || CityId is null || FloorId is null" />

@code {
    private GetRoomListUseCase.Response.Item? _selected;

    [Parameter]
    public Guid? CityId { get; set; }

    [Parameter]
    public Guid? BuildingId { get; set; }

    [Parameter]
    public Guid? FloorId { get; set; }

    [Parameter]
    public Guid? RoomId { get; set; }
    [Parameter]
    public EventCallback<Guid?> RoomIdChanged { get; set; }

    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();
        if (_selected?.Id != RoomId)
        {
            _selected = null;
        }
    }

    private async Task ValueChanged(GetRoomListUseCase.Response.Item? item)
    {
        _selected = item;
        await RoomIdChanged.InvokeAsync(item?.Id);
    }

    private async Task<IEnumerable<GetRoomListUseCase.Response.Item>> SearchAsync(string value, CancellationToken token)
    {
        if (!CityId.HasValue || !BuildingId.HasValue || !FloorId.HasValue) return [];

        GetRoomListUseCase.Response? response = null;
        try
        {
            response = await ScopeFactory.MediatorSend(new GetRoomListUseCase.Query(CityId!.Value, BuildingId!.Value, FloorId!.Value, value), token);
        }
        catch (TaskCanceledException)
        {
            // Search was canceled, just return empty result
            return Enumerable.Empty<GetRoomListUseCase.Response.Item>();
        }
        catch (Exception exc)
        {
            Logger.LogError(exc, "Error searching cities");
            Snackbar.Add("Не удалось получить список этажей из-за непредвиденной ошибки.", MudBlazor.Severity.Error);
            return Enumerable.Empty<GetRoomListUseCase.Response.Item>();
        }

        if (response.Result == GetRoomListUseCase.Result.Success)
            return response.Items;

        if (response.Result == GetRoomListUseCase.Result.ValidationError)
            Snackbar.Add("Ошибка валидации при получении списка городов", MudBlazor.Severity.Error);
        else
            Snackbar.Add("Не удалось получить список городов из-за непредвиденной ошибки.", MudBlazor.Severity.Error);

        return Enumerable.Empty<GetRoomListUseCase.Response.Item>();
    }
}