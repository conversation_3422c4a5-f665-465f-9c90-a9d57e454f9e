﻿@if (Archive is not null)
{
	<MudText Class="ma-2">
		Параметры потока архива
	</MudText>
	<MudSimpleTable Hover="@true"
					Dense="true"
					Bordered="@true"
					Elevation="0"
					Striped="@true">
		<tbody>
			<tr>
				<td>Разрешение:</td>
				<td>
					@Archive.Resolution.GetName()
				</td>
			</tr>
			<tr>
				<td>Частота кадров</td>
				<td>@Archive.FrameRate.GetName()</td>
			</tr>
			<tr>
				<td>Кодек видео</td>
				<td>@Archive.VideoCodec.GetName()</td>
			</tr>
			<tr>
				<td>Кодек аудио</td>
				<td>@Archive.AudioCodec.GetName()</td>
			</tr>
			<tr>
				<td>Ожидаемая динамика сцены</td>
				<td>@Archive.SceneDynamic.GetName()</td>
			</tr>
		</tbody>
	</MudSimpleTable>
}

@if (View is not null)
{
	<MudText Class="ma-2">
		Параметры потока видов
	</MudText>
	<MudSimpleTable Hover="@true"
					Dense="true"
					Bordered="@true"
					Elevation="0"
					Striped="@true">
		<tbody>
			<tr>
				<td>Разрешение:</td>
				<td>
					@View.Resolution.GetName()
				</td>
			</tr>
			<tr>
				<td>Частота кадров</td>
				<td>@View.FrameRate.GetName()</td>
			</tr>
			<tr>
				<td>Кодек видео</td>
				<td>@View.VideoCodec.GetName()</td>
			</tr>
			<tr>
				<td>Кодек аудио</td>
				<td>@View.AudioCodec.GetName()</td>
			</tr>
			<tr>
				<td>Ожидаемая динамика сцены</td>
				<td>@View.SceneDynamic.GetName()</td>
			</tr>
		</tbody>
	</MudSimpleTable>
}

@if (Public is not null)
{
	<MudText Class="ma-2">
		Параметры потока публичного доступа
	</MudText>
	<MudSimpleTable Hover="@true"
					Dense="true"
					Bordered="@true"
					Elevation="0"
					Striped="@true">
		<tbody>
			<tr>
				<td>Разрешение:</td>
				<td>
					@Public.Resolution.GetName()
				</td>
			</tr>
			<tr>
				<td>Частота кадров</td>
				<td>@Public.FrameRate.GetName()</td>
			</tr>
			<tr>
				<td>Кодек видео</td>
				<td>@Public.VideoCodec.GetName()</td>
			</tr>
			<tr>
				<td>Кодек аудио</td>
				<td>@Public.AudioCodec.GetName()</td>
			</tr>
			<tr>
				<td>Ожидаемая динамика сцены</td>
				<td>@Public.SceneDynamic.GetName()</td>
			</tr>
		</tbody>
	</MudSimpleTable>
}

@code
{
	public record Config(Resolution Resolution, VideoCodec VideoCodec, FrameRate FrameRate, SceneDynamic SceneDynamic, AudioCodec AudioCodec);

	[Parameter]
	[EditorRequired]
	public Config Archive { get; set; } = null!;

	[Parameter]
	[EditorRequired]
	public Config View { get; set; } = null!;

	[Parameter]
	[EditorRequired]
	public Config Public { get; set; } = null!;
}
