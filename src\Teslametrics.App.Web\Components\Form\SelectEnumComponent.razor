﻿@typeparam TEnum where TEnum : IConvertible
<MudSelect T="TEnum" Label="@Label" AnchorOrigin="Origin.BottomCenter" Value="SelectedValue" ValueChanged="SelectedValueChanged" Disabled="@Disabled"
	RequiredError="@RequiredError" ReadOnly="@ReadOnly" HelperText="@HelperText" Variant="@Variant" Required="Required" Clearable="@Clearable">
	@foreach (TEnum selectOption in _values)
	{
		<MudSelectItem T="TEnum" Value="selectOption" @key="selectOption">@selectOption.GetName()</MudSelectItem>
	}
</MudSelect>