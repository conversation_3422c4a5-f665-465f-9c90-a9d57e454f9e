using FluentValidation;
using MediatR;
using System.Reactive.Linq;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.Core.Domain.AccessControl.Organizations.Events;
using Teslametrics.Core.Domain.AccessControl.Users.Events;
using Teslametrics.Core.Services.DomainEventBus;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Users.List;

public static class SubscribeUserListUseCase
{
    public record Request(IObserver<UpdatedEvent> Observer, Guid OrganizationId) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public IDisposable? Subscription { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(IDisposable subscription)
        {
            Subscription = subscription;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Subscription = null;
            Result = result;
        }
    }

    public record UpdatedEvent;

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError
    }

    public class Validator : AbstractValidator<Request>
    {
        public Validator()
        {
            RuleFor(r => r.Observer).NotEmpty();
            RuleFor(r => r.OrganizationId).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Request, Response>
    {
        private readonly IDomainEventBus _domainEventBus;
        private readonly IValidator<Request> _validator;

        public Handler(IDomainEventBus domainEventBus,
                       IValidator<Request> validator)
        {
            _domainEventBus = domainEventBus;
            _validator = validator;
        }

        public async Task<Response> Handle(Request request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            var eventStream = await _domainEventBus.GetEventStreamAsync();

            var subscription = eventStream
                .Where(e => e switch
                {
                    UserCreatedEvent @event => @event.OrganizationId == request.OrganizationId,
                    UserUpdatedEvent @event => @event.OrganizationIds.Contains(request.OrganizationId),
                    UserDeletedEvent @event => @event.OrganizationId == request.OrganizationId,
                    OrganizationOwnerChangedEvent @event => @event.OrganizationId == request.OrganizationId,
                    _ => false
                })
                .Select(e => new UpdatedEvent())
                .Subscribe(request.Observer);

            return new Response(subscription);
        }
    }
}