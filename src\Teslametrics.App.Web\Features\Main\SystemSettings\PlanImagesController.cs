using System.Data;
using Dapper;
using Microsoft.AspNetCore.Mvc;
using Teslametrics.Shared;
using Teslametrics.Core.Services.Persistence;

namespace Teslametrics.App.Web.Features.Main.SystemSettings;

[ApiController]
[Route("planimages")]
public class PlanImagesController : ControllerBase
{
    private readonly ILogger<PlanImagesController> _logger;
    private readonly IDbConnection _dbConnection;

    public PlanImagesController(ILogger<PlanImagesController> logger,
                                IDbConnection dbConnection)
    {
        _logger = logger;
        _dbConnection = dbConnection;
    }

    [HttpGet]
    [Route("{floorId}")]
    public async Task<IActionResult> GetFloorImage(Guid floorId)
    {
        try
        {
            var template = SqlQueryBuilder.Create()
                .Select(Db.PlanImages.Props.Image)
                .Select(Db.PlanImages.Props.ContentType)
                .Where(Db.PlanImages.Props.FloorId, ":FloorId", SqlOperator.Equals, new { floorId })
                .Build(QueryType.Standard, Db.PlanImages.Table, RowSelection.AllRows);

            var model = await _dbConnection.QuerySingleOrDefaultAsync<PlanImageModel>(template.RawSql, template.Parameters);

            if (model is null)
            {
                return NotFound();
            }

            return File(model.Image, model.ContentType);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, ex.Message);
            return Problem();
        }
    }

    public record PlanImageModel(byte[] Image, string ContentType);
}