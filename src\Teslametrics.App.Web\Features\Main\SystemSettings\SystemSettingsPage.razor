@using Blazor.Diagrams.Components.Widgets
@using Blazor.Diagrams.Components
@using Teslametrics.App.Web.Features.Main.SystemSettings.SensorForm
@using Teslametrics.App.Web.Features.Main.SystemSettings.SensorList
@attribute [Authorize]
@attribute [Route("/system-settings")]
@inherits InteractiveBaseComponent
<PageTitle>Multimonitor | Настройки системы</PageTitle>
<!-- ... -->
<link href="_content/Z.Blazor.Diagrams/style.min.css"
	  rel="stylesheet" />
<!-- ... -->
<script src="_content/Z.Blazor.Diagrams/script.min.js"></script>
<div class="d_contents">
	<MudContainer MaxWidth="MaxWidth.False"
				  Class="mud-height-full pa-4 ">
		<MudStack Spacing="2"
				  Class="mud-height-full">
			<MudPaper Elevation="0"
					  Outlined="true">
				<MudStack Row="true"
						  Class="pa-4">
					<MudLink OnClick="() => OnSelectedCityChanged(null)">Список городов</MudLink>
					@if (_selectedCity is not null)
					{
						<MudText Color="Color.Surface"
								 Class="divider">/</MudText>
						<MudLink OnClick="() => OnSelectedBuildingChanged(null)"
								 Disabled="_selectedBuilding is null">
							@(string.IsNullOrEmpty(_selectedCity.Name) ? "Нет названия города" : _selectedCity.Name)</MudLink>
					}
					@if (_selectedBuilding is not null)
					{
						<MudText Color="Color.Surface"
								 Class="divider">/</MudText>
						<MudLink OnClick="() => OnSelectedFloorChanged(null)"
								 Disabled="_selectedFloor is null">
							@(string.IsNullOrEmpty(_selectedBuilding.Address) ? "Нет адреса" : _selectedBuilding.Address)</MudLink>
					}
					@if (_selectedFloor is not null)
					{
						<MudText Color="Color.Surface"
								 Class="divider">/</MudText>
						<MudLink OnClick="() => OnSelectedRoomChanged(null)"
								 Disabled="_selectedRoom is null">@_selectedFloor.Number</MudLink>
					}
					@if (_selectedRoom is not null)
					{
						<MudText Color="Color.Surface"
								 Class="divider">/</MudText>
						<MudLink OnClick="() => OnSelectedFreezerChanged(null)"
								 Disabled="_selectedFreezer is null">
							@(string.IsNullOrEmpty(_selectedRoom.Name) ? "Нет наименования комнаты" : _selectedRoom.Name)</MudLink>
					}
					@if (_selectedFreezer is not null)
					{
						<MudText Color="Color.Surface"
								 Class="divider">/</MudText>
						<MudLink Disabled>@_selectedFreezer.Name</MudLink>
					}
				</MudStack>
			</MudPaper>
			<MudPaper Elevation="0"
					  Class="py-4"
					  Outlined="true">
				@if (_currentSelection is null)
				{
					<MudList T="CityModel"
							 SelectedValue="_selectedCity"
							 SelectedValueChanged="OnSelectedCityChanged">
						<MudListSubheader Class="d-flex">
							Список городов
							<MudSpacer />
							<MudButton StartIcon="@Icons.Material.Outlined.Add"
									   OnClick="AddCity"
									   Color="Color.Primary">
								Добавить город
							</MudButton>
						</MudListSubheader>
						@foreach (var item in _page.Cities)
						{
							<MudListItem Value="@item"
										 Text="@(string.IsNullOrEmpty(item.Name) ? "Нет названия города" : item.Name)"
										 Icon="@Icons.Material.Filled.Room"
										 @key="@item">
								<ChildContent>
									<div class="d-flex flex-row align-center gap-3 ">
										<div class="d-flex flex-column px-6 py-2">
											<MudText Typo="Typo.body1">
												Город: @item.Name
											</MudText>
										</div>
										<MudSpacer />
										<MudIconButton Icon="@Icons.Material.Outlined.Delete"
													   Color="Color.Warning"
													   OnClick="() => RemoveCity(item)" />
									</div>
								</ChildContent>
							</MudListItem>
						}
						@if (_page.Cities.Count == 0)
						{
							<div class="pa-4">
								<NoItemsFoundComponent HasItems=false />
							</div>
						}
					</MudList>
				}
				else
				{
					@if (_currentSelection is CityModel city)
					{
						<div class="pa-4">
							<MudTextField T="string"
										  @bind-Value="@city.Name"
										  Immediate="true"
										  Label="Наименование города" />
						</div>

						<MudList T="BuildingModel"
								 SelectedValue="_selectedBuilding"
								 SelectedValueChanged="OnSelectedBuildingChanged">
							<MudListSubheader Class="d-flex">
								Список зданий в городе
								<MudSpacer />
								<MudButton StartIcon="@Icons.Material.Outlined.Add"
										   OnClick="AddBuilding"
										   Disabled="string.IsNullOrWhiteSpace(city.Name)"
										   Color="Color.Primary">
									Добавить здание
								</MudButton>
							</MudListSubheader>
							@foreach (var item in city.Buildings)
							{
								<MudListItem Value="@item"
											 Icon="@Icons.Material.Filled.Room"
											 @key="@item">
									<ChildContent>
										<div class="d-flex flex-row align-center gap-3 ">
											<div class="d-flex flex-column px-6 py-2">
												<MudText Typo="Typo.body1">
													Здание: @(string.IsNullOrEmpty(item.Address) ? "Не указан адрес" : item.Address)
												</MudText>
											</div>
											<MudSpacer />
											<MudIconButton Icon="@Icons.Material.Outlined.Delete"
														   Color="Color.Warning"
														   OnClick="() => RemoveBuilding(item)" />
										</div>
									</ChildContent>
								</MudListItem>
							}
						</MudList>
						@if (city.Buildings.Count == 0)
						{
							<div class="pa-4">
								<NoItemsFoundComponent HasItems=false />
							</div>
						}
					}
					@if (_currentSelection is BuildingModel building)
					{
						<div class="pa-4">
							<MudTextField T="string"
										  @bind-Value="@building.Address"
										  Immediate="true"
										  Label="Адрес" />
						</div>

						<MudList T="FloorModel"
								 SelectedValue="_selectedFloor"
								 SelectedValueChanged="OnSelectedFloorChanged">
							<MudListSubheader Class="d-flex">
								Список этажей в здании
								<MudSpacer />
								<MudButton StartIcon="@Icons.Material.Outlined.Add"
										   OnClick="AddFloor"
										   Disabled="string.IsNullOrWhiteSpace(building.Address)"
										   Color="Color.Primary">
									Добавить этаж
								</MudButton>
							</MudListSubheader>
							@foreach (var item in building.Floors)
							{
								<MudListItem Value="@item"
											 Icon="@Icons.Material.Filled.Reorder"
											 @key="@item">
									<ChildContent>
										<div class="d-flex flex-row align-center gap-3 ">
											<div class="d-flex flex-column px-6 py-2">
												<MudText Typo="Typo.body1">
													Этаж: @item.Number.ToString()
												</MudText>
											</div>
											<MudSpacer />
											<MudIconButton Icon="@Icons.Material.Outlined.Delete"
														   Color="Color.Warning"
														   OnClick="() => RemoveFloor(item)" />
										</div>
									</ChildContent>
								</MudListItem>
							}
							@if (building.Floors.Count == 0)
							{
								<div class="pa-4">
									<NoItemsFoundComponent HasItems=false />
								</div>
							}
						</MudList>
					}
					@if (_currentSelection is FloorModel floor)
					{

						<div class="pa-4">
							<MudNumericField @bind-Value="@floor.Number"
											 Immediate="true"
											 Label="Этаж" />
						</div>

						<MudList T="RoomModel"
								 SelectedValue="_selectedRoom"
								 SelectedValueChanged="OnSelectedRoomChanged">
							<MudListSubheader Class="d-flex">
								Список комнат на этаже
								<MudSpacer />
								<MudButton StartIcon="@Icons.Material.Outlined.Add"
										   OnClick="AddRoom"
										   Disabled="_selectedBuilding!.Floors.Count(x => x.Number == floor.Number) > 1"
										   Color="Color.Primary">
									Добавить комнату
								</MudButton>
							</MudListSubheader>
							@foreach (var item in floor.Rooms)
							{
								<MudListItem Value="@item"
											 Icon="@Icons.Material.Filled.Room"
											 @key="@item">
									<ChildContent>
										<div class="d-flex flex-row align-center gap-3 ">
											<div class="d-flex flex-column px-6 py-2">
												<MudText Typo="Typo.body1">
													Комната: @item.Name
												</MudText>
											</div>
											<MudSpacer />
											<MudIconButton Icon="@Icons.Material.Outlined.Delete"
														   Color="Color.Warning"
														   OnClick="() => RemoveRoom(item)" />
										</div>
									</ChildContent>
								</MudListItem>
							}
							@if (floor.Rooms.Count == 0)
							{
								<div class="pa-4">
									<NoItemsFoundComponent HasItems=false />
								</div>
							}
						</MudList>
					}
					@if (_currentSelection is RoomModel room)
					{
						<div class="pa-4">
							<MudTextField @bind-Value="@room.Name"
										  Immediate="true"
										  Label="Наименование комнаты" />
							<MudStack Row="true">
								<MudAutocomplete T="CamModel"
												 AdornmentIcon="@Icons.Material.Filled.Search"
												 AdornmentColor="Color.Primary"
												 Adornment="Adornment.Start"
												 SearchFunc="@SearchAsync"
												 Label="Камера"
												 ToStringFunc="@(e => e == null ? null : e.Name)"
												 @bind-Value="_camera"
												 Clearable="true"
												 ResetValueOnEmptyText="true" />
								<MudButton StartIcon="@Icons.Material.Outlined.Add"
										   OnClick="AddCamera">Добавить камеру</MudButton>
							</MudStack>


							<MudList T="FreezerModel"
									 SelectedValue="_selectedFreezer"
									 SelectedValueChanged="OnSelectedFreezerChanged">
								<MudListSubheader Class="d-flex">
									Список холодильников в комнате
									<MudSpacer />
									<MudButton OnClick="AddFreezer">Добавить холодильник</MudButton>
								</MudListSubheader>
								@foreach (var freezer in room.Freezers)
								{
									<MudListItem Value="@freezer"
												 Icon="@TeslaIcons.Devices.Fridge"
												 @key="@freezer">
										<ChildContent>
											<div class="d-flex flex-row align-center gap-3 ">
												<div class="d-flex flex-column px-6 py-2">
													<MudText Typo="Typo.body1">
														Холодильник: @freezer.Name
													</MudText>
												</div>
												<MudSpacer />
												<MudIconButton Icon="@Icons.Material.Outlined.Delete"
															   Color="Color.Warning"
															   OnClick="() => RemoveFridge(freezer)" />
											</div>
										</ChildContent>
									</MudListItem>
								}
								@if (room.Freezers.Count == 0)
								{
									<div class="pa-4">
										<NoItemsFoundComponent HasItems=false />
									</div>
								}
							</MudList>
						</div>
					}

					@if (_currentSelection is FreezerModel freezer)
					{
						<MudStack Spacing="4"
								  Class="pa-4">
							<MudTextField @bind-Value="freezer.Name"
										  Immediate="true" />
							<div style="border: 1px solid var(--mud-palette-divider); border-radius: 4px;">
								<SensorListComponent Sensors="freezer.Sensors" />
								<SensorFormComponent Sensors="freezer.Sensors" />
							</div>
						</MudStack>
					}

					@if (_currentSelection is BuildingModel building1)
					{
						<MudPaper Class="ma-4"
								  Outlined="true">
							<YandexMaps @bind-Coordinates="@building1.Coordinates"
										Width="100%"
										ReadOnly="false"
										For="@(() => building1.Coordinates)"
										Class="ma-n4 rounded-b overflow-hidden"
										Height="400px" />
						</MudPaper>
					}

					@if (_selectedFloor is not null)
					{
						<div class="pa-4">
							<MudDivider Class="my-4" />
							<MudStack Row="true">
								<MudText Typo="Typo.h6">План этажа:</MudText>
								<MudSpacer />
								@if (_selectedFloor.Plan is null)
								{
									<MudFileUpload T="IBrowserFile"
												   FilesChanged="OnInputFileChanged"
												   Accept=".jpg,.jpeg,.png,.gif,.svg">
										<ActivatorContent>
											<MudButton Variant="Variant.Filled"
													   Color="Color.Primary"
													   StartIcon="@Icons.Material.Filled.CloudUpload">
												Загрузить изображение
											</MudButton>
										</ActivatorContent>
									</MudFileUpload>
								}
								else
								{
									<MudButton OnClick="DeleteImage"
											   Color="Color.Error"
											   StartIcon="@Icons.Material.Filled.Delete">
										Удалить изображение
									</MudButton>
								}
							</MudStack>
							<MudPaper style="min-height: 530px; display: grid; border: 1px solid var(--mud-palette-divider);"
									  Class="mt-4">
								@if (_currentSelection is RoomModel room)
								{
									<div class="diagram_buttons">
										@if (_polygonBehaivor.IsEditInProgress)
										{
											<MudText Typo="Typo.subtitle2">Нажмите на карте, чтобы установить точку зоны. Нажмите на точку дважды для её удаления.</MudText>
											<MudSpacer />
											<MudButton OnClick="EndRoomZoneEdit">Завершить редактирование</MudButton>
										}
										else
										{
											<MudSpacer />
											<MudButton OnClick="() => StartRoomZoneEdit(room)">Изменить зону комнаты</MudButton>
										}
									</div>
								}
								<CascadingValue Value="Diagram">
									<DiagramCanvas>
										<Widgets>
											<SelectionBoxWidget />
											<GridWidget Size="15"
														Mode="GridMode.Line"
														BackgroundColor="transparent" />
										</Widgets>
									</DiagramCanvas>
								</CascadingValue>
							</MudPaper>

							<style>
								div.grid {
									background-image: linear-gradient(var(--mud-palette-divider) 1px, transparent 1px), linear-gradient(90deg, var(--mud-palette-divider) 1px, transparent 1px) !important;
								}
							</style>
						</div>
					}
				}
			</MudPaper>
			<MudStack Row="true"
					  AlignItems="AlignItems.Center"
					  Justify="Justify.FlexEnd"
					  Class="py-2">
				<MudButton OnClick="SaveAsync">Сохранить</MudButton>
			</MudStack>
		</MudStack>
	</MudContainer>
</div>