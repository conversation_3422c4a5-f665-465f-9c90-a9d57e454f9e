using Teslametrics.App.Web.Abstractions;

namespace Teslametrics.App.Web.Domain.Notifications;

/// <summary>
/// Интерфейс репозитория для работы с уведомлениями об инцидентах
/// </summary>
public interface IIncidentNotificationRepository : IRepository<IncidentNotificationAggregate>
{
    /// <summary>
    /// Получает количество уведомлений для пользователя
    /// </summary>
    Task<int> GetNotificationCountForUserAsync(Guid userId,
                                               CancellationToken cancellationToken = default);

    /// <summary>
    /// Удаляет уведомление по идентификатору инцидента и идентификатору пользователя
    /// </summary>
    Task<bool> DeleteByIncidentIdAndUserIdAsync(Guid incidentId,
                                                Guid userId,
                                                CancellationToken cancellationToken = default);

    Task<List<Guid>> DeleteAllByUserIdAsync(Guid userId, CancellationToken cancellationToken = default);
}
