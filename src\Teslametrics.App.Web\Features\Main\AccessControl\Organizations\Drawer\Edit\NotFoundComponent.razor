﻿@if (IsFound)
{
	<div />
	<MudStack AlignItems="AlignItems.Center" Justify="Justify.Center" Class="mud-height-full">
		<MudIcon Icon="@Icons.Material.Filled.Error" Style="font-size: 8rem;" Color="Color.Error" />
		<MudText Typo="Typo.body1">Не удалось получить организацию</MudText>
		<MudText Typo="Typo.subtitle1">Попробуйте снова позднее</MudText>
	</MudStack>
}
@code {
	[Parameter]
	[EditorRequired]
	public bool IsFound { get; set; }
}