@inherits InteractiveBaseComponent
<MudDialog ActionsClass="mx-2"
		   ContentClass="mt-8 mb-12"
		   @bind-Visible="_isVisible">
	<TitleContent>
		<MudStack Row=true>
			<MudIcon Icon="@Icons.Material.Filled.Block"
					 Class="mt-1" />
			<MudText Typo="Typo.h6"
					 Inline>Учётная запись заблокирована!</MudText>
		</MudStack>
	</TitleContent>
	<DialogContent>
		<MudStack AlignItems="AlignItems.Center"
				  Justify="Justify.Center"
				  Spacing="0"
				  Class="mud-height-full">
			<MudIcon Icon="@Icons.Material.Filled.Block"
					 Color="Color.Error"
					 Style="font-size: 8rem;"
					 Class="mb-2" />
			<MudText Typo="Typo.subtitle1">Сожалеем, но Ваша учётная запись была <MudText Inline="true"
						 Color="Color.Error">заблокирована</MudText>
			</MudText>
			<MudText Typo="Typo.body1">Для разблокировки учётной записи обратитесь <b>к администратору</b></MudText>
		</MudStack>
	</DialogContent>
	<DialogActions>
		<MudSpacer />
		<MudButton OnClick="()  => _isVisible = false">Закрыть</MudButton>
	</DialogActions>
</MudDialog>