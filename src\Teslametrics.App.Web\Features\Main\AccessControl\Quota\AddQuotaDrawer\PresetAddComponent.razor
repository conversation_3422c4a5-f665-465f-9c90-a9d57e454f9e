@inherits InteractiveBaseComponent
<MudStack Row="true">
    <MudText>Список квот</MudText>
    <MudSpacer />
    <MudIconButton OnClick="AddNewQuota"
                   Icon="@Icons.Material.Outlined.Add" />
</MudStack>
<div class="px-1 py-4 mud-height-full gap-2 ">
    @foreach (var context in _items)
    {
        <MudPaper class="pa-4 relative">
            @if (_items.Count > 1)
            {
                <MudIconButton OnClick="() => RemoveQuota(context)"
                               Class="remove_quota"
                               Color="Color.Warning"
                               Icon="@Icons.Material.Outlined.Delete" />
            }
            <MudForm Spacing="4">
                <MudTextField @bind-Value="context.Name"
                              Variant="Variant.Text"
                              Label="Наименование" />

                <MudAutocomplete T="GetCameraPresetListUseCase.Response.Item"
                                 @bind-Value="context.Preset"
                                 Variant="Variant.Text"
                                 Label="Пресет"
                                 ToStringFunc="@(e => e == null ? null : e.Name)"
                                 SearchFunc="@SearchAsync"
                                 Clearable="true"
                                 ShowProgressIndicator="true" />

                <MudCheckBox T="bool"
                             Value="context.TotalQuota == -1"
                             ValueChanged="() => context.TotalQuota = context.TotalQuota != -1 ? -1 : 1"
                             Label="Количество камер неограничено"
                             LabelPlacement="@Placement.End"
                             Class="ml-n4" />
                @if (context.TotalQuota != -1)
                {
                    <MudNumericField T="int"
                                     @bind-Value="context.TotalQuota"
                                     For="() => context.TotalQuota"
                                     Min="-1"
                                     Validation="_validator.Validation"
                                     Immediate="true"
                                     RequiredError="Задайте значение" />
                }
                <MudNumericField T="int"
                                 @bind-Value="context.StorageLimitMb"
                                 For="@(() => context.StorageLimitMb)"
                                 Variant="Variant.Text"
                                 Min="1"
                                 Label="Расчётный объём хранимых записей, мб"
                                 HelperText="Объём записей должен варьироваться от 1 мегабайта" />

                <MudNumericField T="int"
                                 @bind-Value="context.RetentionPeriodDays"
                                 For="@(() => context.RetentionPeriodDays)"
                                 Label="Глубина хранения записей, дней"
                                 Clearable="true"
                                 Variant="Variant.Text"
                                 Min="1"
                                 Max="365"
                                 Immediate="true"
                                 HelperText="От 1 до 365 дней"
                                 RequiredError="Данное поле обязательно"
                                 Pattern="^([+,0-9.]+)"
                                 Required="true" />
            </MudForm>
        </MudPaper>
    }

    <MudStack Row="true"
              Class="mt-4">
        <MudSpacer />

        <MudButton OnClick="CancelAsync">Отменить</MudButton>
        <MudButton OnClick="SubmitAsync"
                   Disabled="@(!_isValid)"
                   Color="Color.Secondary"
                   Variant="Variant.Outlined">Сохранить</MudButton>
    </MudStack>
</div>