﻿.layout {
	display: grid;
    grid-template: "incident_boards incident_boards"   /* строка 1 – полноширокая */
                   "address map";      /* строка 2 – две колонки  */
	grid-template-rows: minmax(208px, auto) 1fr;
    grid-template-columns: 0.7fr 1fr;
    overflow: hidden;
}

.incident_boards {
    grid-area: incident_boards;
}

::deep .address {
    grid-area: address;
    overflow: auto;
}

::deep .map {
    grid-area: map;
}

::deep .card_title {
	color: rgba(92, 97, 102, 1);
}

@media screen and (max-width: 767px) {
    .layout {
        display: flex;
        grid-template: "incident_boards"
                       "address"
                       "map";
        overflow: auto;
        flex-direction: column;
	}

    ::deep .address {
        min-height: -webkit-fill-available;
        max-height: 800px;
        overflow: auto;
        scrollbar-gutter: stable;
    }
    
    ::deep .map {
        min-height: 600px;
        max-height: 600px;
        height: 600px;
    }
}