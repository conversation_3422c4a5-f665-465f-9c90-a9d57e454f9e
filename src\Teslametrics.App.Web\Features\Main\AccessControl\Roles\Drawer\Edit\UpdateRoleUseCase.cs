using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Domain;
using Teslametrics.App.Web.Domain.AccessControl;
using Teslametrics.App.Web.Domain.AccessControl.Organizations;
using Teslametrics.App.Web.Services.Outbox;
using Teslametrics.App.Web.Services.TransactionManager;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Roles.Drawer.Edit;

public static class UpdateRoleUseCase
{
    public record Command(Guid OrganizationId, Guid RoleId, string Name, bool IsAdmin, List<ResourcePermission> ResourcePermissions) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public Guid RoleId { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(Guid roleId)
        {
            RoleId = roleId;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            RoleId = Guid.Empty;
            Result = result;
        }
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        CannotUpdateSystemRole,
        RoleNotFound,
        InvalidPermission,
        OrganizationNotFound,
        RoleNameAlreadyExists
    }

    public class Validator : AbstractValidator<Command>
    {
        public Validator()
        {
            RuleFor(c => c.OrganizationId).NotEmpty();
            RuleFor(c => c.RoleId).NotEmpty();
            RuleFor(c => c.Name).Length(3, 60);
            RuleFor(c => c.ResourcePermissions).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Command, Response>
    {
        private readonly IValidator<Command> _validator;
        private readonly IOrganizationRepository _organizationRepository;
        private readonly ITransactionManager _transactionManager;
        private readonly IPublisher _publisher;
        private readonly IOutbox _outbox;

        public Handler(IValidator<Command> validator,
                       IOrganizationRepository organizationRepository,
                       ITransactionManager transactionManager,
                       IPublisher publisher,
                       IOutbox outbox)
        {
            _validator = validator;
            _organizationRepository = organizationRepository;
            _transactionManager = transactionManager;
            _publisher = publisher;
            _outbox = outbox;
        }

        public async Task<Response> Handle(Command request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            AppPermissions.Validate(request.ResourcePermissions.Select(p => p.Permission.Value), request.IsAdmin);

            using var transaction = await _transactionManager.CreateTransactionAsync();

            var organization = await _organizationRepository.FindAsync(request.OrganizationId, cancellationToken);
            if (organization == null)
            {
                return new Response(Result.OrganizationNotFound);
            }

            var role = organization.Roles.FirstOrDefault(r => r.Id == request.RoleId);
            if (role is null)
            {
                return new Response(Result.RoleNotFound);
            }

            if (organization.Roles.Any(r => r.Id != request.RoleId && r.Name == request.Name))
            {
                return new Response(Result.RoleNameAlreadyExists);
            }

            var currentPermissions = role.ResourcePermissions.Select(p => (p.Id, Permission: p.ResourcePermission));

            var requestPermissions = request.ResourcePermissions;

            if (!role.IsAdmin && request.IsAdmin)
            {
                requestPermissions.Insert(0, new ResourcePermission(new ResourceId(organization.Id), new Permission(Fqdn<AppPermissions>.GetName(AppPermissions.Main.AccessControl.Organizations.Read))));
                requestPermissions.Insert(0, new ResourcePermission(new ResourceId(SystemConsts.ResourceWildcardId), new Permission(Fqdn<AppPermissions>.GetName(AppPermissions.Main.CameraQuotas.Read))));
            }

            var addedPermissions = requestPermissions.Except(currentPermissions.Select(p => p.Permission)).Select(p => (GuidGenerator.New(), p)).ToList();
            var permissions = currentPermissions.Where(p => requestPermissions.Contains(p.Permission)).Select(p => (p.Id, p.Permission)).Concat(addedPermissions).ToList();

            var events = organization.UpdateRole(request.RoleId, request.Name, request.IsAdmin, permissions);

            await _organizationRepository.SaveChangesAsync(cancellationToken);

            foreach (var @event in events)
            {
                await _publisher.Publish(@event, cancellationToken);
            }

            await _outbox.AddRangeAsync(events);

            await transaction.CommitAsync();

            return new Response(request.RoleId);
        }
    }
}