using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.Core.Domain.AccessControl.Organizations;
using Teslametrics.Core.Domain.AccessControl.Users;
using Teslametrics.Core.Services.Outbox;
using Teslametrics.Core.Services.TransactionManager;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Organizations.Drawer.Create;

public static class CreateOrganizationUseCase
{
    public record Command(string Name, Guid OwnerId) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public Guid Id { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(Guid id)
        {
            Id = id;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Id = Guid.Empty;
            Result = result;
        }
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        OrganizationNameAlreadyExists,
        OwnerNotFound
    }

    public class Validator : AbstractValidator<Command>
    {
        public Validator()
        {
            RuleFor(c => c.Name).Length(3, 60);
            RuleFor(c => c.OwnerId).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Command, Response>
    {
        private readonly IValidator<Command> _validator;
        private readonly IOrganizationRepository _organizationRepository;
        private readonly IUserRepository _userRepository;
        private readonly ITransactionManager _transactionManager;
        private readonly IPublisher _publisher;
        private readonly IOutbox _outbox;

        public Handler(IValidator<Command> validator,
                       IOrganizationRepository organizationRepository,
                       IUserRepository userRepository,
                       ITransactionManager transactionManager,
                       IPublisher publisher,
                       IOutbox outbox)
        {
            _validator = validator;
            _organizationRepository = organizationRepository;
            _userRepository = userRepository;
            _transactionManager = transactionManager;
            _publisher = publisher;
            _outbox = outbox;
        }

        public async Task<Response> Handle(Command request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            using var transaction = await _transactionManager.CreateTransactionAsync();

            if (await _organizationRepository.IsOrganizationNameExistsAsync(request.Name))
            {
                return new Response(Result.OrganizationNameAlreadyExists);
            }

            if (!await _userRepository.IsUserExistsAsync(request.OwnerId, cancellationToken))
            {
                return new Response(Result.OwnerNotFound);
            }

            var (organization, events) = OrganizationAggregate.Create(GuidGenerator.New(), request.OwnerId, request.Name);

            await _organizationRepository.AddAsync(organization, cancellationToken);

            await _organizationRepository.SaveChangesAsync(cancellationToken);

            foreach (var @event in events)
            {
                await _publisher.Publish(@event, cancellationToken);
            }

            await _outbox.AddRangeAsync(events);

            await transaction.CommitAsync();

            return new Response(organization.Id);
        }
    }
}