using System.Collections.Concurrent;
using System.Runtime.InteropServices;
using FFmpeg.AutoGen;

namespace FFMpegNET;

public unsafe class MemoryOutputBuffer : IDisposable
{
    private static class Pools
    {
        private sealed class ObjectPool<T> where T : class
        {
            private readonly ConcurrentQueue<T> _objects;
            private readonly Func<T> _objectGenerator;
            private readonly Action<T>? _cleanupAction;
            private readonly int _maxSize;
            private int _count;

            public ObjectPool(Func<T> objectGenerator, Action<T>? cleanupAction = null, int maxSize = 32)
            {
                _objects = new ConcurrentQueue<T>();
                _objectGenerator = objectGenerator ?? throw new ArgumentNullException(nameof(objectGenerator));
                _cleanupAction = cleanupAction;
                _maxSize = maxSize;
                _count = 0;
            }

            public T Rent()
            {
                if (_objects.TryDequeue(out var item))
                {
                    Interlocked.Decrement(ref _count);
                    return item;
                }

                return _objectGenerator();
            }

            public void Return(T item)
            {
                if (item == null)
                    throw new ArgumentNullException(nameof(item));

                _cleanupAction?.Invoke(item);

                // Проверяем, не превысили ли максимальный размер пула
                if (Interlocked.Increment(ref _count) <= _maxSize)
                {
                    _objects.Enqueue(item);
                }
                else
                {
                    Interlocked.Decrement(ref _count);
                    if (item is IDisposable disposable)
                        disposable.Dispose();
                }
            }

            public void Clear()
            {
                while (_objects.TryDequeue(out var item))
                {
                    if (item is IDisposable disposable)
                        disposable.Dispose();
                }

                Interlocked.Exchange(ref _count, 0);
            }
        }

        private static readonly ObjectPool<MemoryStream> StreamPool;
        private static readonly ObjectPool<byte[]> BufferPool;
        private const int MaxPoolSize = 32;

        static Pools()
        {
            StreamPool = new ObjectPool<MemoryStream>(
                () => new MemoryStream(MIN_STREAM_CAPACITY),
                stream =>
                {
                    stream.SetLength(0);
                    stream.Position = 0;
                    if (stream.Capacity > MAX_STREAM_CAPACITY)
                    {
                        stream.Capacity = MIN_STREAM_CAPACITY;
                    }
                },
                MaxPoolSize
            );

            BufferPool = new ObjectPool<byte[]>(
                () => new byte[BUFFER_SIZE],
                null,
                MaxPoolSize
            );
        }

        public static MemoryStream RentStream() => StreamPool.Rent();
        public static void ReturnStream(MemoryStream stream) => StreamPool.Return(stream);

        public static byte[] RentBuffer() => BufferPool.Rent();
        public static void ReturnBuffer(byte[] buffer) => BufferPool.Return(buffer);

        public static void Clear()
        {
            StreamPool.Clear();
            BufferPool.Clear();
        }
    }

    /// <summary>
    /// Очищает статические пулы объектов
    /// </summary>
    public static void ClearPools()
    {
        Pools.Clear();
    }

    private const int BUFFER_SIZE = 8192;
    private const int MIN_STREAM_CAPACITY = BUFFER_SIZE * 32;
    private const int MAX_STREAM_CAPACITY = BUFFER_SIZE * 64;

    private byte[] _copyBuffer;
    private MemoryStream _stream;
    private GCHandle _gcHandle;
    private readonly avio_alloc_context_write_packet _writePacket;
    private AVIOContext* _avioContext;

    public AVIOContext* AvioContext => _avioContext;
    public MemoryStream Stream => _stream;

    public MemoryOutputBuffer()
    {
        _copyBuffer = Pools.RentBuffer();
        _stream = Pools.RentStream();

        _writePacket = WritePacket;
        _gcHandle = GCHandle.Alloc(_writePacket, GCHandleType.Normal);

        var buffer = (byte*)ffmpeg.av_malloc(BUFFER_SIZE);
        _avioContext = ffmpeg.avio_alloc_context(
            buffer,
            BUFFER_SIZE,
            1,
            null,
            null,
            _writePacket,
            null
        );

        if (_avioContext == null)
        {
            ffmpeg.av_free(buffer);
            throw new ApplicationException("Failed to create I/O context");
        }
    }

    private int WritePacket(void* opaque, byte* buf, int bufSize)
    {
        if (buf == null || bufSize <= 0)
            return 0;

        var size = bufSize;
        try
        {
            while (size > 0)
            {
                var copySize = Math.Min(size, _copyBuffer.Length);

                fixed (byte* dest = _copyBuffer)
                {
                    Buffer.MemoryCopy(buf, dest, copySize, copySize);
                }

                _stream.Write(_copyBuffer, 0, copySize);

                buf += copySize;
                size -= copySize;
            }

            return bufSize;
        }
        catch
        {
            return -1;
        }
    }

    public void Dispose()
    {
        if (_gcHandle.IsAllocated)
            _gcHandle.Free();

        if (_avioContext != null)
        {
            var buffer = _avioContext->buffer;

            fixed (AVIOContext** pContext = &_avioContext)
            {
                ffmpeg.avio_context_free(pContext);
            }
        }

        Pools.ReturnBuffer(_copyBuffer);
        Pools.ReturnStream(_stream);
    }
}