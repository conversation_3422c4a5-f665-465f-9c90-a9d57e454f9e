using Orleans;

namespace Teslametrics.MediaServer.Orleans.Camera;

[GenerateSerializer]
public class MicroSegment
{
    [Id(0)]
    public byte[]? Payload { get; set; }

    [Id(1)]
    public DateTimeOffset StartTime { get; set; }

    [Id(2)]
    public double Duration { get; set; }

    public MicroSegment()
    {

    }
    public MicroSegment(byte[] payload, DateTimeOffset startTime, double duration)
    {
        Payload = payload;
        StartTime = startTime;
        Duration = duration;
    }
}