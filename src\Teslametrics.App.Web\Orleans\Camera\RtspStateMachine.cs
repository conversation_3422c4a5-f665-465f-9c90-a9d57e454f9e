using System.Collections.Concurrent;
using System.Reactive.Linq;
using Teslametrics.App.Web.Shared;

namespace Teslametrics.App.Web.Orleans.Camera;

public class RtspStateMachine
{
    private readonly ILogger<RtspStateMachine> _logger;
    private readonly Guid _cameraId;
    private ICameraStreamGrain? _archiveStreamGrain;
    private ICameraStreamGrain? _viewStreamGrain;
    private ICameraStreamGrain? _publicStreamGrain;
    private string _archiveUri = string.Empty;
    private string _viewUri = string.Empty;
    private string _publicUri = string.Empty;
    private readonly IServiceScopeFactory _serviceScopeFactory;
    private State _state = State.Stopped;
    private readonly ConcurrentQueue<Signal> _externalQueue;

    public event Action? Disconnected;

    public RtspStateMachine(ILogger<RtspStateMachine> logger,
                            Guid cameraId,
                            IServiceScopeFactory serviceScopeFactory)
    {
        _logger = logger;
        _cameraId = cameraId;
        _serviceScopeFactory = serviceScopeFactory;

        _externalQueue = new ConcurrentQueue<Signal>();
    }

    public async Task<Status> GetStatusAsync()
    {
        var archiveStatus = _archiveStreamGrain is not null ? await _archiveStreamGrain.GetStatusAsync() : CameraStreamState.Disconnected;
        var viewStatus = _viewStreamGrain is not null ? await _viewStreamGrain.GetStatusAsync() : CameraStreamState.Disconnected;
        var publicStatus = _publicStreamGrain is not null ? await _publicStreamGrain.GetStatusAsync() : CameraStreamState.Disconnected;

        return (_state, archiveStatus, viewStatus, publicStatus) switch
        {
            (State.Stopped, _, _, _) => Status.Stopped,
            (State.Starting, _, _, _) => Status.Starting,
            (State.Running, CameraStreamState.Reconnecting, _, _) => Status.Problem,
            (State.Running, _, CameraStreamState.Reconnecting, _) => Status.Problem,
            (State.Running, _, _, CameraStreamState.Reconnecting) => Status.Problem,
            (State.Running, CameraStreamState.Connected, CameraStreamState.Connected, CameraStreamState.Connected) => Status.Running,
            _ => Status.Starting
        };
    }

    public void Connect(string archiveUri, string viewUri, string publicUri)
    {
        _archiveUri = archiveUri;
        _viewUri = viewUri;
        _publicUri = publicUri;

        _externalQueue.Enqueue(Signal.Start);
    }

    public void Disconnect()
    {
        _externalQueue.Enqueue(Signal.Stop);
    }

    public ICameraStreamGrain? GetCameraStreamGrain(StreamType streamType)
    {
        return streamType switch
        {
            StreamType.Archive => _archiveStreamGrain,
            StreamType.View => _viewStreamGrain,
            StreamType.Public => _publicStreamGrain,
            _ => null
        };
    }

    public async Task ProcessAsync(CancellationToken _)
    {
        if (!_externalQueue.TryDequeue(out var signal))
        {
            return;
        }

        switch (signal)
        {
            case Signal.Start:
                {
                    switch (_state)
                    {
                        case State.Stopped:
                            {
                                ChangeState(State.Starting);
                                await StartAsync(_archiveUri, _viewUri, _publicUri);
                                break;
                            }
                    }

                    break;
                }
            case Signal.Run:
                {
                    switch (_state)
                    {
                        case State.Starting:
                            {
                                ChangeState(State.Running);
                                break;
                            }
                    }

                    break;
                }
            case Signal.Stop:
                {
                    switch (_state)
                    {
                        case State.Running:
                        case State.Starting:
                            {
                                await StopAsync();
                                ChangeState(State.Stopped);
                                Disconnected?.Invoke();
                                break;
                            }
                    }

                    break;
                }
        }
    }

    private async Task StartAsync(string archiveUri, string viewUri, string publicUri)
    {
        try
        {
            var links = new List<(string Uri, bool EnableArchive)>
            {
                (archiveUri, true),
                (viewUri, false),
                (publicUri, false)
            }.DistinctBy(l => l.Uri).ToList();

            using var scope = _serviceScopeFactory.CreateScope();
            var grainFactory = scope.ServiceProvider.GetRequiredService<IGrainFactory>();

            foreach (var (uri, _) in links)
            {
                var streamGrain = grainFactory.GetGrain<ICameraStreamGrain>(GuidGenerator.New());

                StreamType streamType = StreamType.Invalid;

                if (uri == archiveUri)
                {
                    _archiveStreamGrain = streamGrain;
                    streamType |= StreamType.Archive;
                }

                if (uri == viewUri)
                {
                    _viewStreamGrain = streamGrain;
                    streamType |= StreamType.View;
                }

                if (uri == publicUri)
                {
                    _publicStreamGrain = streamGrain;
                    streamType |= StreamType.Public;
                }

                if (streamType is StreamType.Invalid)
                {
                    throw new Exception("Invalid stream type");
                }

                await streamGrain.ConnectAsync(new ICameraStreamGrain.ConnectRequest(_cameraId, uri, streamType));
            }

            _externalQueue.Enqueue(Signal.Run);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to start RTSP camera {CameraId}", _cameraId);
        }
    }

    private async Task StopAsync()
    {
        if (_archiveStreamGrain is not null)
        {
            await _archiveStreamGrain.DisconnectAsync();
        }

        if (_viewStreamGrain is not null)
        {
            await _viewStreamGrain.DisconnectAsync();
        }

        if (_publicStreamGrain is not null)
        {
            await _publicStreamGrain.DisconnectAsync();
        }
    }

    private void ChangeState(State state)
    {
        if (_state != state)
        {
            _state = state;
        }
    }

    public enum State
    {
        Stopped,
        Starting,
        Running
    }

    public enum Status
    {
        Stopped,
        Starting,
        Running,
        Problem
    }

    public enum Signal
    {
        Stop,
        Start,
        Run
    }
}