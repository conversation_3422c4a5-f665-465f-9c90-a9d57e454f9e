﻿using MediatR;
using MudBlazor;
using System.Reactive;
using Teslametrics.App.Web.Events.Users;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Users.PasswordForceChangeDialog;

public partial class PasswordForceChangeDialog
{
	private IDisposable? _subsciption;
	private bool _isVisible;
	private DialogOptions _dialogOptions = new() { CloseOnEscapeKey = true, FullWidth = true, MaxWidth = MaxWidth.Medium, CloseButton = true };

	private GetUserUseCase.Response? _user;

	protected override void OnInitialized()
	{
		CompositeDisposable.Add(EventSystem.Subscribe<ForcePasswordChangeEto>(OnForceChangeEventHandler));

		base.OnInitialized();
	}

	private async Task FetchAsync(Guid id)
	{
		if (IsLoading) return;

		try
		{
			await SetLoadingAsync();
			var response = await ScopeFactory.CreateScope().ServiceProvider.GetRequiredService<IMediator>().Send(new GetUserUseCase.Query(id));
			if (response.IsSuccess)
			{
				_user = response;
				await SubscribeAsync();
				return;
			}

			switch (response.Result)
			{
				case GetUserUseCase.Result.ValidationError:
					Snackbar.Add("Ошибка валидации данных", Severity.Error);
					break;
				case GetUserUseCase.Result.UserNotFound:
					Snackbar.Add("Пользователь не найден", Severity.Error);
					break;
				case GetUserUseCase.Result.Unknown:
				default:
					Snackbar.Add("Не удалось получить пользователя из-за непредвиденной ошибки:" + response.Result.ToString(), Severity.Error);
					break;
			}
		}
		catch (Exception ex)
		{
			Snackbar.Add("Не удалось получить выбранного пользователя. Повторите попытку", Severity.Error);
			Logger.LogError(ex, ex.Message);
		}
		finally
		{
			await SetLoadingAsync(false);
		}
	}

	private void Unsubscribe()
	{
		if (_subsciption is not null)
		{
			CompositeDisposable.Remove(_subsciption);
			_subsciption.Dispose();
		}
	}

	private async Task SubscribeAsync()
	{
		Unsubscribe();
		var result = await ScopeFactory.CreateScope().ServiceProvider.GetRequiredService<IMediator>().Send(new SubscribeUserUseCase.Request(Observer.Create<object>(OnAppEventHandler, OnError), _user!.Id));
		if (result.IsSuccess)
		{
			_subsciption = result.Subscription!;
			CompositeDisposable.Add(_subsciption);
			return;
		}

		switch (result.Result)
		{
			case SubscribeUserUseCase.Result.ValidationError:
				Snackbar.Add("Ошибка валидации при подписке на события", Severity.Error);
				break;

			case SubscribeUserUseCase.Result.Unknown:
			default:
				Snackbar.Add("Не удалось получить подписку на обновления из-за непредвиденной ошибки:" + result.Result.ToString(), Severity.Error);
				break;
		}
	}

	#region [Actions]
	private async Task SubmitAsync()
	{
		if (IsLoading || _user is null) return;

		try
		{
			await SetLoadingAsync(true);
			var result = await ScopeFactory.CreateScope().ServiceProvider.GetRequiredService<IMediator>().Send(new ForceUserPasswordChangeUseCase.Command(_user.Id));
			if (result.IsSuccess)
			{
				Snackbar.Add($"Пароль пользователя {_user?.Username} успешно удалён");
				return;
			}

			switch (result.Result)
			{
				case ForceUserPasswordChangeUseCase.Result.ValidationError:
					Snackbar.Add("Ошибка валидации данных", Severity.Error);
					break;

				case ForceUserPasswordChangeUseCase.Result.UserNotFound:
					Snackbar.Add("Пользователь не найден", Severity.Error);
					break;

				case ForceUserPasswordChangeUseCase.Result.Unknown:
				default:
					Snackbar.Add("Не удалось получить пользователя из-за непредвиденной ошибки:" + result.Result.ToString(), Severity.Error);
					break;
			}
		}
		catch (Exception ex)
		{
			Snackbar.Add("Произошла ошибка при удалении пользователя", Severity.Error);
			Logger.LogError(ex, ex.Message);
		}
		finally
		{
			await SetLoadingAsync(false);
		}
	}

	private Task CancelAsync() => UpdateViewAsync(() =>
	{
		Unsubscribe();
		_isVisible = false;
		_user = null;
	});
	#endregion [Actions]

	#region [Event Handlers]
	public async Task OnForceChangeEventHandler(ForcePasswordChangeEto eto)
	{
		await UpdateViewAsync(() =>
		{
			_isVisible = true;
		});

		if (_user is not null && eto.UserId == _user.Id)
		{
			return;
		}

		await FetchAsync(eto.UserId);
	}

	private async void OnAppEventHandler(object appEvent)
	{
		switch (appEvent)
		{
			case SubscribeUserUseCase.UpdatedEvent updatedEto:
				await FetchAsync(updatedEto.Id);
				await UpdateViewAsync();
				break;

			case SubscribeUserUseCase.DeletedEvent deleteEto:
				Snackbar.Add("Пользователь был удалён.", Severity.Error);
				await CancelAsync();
				break;

			default:
				break;
		}
	}

	private void OnError(Exception exc)
	{
		Logger.LogError(exc, exc.Message);
		Snackbar.Add("Ошибка валидации при подписке на события", Severity.Error);
	}
	private void VisibilityChanged(bool isVisible)
	{
		_isVisible = isVisible;
		if (!isVisible)
		{
			Unsubscribe();
			_user = null;
		}
	}
	#endregion
}
