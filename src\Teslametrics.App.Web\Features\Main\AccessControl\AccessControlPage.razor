@using Teslametrics.App.Web.Domain.AccessControl.Users
@using Teslametrics.App.Web.Features.Main.AccessControl.Permissions
@using Teslametrics.App.Web.Features.Main.AccessControl.Roles.List
@using Teslametrics.App.Web.Features.Main.AccessControl.Users.List
@using Teslametrics.App.Web.Features.Main.AccessControl.Organizations.List
@rendermode InteractiveServer
@attribute [AppAuthorize(AppPermissions.Main.AccessControl.Organizations.Read)]
@attribute [Route(RouteConstants.AccessControl)]
@attribute [StreamRendering]
@inherits InteractiveBaseComponent
<div class="overflow-auto mud-height-full pt-4">
	<MudContainer MaxWidth="MaxWidth.False"
				  Class="mud-height-full">
		<MudGrid Spacing="2"
				 Class="mud-height-full overflow-hidden">
			<MudItem xs="12"
					 md="3"
					 Class="pb-1">
				<MudCard Class="mud-height-full d-flex flex-column">
					<OrganizationsListComponent Selected="_organizationId"
												SelectedChanged="OnOrganizationChanged" />
				</MudCard>
			</MudItem>

			<MudItem xs="12"
					 md="9"
					 Class="pb-1">
				@if (_organizationId.HasValue)
				{
					<MudTabs ApplyEffectsToContainer="true"
							 PanelClass="pt-6"
							 TabHeaderClass="tabs"
							 KeepPanelsAlive="false">
						<AuthorizeView Policy="@AppPermissions.Main.AccessControl.Users.Read.GetEnumPermissionString()"
									   Context="innerContext">
							<MudTabPanel Text="Пользователи"
										 Icon="@Icons.Material.Outlined.Person">
								<MudPaper Class="py-3">
									<UserListComponent OrganizationId="@_organizationId.Value" />
								</MudPaper>
							</MudTabPanel>
						</AuthorizeView>
						<AuthorizeView Policy="@AppPermissions.Main.AccessControl.Roles.Read.GetEnumPermissionString()"
									   Context="innerContext">
							<MudTabPanel Text="Роли"
										 Icon="@Icons.Material.Outlined.WorkOutline">
								<MudPaper Class="py-3">
									<RoleListComponent OrganizationId="@_organizationId.Value" />
								</MudPaper>
							</MudTabPanel>
							<MudTabPanel Text="Права"
										 Icon="@Icons.Material.Outlined.Shield">
								<MudPaper Class="py-3">
									<PermissionsListComponent />
								</MudPaper>
							</MudTabPanel>
						</AuthorizeView>
						<AuthorizeView Policy="@AppPermissions.Main.CameraQuotas.Read.GetEnumPermissionString()"
									   Context="innerContext">
							<MudTabPanel Text="Квоты"
										 Icon="@Icons.Material.Outlined.Dataset">
								<MudStack Spacing="4">
									<MudPaper Class="py-3">
										<Teslametrics.App.Web.Features.Main.AccessControl.Quota.CameraQuotaList.PresetListComponent OrganizationId="@_organizationId.Value" />
									</MudPaper>
								</MudStack>
							</MudTabPanel>
						</AuthorizeView>
					</MudTabs>
				}
				else
				{
					<MudStack AlignItems="AlignItems.Center"
							  Justify="Justify.Center"
							  Class="mud-height-full">
						<MudIcon Icon="@Icons.Material.Filled.Business"
								 Style="font-size: 8rem;" />
						<MudText Typo="Typo.body1">Организация не выбрана</MudText>
						<MudText Typo="Typo.subtitle1">Для продолжения работы выберите организацию</MudText>
					</MudStack>
				}
			</MudItem>
		</MudGrid>
	</MudContainer>
</div>
@if (_organizationId.HasValue)
{
	<AuthorizeView Policy="@AppPermissions.Main.AccessControl.Users.Update.GetEnumPermissionString()"
				   Context="innerContext">
		<Teslametrics.App.Web.Features.Main.AccessControl.Users.ChangeUserPasswordDialog.ChangeUserPasswordDialog />
	</AuthorizeView>
	<AuthorizeView Policy="@AppPermissions.Main.AccessControl.Users.ForceChangePassword.GetEnumPermissionString()"
				   Context="innerContext">
		<Teslametrics.App.Web.Features.Main.AccessControl.Users.PasswordForceChangeDialog.PasswordForceChangeDialog />
	</AuthorizeView>
	<AuthorizeView Policy="@AppPermissions.Main.AccessControl.Roles.Read.GetEnumPermissionString()"
				   Context="innerContext">
		<Teslametrics.App.Web.Features.Main.AccessControl.Roles.Drawer.RoleDrawer />
	</AuthorizeView>
	<Teslametrics.App.Web.Features.Main.AccessControl.Roles.DeleteDialog.DeleteDialog />
	<AuthorizeView Policy="@AppPermissions.Main.AccessControl.Users.Read.GetEnumPermissionString()"
				   Context="innerContext">
		<Teslametrics.App.Web.Features.Main.AccessControl.Users.Drawer.UserDrawer OrganizationId="@_organizationId.Value" />
	</AuthorizeView>
	<AuthorizeView Policy="@AppPermissions.Main.AccessControl.Users.Delete.GetEnumPermissionString()"
				   Context="innerContext">
		<Teslametrics.App.Web.Features.Main.AccessControl.Users.DeleteDialog.DeleteDialog />
	</AuthorizeView>
@* 	<Teslametrics.App.Web.Features.Main.AccessControl.Quota.AddQuotaDrawer.AddQuotaDrawer />*@
}
<Teslametrics.App.Web.Features.Main.AccessControl.Quota.Drawer.QuotaDrawer/>
<AuthorizeView Policy="@AppPermissions.Main.AccessControl.Organizations.Delete.GetEnumPermissionString()"
			   Context="innerContext">
	<Teslametrics.App.Web.Features.Main.AccessControl.Organizations.DeleteDialog.DeleteDialog />
</AuthorizeView>
<Teslametrics.App.Web.Features.Main.AccessControl.Organizations.Drawer.OrganizationDrawer />