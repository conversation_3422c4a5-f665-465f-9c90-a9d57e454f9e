@using Teslametrics.App.Web.Domain.AccessControl.Users
@using Teslametrics.App.Web.Features.Main.AccessControl.Permissions
@using Teslametrics.App.Web.Features.Main.AccessControl.Roles.List
@using Teslametrics.App.Web.Features.Main.AccessControl.Users.List
@using Teslametrics.App.Web.Features.Main.AccessControl.Organizations.List
@using Teslametrics.Shared
@rendermode InteractiveServer
@attribute [AppAuthorize(AppPermissions.Main.AccessControl.Organizations.Read)]
@attribute [Route(RouteConstants.AccessControl)]
@attribute [StreamRendering]
@inherits InteractiveBaseComponent


<MudSwipeArea OnSwipeEnd="@OnSwipeEnd"
              Class="mud-width-full mud-height-full overflow-hidden">
    <div class="@(_userDeviceService.IsMobile ? "mobile_layout" : "layout")">
        @if (!_userDeviceService.IsMobile)
        {
            <MudPaper Class="mud-height-full d-flex flex-column overflow-hidden"
                      Outlined="true">
                <OrganizationsListComponent Selected="_organizationId"
                                            SelectedChanged="OnOrganizationChanged" />
            </MudPaper>
        }
        else
        {
            <MudDrawer Open="@_drawerOpen"
                       Fixed="false"
                       OverlayAutoClose="false"
                       Overlay="false"
                       Breakpoint="Breakpoint.Always"
                       Width="100%"
                       Elevation="1"
                       Variant="@DrawerVariant.Temporary">
                <OrganizationsListComponent Selected="_organizationId"
                                            SelectedChanged="OnOrganizationChanged" />
            </MudDrawer>
        }

        @if (_organizationId.HasValue)
        {
            <MudTabs ApplyEffectsToContainer="true"
                     PanelClass="mud-paper mud-paper-outlined mud-height-full overflow-hidden d-grid panel"
                     Class="mud-height-full overflow-hidden"
                     TabHeaderClass="tabs"
                     KeepPanelsAlive="false">
                <AuthorizeView Policy="@AppPermissions.Main.AccessControl.Users.Read.GetEnumPermissionString()"
                               Context="innerContext">
                    <MudTabPanel Text="Пользователи"
                                 Icon="@Icons.Material.Outlined.Person">
                        <UserListComponent OrganizationId="@_organizationId.Value" />
                    </MudTabPanel>
                </AuthorizeView>
                <AuthorizeView Policy="@AppPermissions.Main.AccessControl.Roles.Read.GetEnumPermissionString()"
                               Context="innerContext">
                    <MudTabPanel Text="Роли"
                                 Icon="@Icons.Material.Outlined.WorkOutline">
                        <RoleListComponent OrganizationId="@_organizationId.Value" />
                    </MudTabPanel>
                    <MudTabPanel Text="Права"
                                 Icon="@Icons.Material.Outlined.Shield">
                        <PermissionsListComponent />
                    </MudTabPanel>
                </AuthorizeView>
                <AuthorizeView Policy="@AppPermissions.Main.CameraQuotas.Read.GetEnumPermissionString()"
                               Context="innerContext">
                    <MudTabPanel Text="Квоты"
                                 Icon="@Icons.Material.Outlined.Dataset">
                        <Teslametrics.App.Web.Features.Main.AccessControl.Quota.CameraQuotaList.PresetListComponent OrganizationId="@_organizationId.Value" />
                    </MudTabPanel>
                </AuthorizeView>
            </MudTabs>
        }
        else
        {
            <MudStack AlignItems="AlignItems.Center"
                      Justify="Justify.Center"
                      Class="mud-height-full">
                <MudIcon Icon="@Icons.Material.Filled.Business"
                         Style="font-size: 8rem;" />
                <MudText Typo="Typo.body1">Организация не выбрана</MudText>
                <MudText Typo="Typo.subtitle1">Для продолжения работы выберите организацию</MudText>
            </MudStack>
        }
    </div>
</MudSwipeArea>
@if (_organizationId.HasValue)
{
    <AuthorizeView Policy="@AppPermissions.Main.AccessControl.Users.Update.GetEnumPermissionString()"
                   Context="innerContext">
        <Teslametrics.App.Web.Features.Main.AccessControl.Users.ChangeUserPasswordDialog.ChangeUserPasswordDialog />
    </AuthorizeView>
    <AuthorizeView Policy="@AppPermissions.Main.AccessControl.Users.ForceChangePassword.GetEnumPermissionString()"
                   Context="innerContext">
        <Teslametrics.App.Web.Features.Main.AccessControl.Users.PasswordForceChangeDialog.PasswordForceChangeDialog />
    </AuthorizeView>
    <AuthorizeView Policy="@AppPermissions.Main.AccessControl.Roles.Read.GetEnumPermissionString()"
                   Context="innerContext">
        <Teslametrics.App.Web.Features.Main.AccessControl.Roles.Drawer.RoleDrawer />
    </AuthorizeView>
    <Teslametrics.App.Web.Features.Main.AccessControl.Roles.DeleteDialog.DeleteDialog />
    <AuthorizeView Policy="@AppPermissions.Main.AccessControl.Users.Read.GetEnumPermissionString()"
                   Context="innerContext">
        <Teslametrics.App.Web.Features.Main.AccessControl.Users.Drawer.UserDrawer OrganizationId="@_organizationId.Value" />
    </AuthorizeView>
    <AuthorizeView Policy="@AppPermissions.Main.AccessControl.Users.Delete.GetEnumPermissionString()"
                   Context="innerContext">
        <Teslametrics.App.Web.Features.Main.AccessControl.Users.DeleteDialog.DeleteDialog />
    </AuthorizeView>
    @* 	<Teslametrics.App.Web.Features.Main.AccessControl.Quota.AddQuotaDrawer.AddQuotaDrawer />*@
}
<Teslametrics.App.Web.Features.Main.AccessControl.Quota.Drawer.QuotaDrawer />
<AuthorizeView Policy="@AppPermissions.Main.AccessControl.Organizations.Delete.GetEnumPermissionString()"
               Context="innerContext">
    <Teslametrics.App.Web.Features.Main.AccessControl.Organizations.DeleteDialog.DeleteDialog />
</AuthorizeView>
<Teslametrics.App.Web.Features.Main.AccessControl.Organizations.Drawer.OrganizationDrawer />