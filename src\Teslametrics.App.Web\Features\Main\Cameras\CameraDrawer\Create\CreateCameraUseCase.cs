using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.Core.Domain.AccessControl.Organizations;
using Teslametrics.Core.Domain.Cameras;
using Teslametrics.Core.Domain.Folders;
using Teslametrics.MediaServer.Orleans.Camera;
using Teslametrics.Core.Services.Outbox;
using Teslametrics.Core.Services.TransactionManager;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Features.Main.Cameras.CameraDrawer.Create;

public static class CreateCameraUseCase
{
    public record Command(Guid OrganizationId,
                          Guid FolderId,
                          Guid QuotaId,
                          string Name,
                          TimeSpan TimeZone,
                          Coordinates? Coordinates,
                          string AchiveUri,
                          string ViewUri,
                          string PublicUri,
                          bool AutoStart,
                          bool StartOnCreate,
                          bool OnvifEnabled,
                          Command.OnvifSettings? OnvifOptions) : BaseRequest<Response>
    {
        public record OnvifSettings(string Host, int Port, string Username, string Password);
    };

    public record Response : BaseResponse
    {
        public Guid Id { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(Guid id)
        {
            Id = id;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Id = Guid.Empty;
            Result = result;
        }
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        OrganizationNotFound,
        FolderNotFound,
        CameraQuotaNotFound,
        CameraQuotaLimitReached
    }

    public class Validator : AbstractValidator<Command>
    {
        public Validator()
        {
            RuleFor(c => c.OrganizationId).NotEmpty();
            RuleFor(c => c.FolderId).NotEmpty();
            RuleFor(c => c.QuotaId).NotEmpty();
            RuleFor(c => c.Name).Length(3, 60);
            RuleFor(c => c.AchiveUri).NotEmpty();
            RuleFor(c => c.ViewUri).NotEmpty();
            RuleFor(c => c.PublicUri).NotEmpty();
            RuleFor(c => c.OnvifOptions).NotNull().When(c => c.OnvifEnabled);
        }
    }

    public class Handler : IRequestHandler<Command, Response>
    {
        private readonly IValidator<Command> _validator;
        private readonly ICameraRepository _cameraRepository;
        private readonly IOrganizationRepository _organizationRepository;
        private readonly IFolderRepository _folderRepository;
        private readonly IClusterClient _clusterClient;
        private readonly ITransactionManager _transactionManager;
        private readonly IPublisher _publisher;
        private readonly IOutbox _outbox;

        public Handler(IValidator<Command> validator,
                       ICameraRepository cameraRepository,
                       IOrganizationRepository organizationRepository,
                       IFolderRepository folderRepository,
                       IClusterClient clusterClient,
                       ITransactionManager transactionManager,
                       IPublisher publisher,
                       IOutbox outbox)
        {
            _validator = validator;
            _cameraRepository = cameraRepository;
            _organizationRepository = organizationRepository;
            _folderRepository = folderRepository;
            _clusterClient = clusterClient;
            _transactionManager = transactionManager;
            _publisher = publisher;
            _outbox = outbox;
        }

        public async Task<Response> Handle(Command request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            using var transaction = await _transactionManager.CreateTransactionAsync();

            var organization = await _organizationRepository.FindAsync(request.OrganizationId, cancellationToken);
            if (organization is null)
            {
                return new Response(Result.OrganizationNotFound);
            }

            var cameraQuota = organization.CameraQuotas.SingleOrDefault(cq => cq.Id == request.QuotaId);
            if (cameraQuota is null)
            {
                return new Response(Result.CameraQuotaNotFound);
            }

            var cameraCount = await _cameraRepository.GetCameraCountByQuotaAsync(request.QuotaId, cancellationToken);

            if (cameraQuota.Limit != -1 && cameraQuota.Limit <= cameraCount)
            {
                return new Response(Result.CameraQuotaLimitReached);
            }

            if (!await _folderRepository.IsFolderExistsAsync(request.FolderId, cancellationToken))
            {
                return new Response(Result.FolderNotFound);
            }

            var (camera, events) = CameraAggregate.Create(GuidGenerator.New(),
                                                          request.OrganizationId,
                                                          request.FolderId,
                                                          request.QuotaId,
                                                          request.Name,
                                                          request.TimeZone,
                                                          request.Coordinates?.Latitude,
                                                          request.Coordinates?.Longitude,
                                                          request.AchiveUri,
                                                          request.ViewUri,
                                                          request.PublicUri,
                                                          request.OnvifEnabled,
                                                          ToOnvifSettingsValueObject(request.OnvifOptions),
                                                          request.AutoStart);

            await _cameraRepository.AddAsync(camera, cancellationToken);
            await _cameraRepository.SaveChangesAsync(cancellationToken);

            foreach (var @event in events)
            {
                await _publisher.Publish(@event, cancellationToken);
            }

            await _outbox.AddRangeAsync(events);

            await transaction.CommitAsync();

            if (request.StartOnCreate)
            {
                var grain = _clusterClient.GetGrain<IMediaServerGrain>(Guid.Empty);
                await grain.ConnectRtspAsync(new IMediaServerGrain.CameraConnectRtspRequest(camera.Id, camera.ArchiveUri, camera.ViewUri, camera.PublicUri));
                if (request.OnvifEnabled)
                {
                    await grain.ConnectOnvifAsync(new IMediaServerGrain.CameraConnectOnvifRequest(camera.Id, camera.OnvifSettings!.Host, camera.OnvifSettings.Port, camera.OnvifSettings.Username, camera.OnvifSettings.Password));
                }
            }

            return new Response(camera.Id);
        }

        private static OnvifSettingsValueObject? ToOnvifSettingsValueObject(Command.OnvifSettings? onvifSettings)
        {
            if (onvifSettings is null)
            {
                return null;
            }

            return new OnvifSettingsValueObject(onvifSettings.Host, onvifSettings.Port, onvifSettings.Username, onvifSettings.Password);
        }
    }
}