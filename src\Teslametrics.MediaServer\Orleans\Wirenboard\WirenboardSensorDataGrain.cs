using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Orleans.Utilities;
using System.Collections.Concurrent;
using System.Data;
using System.Reactive.Linq;
using Teslametrics.Core.Domain.AccessControl.Users;
using Teslametrics.Core.Domain.Incidents;
using Teslametrics.Core.Domain.Incidents.Events;
using Teslametrics.Core.Domain.Notifications;
using Teslametrics.Core.Services.Outbox;
using Teslametrics.Core.Services.TransactionManager;
using Teslametrics.MediaServer.Mqtt;
using Teslametrics.Shared;

namespace Teslametrics.MediaServer.Orleans.Wirenboard;

public class WirenboardSensorDataGrain : Grain, IWirenboardSensorDataGrain, IRemindable
{
    private readonly MqttClientService _mqttClientService;
    private readonly MediaServerModule.WirenboardSettings _settings;
    private readonly ILogger<WirenboardSensorDataGrain> _logger;
    private readonly IServiceScopeFactory _serviceScopeFactory;
    private readonly IHostApplicationLifetime _applicationLifetime;
    private readonly ConcurrentDictionary<string, SensorTopicInfo> _activeTopics = new();
    private bool _isConnected = false;
    private IDisposable? _messageSubscription;
    private readonly ObserverManager<ISensorObserver> _observerManager;
    private readonly ConcurrentDictionary<ISensorObserver, string> _subscribedTopics = new();
    private readonly ConcurrentDictionary<string, DoorState> _doorStates = new();
    private readonly ConcurrentDictionary<string, ISensorData> _lastTopicValues = new();
    private IGrainTimer? _doorCheckTimer;
    private const int DOOR_CHECK_INTERVAL_MS = 1000;
    private const string _keepAliveReminderName = "WirenboardSensorDataGrainKeepAliveReminder";
    private readonly TimeSpan _keepAliveReminderPeriod = TimeSpan.FromMinutes(10);
    private IGrainReminder? _keepAliveReminder;

    private class DoorState
    {
        public DateTimeOffset OpenTime { get; set; }
        public DoorModel Config { get; set; }
        public SensorTopicInfo TopicInfo { get; set; }

        public DoorState(DateTimeOffset openTime, DoorModel config, SensorTopicInfo topicInfo)
        {
            OpenTime = openTime;
            Config = config;
            TopicInfo = topicInfo;
        }
    }

    public WirenboardSensorDataGrain(ILogger<WirenboardSensorDataGrain> logger,
                                     IOptions<MediaServerModule.WirenboardSettings> options,
                                     MqttClientService mqttClientService,
                                     IServiceScopeFactory serviceScopeFactory,
                                     IHostApplicationLifetime applicationLifetime)
    {
        _logger = logger;
        _settings = options.Value;
        _mqttClientService = mqttClientService;
        _serviceScopeFactory = serviceScopeFactory;
        _applicationLifetime = applicationLifetime;

        _observerManager = new ObserverManager<ISensorObserver>(TimeSpan.FromMinutes(5), logger);

        SubscribeToMessages();
    }

    public override async Task OnActivateAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("MqttGrain activated with ID: {GrainId}", this.GetPrimaryKeyString());

        // Настраиваем таймер для проверки дверей
        var options = new GrainTimerCreationOptions
        {
            Period = TimeSpan.FromMilliseconds(DOOR_CHECK_INTERVAL_MS),
            DueTime = TimeSpan.FromMilliseconds(DOOR_CHECK_INTERVAL_MS)
        };

        _doorCheckTimer = this.RegisterGrainTimer<object?>(
            CheckDoorsAsync,
            null,
            options);

        // Регистрируем напоминание для предотвращения деактивации зерна
        try
        {
            _keepAliveReminder = await this.RegisterOrUpdateReminder(
                _keepAliveReminderName,
                TimeSpan.FromMinutes(1), // Начальная задержка
                _keepAliveReminderPeriod // Период между вызовами
            );

            _logger.LogInformation("Registered keep-alive reminder for MqttGrain with ID: {GrainId}",
                this.GetPrimaryKeyString());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to register keep-alive reminder for MqttGrain with ID: {GrainId}",
                this.GetPrimaryKeyString());
        }

        await base.OnActivateAsync(cancellationToken);
    }

    public override async Task OnDeactivateAsync(DeactivationReason reason, CancellationToken cancellationToken)
    {
        _logger.LogInformation("MqttGrain deactivating with ID: {GrainId}, reason: {Reason}",
            this.GetPrimaryKeyString(), reason);

        // Удаляем таймер проверки дверей
        if (_doorCheckTimer != null)
        {
            _doorCheckTimer.Dispose();
            _doorCheckTimer = null;
        }

        // Удаляем напоминание
        if (_keepAliveReminder != null)
        {
            // Проверяем, не находится ли приложение в процессе завершения работы
            if (!_applicationLifetime.ApplicationStopping.IsCancellationRequested)
            {
                try
                {
                    await this.UnregisterReminder(_keepAliveReminder);
                    _logger.LogInformation("Unregistered keep-alive reminder for MqttGrain with ID: {GrainId}",
                        this.GetPrimaryKeyString());
                }
                catch (OperationCanceledException)
                {
                    _logger.LogWarning("ReminderService has been stopped while unregistering reminder for WirenboardSensorDataGrain with ID: {GrainId}",
                        this.GetPrimaryKeyString());
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to unregister keep-alive reminder for MqttGrain with ID: {GrainId}",
                        this.GetPrimaryKeyString());
                }
            }
            else
            {
                _logger.LogInformation("Skipping reminder unregistration for WirenboardSensorDataGrain with ID: {GrainId} as application is stopping",
                    this.GetPrimaryKeyString());
            }
        }

        _doorStates.Clear();

        if (_isConnected)
        {
            await ShutdownAsync();
        }

        _logger.LogInformation("MqttGrain resources cleaned up for ID: {GrainId}",
            this.GetPrimaryKeyString());

        await base.OnDeactivateAsync(reason, cancellationToken);
    }

    /// <summary>
    /// Метод, вызываемый системой Orleans при срабатывании напоминания
    /// </summary>
    /// <param name="reminderName">Имя напоминания</param>
    /// <param name="status">Статус напоминания</param>
    /// <returns>Task</returns>
    public Task ReceiveReminder(string reminderName, TickStatus status)
    {
        if (reminderName == _keepAliveReminderName)
        {
            _logger.LogDebug("Keep-alive reminder received for MqttGrain with ID: {GrainId}",
                this.GetPrimaryKeyString());
        }

        return Task.CompletedTask;
    }

    private async Task CheckDoorsAsync(object? _, CancellationToken cancellationToken)
    {
        var now = DateTimeOffset.UtcNow;

        foreach (var doorEntry in _doorStates)
        {
            var doorState = doorEntry.Value;
            var openDuration = now - doorState.OpenTime;

            if (openDuration.TotalSeconds > doorState.Config.AvailableOpeningTime)
            {
                var incident = CreateIncident(doorState.TopicInfo, IncidentType.Door, now);

                using var scope = _serviceScopeFactory.CreateScope();

                var incidentRepository = scope.ServiceProvider.GetRequiredService<IIncidentRepository>();
                var incidentNotificationRepository = scope.ServiceProvider.GetRequiredService<IIncidentNotificationRepository>();
                var outbox = scope.ServiceProvider.GetRequiredService<IOutbox>();
                var transactionManager = scope.ServiceProvider.GetRequiredService<ITransactionManager>();

                using var transaction = await transactionManager.CreateTransactionAsync();

                await incidentRepository.AddAsync(incident);

                // Получаем репозиторий пользователей
                var userRepository = scope.ServiceProvider.GetRequiredService<IUserRepository>();

                // Создаем уведомления для всех пользователей
                var notifications = await CreateIncidentNotification(incident, userRepository);
                foreach (var notification in notifications)
                {
                    await incidentNotificationRepository.AddAsync(notification, CancellationToken.None);
                }

                // Отправляем событие в outbox
                var incidentCreatedEvent = new IncidentCreatedEvent(incident.Id,
                                                                    incident.CityId,
                                                                    incident.BuildingId,
                                                                    incident.FloorId,
                                                                    incident.RoomId,
                                                                    incident.DeviceId,
                                                                    incident.IncidentType,
                                                                    incident.CreatedAt);
                await outbox.AddRangeAsync([incidentCreatedEvent]);

                await incidentRepository.SaveChangesAsync(CancellationToken.None);
                await incidentNotificationRepository.SaveChangesAsync(CancellationToken.None);
                await transaction.CommitAsync();

                _doorStates.TryRemove(doorEntry.Key, out var _);
            }
        }
    }

    private async Task<bool> SubscribeTopicsAsync(IEnumerable<SensorTopicInfo> topicInfos)
    {
        try
        {
            // Отфильтровываем только новые топики, на которые еще не подписаны
            var newTopicInfos = topicInfos.Where(t => !_activeTopics.ContainsKey(t.Topic)).ToList();
            if (newTopicInfos.Count > 0)
            {
                var newTopicNames = newTopicInfos.Select(t => t.Topic).ToArray();

                await _mqttClientService.SubscribeAsync(newTopicNames);
                foreach (var topicInfo in newTopicInfos)
                {
                    _activeTopics[topicInfo.Topic] = topicInfo;
                }
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error subscribing to topics: {Message}", ex.Message);
            return false;
        }
    }

    private async Task<bool> UnsubscribeTopicsAsync(IEnumerable<string> topics)
    {
        try
        {
            if (!_isConnected)
            {
                return false;
            }

            if (topics.Any())
            {
                await _mqttClientService.UnsubscribeAsync(topics.ToArray());
                foreach (var topic in topics)
                {
                    _activeTopics.TryRemove(topic, out _);

                    // Удаляем последнее значение для топика из кэша
                    if (_lastTopicValues.ContainsKey(topic))
                    {
                        _lastTopicValues.TryRemove(topic, out _);
                        _logger.LogDebug("Removed last known value for topic {Topic} from cache", topic);
                    }
                }
            }

            // Если после отписки не осталось активных топиков, отключаемся
            if (_activeTopics.Count == 0)
            {
                await ShutdownAsync();
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error unsubscribing from topics: {Message}", ex.Message);
            return false;
        }
    }

    public async Task ConfigureSensorTopicsAsync(IWirenboardSensorDataGrain.ConfigureSensorTopicsRequest request)
    {
        if (_settings.BrokerAddress is null || _settings.Port is null)
        {
            _logger.LogError("Broker address or port is not configured");
            return;
        }

        try
        {
            var newTopics = request.TopicInfos.ToList();
            var newTopicNames = newTopics.Select(t => t.Topic).ToList();

            if (!_isConnected && newTopics.Count > 0)
            {
                await _mqttClientService.ConnectAsync(_settings.BrokerAddress!, _settings.Port!.Value, $"ClientId-{this.GetPrimaryKey()}");
                _isConnected = true;
            }

            var topicsToAdd = newTopics.Where(t => !_activeTopics.ContainsKey(t.Topic)).ToList();
            var topicsToRemove = _activeTopics.Keys.Where(t => !newTopicNames.Contains(t)).ToList();

            if (topicsToRemove.Count > 0)
            {
                await UnsubscribeTopicsAsync(topicsToRemove);
            }

            if (topicsToAdd.Count > 0)
            {
                await SubscribeTopicsAsync(topicsToAdd);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating topics: {Message}", ex.Message);
        }
    }

    private async Task<bool> ShutdownAsync()
    {
        try
        {
            _logger.LogInformation("Shutting down MQTT client for grain ID: {GrainId}", this.GetPrimaryKeyString());

            // Отписываемся от сообщений
            _messageSubscription?.Dispose();
            _messageSubscription = null;

            if (_isConnected)
            {
                await _mqttClientService.DisconnectAsync();
                _isConnected = false;
                _activeTopics.Clear();
                _logger.LogInformation("MQTT client disconnected for grain ID: {GrainId}", this.GetPrimaryKeyString());
            }
            else
            {
                _logger.LogInformation("MQTT client was not connected for grain ID: {GrainId}", this.GetPrimaryKeyString());
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error disconnecting MQTT client for grain ID: {GrainId}: {Message}",
                this.GetPrimaryKeyString(), ex.Message);
            return false;
        }
    }

    /// <summary>
    /// Подписывается на сообщения MQTT и сохраняет их в TimescaleDB
    /// </summary>
    private void SubscribeToMessages()
    {
        _logger.LogInformation("Setting up MQTT message subscription for grain ID: {GrainId}", this.GetPrimaryKeyString());

        // Отписываемся от предыдущей подписки, если она существует
        if (_messageSubscription != null)
        {
            _logger.LogDebug("Disposing previous MQTT subscription for grain ID: {GrainId}", this.GetPrimaryKeyString());
            _messageSubscription.Dispose();
        }

        // Подписываемся на сообщения от MQTT клиента
        _messageSubscription = _mqttClientService.Messages
            .Subscribe(async message =>
            {
                try
                {
                    _logger.LogDebug("Received MQTT message for topic: {Topic}, grain ID: {GrainId}",
                        message.Topic, this.GetPrimaryKeyString());

                    var topicInfo = _activeTopics[message.Topic];

                    var data = topicInfo.ValueType switch
                    {
                        SensorValueType.String => (ISensorData)new SensorStringData(message.Topic, message.Value),
                        SensorValueType.Bool => new SensorBoolData(message.Topic, ParseBooleanValue(message.Value)),
                        SensorValueType.Double => new SensorDoubleData(message.Topic, double.Parse(message.Value, System.Globalization.CultureInfo.InvariantCulture)),
                        SensorValueType.Integer => new SensorIntData(message.Topic, int.Parse(message.Value, System.Globalization.CultureInfo.InvariantCulture)),
                        _ => throw new ArgumentOutOfRangeException(topicInfo.ValueType.ToString(), "Unknown sensor value type")
                    };

                    // Сохраняем последнее значение для топика
                    _lastTopicValues[message.Topic] = data;

                    await _observerManager.Notify(s => s.ReceiveData(data), s => _subscribedTopics.TryGetValue(s, out var topic) && topic == message.Topic);

                    await ProcessSensorData(data, topicInfo, message.Timestamp);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing MQTT message for topic: {Topic}, grain ID: {GrainId}: {Message}",
                        message.Topic, this.GetPrimaryKeyString(), ex.Message);
                }
            });

        _logger.LogInformation("MQTT message subscription set up for grain ID: {GrainId}", this.GetPrimaryKeyString());
    }

    /// <summary>
    /// Преобразует строковое значение в логическое с поддержкой различных форматов
    /// </summary>
    /// <param name="value">Строковое представление логического значения</param>
    /// <returns>Логическое значение</returns>
    private static bool ParseBooleanValue(string value)
    {
        // Приводим к нижнему регистру для сравнения без учета регистра
        value = value.Trim().ToLowerInvariant();

        // Проверяем различные форматы представления "истины"
        return value switch
        {
            "true" or "1" => true,
            "false" or "0" => false,
            _ => throw new FormatException($"String '{value}' was not recognized as a valid Boolean.")
        };
    }

    private async Task ProcessSensorData(ISensorData data, SensorTopicInfo topicInfo, DateTimeOffset timestamp)
    {
        bool isAlertCondition = false;

        switch (data)
        {
            case SensorDoubleData temperatureData when topicInfo.SensorModel is TemperatureModel tempConfig:
                isAlertCondition = temperatureData.Value > tempConfig.MaxTemp || temperatureData.Value < tempConfig.MinTemp;
                break;

            case SensorDoubleData humidityData when topicInfo.SensorModel is HumidityModel humidityConfig:
                isAlertCondition = humidityData.Value > humidityConfig.MaxHumidity || humidityData.Value < humidityConfig.MinHumidity;
                break;

            case SensorBoolData doorData when topicInfo.SensorModel is DoorModel doorConfig:
                await ProcessDoorStateAsync(doorData, doorConfig, topicInfo, timestamp);
                return;

            case SensorBoolData powerData when topicInfo.SensorModel is PowerModel:
                isAlertCondition = !powerData.Value;
                break;

            case SensorBoolData leakData when topicInfo.SensorModel is LeakModel:
                isAlertCondition = leakData.Value;
                break;
        }

        try
        {
            using var scope = _serviceScopeFactory.CreateScope();
            var transactionManager = scope.ServiceProvider.GetRequiredService<ITransactionManager>();
            var incidentRepository = scope.ServiceProvider.GetRequiredService<IIncidentRepository>();
            var incidentNotificationRepository = scope.ServiceProvider.GetRequiredService<IIncidentNotificationRepository>();
            var outbox = scope.ServiceProvider.GetRequiredService<IOutbox>();

            using var transaction = await transactionManager.CreateTransactionAsync();

            // Определяем ожидаемый тип инцидента для данного датчика
            var expectedIncidentType = GetIncidentTypeForData(topicInfo.SensorModel);

            // Получаем последний неразрешенный инцидент для данного топика с ожидаемым типом
            var existingIncident = await incidentRepository.GetLastUnresolvedIncidentByTypeAsync(topicInfo.Topic, expectedIncidentType);

            if (isAlertCondition)
            {
                if (existingIncident == null)
                {
                    var incident = CreateIncident(topicInfo, GetIncidentTypeForData(topicInfo.SensorModel), timestamp);
                    await incidentRepository.AddAsync(incident);

                    // Получаем репозиторий пользователей
                    var userRepository = scope.ServiceProvider.GetRequiredService<IUserRepository>();

                    var notifications = await CreateIncidentNotification(incident, userRepository);
                    foreach (var notification in notifications)
                    {
                        await incidentNotificationRepository.AddAsync(notification, CancellationToken.None);
                    }

                    var incidentCreatedEvent = new IncidentCreatedEvent(incident.Id,
                                                                        incident.CityId,
                                                                        incident.BuildingId,
                                                                        incident.FloorId,
                                                                        incident.RoomId,
                                                                        incident.DeviceId,
                                                                        incident.IncidentType,
                                                                        incident.CreatedAt);
                    await outbox.AddRangeAsync([incidentCreatedEvent]);

                    await incidentRepository.SaveChangesAsync(CancellationToken.None);
                    await incidentNotificationRepository.SaveChangesAsync(CancellationToken.None);
                    await transaction.CommitAsync();

                    _logger.LogWarning(
                        "New incident created: {IncidentType} for topic {Topic}",
                        incident.IncidentType,
                        incident.Topic
                    );
                }
            }
            else if (existingIncident != null)
            {
                existingIncident.Resolve();
                await incidentRepository.SaveChangesAsync();
                await outbox.AddRangeAsync([new IncidentResolvedEvent(existingIncident.Id,
                                                                      existingIncident.CityId,
                                                                      existingIncident.BuildingId,
                                                                      existingIncident.FloorId,
                                                                      existingIncident.RoomId,
                                                                      existingIncident.DeviceId,
                                                                      existingIncident.IncidentType,
                                                                      existingIncident.CreatedAt)]);
                await transaction.CommitAsync();

                _logger.LogInformation(
                    "Incident resolved: {IncidentType} for topic {Topic}",
                    existingIncident.IncidentType,
                    existingIncident.Topic
                );
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex,
                "Failed to process incident for topic {Topic}",
                topicInfo.Topic
            );
        }
    }

    private async Task ProcessDoorStateAsync(SensorBoolData doorData, DoorModel doorConfig, SensorTopicInfo topicInfo, DateTimeOffset timestamp)
    {
        var topic = doorData.Topic;
        var isOpen = doorData.Value;

        if (isOpen)
        {
            // Если это новое открытие двери
            if (!_doorStates.ContainsKey(topic))
            {
                _doorStates[topic] = new DoorState(timestamp, doorConfig, topicInfo);
            }
        }
        else
        {
            // Если дверь закрыта, удаляем её из отслеживания
            _doorStates.TryRemove(topic, out _);

            // Проверяем, есть ли неразрешенный инцидент для этой двери
            try
            {
                using var scope = _serviceScopeFactory.CreateScope();
                var transactionManager = scope.ServiceProvider.GetRequiredService<ITransactionManager>();
                var incidentRepository = scope.ServiceProvider.GetRequiredService<IIncidentRepository>();
                var outbox = scope.ServiceProvider.GetRequiredService<IOutbox>();

                using var transaction = await transactionManager.CreateTransactionAsync();

                // Получаем последний неразрешенный инцидент для данного топика с типом Door
                var existingIncident = await incidentRepository.GetLastUnresolvedIncidentByTypeAsync(topic, IncidentType.Door);

                // Проверяем, что инцидент существует
                if (existingIncident != null)
                {
                    existingIncident.Resolve();
                    await incidentRepository.SaveChangesAsync();
                    await outbox.AddRangeAsync([new IncidentResolvedEvent(existingIncident.Id,
                                                                          existingIncident.CityId,
                                                                          existingIncident.BuildingId,
                                                                          existingIncident.FloorId,
                                                                          existingIncident.RoomId,
                                                                          existingIncident.DeviceId,
                                                                          existingIncident.IncidentType,
                                                                          existingIncident.CreatedAt)]);
                    await transaction.CommitAsync();

                    _logger.LogInformation(
                        "Door incident resolved: {IncidentType} for topic {Topic}",
                        existingIncident.IncidentType,
                        existingIncident.Topic
                    );
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex,
                    "Failed to resolve door incident for topic {Topic}",
                    topic
                );
            }
        }
    }

    private static IncidentAggregate CreateIncident(SensorTopicInfo topicInfo, IncidentType incidentType, DateTimeOffset timestamp)
    {
        return IncidentAggregate.Create(
            id: GuidGenerator.New(),
            incidentType: incidentType,
            cityId: topicInfo.CityId,
            city: topicInfo.City,
            buildingId: topicInfo.BuildingId,
            building: topicInfo.Building,
            floorId: topicInfo.FloorId,
            floor: topicInfo.Floor,
            roomId: topicInfo.RoomId,
            room: topicInfo.Room,
            deviceId: topicInfo.DeviceId,
            device: topicInfo.Device,
            sensorId: topicInfo.SensorId,
            topic: topicInfo.Topic,
            createdAt: timestamp
        );
    }

    private static IncidentType GetIncidentTypeForData(ISensorModel model)
    {
        return model switch
        {
            TemperatureModel => IncidentType.Temperature,
            HumidityModel => IncidentType.Humidity,
            DoorModel => IncidentType.Door,
            PowerModel => IncidentType.Power,
            LeakModel => IncidentType.Leak,
            _ => throw new ArgumentException($"Unknown sensor type: {model.GetType()}")
        };
    }

    /// <summary>
    /// Создает уведомления для всех пользователей на основе инцидента
    /// </summary>
    private static async Task<List<IncidentNotificationAggregate>> CreateIncidentNotification(IncidentAggregate incident, IUserRepository userRepository)
    {
        // Получаем список всех пользователей из репозитория
        var users = await userRepository.GetAllUsersAsync();

        // Создаем уведомления для каждого пользователя
        var notifications = new List<IncidentNotificationAggregate>();

        foreach (var user in users)
        {
            var notification = IncidentNotificationAggregate.Create(
                id: GuidGenerator.New(),
                incidentId: incident.Id,
                userId: user.Id
            );

            notifications.Add(notification);
        }

        return notifications;
    }

    public async Task SubscribeAsync(IWirenboardSensorDataGrain.SubscribeRequest request)
    {
        _observerManager.Subscribe(request.Observer, request.Observer);
        _observerManager.ClearExpired();

        if (!_subscribedTopics.TryGetValue(request.Observer, out _))
        {
            // Отправляем последнее известное значение для топика, если оно есть
            if (_lastTopicValues.TryGetValue(request.Topic, out var lastValue))
            {
                try
                {
                    await request.Observer.ReceiveData(lastValue);
                    _logger.LogDebug("Sent last known value for topic {Topic} to new subscriber", request.Topic);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to send last known value for topic {Topic} to new subscriber", request.Topic);
                }
            }
        }

        _subscribedTopics[request.Observer] = request.Topic;
    }

    public Task UnsubscribeAsync(IWirenboardSensorDataGrain.UnsubscribeRequest request)
    {
        _subscribedTopics.TryRemove(request.Observer, out _);
        _observerManager.Unsubscribe(request.Observer);
        _observerManager.ClearExpired();

        return Task.CompletedTask;
    }
}