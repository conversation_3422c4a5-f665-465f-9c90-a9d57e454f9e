using FluentValidation;
using Microsoft.AspNetCore.Components;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Components.Drawer;
using Teslametrics.App.Web.Extensions;
using Teslametrics.App.Web.Shared;

namespace Teslametrics.App.Web.Features.Main.CameraViews.Drawer.Create;

public partial class DrawerCreateComponent
{
	private class Cell(GridItem.GridItemComponent.CameraViewCell? camera, short cellIndex)
	{
		public GridItem.GridItemComponent.CameraViewCell? Camera { get; set; } = camera;
		public short CellIndex { get; set; } = cellIndex;
	}
	private class Model
	{

		private short _rows = 2;
		private short _columns = 2;
		private int _cellCount = 4;
		private GridType _gridType = GridType.GridCustom;
		public GridType Grid
		{
			get => _gridType; set
			{
				if (_gridType == value)
				{
					return;
				}

				_gridType = value;
				switch (value)
				{
					case GridType.Grid1Plus5:
						_rows = 3;
						_columns = 3;
						_cellCount = 6;
						break;

					case GridType.Grid1Plus12:
						_rows = 4;
						_columns = 4;
						_cellCount = 13;
						break;

					case GridType.Grid1Plus7:
						_rows = 4;
						_columns = 4;
						_cellCount = 8;
						break;

					case GridType.Grid2Plus8:
						_rows = 4;
						_columns = 4;
						_cellCount = 10;
						break;
					case GridType.Grid3Plus4:
						_rows = 4;
						_columns = 4;
						_cellCount = 7;
						break;

					default:
						_rows = 2;
						_columns = 2;
						_cellCount = 4;
						break;
				}
				UpdateCellsLayout();
			}
		}

		public string Name { get; set; } = string.Empty;

		public short Rows
		{
			get => _rows;
			set
			{
				if (_rows != value)
				{
					_rows = value;
					_cellCount = value * _columns;
					UpdateCellsLayout();
				}
			}
		}

		public short Columns
		{
			get => _columns;
			set
			{
				if (_columns != value)
				{
					_columns = value;
					_cellCount = value * _rows;
					UpdateCellsLayout();
				}
			}
		}

		public int CellCount
		{

			get => _cellCount;
			set
			{
				if (_cellCount != value)
				{
					_cellCount = value;
					UpdateCellsLayout();
				}
			}
		}

		public List<Cell> Cells { get; set; } = Enumerable.Range(0, 4).Select(i => new Cell(null, (short)i)).ToList();

		private void UpdateCellsLayout()
		{
			var existingCells = Cells.OrderBy(c => c.CellIndex).ToList();
			var newCells = new List<Cell>();

			var cells = existingCells.Where(c => c.Camera != null).Take((int)_cellCount);
			if (Cells.Count < _cellCount)
			{
				Cells.AddRange(Enumerable.Range(Cells.Count, _cellCount - Cells.Count).Select(i => new Cell(null, (short)i)));
			}
			else if (Cells.Count > _cellCount)
			{
				Cells.RemoveRange(_cellCount, Cells.Count - _cellCount);
			}
		}
	}

	private class Validator : BaseFluentValidator<Model>
	{
		public Validator()
		{
			RuleFor(model => model.Name)
				.Length(3, 60)
				.WithMessage("наименование должно быть длиной от 3 до 60 символов");
		}
	}

	private bool _isValid;
	private Model _model = new();
	private Validator _validator = new();

	#region Parameters
	[CascadingParameter(Name = DrawerConsts.InstanceName)]
	private DrawerComponent Drawer { get; set; } = null!;

	[Parameter]
	[EditorRequired]
	public Guid OrganizationId { get; set; }
	#endregion

	private void SortList((int oldIndex, int newIndex) indices)
	{
		// deconstruct the tuple
		var (oldIndex, newIndex) = indices;

		var itemToMove = _model.Cells[oldIndex];
		_model.Cells.RemoveAt(oldIndex);

		if (newIndex < _model.Cells.Count)
		{
			_model.Cells.Insert(newIndex, itemToMove);
		}
		else
		{
			_model.Cells.Add(itemToMove);
		}

		// Update indices for all cells based on their new positions
		for (short i = 0; i < _model.Cells.Count; i++)
		{
			_model.Cells[i].CellIndex = i;
		}
	}

	#region [Action]
	private async Task SubmitAsync()
	{
		CreateViewUseCase.Response? response = null;
		try
		{
			var cells = _model.Cells.Where(c => c.Camera is not null && c.Camera.CameraId != Guid.Empty).Select(c => (c.Camera!.CameraId, c.CellIndex)).ToList();
			response = await ScopeFactory.MediatorSend(new CreateViewUseCase.Command(OrganizationId, _model.Name, _model.Columns, _model.Rows, _model.Grid, cells));
		}
		catch (Exception exc)
		{
			response = null;
			Logger.LogError(exc, exc.Message);
			Snackbar.Add("Не удалось создать вид из-за непредвиденной ошибки отправки на сервер. Повторите попытку и обратитесь к администратору.", MudBlazor.Severity.Error);
		}

		if (response is null) return;
		switch (response.Result)
		{
			case CreateViewUseCase.Result.Success:
				Snackbar.Add("Вид успешно создан.", MudBlazor.Severity.Success);
				await Drawer.HideAsync();
				break;
			case CreateViewUseCase.Result.ValidationError:
				Snackbar.Add("Ошибка валидации, проверьте правильность заполнения полей", MudBlazor.Severity.Error);
				break;
			case CreateViewUseCase.Result.CameraViewNameAlreadyExists:
				Snackbar.Add("Вид с данным названием уже существует", MudBlazor.Severity.Error);
				break;
			case CreateViewUseCase.Result.Unknown:
				Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(DrawerCreateComponent), nameof(CreateViewUseCase));
				Snackbar.Add($"Не удалось создать вид из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
				break;
			default:
				Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(DrawerCreateComponent), nameof(CreateViewUseCase), response.Result);
				Snackbar.Add($"Не удалось создать вид из-за непредвиденной ошибки: {response.Result}. Обратитесь к администратору.", MudBlazor.Severity.Error);
				break;
		}

	}

	private void CancelAsync() => Drawer.HideAsync();
	#endregion


	private void SetGridType(GridType type)
	{
		_model.Grid = type;
	}
}