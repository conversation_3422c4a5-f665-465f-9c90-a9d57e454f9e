namespace FFMpegNET;

public readonly struct Segment
{
    public readonly MemoryStream Stream { get; init; }
    public readonly DateTimeOffset StartTime { get; init; }
    public readonly double Duration { get; init; }

    public Segment(MemoryStream stream, DateTimeOffset startTime, double duration)
    {
        Stream = stream;
        StartTime = startTime;
        Duration = duration;
    }
}
