@using Teslametrics.App.Web.Components.DragAndDrop
@using Teslametrics.Shared
@using Teslametrics.App.Web.Features.Main.Cameras.TreeView.Organization
@using Teslametrics.App.Web.Features.Main.Cameras.TreeView.Folder
@using Teslametrics.App.Web.Features.Main.Cameras.TreeView.Camera
@inherits InteractiveBaseComponent
<div class="mud-height-full sidebar">
    <MudStack Row="true"
              Class="mb-4 pl-4 pt-2"
              Style="min-height: 56px;"
              AlignItems="AlignItems.Center">
        <MudTooltip Text="Свернуть все"
                    Arrow="true"
                    Placement="Placement.Right">
            <MudIconButton Icon="@Icons.Material.Filled.UnfoldLess"
                           OnClick="CollapseAllAsync" />
        </MudTooltip>
        <MudTooltip Text="Развернуть все"
                    Arrow="true"
                    Placement="Placement.Right">
            <MudIconButton Icon="@Icons.Material.Filled.UnfoldMore"
                           OnClick="ExpandAllAsync" />
        </MudTooltip>
        <MudSpacer />
        @if (IsLoading)
        {
            <MudProgressCircular Color="Color.Default"
                                 Indeterminate="true" />
        }
        else
        {
            <MudTooltip Text="@(_orderDirection == OrderDirection.Ascending ? "Сортировка по алфавиту" : "Сортировка против алфавита")"
                        Arrow="true"
                        Placement="Placement.Left">
                <MudBadge Icon="@(_orderDirection == OrderDirection.Ascending ? Icons.Material.Filled.ArrowUpward : Icons.Material.Filled.ArrowDownward)"
                          Color="Color.Primary"
                          Overlap="true"
                          Origin="Origin.BottomLeft"
                          Bordered="true"
                          BadgeClass="badge_position">
                    <MudIconButton Icon="@Icons.Material.Filled.SortByAlpha"
                                   OnClick="AlphabetSortToggleAsync" />
                </MudBadge>
            </MudTooltip>
            <MudIconButton OnClick="RefreshAsync"
                           Icon="@Icons.Material.Outlined.Refresh" />
            <SubscriptionErrorComponent Show="@(!_subscribing && (_subscriptionResult is null || !_subscriptionResult.IsSuccess))"
                                        RetrySubscribe="SubscribeAsync" />
        }
    </MudStack>
    <MudDivider />
    @if (IsLoading)
    {
        <MudProgressLinear Color="Color.Primary"
                           Indeterminate="true" />
    }
    else
    {
        <div style="height: 4px;"></div>
    }
    @if (Items.Any())
    {
        <DragAndDropContainer TValue="TreeItemData<Guid>"
                              CanDropAsync="CanDropAsync"
                              CanDragAsync="CanDragAsync"
                              OnDrop="OnDrop">
            <MudTreeView T="Guid"
                         Items="@Items"
                         SelectionMode="SelectionMode.ToggleSelection"
                         Class="tree overflow-auto"
                         AutoExpand="true"
                         @bind-SelectedValue="_selected">
                <ItemTemplate>
                    @{
                        var presenter = (TreeItemPresenter)context;
                        switch (presenter.Type)
                        {
                            case ItemType.Organization:
                                <OrganizationItemComponent Item="presenter"
                                                           OnSelect="SelectOrganizationAsync"
                                                           OnExpandChanged="OnOrganizationExpandChanged"
                                                           CameraCount="presenter.CameraCount"
                                                           @key="presenter.Id" />
                                break;
                            case ItemType.Folder:
                                <FolderItemComponent Item="presenter"
                                                     OrganizationId="presenter.OrganizationId"
                                                     OnSelect="SelectFolderAsync"
                                                     CameraCount="presenter.CameraCount"
                                                     @key="presenter.Id" />
                                break;
                            case ItemType.Camera:
                                <CameraItemComponent Item="presenter"
                                                     OrganizationId="presenter.OrganizationId"
                                                     OnSelect="SelectCamera"
                                                     @key="presenter.Id" />
                                break;
                        }
                    }
                </ItemTemplate>
            </MudTreeView>
        </DragAndDropContainer>
    }
    <TreeViewNoItemsFound HasItems="IsLoading || Items.Any()"
                          LastRefreshTime="_lastRefreshTime"
                          Refresh="RefreshAsync" />
    <TreeViewPreloaderComponent IsLoading="@IsLoading" />
    @if (_totalPages > 1)
    {
        <MudPagination Color="Color.Primary"
                       Count="@_totalPages"
                       Selected="@_currentPage"
                       SelectedChanged="PageChanged"
                       Class="justify-center" />
    }
</div>