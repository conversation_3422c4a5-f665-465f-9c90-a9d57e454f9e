let resizeObserver;
let intersectionObserver;
let isObserverInitialized = false;
let resizeInProgress = false;
let areObserversActive = false;

// Получаем элементы только один раз
const getElements = () => ({
	div: document.querySelector('.grid_container')
});

// Функция очистки observers
function cleanupObservers() {
	if (resizeObserver) {
		resizeObserver.disconnect();
	}
	if (intersectionObserver) {
		intersectionObserver.disconnect();
	}
	isObserverInitialized = false;
	areObserversActive = false;
	resizeInProgress = false;
}

// Функция для инициализации наблюдателей
function initObservers() {
	// Очищаем предыдущие observers если они были
	cleanupObservers();

	const { div } = getElements();

	// Проверяем наличие элементов
	if (!div) {
		console.error('Элементы не найдены на странице');
		return;
	}

	// Создаем ResizeObserver для отслеживания изменения размера
	resizeObserver = new ResizeObserver(entries => {
		if (!areObserversActive) return;

		for (let entry of entries) {
			const width = entry.contentRect.width;
			const height = entry.contentRect.height;

			if (resizeInProgress) return;

			resizeInProgress = true;

			div.style.setProperty('--width', `${width}px`);
			div.style.setProperty('--height', `${height}px`);

			resizeInProgress = false;
		}
	});

	// Создаем IntersectionObserver для отслеживания видимости элемента
	intersectionObserver = new IntersectionObserver(entries => {
		if (!areObserversActive) return;

		const isElementVisible = entries[0].isIntersecting;
		if (!isElementVisible) {
			resizeObserver.disconnect();
		} else {
			resizeObserver.observe(div);
		}
	}, { threshold: 0.1 });

	// Начинаем отслеживать видимость элемента
	intersectionObserver.observe(div);
	resizeObserver.observe(div);

	isObserverInitialized = true;
	areObserversActive = true;
}

// Экспортируем функции для использования в компоненте
export { initObservers, cleanupObservers }