using Dapper;
using FluentValidation;
using MediatR;
using System.Data;
using Teslametrics.App.Web.Services.Persistence;
using Teslametrics.App.Web.Data.Sql;
using Teslametrics.App.Web.Abstractions;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Organizations.Drawer.Edit;

public static class GetUserListUseCase
{
    public record Query(string Filter) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public List<Item> Items { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(List<Item> users)
        {
            Items = users;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Result = result;

            Items = [];
        }

        public record Item(Guid Id, string Name);
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError
    }

    public class Validator : AbstractValidator<Query>
    {
        public Validator()
        {
            RuleFor(q => q.Filter).MaximumLength(60);
        }
    }

    public class Handler : IRequestHandler<Query, Response>
    {
        private readonly IValidator<Query> _validator;
        private readonly IDbConnection _dbConnection;

        public Handler(IValidator<Query> validator,
                       IDbConnection dbConnection)
        {
            _validator = validator;
            _dbConnection = dbConnection;
        }

        public async Task<Response> Handle(Query request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            var template = SqlQueryBuilder.Create()
                .Select(Db.Users.Props.Id)
                .Select(Db.Users.Props.Name)
                .WhereIf(!string.IsNullOrEmpty(request.Filter),
                         Db.Users.Props.Name,
                         "CONCAT('%', :Filter, '%')",
                         SqlOperator.Like,
                         new { request.Filter })
                .Build(QueryType.Standard, Db.Users.Table, RowSelection.AllRows);

            var users = await _dbConnection.QueryAsync<UserModel>(template.RawSql, template.Parameters);

            return new Response(users.Select(u => new Response.Item(u.Id, u.Name)).ToList());
        }
    }

    public record UserModel(Guid Id, string Name);
}