﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!--
    Microsoft ResX Schema

    Version 2.0

    The primary goals of this format is to allow a simple XML format
    that is mostly human readable. The generation and parsing of the
    various data types are done through the TypeConverter classes
    associated with the data types.

    Example:

    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>

    There are any number of "resheader" rows that contain simple
    name/value pairs.

    Each data row contains a name, and value. The row also contains a
    type or mimetype. Type corresponds to a .NET class that support
    text/value conversion through the TypeConverter architecture.
    Classes that don't support this are serialized and stored with the
    mimetype set.

    The mimetype is used for serialized objects, and tells the
    ResXResourceReader how to depersist the object. This is currently not
    extensible. For a given mimetype the value must be set accordingly:

    Note - application/x-microsoft.net.object.binary.base64 is the format
    that the ResXResourceWriter will generate, however the reader can
    read any of the formats listed below.

    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Main.AccessControl.Users.Unlock" xml:space="preserve">
    <value>Unlock users</value>
  </data>
  <data name="Main.AccessControl.Users.Update" xml:space="preserve">
    <value>Edit user</value>
  </data>
  <data name="Main.AccessControl.Users.ForceChangePassword" xml:space="preserve">
    <value>Force the user to change their password upon login</value>
  </data>
  <data name="Main.AccessControl.Users.Read" xml:space="preserve">
    <value>Read users</value>
  </data>
  <data name="Main.AccessControl.Users.Lock" xml:space="preserve">
    <value>Lock users</value>
  </data>
  <data name="Main.AccessControl.Users.Delete" xml:space="preserve">
    <value>Delete users</value>
  </data>
  <data name="Main.AccessControl.Users.Create" xml:space="preserve">
    <value>Create user</value>
  </data>
  <data name="Main.AccessControl.Roles.Update" xml:space="preserve">
    <value>Edit roles</value>
  </data>
  <data name="Main.AccessControl.Roles.Read" xml:space="preserve">
    <value>Read roles</value>
  </data>
  <data name="Main.AccessControl.Roles.Delete" xml:space="preserve">
    <value>Delete roles</value>
  </data>
  <data name="Main.AccessControl.Roles.Create" xml:space="preserve">
    <value>Create roles</value>
  </data>
  <data name="Main.AccessControl.Organizations.Update" xml:space="preserve">
    <value>Edit organization</value>
  </data>
  <data name="Main.AccessControl.Organizations.Read" xml:space="preserve">
    <value>Read organizations</value>
  </data>
  <data name="Main.AccessControl.Organizations.Delete" xml:space="preserve">
    <value>Delete organizations</value>
  </data>
  <data name="Main.AccessControl.Organizations.Create" xml:space="preserve">
    <value>Create organizations</value>
  </data>
  <data name="Main.Cameras.Read" xml:space="preserve">
    <value>Read cameras</value>
  </data>
  <data name="Main.Cameras.Connect" xml:space="preserve">
    <value>Connect cameras</value>
  </data>
  <data name="Main.Cameras.Create" xml:space="preserve">
    <value>Create cameras</value>
  </data>
  <data name="Main.Cameras.Delete" xml:space="preserve">
    <value>Delete cameras</value>
  </data>
  <data name="Main.Cameras.Disconnect" xml:space="preserve">
    <value>Disconnect cameras</value>
  </data>
  <data name="Main.Cameras.Move" xml:space="preserve">
    <value>Move to another folder</value>
  </data>
  <data name="Main.Cameras.Update" xml:space="preserve">
    <value>Edit cameras</value>
  </data>
  <data name="Main.Folders.Create" xml:space="preserve">
    <value>Create folder</value>
  </data>
  <data name="Main.Folders.Delete" xml:space="preserve">
    <value>Delete folder</value>
  </data>
  <data name="Main.Folders.Move" xml:space="preserve">
    <value>Move folder to another folder</value>
  </data>
  <data name="Main.Folders.Read" xml:space="preserve">
    <value>Read folders</value>
  </data>
  <data name="Main.Folders.Update" xml:space="preserve">
    <value>Edit folder</value>
  </data>
  <data name="Main.CameraPresets.Update" xml:space="preserve">
    <value>Edit camera presets</value>
  </data>
  <data name="Main.CameraPresets.Create" xml:space="preserve">
    <value>Create camera presets</value>
  </data>
  <data name="Main.CameraPresets.Delete" xml:space="preserve">
    <value>Delete camera presets</value>
  </data>
  <data name="Main.CameraPresets.Read" xml:space="preserve">
    <value>Read cameras presets</value>
  </data>
  <data name="Main.CameraQuotas.Read" xml:space="preserve">
    <value>Read camera quotas</value>
  </data>
  <data name="Main.CameraQuotas.Update" xml:space="preserve">
    <value>Edit camera quotas</value>
  </data>
  <data name="Main.CameraQuotas.Create" xml:space="preserve">
    <value>Create camera quotas</value>
  </data>
  <data name="Main.CameraQuotas.Delete" xml:space="preserve">
    <value>Delete camera quotas</value>
  </data>
  <data name="Main.CameraPublicAccess.Read" xml:space="preserve">
    <value>View camera public access</value>
  </data>
  <data name="Main.CameraPublicAccess.Update" xml:space="preserve">
    <value>Edit camera public access</value>
  </data>
  <data name="Main.CameraPublicAccess.Create" xml:space="preserve">
    <value>Create camera public access</value>
  </data>
  <data name="Main.CameraPublicAccess.Delete" xml:space="preserve">
    <value>Delete camera public access</value>
  </data>
    <data name="Main.CameraViews.Read" xml:space="preserve">
    <value>View Views</value>
  </data>
  <data name="Main.CameraViews.Create" xml:space="preserve">
    <value>Create Views</value>
  </data>
  <data name="Main.CameraViews.Update" xml:space="preserve">
    <value>Edit Views</value>
  </data>
  <data name="Main.CameraViews.Delete" xml:space="preserve">
    <value>Delete Views</value>
  </data>
</root>