@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.Extensions.Localization
@using Teslametrics.App.Web.Features.Authentication
@using Teslametrics.App.Web.Features.Main.AuthToolbarComponent
@using Teslametrics.App.Web.Features.Main.NotificationsToolbar
@inherits LayoutComponentBase
<MudThemeProvider @ref="@_mudThemeProvider"
				  @bind-IsDarkMode="@_isDarkMode"
				  Theme="CustomTheme" />
<div class="d_contents">
	<CascadingValue Value="_mudThemeProvider">
		<AuthorizeView Context="layoutContext">
			<Authorized>
				<MudLayout UserAttributes="@(new Dictionary<string, object>() { { "id", "main_layout" } })"
						   Class="@(_isDarkMode ? "mud_theme_dark" : "mud_theme_light")">
					<MudAppBar Elevation="0"
							   UserAttributes="@(new Dictionary<string, object>() { { "id", "layout_toolbar" } })"
							   Class="appbar px-6">
						@* <MudToggleIconButton Toggled="@_drawerOpen" ToggledChanged="OnToggle" Icon="@Icons.Material.Filled.Menu" Color="@Color.Inherit"
							ToggledIcon="@Icons.Material.Filled.MenuOpen" ToggledColor="@Color.Inherit"
							UserAttributes="@(new Dictionary<string, object>() { { "id", "layout_menu_size_toggle" } })" /> *@
						<MudSpacer />
						<MudStack Row="true"
								  Spacing="4"
								  AlignItems="AlignItems.Center">
							<MudChip T="string"
									 Class="px-4 py-2 time_chip ma-0 bg_light_blue_surface">@(DateTime.Now.ToLongDateString())</MudChip>
							<AuthorizeView>
								<NotificationComponent />
								<AuthToolbarComponent />
							</AuthorizeView>
						</MudStack>
					</MudAppBar>
					<MudDrawer @bind-Open="_drawerOpen"
							   Elevation="1"
							   UserAttributes="@(new Dictionary<string, object>() { { "id", "layout_sidebar" } })">
						<MudNavMenu Rounded="true"
									Margin="Margin.Normal"
									Color="Color.Primary"
									Class="nav_menu pt-6">
							<MudNavLink Href="/"
										Icon="@TeslaIcons.PageIcons.Dashboard"
										Match="NavLinkMatch.All"
										Class="nav_link px-2"
										IconColor="Color.Inherit"
										title="Главная">
								Главная
							</MudNavLink>
							<AuthorizeView Policy="@(Fqdn<AppPermissions>.GetNames(AppPermissions.Main.AccessControl.Organizations.Read).Last())">
								<MudNavLink Href="/system-settings"
											Icon="@Icons.Material.Outlined.Map"
											Match="NavLinkMatch.All"
											Class="nav_link px-2"
											IconColor="Color.Inherit"
											title="Главная">
									Планы
								</MudNavLink>
							</AuthorizeView>
							<MudNavLink Href="/devices"
										Icon="@TeslaIcons.Devices.Fridge"
										Match="NavLinkMatch.Prefix"
										Class="nav_link px-2"
										IconColor="Color.Inherit"
										title="Устройства">
								Оборудование
							</MudNavLink>
							<MudNavLink Href="/incidents"
										Icon="@TeslaIcons.State.Warning"
										Match="NavLinkMatch.Prefix"
										Class="nav_link px-2"
										IconColor="Color.Inherit"
										title="Происшествия">
								Происшествия
							</MudNavLink>
							<MudNavLink Href="/incidents-dashboard"
										Icon="@TeslaIcons.PageIcons.Analytics"
										Match="NavLinkMatch.Prefix"
										Class="nav_link px-2"
										IconColor="Color.Inherit"
										title="Аналитика">
								Аналитика
							</MudNavLink>
							<AuthorizeView Policy="@AppPermissions.Main.CameraViews.Read.GetEnumPermissionString()">
								<MudNavLink Href="@RouteConstants.CameraViews"
											Icon="@Icons.Material.Filled.ViewModule"
											Match="NavLinkMatch.Prefix"
											Class="nav_link px-2"
											IconColor="Color.Inherit">
									Виды
								</MudNavLink>
							</AuthorizeView>
							<AuthorizeView
										   Policy="@($"{AppPermissions.Main.Cameras.Read.GetEnumPermissionString()},{AppPermissions.Main.Folders.Read.GetEnumPermissionString()}")">
								<MudNavLink Href="@RouteConstants.Cameras"
											Icon="@Icons.Material.Outlined.Camera"
											Match="NavLinkMatch.Prefix"
											Class="nav_link px-2"
											IconColor="Color.Inherit"
											title="Камеры">
									Камеры
								</MudNavLink>
							</AuthorizeView>
							@* <AuthorizeView Policy="@(Fqdn<AppPermissions>.GetNames(AppPermissions.Main.AccessControl.Organizations.Read).Last())">
								<MudNavLink Href="@RouteConstants.Organizations" Icon="@Icons.Material.Filled.Business" Match="NavLinkMatch.Prefix"
									Class="nav_link px-2" IconColor="Color.Inherit" title="Организации">
									Организации
								</MudNavLink>
							</AuthorizeView> *@
							<AuthorizeView Policy="@(Fqdn<AppPermissions>.GetNames(AppPermissions.Main.AccessControl.Organizations.Read).Last())">
								<MudNavLink Href="@RouteConstants.AccessControl"
											Icon="@Icons.Material.Outlined.Key"
											Match="NavLinkMatch.Prefix"
											Class="nav_link px-2"
											IconColor="Color.Inherit"
											title="Контроль доступа">
									Контроль доступа
								</MudNavLink>
							</AuthorizeView>
							<AuthorizeView Policy="@AppPermissions.Main.CameraPresets.Read.GetEnumPermissionString()">
								<MudNavLink Href="@RouteConstants.CameraPresets"
											Icon="@Icons.Material.Filled.Pattern"
											Match="NavLinkMatch.Prefix"
											Class="nav_link px-2"
											IconColor="Color.Inherit"
											title="Пресеты">
									Пресеты
								</MudNavLink>
							</AuthorizeView>
							<AuthorizeView Policy="@AppPermissions.Main.CameraPublicAccess.Read.GetEnumPermissionString()">
								<MudNavLink Href="@RouteConstants.CameraPublicAccess"
											Icon="@Icons.Material.Filled.Public"
											Match="NavLinkMatch.Prefix"
											Class="nav_link px-2"
											IconColor="Color.Inherit"
											title="Публичный доступ">
									Публичный доступ
								</MudNavLink>
							</AuthorizeView>
						</MudNavMenu>
					</MudDrawer>
					<MudMainContent UserAttributes="@(new Dictionary<string, object>() { { "id", "layout_main_content_wrapper" } })"
									Class="mud-height-full">
						@Body
					</MudMainContent>
				</MudLayout>
			</Authorized>
			<NotAuthorized>
				<MudLayout Style="height: 100vh;">
					<div class="pa-4 h-100 d-flex align-center justify-center mud-height-full mud-width-full">
						@Body
					</div>
				</MudLayout>
			</NotAuthorized>
		</AuthorizeView>
	</CascadingValue>
</div>