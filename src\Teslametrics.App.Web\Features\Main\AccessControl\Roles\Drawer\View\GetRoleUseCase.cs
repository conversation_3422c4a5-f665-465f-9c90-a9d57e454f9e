using Dapper;
using FluentValidation;
using MediatR;
using System.Data;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.Shared;
using Teslametrics.Core.Domain.AccessControl;
using Teslametrics.Core.Services.Persistence;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Roles.Drawer.View;

public static class GetRoleUseCase
{
    public record Query(Guid Id) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public Guid Id { get; init; }

        public string Name { get; init; }

        public bool IsAdmin { get; init; }

        public List<ResourcePermission> Permissions { get; init; }

        public List<User> Users { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(Guid id, string name, bool isAdmin, List<ResourcePermission> permissions, List<User> users)
        {
            Id = id;
            Name = name;
            IsAdmin = isAdmin;
            Permissions = permissions;
            Users = users;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Result = result;

            Id = Guid.Empty;
            Name = string.Empty;
            Permissions = [];
            Users = [];
        }

        public record User(Guid Id, string Username);
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        RoleNotFound
    }

    public class Validator : AbstractValidator<Query>
    {
        public Validator()
        {
            RuleFor(q => q.Id).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Query, Response>
    {
        private readonly IValidator<Query> _validator;
        private readonly IDbConnection _dbConnection;

        public Handler(IValidator<Query> validator,
                       IDbConnection dbConnection)
        {
            _validator = validator;
            _dbConnection = dbConnection;
        }

        public async Task<Response> Handle(Query request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            var template = SqlQueryBuilder.Create()
                .Select(Db.Roles.Props.Id, "RoleId")
                .Select(Db.Roles.Props.Name, "RoleName")
                .Select(Db.Roles.Props.IsAdmin)
                .Select(Db.RolePermissions.Props.Permission)
                .Select(Db.RolePermissions.Props.ResourceId)
                .Select(Db.Users.Props.Id, "UserId")
                .Select(Db.Users.Props.Name, "Username")
                .LeftJoin(Db.RolePermissions.Table, Db.RolePermissions.Props.RoleId, Db.Roles.Props.Id, SqlOperator.Equals)
                .LeftJoin(Db.UserRoles.Table, Db.UserRoles.Props.RoleId, Db.Roles.Props.Id, SqlOperator.Equals)
                .LeftJoin(Db.Users.Table, Db.Users.Props.Id, Db.UserRoles.Props.UserId, SqlOperator.Equals)
                .Where(Db.Roles.Props.Id, ":Id", SqlOperator.Equals, parameters: new { request.Id })
                .Build(QueryType.Standard, Db.Roles.Table, RowSelection.AllRows);

            var roles = await _dbConnection.QueryAsync<RoleModel, PermissionModel, UserModel, RoleModel>(template.RawSql, (role, permission, user) =>
            {
                if (permission is not null)
                {
                    role.Permissions.Add(permission);
                }

                if (user is not null)
                {
                    role.Users.Add(user);
                }

                return role;
            },
            template.Parameters, splitOn: "Permission, UserId");

            if (!roles.Any())
            {
                return new Response(Result.RoleNotFound);
            }

            var result = roles.GroupBy(r => r.RoleId)
                .Select(g =>
                {
                    var groupedRole = g.First();
                    groupedRole.Permissions = g.SelectMany(r => r.Permissions).Distinct().ToList();

                    groupedRole.Users = g.SelectMany(r => r.Users)
                                            .GroupBy(u => u.UserId)
                                            .Select(g => g.First())
                                            .ToList();

                    return groupedRole;
                });

            var role = result.Single();

            return new Response(role.RoleId,
                                role.RoleName,
                                role.IsAdmin,
                                role.Permissions.Select(p => new ResourcePermission(new ResourceId(p.ResourceId), new Permission(p.Permission))).ToList(),
                                role.Users.Select(u => new Response.User(u.UserId, u.Username)).ToList());
        }
    }

    public record RoleModel(Guid RoleId, string RoleName, bool IsAdmin)
    {
        public List<PermissionModel> Permissions { get; set; } = [];
        public List<UserModel> Users { get; set; } = [];
    }

    public record PermissionModel(string Permission, Guid ResourceId);

    public record UserModel(Guid UserId, string Username);
}