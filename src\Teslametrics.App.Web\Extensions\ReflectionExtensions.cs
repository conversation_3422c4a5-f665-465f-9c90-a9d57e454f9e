﻿using System.Reflection;

namespace Teslametrics.App.Web.Extensions;

public static class ReflectionExtensions
{
    public static async Task<object?> InvokeAsync(this MethodInfo @this, object? obj, params object?[]? parameters)
    {
        dynamic? awaitable = @this.Invoke(obj, parameters);
        if (awaitable is not null)
        {
            await awaitable;
            return awaitable.GetAwaiter().GetResult();
        }
        else
        {
            return null;
        }
    }

    public static async Task InvokeVoidAsync(this MethodInfo @this, object? obj, params object?[]? parameters)
    {
        dynamic? awaitable = @this.Invoke(obj, parameters);
        if (awaitable is not null)
        {
            await awaitable;
        }
        else
        {
            return;
        }
    }
}
