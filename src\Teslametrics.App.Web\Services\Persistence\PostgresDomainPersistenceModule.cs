using System.Data;
using Dapper;
using Microsoft.EntityFrameworkCore;
using Npgsql;
using Teslametrics.App.Web.Domain.AccessControl;
using Teslametrics.App.Web.Domain.AccessControl.Organizations;
using Teslametrics.App.Web.Domain.AccessControl.Users;
using Teslametrics.App.Web.Domain.CameraPresets;
using Teslametrics.App.Web.Domain.Cameras;
using Teslametrics.App.Web.Domain.CameraViews;
using Teslametrics.App.Web.Domain.Folders;
using Teslametrics.App.Web.Domain.Incidents;
using Teslametrics.App.Web.Domain.Notifications;
using Teslametrics.App.Web.Domain.PublicLinks;
using Teslametrics.Core.Services.Persistence;

namespace Teslametrics.App.Web.Services.Persistence;

public static class PostgresDomainPersistenceModule
{
    public static void Install(IServiceCollection services, IConfiguration configuration)
    {
        var connectionString = configuration.GetConnectionString("Default") ?? throw new InvalidOperationException("Connection string not found.");

        SqlMapper.AddTypeHandler(typeof(DateTimeOffset), new DateTimeOffsetTypeHandler());

        DefaultTypeMap.MatchNamesWithUnderscores = true;

        services.AddDbContextPool<CommandAppDbContext>(options =>
        {
            options.UseNpgsql(connectionString, b => b.MigrationsAssembly(typeof(Program).Assembly.FullName));
            options.UseSnakeCaseNamingConvention();
        });

        services.AddTransient<IDbConnection>(s => new NpgsqlConnection(connectionString));

        services.AddTransient<IOrganizationRepository, OrganizationRepository>();

        services.AddTransient<IUserRepository, UserRepository>();

        services.AddTransient<IPermissionRepository, PermissionRepository>();

        services.AddTransient<ICameraRepository, CameraRepository>();

        services.AddTransient<IFolderRepository, FolderRepository>();

        services.AddTransient<IPresetRepository, PresetRepository>();

        services.AddTransient<IPublicLinkRepository, PublicLinkRepository>();

        services.AddTransient<ICameraViewRepository, CameraViewRepository>();

        services.AddTransient<IIncidentRepository, IncidentRepository>();

        services.AddTransient<IIncidentNotificationRepository, IncidentNotificationRepository>();
    }
}