﻿@inherits InteractiveBaseComponent
@if (IsLoading)
{
    <MudProgressLinear Indeterminate="true" />
    @if (!IsLoading && _response is null || _response?.Items.Count == 0)
    {
        <MudSkeleton Width="60%"
                     Height="calc(var(--mud-typography-subtitle1-lineheight) * var(--mud-typography-subtitle1-size))" />
        <MudSkeleton Width="60%"
                     Height="calc(var(--mud-typography-subtitle1-lineheight) * var(--mud-typography-subtitle1-size))" />
        <MudSkeleton Width="60%"
                     Height="calc(var(--mud-typography-subtitle1-lineheight) * var(--mud-typography-subtitle1-size))" />
        <MudSkeleton Width="60%"
                     Height="calc(var(--mud-typography-subtitle1-lineheight) * var(--mud-typography-subtitle1-size))" />
        <MudSkeleton Width="60%"
                     Height="calc(var(--mud-typography-subtitle1-lineheight) * var(--mud-typography-subtitle1-size))" />
    }
}

@if (!IsLoading && _response is not null && _response.IsSuccess)
{
    <MudStack AlignItems="AlignItems.Center"
              Row="true"
              Class="mr-n4">
        <MudStack Row="true"
                  Spacing="1">
            Обновлено:
            <TimePassedComponent InputTime="@_lastRefreshTime"
                                 TooltipText="@($"Время последнего обновления: {_lastRefreshTime.ToLocalTime()}")" />
        </MudStack>
        <MudSpacer />
        @if (!_subscribing && (_subscriptionResult is null || !_subscriptionResult.IsSuccess))
        {
            <MudTooltip Arrow="true"
                        Placement="Placement.Start"
                        Text="Ошибка подписки на события">
                <MudIconButton OnClick="SubscribeAsync"
                               Icon="@Icons.Material.Filled.ErrorOutline"
                               Color="Color.Error" />
            </MudTooltip>
        }
        <MudIconButton OnClick="RefreshAsync"
                       Icon="@Icons.Material.Filled.Refresh"
                       Color="Color.Primary" />
    </MudStack>
    <MudStack Spacing="16">
        @foreach (var item in _response.Items)
        {
            <MudStack @key="item.Id">
                <MudText Typo="Typo.subtitle1"
                         Color="Color.Primary"><b>@item.Name</b></MudText>
                <CameraPublicAccessComponent AccessId="item.Id" />
            </MudStack>
        }
        <NoItemsFoundComponent HasItems="_response.Items.Count > 0"
                               RefreshAsync="RefreshAsync"
                               LastRefreshTime="_lastRefreshTime" />
    </MudStack>
}

@if (!IsLoading && _response is null || (!_response?.IsSuccess ?? true))
{
    <MudStack>
        <MudText Typo="Typo.subtitle1">Ничего не нашлось</MudText>
    </MudStack>
}