@using Teslametrics.Shared
@inherits InteractiveBaseComponent
<DrawerHeader>
    <MudText Typo="Typo.h3">Создание пресета</MudText>
</DrawerHeader>
@if (IsLoading)
{
    <MudProgressLinear Indeterminate="true" />
}
else
{
    <div style="height: 4px;"></div>
}
<div class="px-4 py-4">
    <MudForm Model="_model"
             Validation="_validator.ValidateValue"
             @bind-IsValid="_isValid"
             ValidationDelay="0"
             Class="flex-1"
             OverrideFieldValidation="true"
             UserAttributes="@(new Dictionary<string, object>() { { "autocomplete", "off" }, { "aria-autocomplete", "none" }, { "role", "presentation" } })"
             Spacing="8">
        <FormSectionComponent Title="Описание пресета"
                              Subtitle="Настройки, которые влияют только на восприятие человеком">
            <MudTextField @bind-Value="_model.Name"
                          For="@(() => _model.Name)"
                          Clearable="true"
                          InputType="InputType.Text"
                          Immediate="true"
                          Label="Наименование"
                          HelperText="Необходимо для идентификации пресета человеком"
                          RequiredError="Данное поле обязательно"
                          Required="true"
                          @ref="_nameFieldRef" />
        </FormSectionComponent>
        <PresetFormComponent Title="Настройки потока архива"
                             Subtitle="Настройки, которые влияют на параметры хранения записей"
                             PresetConfig="_model.ArchiveStreamConfig" />
        <PresetFormComponent Title="Настройки потока видов"
                             Subtitle="Настройки, которые влияют на восприятие человеком"
                             PresetConfig="_model.ViewStreamConfig" />
        <PresetFormComponent Title="Настройки потока публичного доступа"
                             Subtitle="Настройки, которые влияют на восприятие человеком"
                             PresetConfig="_model.PublicStreamConfig" />
    </MudForm>
</div>
<DrawerActions>
    <MudSpacer />
    <MudButton OnClick="CancelAsync"
               Variant="Variant.Outlined"
               StartIcon="@Icons.Material.Outlined.Close">Закрыть</MudButton>
    <AuthorizeView Policy="@AppPermissions.Main.CameraPresets.Create.GetEnumPermissionString()">
        <MudButton OnClick="SubmitAsync"
                   Disabled="@(!_isValid || IsLoading)"
                   Color="Color.Secondary"
                   Variant="Variant.Outlined">Сохранить</MudButton>
    </AuthorizeView>
</DrawerActions>