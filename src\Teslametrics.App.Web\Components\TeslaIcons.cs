using System.Security.AccessControl;

namespace Teslametrics.App.Web.Components;

public static class TeslaIcons
{
	public static class Grid
	{
		public const string Custom = "<svg width=\"100%\" height=\"100%\" fill=\"currentColor\" viewBox=\"0 0 160 160\" xmlns=\"http://www.w3.org/2000/svg\" baseProfile=\"full\" version=\"1.1\"><g><rect height=\"65\" width=\"65\" y=\"10\" x=\"10\"/><rect height=\"65\" width=\"65\" y=\"10\" x=\"85\"/><rect height=\"65\" width=\"65\" y=\"85\" x=\"10\"/><rect height=\"65\" width=\"65\" y=\"85\" x=\"85\"/></g></svg>";
		public const string Grid1Plus5 = "<svg width=\"100%\" height=\"100%\" xmlns=\"http://www.w3.org/2000/svg\" baseProfile=\"full\" version=\"1.1\" fill=\"currentColor\" viewBox=\"0 0 160 160\"><g><rect height=\"40\" width=\"40\" y=\"10\" x=\"110\"/><rect height=\"90\" width=\"90\" y=\"10\" x=\"10\"/><rect height=\"40\" width=\"40\" y=\"60\" x=\"110\"/><rect height=\"40\" width=\"40\" y=\"110\" x=\"110\"/><path d=\"m60,109.83553l40,0l0,40l-40,0l0,-40z\"/><path d=\"m10.32877,110l40,0l0,40l-40,0l0,-40z\"/></g></svg>";

		public const string Grid2Plus8 = "<svg xmlns=\"http://www.w3.org/2000/svg\" baseProfile=\"full\" version=\"1.1\" width=\"100%\" height=\"100%\" fill=\"currentColor\" viewBox=\"0 0 160 160\"><g><rect height=\"65\" width=\"65\" y=\"9.67105\" x=\"10\"/><rect height=\"65\" width=\"65\" y=\"10\" x=\"86\"/><rect height=\"27\" width=\"27\" y=\"85\" x=\"10\"/><rect height=\"27\" width=\"27\" y=\"85\" x=\"48\"/><rect height=\"27\" width=\"27\" y=\"85\" x=\"86\"/><rect height=\"27\" width=\"27\" y=\"85\" x=\"124\"/><rect height=\"27\" width=\"27\" y=\"123\" x=\"10\"/><rect height=\"27\" width=\"27\" y=\"123\" x=\"48\"/><rect height=\"27\" width=\"27\" y=\"123\" x=\"86\"/><rect height=\"27\" width=\"27\" y=\"123\" x=\"124\"/></g></svg>";

		public const string Grid1Plus12 = "<svg xmlns=\"http://www.w3.org/2000/svg\" baseProfile=\"full\" version=\"1.1\" width=\"100%\" height=\"100%\" fill=\"currentColor\" viewBox=\"0 0 160 160\"><g><rect height=\"65\" width=\"65\" y=\"9.67105\" x=\"10\" /><rect height=\"27\" width=\"27\" y=\"85.23026\" x=\"10\" /><rect height=\"27\" width=\"27\" y=\"85.23025\" x=\"48\" /><rect height=\"27\" width=\"27\" y=\"85.06578\" x=\"86\" /><rect height=\"27\" width=\"27\" y=\"85.06578\" x=\"124\" /><rect height=\"27\" width=\"27\" y=\"123\" x=\"10\" /><rect height=\"27\" width=\"27\" y=\"123\" x=\"48\" /><rect height=\"27\" width=\"27\" y=\"123\" x=\"86\" /><rect height=\"27\" width=\"27\" y=\"123\" x=\"124\" /><rect height=\"27\" width=\"27\" y=\"10\" x=\"86\" /><rect height=\"27\" width=\"27\" y=\"10\" x=\"124\" /><rect height=\"27\" width=\"27\" y=\"48\" x=\"86\" /><rect height=\"27\" width=\"27\" y=\"48\" x=\"124\" /></g></svg>";

		public const string Grid1Plus7 = "<svg width=\"100%\" height=\"100%\" fill=\"currentColor\" viewBox=\"0 0 160 160\" xmlns=\"http://www.w3.org/2000/svg\" baseProfile=\"full\" version=\"1.1\"><g><rect height=\"103\" width=\"103\" y=\"10\" x=\"10\" /><rect height=\"27\" width=\"27\" y=\"85\" x=\"124\"/><rect height=\"27\" width=\"27\" y=\"123\" x=\"10\"/><rect height=\"27\" width=\"27\" y=\"123\" x=\"48\"/><rect height=\"27\" width=\"27\" y=\"123\" x=\"86\"/><rect height=\"27\" width=\"27\" y=\"123\" x=\"124\"/><rect height=\"27\" width=\"27\" y=\"10\" x=\"124\"/><rect height=\"27\" width=\"27\" y=\"48\" x=\"124\"/></g></svg>";
		public const string Grid3Plus4 = "<svg width=\"100%\" height=\"100%\" fill=\"currentColor\" viewBox=\"0 0 160 160\" xmlns=\"http://www.w3.org/2000/svg\" baseProfile=\"full\" version=\"1.1\"><g><rect height=\"65\" width=\"65\" y=\"10\" x=\"10\"/><rect height=\"27\" width=\"27\" y=\"85\" x=\"86\"/><rect height=\"27\" width=\"27\" y=\"85\" x=\"124\"/><rect height=\"27\" width=\"27\" y=\"123\" x=\"86\"/><rect height=\"27\" width=\"27\" y=\"123\" x=\"124\"/><rect height=\"65\" width=\"65\" y=\"10\" x=\"86\"/><rect height=\"65\" width=\"65\" y=\"85\" x=\"10\"/></g></svg>";
	}

	public static class Sensors
	{
		public const string Door = "<svg width=\"24\" height=\"24\" viewBox=\"0 0 12 12\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M1 11H11M3.5 6.5H3.505M3 1H9C9.55228 1 10 1.44772 10 2V11H2L2 2C2 1.44772 2.44771 1 3 1Z\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>";

		public const string Temperature = "<svg width=\"24\" height=\"24\" viewBox=\"0 0 9 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M6.33342 9.83984V2.33317C6.33342 1.89114 6.15782 1.46722 5.84526 1.15466C5.5327 0.842099 5.10878 0.666504 4.66675 0.666504C4.22472 0.666504 3.8008 0.842099 3.48824 1.15466C3.17568 1.46722 3.00008 1.89114 3.00008 2.33317V9.83984C2.46493 10.1974 2.05896 10.7176 1.84213 11.3236C1.6253 11.9296 1.60909 12.5892 1.79592 13.2051C1.98274 13.821 2.36269 14.3605 2.87964 14.7439C3.39659 15.1273 4.02314 15.3343 4.66675 15.3343C5.31036 15.3343 5.93691 15.1273 6.45386 14.7439C6.97081 14.3605 7.35075 13.821 7.53758 13.2051C7.7244 12.5892 7.7082 11.9296 7.49137 11.3236C7.27453 10.7176 6.86856 10.1974 6.33342 9.83984Z\" stroke=\"currentColor\" stroke-width=\"1.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>";

		public const string Leak = "<svg width=\"24\" height=\"24\" viewBox=\"0 0 16 14\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M7.33301 5.83366H3.33301V2.50033H8.74676M7.33301 5.83366H12.6663V2.50033H8.74676M7.33301 5.83366L8.74676 2.50033M7.99842 8.50033L9.41342 9.86757C9.69326 10.1378 9.88387 10.4821 9.96115 10.857C10.0384 11.2319 9.9989 11.6205 9.84756 11.9737C9.69622 12.3269 9.43988 12.6288 9.11095 12.8412C8.78202 13.0536 8.39529 13.167 7.99967 13.167C7.60406 13.167 7.21733 13.0536 6.8884 12.8412C6.55947 12.6288 6.30313 12.3269 6.15179 11.9737C6.00045 11.6205 5.96092 11.2319 6.0382 10.857C6.11548 10.4821 6.30609 10.1378 6.58593 9.86757L7.99842 8.50033ZM3.33301 7.16699V1.16699H1.33301V7.16699H3.33301ZM12.6663 1.16699V7.16699H14.6663V1.16699H12.6663Z\" stroke=\"currentColor\" stroke-width=\"1.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>";

		public const string Humidity = "<svg width=\"24\" height=\"24\" viewBox=\"0 0 14 15\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M6.99992 1.29297L10.7733 5.0663C11.5195 5.81204 12.0278 6.76235 12.2339 7.79701C12.4399 8.83167 12.3345 9.9042 11.9309 10.8789C11.5274 11.8537 10.8438 12.6868 9.96666 13.273C9.08953 13.8592 8.05824 14.1721 7.00326 14.1721C5.94828 14.1721 4.91699 13.8592 4.03985 13.273C3.16272 12.6868 2.47913 11.8537 2.07557 10.8789C1.672 9.9042 1.56659 8.83167 1.77266 7.79701C1.97873 6.76235 2.48703 5.81204 3.23326 5.0663L6.99992 1.29297Z\" stroke=\"currentColor\" stroke-width=\"1.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>";

		public const string Wifi = "<svg width=\"24\" height=\"24\" viewBox=\"0 0 12 10\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M8.11991 2.87984C8.39884 3.15846 8.62012 3.48933 8.77109 3.85352C8.92206 4.21772 8.99977 4.6081 8.99977 5.00234C8.99977 5.39659 8.92206 5.78697 8.77109 6.15117C8.62012 6.51536 8.39884 6.84622 8.11991 7.12484M3.87991 7.11984C3.60098 6.84122 3.3797 6.51036 3.22873 6.14616C3.07776 5.78197 3.00005 5.39159 3.00005 4.99734C3.00005 4.6031 3.07776 4.21272 3.22873 3.84852C3.3797 3.48433 3.60098 3.15346 3.87991 2.87484M9.53491 1.46484C10.4723 2.40248 10.9988 3.67402 10.9988 4.99984C10.9988 6.32566 10.4723 7.5972 9.53491 8.53484M2.46491 8.53484C1.52755 7.5972 1.00098 6.32566 1.00098 4.99984C1.00098 3.67402 1.52755 2.40248 2.46491 1.46484M6.99991 4.99984C6.99991 5.55213 6.55219 5.99984 5.99991 5.99984C5.44762 5.99984 4.99991 5.55213 4.99991 4.99984C4.99991 4.44756 5.44762 3.99984 5.99991 3.99984C6.55219 3.99984 6.99991 4.44756 6.99991 4.99984Z\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>";

		public const string Power = "<svg width=\"24\" height=\"24\" viewBox=\"0 0 12 8\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M2.5 7H1.5C1.23478 7 0.98043 6.89464 0.792893 6.70711C0.605357 6.51957 0.5 6.26522 0.5 6V2C0.5 1.73478 0.605357 1.48043 0.792893 1.29289C0.98043 1.10536 1.23478 1 1.5 1H3.095M7.5 1H8.5C8.76522 1 9.01957 1.10536 9.20711 1.29289C9.39464 1.48043 9.5 1.73478 9.5 2V6C9.5 6.26522 9.39464 6.51957 9.20711 6.70711C9.01957 6.89464 8.76522 7 8.5 7H6.905M11.5 4.5V3.5M5.5 1L3.5 4H6.5L4.5 7\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>";

	}

	public static class State
	{
		public const string Warning = "<svg width=\"24\" height=\"24\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M8.00004 5.3335V8.00016M8.00004 10.6668H8.00671M14.6667 8.00016C14.6667 11.6821 11.6819 14.6668 8.00004 14.6668C4.31814 14.6668 1.33337 11.6821 1.33337 8.00016C1.33337 4.31826 4.31814 1.3335 8.00004 1.3335C11.6819 1.3335 14.6667 4.31826 14.6667 8.00016Z\" stroke=\"currentColor\" stroke-width=\"1.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>";

		public const string Success = "<svg width=\"24\" height=\"24\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M14.6667 7.38674V8.00007C14.6659 9.43769 14.2004 10.8365 13.3396 11.988C12.4788 13.1394 11.2689 13.9817 9.89028 14.3893C8.51166 14.797 7.03821 14.748 5.68969 14.2498C4.34116 13.7516 3.18981 12.8308 2.40735 11.6248C1.62488 10.4188 1.25323 8.99211 1.34783 7.55761C1.44242 6.12312 1.99818 4.75762 2.93223 3.66479C3.86628 2.57195 5.12856 1.81033 6.53083 1.4935C7.9331 1.17668 9.40022 1.32163 10.7134 1.90674M14.6667 2.66674L8.00004 9.34007L6.00004 7.34007\" stroke=\"currentColor\" stroke-width=\"1.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>";
	}

	public static class Devices
	{
		public const string Fridge = "<svg width=\"24\" height=\"24\" viewBox=\"0 0 14 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M1.66638 14.6668H12.3331M3.66639 8.66683H3.67305V9.3335M1.66638 6.66683C1.66638 6.66683 1.66638 11.6668 1.66638 12.6668C1.66638 13.6668 2.33305 14.0002 2.99972 14.0002C3.66638 14.0002 10.333 14.0002 10.9997 14.0002C11.6664 14.0002 12.333 13.6668 12.333 12.6668C12.333 11.6668 12.333 6.66683 12.333 6.66683M1.66638 6.66683L1.66638 2.66683C1.66638 1.93045 2.26334 1.3335 2.99972 1.3335H10.9997C11.7361 1.3335 12.333 1.93045 12.333 2.66683V6.66683M1.66638 6.66683H12.333M3.67305 4.00016V4.66683\" stroke=\"currentColor\" stroke-width=\"1.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>";

		public const string Camera = "<svg width=\"24\" height=\"24\" viewBox=\"0 0 16 12\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M15.3333 2.66683L10.6666 6.00016L15.3333 9.3335V2.66683Z\" stroke=\"currentColor\" stroke-width=\"1.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/><path d=\"M9.33329 1.3335H1.99996C1.26358 1.3335 0.666626 1.93045 0.666626 2.66683V9.3335C0.666626 10.0699 1.26358 10.6668 1.99996 10.6668H9.33329C10.0697 10.6668 10.6666 10.0699 10.6666 9.3335V2.66683C10.6666 1.93045 10.0697 1.3335 9.33329 1.3335Z\" stroke=\"currentColor\" stroke-width=\"1.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>";
	}

	public static class Notifications
	{
		public const string Bell = "<svg width=\"24\" height=\"24\" viewBox=\"0 0 20 22\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M11.73 20C11.5542 20.3031 11.3018 20.5547 10.9982 20.7295C10.6946 20.9044 10.3504 20.9965 10 20.9965C9.64962 20.9965 9.30539 20.9044 9.00177 20.7295C8.69816 20.5547 8.44581 20.3031 8.27 20M16 7C16 5.4087 15.3679 3.88258 14.2426 2.75736C13.1174 1.63214 11.5913 1 10 1C8.4087 1 6.88258 1.63214 5.75736 2.75736C4.63214 3.88258 4 5.4087 4 7C4 14 1 16 1 16H19C19 16 16 14 16 7Z\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>";
	}

	public static class Profile
	{
		public const string UserOutline = "<svg width=\"24\" height=\"24\" viewBox=\"0 0 18 20\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M17 19V17C17 15.9391 16.5786 14.9217 15.8284 14.1716C15.0783 13.4214 14.0609 13 13 13H5C3.93913 13 2.92172 13.4214 2.17157 14.1716C1.42143 14.9217 1 15.9391 1 17V19M13 5C13 7.20914 11.2091 9 9 9C6.79086 9 5 7.20914 5 5C5 2.79086 6.79086 1 9 1C11.2091 1 13 2.79086 13 5Z\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>";
	}

	public static class PageIcons
	{
		public const string Dashboard = "<svg width=\"24\" height=\"24\" viewBox=\"0 0 14 14\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M5.66667 1H1V5.66667H5.66667V1Z\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/><path d=\"M13 1H8.33333V5.66667H13V1Z\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/><path d=\"M13 8.33333H8.33333V13H13V8.33333Z\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/><path d=\"M5.66667 8.33333H1V13H5.66667V8.33333Z\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>";

		public const string Analytics = "<svg width=\"24\" height=\"24\" viewBox=\"0 0 10 12\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M5 11.3332V4.6665M9 11.3332V0.666504M1 11.3332V8.6665\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>";
	}

}
