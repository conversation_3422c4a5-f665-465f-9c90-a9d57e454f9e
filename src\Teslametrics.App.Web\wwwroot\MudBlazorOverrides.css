﻿/* :root {
	--mud-typography-input-size: 0.75rem;
	 12px 
}
*/
.mud-card-header .mud-card-header-actions {
	align-self: center !important;
	margin-bottom: -8px;
}

/*.mud-paper.mud-card.mud-elevation-0 {
	border-radius: 16px;
	border: 1px solid #E7E9EE;
}

.mud_theme_dark .mud-card {
	border: 1px solid #434345;
}
*/

.mud_theme_dark .mud-paper,
.mud_theme_light .mud-paper {
	border-radius: 16px;
}

.mud_theme_light {
	--mud-palette-lines-default: #E7E9EE;
}

.mud_theme_light .mud-typography-body1 {
	color: rgba(92, 97, 102, 1);
}

.mud_theme_dark {
	--mud-palette-lines-default: #434345;
}

.mud_theme_light .mud-typography-subtitle1 .mud-paper {
	color: rgba(92, 97, 102, 1);
}

.mud-input.mud-input-outlined .mud-input-outlined-border {
	border-radius: 8px !important;
}

.flex-grow-all>* {
	flex: 1;
}

.mud_theme_dark,
.mud_theme_light {
	--mud-palette-table-lines: rgba(223, 232, 241, 1) !important;
	--mud-palette-appbar-background: none;
}

/* .mud-select::after {
	content: " ";
	display: block;
	height: 20px;
} */

/* .mud-input>input.mud-input-root-outlined,
div.mud-input-slot.mud-input-root-outlined {
	padding: 13.5px 16px !important;
}

.mud-input.mud-input-outlined.mud-shrink {
	height: 40px;
}

.mud-input-control>.mud-input-control-input-container>.mud-input-label-inputcontrol {
	/* Label у outlined input
font-size: var(--mud-typography-input-size) !important;
font-family: var(--mud-typography-input-family) !important;
font-weight: var(--mud-typography-input-weight) !important;
line-height: var(--mud-typography-input-lineheight) !important;
letter-spacing: var(--mud-typography-input-letterspacing) !important;
text-transform: var(--mud-typography-input-text-transform) !important;
transform: translate(16px, 14px) scale(1);
}
 */
.overflow-auto {
	scrollbar-gutter: stable;
}

.mud-input-control.mud-input-control-margin-dense>.mud-input-control-input-container>.mud-input-label-inputcontrol {
	font-size: 12px;
	line-height: 1.1 !important;
	font-weight: 500;
}

.mud_theme_light .mud-input-control.mud-input-control-margin-dense>.mud-input-control-input-container>.mud-input-label-inputcontrol {
	color: var(--color-neutral-70);
}