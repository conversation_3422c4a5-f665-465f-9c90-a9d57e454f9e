using System.Reactive;
using Microsoft.AspNetCore.Components;
using Teslametrics.App.Web.Exceptions;
using Teslametrics.App.Web.Extensions;

namespace Teslametrics.App.Web.Features.Main.CameraViews.Drawer.Create.GridItem;

public partial class GridItemComponent
{
	public record CameraViewCell(Guid CameraId, string Name);
	private MudBlazor.MudAutocomplete<CameraViewCell>? _ref;

	[Parameter]
	public CameraViewCell? Camera { get; set; }

	[Parameter]
	public EventCallback<CameraViewCell?> CameraChanged { get; set; }

	[Parameter]
	[EditorRequired]
	public Guid OrganizationId { get; set; }

	private bool _subscribing;
	private DateTime _lastRefreshTime;

	private SubscribeCameraListUseCase.Response? _subscriptionResult;
	private GetCameraListUseCase.Response? _listResponse;

	private async Task SubscribeAsync()
	{
		try
		{
			Unsubscribe();

			await SetSubscribingAsync(true);
			_subscriptionResult = await ScopeFactory.MediatorSend(new SubscribeCameraListUseCase.Request(Observer.Create<object>(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OnError), OrganizationId));
			await SetSubscribingAsync(false);
			switch (_subscriptionResult.Result)
			{
				case SubscribeCameraListUseCase.Result.Success:
					CompositeDisposable.Add(_subscriptionResult.Subscription!);
					break;
				case SubscribeCameraListUseCase.Result.ValidationError:
					Snackbar.Add("Ошибка валидации при подписке на события", MudBlazor.Severity.Error);
					break;
				case SubscribeCameraListUseCase.Result.Unknown:
				default:
					throw new Exception($"Unexpected result in {nameof(SubscribeCameraListUseCase)}: {_subscriptionResult.Result}");
			}
		}
		catch (Exception ex)
		{
			await SetSubscribingAsync(false);
			Snackbar.Add("Не удалось получить подписку на события списка камер из-за непредвиденной ошибки. Повторите попытку", MudBlazor.Severity.Error);
			Logger.LogError(ex, ex.Message);
		}
	}

	private void Unsubscribe()
	{
		if (_subscriptionResult?.Subscription is not null)
		{
			CompositeDisposable.Remove(_subscriptionResult.Subscription);
			_subscriptionResult.Subscription.Dispose();
		}
	}

	protected Task SetSubscribingAsync(bool isLoading = true) => UpdateViewAsync(() =>
	{
		_subscribing = isLoading;
	});

	private async Task<IEnumerable<CameraViewCell>> SearchAsync(string value, CancellationToken token)
	{
		try
		{
			await SetLoadingAsync(true);
			_lastRefreshTime = DateTime.Now;
			var userId = await GetCurrentUserIdAsync() ?? throw new NotAuthorizedException();
			_listResponse = await ScopeFactory.MediatorSend(new GetCameraListUseCase.Query(OrganizationId, userId, value), cancellationToken: token);
			switch (_listResponse.Result)
			{
				case GetCameraListUseCase.Result.Success:
					return _listResponse.Items.Select(x => new CameraViewCell(x.Id, x.Name));
				case GetCameraListUseCase.Result.Unknown:
				default:
					throw new Exception($"Unexpected result in {nameof(GetCameraListUseCase)}: {_listResponse.Result}");
			}
		}
		catch (Exception exc)
		{
			Logger.LogError(exc, exc.Message);
			Snackbar.Add("Не удалось получить список камер из-за непредвиденной ошибки.", MudBlazor.Severity.Error);
			return Enumerable.Empty<CameraViewCell>();
		}
		finally
		{
			await SetLoadingAsync(false);
		}
	}

	#region [Event Handlers]
	private async Task OnSelectedValudeChanged(CameraViewCell? value)
	{
		Camera = value;

		await CameraChanged.InvokeAsync(Camera);

		if (value is null)
		{
			Unsubscribe();
		}
	}

	private async void OnAppEventHandler(object appEvent)
	{
		if (_ref is null) return;

		switch (appEvent)
		{
			case SubscribeCameraListUseCase.UpdatedEvent updatedEto:
				if (_ref.Open)
					await _ref.ForceUpdate();
				break;
			case SubscribeCameraListUseCase.DeletedEvent deleteEto:
				if (deleteEto.CameraId == Camera?.CameraId)
				{
					Camera = null;
					await CameraChanged.InvokeAsync(Camera);
				}
				if (_ref.Open)
					await _ref.ForceUpdate();
				break;
			default:
				break;
		}

		await _ref.ForceUpdate();
	}

	private void OnError(Exception exc)
	{
		Snackbar.Add("Ошибка при подписке на события", MudBlazor.Severity.Error);
		Logger.LogError(exc, exc.Message);
	}

	private async Task OnOpenChangedAsync(bool value)
	{
		if (value)
		{
			await SubscribeAsync();
		}

		if (!value && Camera is null)
		{
			Unsubscribe();
		}
	}
	#endregion [Event Handlers]
}
