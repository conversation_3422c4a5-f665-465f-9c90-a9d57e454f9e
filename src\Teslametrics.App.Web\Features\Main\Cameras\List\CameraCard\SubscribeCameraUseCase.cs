using System.Reactive.Linq;
using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Domain.Cameras.Events;
using Teslametrics.App.Web.Exceptions;
using Teslametrics.App.Web.Orleans.Camera.Events;
using Teslametrics.App.Web.Services.DomainEventBus;

namespace Teslametrics.App.Web.Features.Main.Cameras.List.CameraCard;

public static class SubscribeCameraUseCase
{
    public record Request(IObserver<object> Observer, Guid CameraId) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public IDisposable? Subscription { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(IDisposable subscription)
        {
            Subscription = subscription;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Subscription = null;
            Result = result;
        }
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        CameraNotFound
    }

    public record ConnectingEvent(Guid Id);
    public record ConnectedEvent(Guid Id);
    public record ReconnectingEvent(Guid Id);
    public record DisconnectedEvent(Guid Id);
    public record UpdatedEvent(Guid Id); // У камеры обновилось название, статус, ссылка и т.д.

    public class Validator : AbstractValidator<Request>
    {
        public Validator()
        {
            RuleFor(r => r.Observer).NotEmpty();
            RuleFor(r => r.CameraId).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Request, Response>
    {
        private readonly IValidator<Request> _validator;
        private readonly IDomainEventBus _domainEventBus;

        public Handler(IValidator<Request> validator,
                       IDomainEventBus domainEventBus)
        {
            _validator = validator;
            _domainEventBus = domainEventBus;
        }

        public async Task<Response> Handle(Request request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            var eventStream = await _domainEventBus.GetEventStreamAsync();

            var subscription = eventStream
                .Where(e => e switch
                {
                    CameraStartingEvent @event => @event.Id == request.CameraId,
                    CameraRunningEvent @event => @event.Id == request.CameraId,
                    CameraProblemEvent @event => @event.Id == request.CameraId,
                    CameraStoppedEvent @event => @event.Id == request.CameraId,
                    CameraUpdatedEvent @event => @event.Id == request.CameraId,
                    CameraBlockedEvent @event => @event.Id == request.CameraId,
                    _ => false
                })
                .Select<object, object>(e => e switch
                {
                    CameraStartingEvent @event => new ConnectingEvent(@event.Id),
                    CameraRunningEvent @event => new ConnectedEvent(@event.Id),
                    CameraProblemEvent @event => new ReconnectingEvent(@event.Id),
                    CameraStoppedEvent @event => new DisconnectedEvent(@event.Id),
                    CameraUpdatedEvent @event => new UpdatedEvent(@event.Id),
                    CameraBlockedEvent @event => new UpdatedEvent(@event.Id),
                    _ => throw new AppException("Invalid event type")
                })
                .Subscribe(request.Observer);

            return new Response(subscription);
        }
    }
}