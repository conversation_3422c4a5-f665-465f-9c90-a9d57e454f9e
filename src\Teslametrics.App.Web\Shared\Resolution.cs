using System.ComponentModel.DataAnnotations;

namespace Teslametrics.App.Web.Shared;

public enum Resolution
{
    [Display(Name = "320x240 (QVGA)")]
    R320x240 = 0,

    [Display(Name = "640x480 (VGA)")]
    R640x480 = 1,

    [Display(Name = "800x600 (SVGA)")]
    R800x600 = 2,

    [Display(Name = "1024x768 (XGA)")]
    R1024x768 = 3,

    [Display(Name = "1280x720 (HD/720p)")]
    R1280x720 = 4,

    [Display(Name = "1280x1024 (SXGA)")]
    R1280x1024 = 5,

    [Display(Name = "1920x1080 (Full HD/1080p)")]
    R1920x1080 = 6,

    [Display(Name = "2048x1536 (QXGA)")]
    R2048x1536 = 7,

    [Display(Name = "2592x1944 (5MP)")]
    R2592x1944 = 8,

    [Display(Name = "2688x1520 (4MP)")]
    R2688x1520 = 9,

    [Display(Name = "3840x2160 (4K/UHD)")]
    R3840x2160 = 10
}