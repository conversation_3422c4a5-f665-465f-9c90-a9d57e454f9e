using Teslametrics.App.Web.Extensions;

namespace Teslametrics.App.Web.Services.Persistence;

/// <summary>
/// Provides strongly-typed database schema information
/// </summary>
public static class Db
{
    /// <summary>
    /// Outbox messages table schema
    /// </summary>
    public static class OutboxMessages
    {
        public static readonly string Table = nameof(OutboxMessages).ToSnakeCase();

        public static class Columns
        {
            public static readonly string Id = nameof(Id).ToSnakeCase();
            public static readonly string EventType = nameof(EventType).ToSnakeCase();
            public static readonly string Content = nameof(Content).ToSnakeCase();
        }

        public static class Props
        {
            public static readonly string Id = GetQualifiedColumnName(Table, Columns.Id);
            public static readonly string EventType = GetQualifiedColumnName(Table, Columns.EventType);
            public static readonly string Content = GetQualifiedColumnName(Table, Columns.Content);
        }
    }

    /// <summary>
    /// Organizations table schema
    /// </summary>
    public static class Organizations
    {
        public static readonly string Table = nameof(Organizations).ToSnakeCase();

        public static class Columns
        {
            public static readonly string Id = nameof(Id).ToSnakeCase();
            public static readonly string OwnerId = nameof(OwnerId).ToSnakeCase();
            public static readonly string Name = nameof(Name).ToSnakeCase();
        }

        public static class Props
        {
            public static readonly string Id = GetQualifiedColumnName(Table, Columns.Id);
            public static readonly string OwnerId = GetQualifiedColumnName(Table, Columns.OwnerId);
            public static readonly string Name = GetQualifiedColumnName(Table, Columns.Name);
        }
    }

    /// <summary>
    /// User roles table schema
    /// </summary>
    public static class UserRoles
    {
        public static readonly string Table = nameof(UserRoles).ToSnakeCase();

        public static class Columns
        {
            public static readonly string UserId = nameof(UserId).ToSnakeCase();
            public static readonly string RoleId = nameof(RoleId).ToSnakeCase();
        }

        public static class Props
        {
            public static readonly string UserId = GetQualifiedColumnName(Table, Columns.UserId);
            public static readonly string RoleId = GetQualifiedColumnName(Table, Columns.RoleId);
        }
    }

    /// <summary>
    /// Users table schema
    /// </summary>
    public static class Users
    {
        public static readonly string Table = nameof(Users).ToSnakeCase();

        public static class Columns
        {
            public static readonly string Id = nameof(Id).ToSnakeCase();
            public static readonly string Name = nameof(Name).ToSnakeCase();
            public static readonly string Password = nameof(Password).ToSnakeCase();
            public static readonly string LockoutEnabled = nameof(LockoutEnabled).ToSnakeCase();
            public static readonly string LastLogInTime = nameof(LastLogInTime).ToSnakeCase();
            public static readonly string ForcePasswordChange = nameof(ForcePasswordChange).ToSnakeCase();
            public static readonly string SecretKey = nameof(SecretKey).ToSnakeCase();
            public static readonly string Is2faEnabled = nameof(Is2faEnabled).ToSnakeCase();
            public static readonly string Setup2faIsCompleted = nameof(Setup2faIsCompleted).ToSnakeCase();
        }

        public static class Props
        {
            public static readonly string Id = GetQualifiedColumnName(Table, Columns.Id);
            public static readonly string Name = GetQualifiedColumnName(Table, Columns.Name);
            public static readonly string LockoutEnabled = GetQualifiedColumnName(Table, Columns.LockoutEnabled);
            public static readonly string LastLogInTime = GetQualifiedColumnName(Table, Columns.LastLogInTime);
        }
    }

    /// <summary>
    /// User organizations table schema
    /// </summary>
    public static class UserOrganizations
    {
        public static readonly string Table = nameof(UserOrganizations).ToSnakeCase();

        public static class Columns
        {
            public static readonly string UserId = nameof(UserId).ToSnakeCase();
            public static readonly string OrganizationId = nameof(OrganizationId).ToSnakeCase();
        }

        public static class Props
        {
            public static readonly string UserId = GetQualifiedColumnName(Table, Columns.UserId);
            public static readonly string OrganizationId = GetQualifiedColumnName(Table, Columns.OrganizationId);
        }
    }

    public static class CameraQuotas
    {
        public static readonly string Table = nameof(CameraQuotas).ToSnakeCase();

        public static class Columns
        {
            public static readonly string Id = nameof(Id).ToSnakeCase();
            public static readonly string OrganizationId = nameof(OrganizationId).ToSnakeCase();
            public static readonly string PresetId = nameof(PresetId).ToSnakeCase();
            public static readonly string Name = nameof(Name).ToSnakeCase();
            public static readonly string Limit = nameof(Limit).ToSnakeCase();
            public static readonly string RetentionPeriodDays = nameof(RetentionPeriodDays).ToSnakeCase();
            public static readonly string StorageLimitMb = nameof(StorageLimitMb).ToSnakeCase();
        }

        public static class Props
        {
            public static readonly string Id = GetQualifiedColumnName(Table, Columns.Id);
            public static readonly string OrganizationId = GetQualifiedColumnName(Table, Columns.OrganizationId);
            public static readonly string PresetId = GetQualifiedColumnName(Table, Columns.PresetId);
            public static readonly string Name = GetQualifiedColumnName(Table, Columns.Name);
            public static readonly string Limit = GetQualifiedColumnName(Table, Columns.Limit);
            public static readonly string RetentionPeriodDays = GetQualifiedColumnName(Table, Columns.RetentionPeriodDays);
            public static readonly string StorageLimitMb = GetQualifiedColumnName(Table, Columns.StorageLimitMb);
        }
    }

    /// <summary>
    /// Roles table schema
    /// </summary>
    public static class Roles
    {
        public static readonly string Table = nameof(Roles).ToSnakeCase();

        public static class Columns
        {
            public static readonly string Id = nameof(Id).ToSnakeCase();
            public static readonly string OrganizationId = nameof(OrganizationId).ToSnakeCase();
            public static readonly string Name = nameof(Name).ToSnakeCase();
            public static readonly string IsAdmin = nameof(IsAdmin).ToSnakeCase();
        }

        public static class Props
        {
            public static readonly string Id = GetQualifiedColumnName(Table, Columns.Id);
            public static readonly string OrganizationId = GetQualifiedColumnName(Table, Columns.OrganizationId);
            public static readonly string Name = GetQualifiedColumnName(Table, Columns.Name);
            public static readonly string IsAdmin = GetQualifiedColumnName(Table, Columns.IsAdmin);
        }
    }

    /// <summary>
    /// Role permissions table schema
    /// </summary>
    public static class RolePermissions
    {
        public static readonly string Table = nameof(RolePermissions).ToSnakeCase();

        public static class Columns
        {
            public static readonly string Id = nameof(Id).ToSnakeCase();
            public static readonly string RoleId = nameof(RoleId).ToSnakeCase();
            public static readonly string OrganizationId = nameof(OrganizationId).ToSnakeCase();
            public static readonly string Permission = nameof(Permission).ToSnakeCase();
            public static readonly string ResourceId = nameof(ResourceId).ToSnakeCase();
        }

        public static class Props
        {
            public static readonly string Id = GetQualifiedColumnName(Table, Columns.Id);
            public static readonly string RoleId = GetQualifiedColumnName(Table, Columns.RoleId);
            public static readonly string OrganizationId = GetQualifiedColumnName(Table, Columns.OrganizationId);
            public static readonly string Permission = GetQualifiedColumnName(Table, Columns.Permission);
            public static readonly string ResourceId = GetQualifiedColumnName(Table, Columns.ResourceId);
        }
    }

    /// <summary>
    /// Folders table schema
    /// </summary>
    public static class Folders
    {
        public static readonly string Table = nameof(Folders).ToSnakeCase();

        public static class Columns
        {
            public static readonly string Id = nameof(Id).ToSnakeCase();
            public static readonly string OrganizationId = nameof(OrganizationId).ToSnakeCase();
            public static readonly string ParentId = nameof(ParentId).ToSnakeCase();
            public static readonly string Name = nameof(Name).ToSnakeCase();
        }

        public static class Props
        {
            public static readonly string Id = GetQualifiedColumnName(Table, Columns.Id);
            public static readonly string OrganizationId = GetQualifiedColumnName(Table, Columns.OrganizationId);
            public static readonly string ParentId = GetQualifiedColumnName(Table, Columns.ParentId);
            public static readonly string Name = GetQualifiedColumnName(Table, Columns.Name);
        }
    }

    /// <summary>
    /// Cameras table schema
    /// </summary>
    public static class Cameras
    {
        public static readonly string Table = nameof(Cameras).ToSnakeCase();

        public static class Columns
        {
            public static readonly string Id = nameof(Id).ToSnakeCase();
            public static readonly string OrganizationId = nameof(OrganizationId).ToSnakeCase();
            public static readonly string FolderId = nameof(FolderId).ToSnakeCase();
            public static readonly string QuotaId = nameof(QuotaId).ToSnakeCase();
            public static readonly string Name = nameof(Name).ToSnakeCase();
            public static readonly string TimeZone = nameof(TimeZone).ToSnakeCase();
            public static readonly string Latitude = nameof(Latitude).ToSnakeCase();
            public static readonly string Longitude = nameof(Longitude).ToSnakeCase();
            public static readonly string ArchiveUri = nameof(ArchiveUri).ToSnakeCase();
            public static readonly string ViewUri = nameof(ViewUri).ToSnakeCase();
            public static readonly string PublicUri = nameof(PublicUri).ToSnakeCase();
            public static readonly string AutoStart = nameof(AutoStart).ToSnakeCase();
            public static readonly string IsBlocked = nameof(IsBlocked).ToSnakeCase();
            public static readonly string OnvifEnabled = nameof(OnvifEnabled).ToSnakeCase();
            public static readonly string OnvifSettings = nameof(OnvifSettings).ToSnakeCase();
        }

        public static class Props
        {
            public static readonly string Id = GetQualifiedColumnName(Table, Columns.Id);
            public static readonly string OrganizationId = GetQualifiedColumnName(Table, Columns.OrganizationId);
            public static readonly string FolderId = GetQualifiedColumnName(Table, Columns.FolderId);
            public static readonly string QuotaId = GetQualifiedColumnName(Table, Columns.QuotaId);
            public static readonly string Name = GetQualifiedColumnName(Table, Columns.Name);
            public static readonly string TimeZone = GetQualifiedColumnName(Table, Columns.TimeZone);
            public static readonly string Latitude = GetQualifiedColumnName(Table, Columns.Latitude);
            public static readonly string Longitude = GetQualifiedColumnName(Table, Columns.Longitude);
            public static readonly string ArchiveUri = GetQualifiedColumnName(Table, Columns.ArchiveUri);
            public static readonly string ViewUri = GetQualifiedColumnName(Table, Columns.ViewUri);
            public static readonly string PublicUri = GetQualifiedColumnName(Table, Columns.PublicUri);
            public static readonly string AutoStart = GetQualifiedColumnName(Table, Columns.AutoStart);
            public static readonly string IsBlocked = GetQualifiedColumnName(Table, Columns.IsBlocked);
            public static readonly string OnvifEnabled = GetQualifiedColumnName(Table, Columns.OnvifEnabled);
            public static readonly string OnvifSettings = GetQualifiedColumnName(Table, Columns.OnvifSettings);
        }
    }

    /// <summary>
    /// Stream segments table schema
    /// </summary>
    public static class StreamSegments
    {
        public static readonly string Table = nameof(StreamSegments).ToSnakeCase();

        public static class Columns
        {
            public static readonly string SegmentIndex = nameof(SegmentIndex).ToSnakeCase();
            public static readonly string FileName = nameof(FileName).ToSnakeCase();
            public static readonly string StartTime = nameof(StartTime).ToSnakeCase();
            public static readonly string EndTime = nameof(EndTime).ToSnakeCase();
        }
    }

    /// <summary>
    /// Presets table schema
    /// </summary>
    public static class Presets
    {
        public static readonly string Table = nameof(Presets).ToSnakeCase();

        public static class Columns
        {
            public static readonly string Id = nameof(Id).ToSnakeCase();
            public static readonly string Name = nameof(Name).ToSnakeCase();
            public static readonly string ArchiveStreamConfig = nameof(ArchiveStreamConfig).ToSnakeCase();
            public static readonly string ViewStreamConfig = nameof(ViewStreamConfig).ToSnakeCase();
            public static readonly string PublicStreamConfig = nameof(PublicStreamConfig).ToSnakeCase();
        }

        public static class Props
        {
            public static readonly string Id = GetQualifiedColumnName(Table, Columns.Id);
            public static readonly string Name = GetQualifiedColumnName(Table, Columns.Name);
            public static readonly string ArchiveStreamConfig = GetQualifiedColumnName(Table, Columns.ArchiveStreamConfig);
            public static readonly string ViewStreamConfig = GetQualifiedColumnName(Table, Columns.ViewStreamConfig);
            public static readonly string PublicStreamConfig = GetQualifiedColumnName(Table, Columns.PublicStreamConfig);
        }
    }

    /// <summary>
    /// Public links table schema
    /// </summary>
    public static class PublicLinks
    {
        public static readonly string Table = nameof(PublicLinks).ToSnakeCase();

        public static class Columns
        {
            public static readonly string Id = nameof(Id).ToSnakeCase();
            public static readonly string CameraId = nameof(CameraId).ToSnakeCase();
            public static readonly string Name = nameof(Name).ToSnakeCase();
        }

        public static class Props
        {
            public static readonly string Id = GetQualifiedColumnName(Table, Columns.Id);
            public static readonly string CameraId = GetQualifiedColumnName(Table, Columns.CameraId);
            public static readonly string Name = GetQualifiedColumnName(Table, Columns.Name);
        }
    }

    /// <summary>
    /// Camera views table schema
    /// </summary>
    public static class CameraViews
    {
        public static readonly string Table = nameof(CameraViews).ToSnakeCase();

        public static class Columns
        {
            public static readonly string Id = nameof(Id).ToSnakeCase();
            public static readonly string OrganizationId = nameof(OrganizationId).ToSnakeCase();
            public static readonly string Name = nameof(Name).ToSnakeCase();
            public static readonly string ColumnCount = nameof(ColumnCount).ToSnakeCase();
            public static readonly string RowCount = nameof(RowCount).ToSnakeCase();
            public static readonly string GridType = nameof(GridType).ToSnakeCase();
            public static readonly string Cells = nameof(Cells).ToSnakeCase();
        }

        public static class Props
        {
            public static readonly string Id = GetQualifiedColumnName(Table, Columns.Id);
            public static readonly string OrganizationId = GetQualifiedColumnName(Table, Columns.OrganizationId);
            public static readonly string Name = GetQualifiedColumnName(Table, Columns.Name);
            public static readonly string ColumnCount = GetQualifiedColumnName(Table, Columns.ColumnCount);
            public static readonly string RowCount = GetQualifiedColumnName(Table, Columns.RowCount);
            public static readonly string GridType = GetQualifiedColumnName(Table, Columns.GridType);
            public static readonly string Cells = GetQualifiedColumnName(Table, Columns.Cells);
        }
    }

    /// <summary>
    /// Stream segments table schema
    /// </summary>
    public static class MotionEvents
    {
        public static readonly string Table = nameof(MotionEvents).ToSnakeCase();

        public static class Columns
        {
            public static readonly string StartTime = nameof(StartTime).ToSnakeCase();
            public static readonly string EndTime = nameof(EndTime).ToSnakeCase();
        }

        public static class Props
        {
            public static readonly string StartTime = GetQualifiedColumnName(Table, Columns.StartTime);
            public static readonly string EndTime = GetQualifiedColumnName(Table, Columns.EndTime);
        }
    }

    /// <summary>
    /// Plans table schema
    /// </summary>
    public static class Plans
    {
        public static readonly string Table = nameof(Plans).ToSnakeCase();

        public static class Columns
        {
            public static readonly string Id = nameof(Id).ToSnakeCase();
            public static readonly string Page = nameof(Page).ToSnakeCase();
        }

        public static class Props
        {
            public static readonly string Id = GetQualifiedColumnName(Table, Columns.Id);
            public static readonly string Page = GetQualifiedColumnName(Table, Columns.Page);
        }
    }

    public static class PlanImages
    {
        public static readonly string Table = nameof(PlanImages).ToSnakeCase();

        public static class Columns
        {
            public static readonly string FloorId = nameof(FloorId).ToSnakeCase();
            public static readonly string Image = nameof(Image).ToSnakeCase();
            public static readonly string ContentType = nameof(ContentType).ToSnakeCase();
        }

        public static class Props
        {
            public static readonly string FloorId = GetQualifiedColumnName(Table, Columns.FloorId);
            public static readonly string Image = GetQualifiedColumnName(Table, Columns.Image);
            public static readonly string ContentType = GetQualifiedColumnName(Table, Columns.ContentType);
        }
    }

    /// <summary>
    /// Incidents table schema
    /// </summary>
    public static class Incidents
    {
        public static readonly string Table = nameof(Incidents).ToSnakeCase();

        public static class Columns
        {
            public static readonly string Id = nameof(Id).ToSnakeCase();
            public static readonly string IncidentType = nameof(IncidentType).ToSnakeCase();

            public static readonly string CityId = nameof(CityId).ToSnakeCase();
            public static readonly string City = nameof(City).ToSnakeCase();

            public static readonly string BuildingId = nameof(BuildingId).ToSnakeCase();
            public static readonly string Building = nameof(Building).ToSnakeCase();

            public static readonly string FloorId = nameof(FloorId).ToSnakeCase();
            public static readonly string Floor = nameof(Floor).ToSnakeCase();

            public static readonly string RoomId = nameof(RoomId).ToSnakeCase();
            public static readonly string Room = nameof(Room).ToSnakeCase();

            public static readonly string DeviceId = nameof(DeviceId).ToSnakeCase();
            public static readonly string Device = nameof(Device).ToSnakeCase();

            public static readonly string SensorId = nameof(SensorId).ToSnakeCase();
            public static readonly string Topic = nameof(Topic).ToSnakeCase();

            public static readonly string CreatedAt = nameof(CreatedAt).ToSnakeCase();
            public static readonly string ResolvedAt = nameof(ResolvedAt).ToSnakeCase();
        }

        public static class Props
        {
            public static readonly string Id = GetQualifiedColumnName(Table, Columns.Id);
            public static readonly string IncidentType = GetQualifiedColumnName(Table, Columns.IncidentType);

            public static readonly string CityId = GetQualifiedColumnName(Table, Columns.CityId);
            public static readonly string City = GetQualifiedColumnName(Table, Columns.City);

            public static readonly string BuildingId = GetQualifiedColumnName(Table, Columns.BuildingId);
            public static readonly string Building = GetQualifiedColumnName(Table, Columns.Building);

            public static readonly string FloorId = GetQualifiedColumnName(Table, Columns.FloorId);
            public static readonly string Floor = GetQualifiedColumnName(Table, Columns.Floor);

            public static readonly string RoomId = GetQualifiedColumnName(Table, Columns.RoomId);
            public static readonly string Room = GetQualifiedColumnName(Table, Columns.Room);

            public static readonly string DeviceId = GetQualifiedColumnName(Table, Columns.DeviceId);
            public static readonly string Device = GetQualifiedColumnName(Table, Columns.Device);

            public static readonly string SensorId = GetQualifiedColumnName(Table, Columns.SensorId);
            public static readonly string Topic = GetQualifiedColumnName(Table, Columns.Topic);

            public static readonly string CreatedAt = GetQualifiedColumnName(Table, Columns.CreatedAt);
            public static readonly string ResolvedAt = GetQualifiedColumnName(Table, Columns.ResolvedAt);
        }
    }

    /// <summary>
    /// Incident notifications table schema
    /// </summary>
    public static class IncidentNotifications
    {
        public static readonly string Table = nameof(IncidentNotifications).ToSnakeCase();

        public static class Columns
        {
            public static readonly string Id = nameof(Id).ToSnakeCase();
            public static readonly string IncidentId = nameof(IncidentId).ToSnakeCase();
            public static readonly string UserId = nameof(UserId).ToSnakeCase();
        }

        public static class Props
        {
            public static readonly string Id = GetQualifiedColumnName(Table, Columns.Id);
            public static readonly string IncidentId = GetQualifiedColumnName(Table, Columns.IncidentId);
            public static readonly string UserId = GetQualifiedColumnName(Table, Columns.UserId);
        }
    }

    public static class SensorHistory
    {
        public static readonly string Table = nameof(SensorHistory).ToSnakeCase();

        public static class Columns
        {
            public static readonly string Timestamp = nameof(Timestamp).ToSnakeCase();
            public static readonly string Value = nameof(Value).ToSnakeCase();
        }
    }

    /// <summary>
    /// Creates a fully qualified column name in the format "table.column"
    /// </summary>
    private static string GetQualifiedColumnName(string table, string column)
    {
        return $"{table}.{column}";
    }
}
