using System;
using System.Security.Claims;

namespace Teslametrics.App.Web.Services.UserSession;

public interface ISessionProvider
{
    Task InitializeAsync();
	SessionResult CreateSession(ClaimsPrincipal principal);
    SessionResult KillSession(Guid sessionId);
    SessionResult UpdateSession(ClaimsPrincipal principal);
    Session? GetSessionBySessionId(Guid sessionId);
    Session? GetSessionByUserId(Guid userId);
    IDisposable Subscribe(Guid userId, IObserver<Guid> observer);
}

public record SessionResult(Guid SessionId, bool IsSuccess);