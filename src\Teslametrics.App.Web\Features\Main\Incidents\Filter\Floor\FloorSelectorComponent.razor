﻿@inherits InteractiveBaseComponent
<MudAutocomplete T="GetFloorListUseCase.Response.Item"
                 SearchFunc="@SearchAsync"
                 ToStringFunc="@(e => e == null ? null : e.Number.ToString())"
                 Value="@_selected"
                 Margin="Margin.Dense"
                 ValueChanged="@ValueChanged"
                 Label="Этаж"
                 Clearable="true"
                 ResetValueOnEmptyText="true"
                 Variant="Variant.Outlined"
                 Disabled="BuildingId is null || CityId is null" />

@code {
    private GetFloorListUseCase.Response.Item? _selected;

    [Parameter]
    public Guid? CityId { get; set; }

    [Parameter]
    public Guid? BuildingId { get; set; }

    [Parameter]
    public Guid? Floor { get; set; }
    [Parameter]
    public EventCallback<Guid?> FloorChanged { get; set; }

    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();
        if (_selected?.Id != Floor)
        {
            _selected = null;
        }
    }

    private async Task ValueChanged(GetFloorListUseCase.Response.Item? item)
    {
        _selected = item;
        await FloorChanged.InvokeAsync(item?.Id);
    }

    private async Task<IEnumerable<GetFloorListUseCase.Response.Item>> SearchAsync(string value, CancellationToken token)
    {
        if (!CityId.HasValue || !BuildingId.HasValue) return [];

        GetFloorListUseCase.Response? response = null;
        try
        {
            response = await ScopeFactory.MediatorSend(new GetFloorListUseCase.Query(CityId.Value, BuildingId.Value, value is null ? null : int.Parse(value)), token);
        }
        catch (TaskCanceledException)
        {
            // Search was canceled, just return empty result
            return Enumerable.Empty<GetFloorListUseCase.Response.Item>();
        }
        catch (Exception exc)
        {
            Logger.LogError(exc, "Error searching cities");
            Snackbar.Add("Не удалось получить список этажей из-за непредвиденной ошибки.", MudBlazor.Severity.Error);
            return Enumerable.Empty<GetFloorListUseCase.Response.Item>();
        }

        if (response.Result == GetFloorListUseCase.Result.Success)
            return response.Items;

        if (response.Result == GetFloorListUseCase.Result.ValidationError)
            Snackbar.Add("Ошибка валидации при получении списка городов", MudBlazor.Severity.Error);
        else
            Snackbar.Add("Не удалось получить список городов из-за непредвиденной ошибки.", MudBlazor.Severity.Error);

        return Enumerable.Empty<GetFloorListUseCase.Response.Item>();
    }
}