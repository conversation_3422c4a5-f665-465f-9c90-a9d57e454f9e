﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Teslametrics.DbMigrator.Migrations
{
    /// <inheritdoc />
    public partial class IncidentsAdded : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "incident_notifications",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    incident_id = table.Column<Guid>(type: "uuid", nullable: false),
                    user_id = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_incident_notifications", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "incidents",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    incident_type = table.Column<string>(type: "text", nullable: false),
                    city_id = table.Column<Guid>(type: "uuid", nullable: false),
                    city = table.Column<string>(type: "text", nullable: false),
                    building_id = table.Column<Guid>(type: "uuid", nullable: false),
                    building = table.Column<string>(type: "text", nullable: false),
                    floor_id = table.Column<Guid>(type: "uuid", nullable: false),
                    floor = table.Column<int>(type: "integer", nullable: false),
                    room_id = table.Column<Guid>(type: "uuid", nullable: false),
                    room = table.Column<string>(type: "text", nullable: false),
                    device_id = table.Column<Guid>(type: "uuid", nullable: true),
                    device = table.Column<string>(type: "text", nullable: true),
                    sensor_id = table.Column<Guid>(type: "uuid", nullable: false),
                    topic = table.Column<string>(type: "text", nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false),
                    resolved_at = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_incidents", x => x.id);
                });

            migrationBuilder.CreateIndex(
                name: "ix_incident_notifications_incident_id",
                table: "incident_notifications",
                column: "incident_id");

            migrationBuilder.CreateIndex(
                name: "ix_incident_notifications_incident_id_user_id",
                table: "incident_notifications",
                columns: new[] { "incident_id", "user_id" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_incident_notifications_user_id",
                table: "incident_notifications",
                column: "user_id");

            migrationBuilder.CreateIndex(
                name: "ix_incidents_building",
                table: "incidents",
                column: "building");

            migrationBuilder.CreateIndex(
                name: "ix_incidents_building_id",
                table: "incidents",
                column: "building_id");

            migrationBuilder.CreateIndex(
                name: "ix_incidents_city",
                table: "incidents",
                column: "city");

            migrationBuilder.CreateIndex(
                name: "ix_incidents_city_building_floor_room",
                table: "incidents",
                columns: new[] { "city", "building", "floor", "room" });

            migrationBuilder.CreateIndex(
                name: "ix_incidents_city_id",
                table: "incidents",
                column: "city_id");

            migrationBuilder.CreateIndex(
                name: "ix_incidents_created_at",
                table: "incidents",
                column: "created_at");

            migrationBuilder.CreateIndex(
                name: "ix_incidents_device",
                table: "incidents",
                column: "device");

            migrationBuilder.CreateIndex(
                name: "ix_incidents_device_id",
                table: "incidents",
                column: "device_id");

            migrationBuilder.CreateIndex(
                name: "ix_incidents_floor",
                table: "incidents",
                column: "floor");

            migrationBuilder.CreateIndex(
                name: "ix_incidents_floor_id",
                table: "incidents",
                column: "floor_id");

            migrationBuilder.CreateIndex(
                name: "ix_incidents_incident_type",
                table: "incidents",
                column: "incident_type");

            migrationBuilder.CreateIndex(
                name: "ix_incidents_resolved_at",
                table: "incidents",
                column: "resolved_at");

            migrationBuilder.CreateIndex(
                name: "ix_incidents_room",
                table: "incidents",
                column: "room");

            migrationBuilder.CreateIndex(
                name: "ix_incidents_room_id",
                table: "incidents",
                column: "room_id");

            migrationBuilder.CreateIndex(
                name: "ix_incidents_sensor_id",
                table: "incidents",
                column: "sensor_id");

            migrationBuilder.CreateIndex(
                name: "ix_incidents_topic",
                table: "incidents",
                column: "topic");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "incident_notifications");

            migrationBuilder.DropTable(
                name: "incidents");
        }
    }
}
