using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using MudBlazor;
using System.Reactive;
using Teslametrics.App.Web.Events.Organization;
using Teslametrics.App.Web.Exceptions;
using Teslametrics.App.Web.Extensions;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Organizations.List;

public partial class OrganizationsListComponent
{
	private bool _disposedValue;

	private record Organization(Guid Id, string Name, string Owner);

	private IDisposable? _subscription;
	private DateTime _lastRefreshTime = DateTime.Now;
	private List<Organization> _organizations = [];
	private int _totalPages;

	protected int CurrentPage => Offset / Limit;

	#region Parameters
	[Parameter]
	public int Offset { get; set; } = 0;
	[Parameter]
	public EventCallback<int> OffsetChanged { get; set; }

	[Parameter]
	public int Limit { get; set; } = 25;
	[Parameter]
	public EventCallback<int> LimitChanged { get; set; }

	[Parameter]
	public string? SearcString { get; set; } = string.Empty;
	[Parameter]
	public EventCallback<string?> SearcStringChanged { get; set; }
	[Parameter]
	public Guid? Selected { get; set; }
	[Parameter]
	public EventCallback<Guid?> SelectedChanged { get; set; }
	#endregion

	protected override async Task OnInitializedAsync()
	{
		await base.OnInitializedAsync();
		await FetchAsync();
		await SubscribeAsync();

		AuthenticationStateProvider.AuthenticationStateChanged += OnAuthenticationStateChanged;
	}

	protected override void Dispose(bool disposing)
	{
		if (!_disposedValue)
		{
			if (disposing)
			{
				AuthenticationStateProvider.AuthenticationStateChanged -= OnAuthenticationStateChanged;
			}

			_disposedValue = true;
		}

		base.Dispose(disposing);
	}

	protected virtual async Task FetchAsync()
	{
		if (IsLoading) return;

		await SetLoadingAsync();
		GetOrganizationListUseCase.Response? _response = null;
		try
		{
			var userId = await GetCurrentUserIdAsync() ?? throw new NotAuthorizedException();
			_response = await ScopeFactory.MediatorSend(new GetOrganizationListUseCase.Query(userId, Offset, Limit, SearcString ?? string.Empty));
		}
		catch (Exception ex)
		{
			_response = null;
			Snackbar.Add("Не удалось получить список организаций повторите попытку. Если проблема сохраняется - обратитесь к администратору", Severity.Error);
			Logger.LogError(ex, ex.Message);
		}

		await SetLoadingAsync(false);
		if (_response is null) return;

		switch (_response.Result)
		{
			case GetOrganizationListUseCase.Result.Success:
				_lastRefreshTime = DateTime.Now;
				_organizations = _response.Items.Select(item => new Organization(item.Id, item.Name, item.Owner)).ToList();
				_totalPages = _response.TotalCount / Limit;
				break;

			case GetOrganizationListUseCase.Result.ValidationError:
				Snackbar.Add("Ошибка валидации при получении списка организаций", Severity.Error);
				break;

			case GetOrganizationListUseCase.Result.Unknown:
				Logger.LogError("Unknown error while subscribing to organization events in Component: {Component}, UseCase: {UseCase}", nameof(OrganizationsListComponent), nameof(GetOrganizationListUseCase));
				Snackbar.Add("Неизвестная ошибка при получении списка организаций. Повторите попытку и обратитесь к администратору.", MudBlazor.Severity.Error);
				break;
			default:
				Logger.LogError("Unknown error while subscribing to organization events in Component: {Component}, UseCase: {UseCase}, Result: {Result}", nameof(OrganizationsListComponent), nameof(GetOrganizationListUseCase), _response.Result);
				Snackbar.Add($"Неизвестная ошибка при получении списка организаций. Повторите попытку и обратитесь к администратору. Код ошибки: {_response.Result}", MudBlazor.Severity.Error);
				break;
		}
	}

	private async Task SubscribeAsync()
	{
		Unsubscribe();
		var userId = await GetCurrentUserIdAsync() ?? throw new NotAuthorizedException();
		var result = await ScopeFactory.MediatorSend(new SubscribeOrganizationListUseCase.Request(Observer.Create<object>(OnAppEventHandler, OnError), userId));
		switch (result.Result)
		{
			case SubscribeOrganizationListUseCase.Result.Success:
				_subscription = result.Subscription!;
				CompositeDisposable.Add(_subscription);
				break;
			case SubscribeOrganizationListUseCase.Result.ValidationError:
				Snackbar.Add("Ошибка валидации при подписке на события", Severity.Error);
				break;

			case SubscribeOrganizationListUseCase.Result.Unknown:
			default:
				Snackbar.Add("Не удалось получить подписку на обновления из-за непредвиденной ошибки:" + result.Result.ToString(), Severity.Error);
				break;
		}
	}

	private void Unsubscribe()
	{
		if (_subscription is not null)
		{
			CompositeDisposable.Remove(_subscription);
			_subscription?.Dispose();
			_subscription = null;
		}
	}

	#region [Actions]
	private void Create() => EventSystem.Publish(new OrganizationCreateEto());
	private Task SelectAsync(Guid? id)
	{
		Selected = id;
		if (SelectedChanged.HasDelegate)
			return SelectedChanged.InvokeAsync(Selected);

		return Task.CompletedTask;
	}
	private Task RefreshAsync() => FetchAsync();

	private void Show(Guid id) => EventSystem.Publish(new OrganizationSelectEto(id));
	private void Edit(Guid id) => EventSystem.Publish(new OrganizationEditEto(id));
	private void Delete(Guid id) => EventSystem.Publish(new OrganizationDeleteEto(id));
	#endregion

	#region [EventHandlers]
	private async Task OnSearchChanged(string? _searchString)
	{
		SearcString = _searchString;
		if (SearcStringChanged.HasDelegate)
			await SearcStringChanged.InvokeAsync(SearcString);

		await FetchAsync();
	}

	private async Task OnPageChanged(int page)
	{
		Offset = page * Limit - 1;
		if (OffsetChanged.HasDelegate)
			await OffsetChanged.InvokeAsync(Offset);

		await FetchAsync();
	}

	private async void OnAppEventHandler(object appEvent)
	{
		await RefreshAsync();
		await UpdateViewAsync();
	}

	private void OnError(Exception exc)
	{
		Logger.LogError(exc, exc.Message);
		Snackbar.Add("Ошибка валидации при подписке на события", Severity.Error);
	}

	private async void OnAuthenticationStateChanged(Task<AuthenticationState> authState)
	{
		await FetchAsync();
	}
	#endregion
}
