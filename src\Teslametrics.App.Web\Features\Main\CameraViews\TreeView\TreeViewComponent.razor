@using Teslametrics.App.Web.Components.DragAndDrop
@using Teslametrics.App.Web.Data.Sql
@inherits InteractiveBaseComponent
<div class="mud-height-full sidebar tree">
	<MudStack Row="true"
			  Class="mb-4 pl-4 pt-2"
			  AlignItems="AlignItems.Center">
		@if (IsLoading)
		{
			<MudSkeleton Width="65%"
						 Height="42px" />
			<MudSpacer />
			<MudSkeleton SkeletonType="SkeletonType.Circle"
						 Animation="Animation.Wave"
						 Height="48px"
						 Width="48px" />
		}
		else
		{
			<MudText Typo="Typo.subtitle1"
					 Class="mud-width-full">Последнее обновление: @_lastRefreshTime.ToLocalTime()</MudText>
			<MudIconButton OnClick="RefreshAsync"
						   Icon="@Icons.Material.Outlined.Refresh" />
		}
		@if (!_subscribing && (_subscriptionResult is null || !_subscriptionResult.IsSuccess))
		{
			<MudTooltip Arrow="true"
						Placement="Placement.Start"
						Text="Ошибка подписки на события">
				<MudIconButton OnClick="SubscribeAsync"
							   Icon="@Icons.Material.Filled.ErrorOutline"
							   Color="Color.Error" />
			</MudTooltip>
		}
	</MudStack>
	<MudDivider />
	@if (IsLoading)
	{
		<MudProgressLinear Indeterminate="true"
						   Color="Color.Primary" />
	}
	else
	{
		<div style="height: 4px;" />
	}
	@if (IsLoading && _items.Count == 0)
	{
		@for (var i = 0; i < Random.Shared.Next(3, 8); i++)
		{
			<MudStack Row="true"
					  Class="px-4 py-1">
				<MudSkeleton SkeletonType="SkeletonType.Circle"
							 Height="24px"
							 Width="24px"
							 Class="mt-1" />
				<MudSkeleton Width="@($"{Random.Shared.Next(30, 70)}%")"
							 Height="40px" />
			</MudStack>

		}

	}
	<NoItemsFoundComponent HasItems="IsLoading || _items.Count > 0"
						   LastRefreshTime="_lastRefreshTime" />
	@if (!IsLoading && _items.Count > 0)
	{
		<MudTreeView T="Guid"
					 Items="@_items"
					 SelectionMode="SelectionMode.ToggleSelection"
					 Class="tree overflow-auto"
					 SelectedValue="@(ViewId is null ? (OrganizationId ?? Guid.Empty) : ViewId.Value)"
					 AutoExpand="true">
			<ItemTemplate Context="Item">
				@{
					var presenter = (TreeItemPresenter)Item;
				}
				<MudTreeViewItem @bind-Expanded="@presenter.Expanded"
								 Items="@presenter.Children"
								 Value="@presenter.Value"
								 CanExpand="@presenter.HasChildren"
								 Selected="@presenter.Selected"
								 T="Guid"
								 OnClick="(async () => await SelectAsync(presenter))">
					<Content>
						<MudTreeViewItemToggleButton @bind-Expanded="@presenter.Expanded"
													 Visible="@presenter.HasChildren" />
						<div class="d-flex flex-row align-center mud-width-full">
							<div class="py-2 px-1 d-flex flex-row align-center overflow-auto">
								<MudIcon Icon="@presenter.Icon"
										 Class="ml-0 mr-2"
										 Color="@Color.Default" />
								<MudText>@presenter.Text</MudText>
							</div>
							<MudSpacer />
							<MudStack Row="true"
									  AlignItems="AlignItems.Center">
								@switch (presenter.Type)
								{
									case ItemType.Organization:
										<MudTooltip Arrow="true"
													Placement="Placement.Start"
													Text="Количество видов в организации">
											<MudChip T="int"
													 Variant="Variant.Outlined"
													 Color="Color.Primary"
													 Size="Size.Small"
													 Icon="@Icons.Material.Filled.ViewComfyAlt">@presenter.ViewCount</MudChip>
										</MudTooltip>
										<MudTooltip Arrow="true"
													Placement="Placement.Start"
													Text="Количество камер в видах организации">
											<MudChip T="int"
													 Variant="Variant.Outlined"
													 Color="Color.Info"
													 Size="Size.Small"
													 Icon="@Icons.Material.Filled.Camera">@presenter.CameraCount</MudChip>
										</MudTooltip>
										<AuthorizeView Policy="@AppPermissions.Main.CameraViews.Create.GetEnumPermissionString()"
													   Context="innerContext">
											<MudBadge Color="Color.Primary"
													  Icon="@Icons.Material.Outlined.Add"
													  Overlap="true"
													  Origin="Origin.BottomLeft"
													  Bordered="true">
												<MudTooltip Arrow="true"
															Placement="Placement.Start"
															Text="Создать вид">
													<MudIconButton OnClick="() => CreateView(presenter.Id)"
																   Icon="@Icons.Material.Outlined.ViewModule"
																   Variant="Variant.Outlined"
																   Color="Color.Secondary" />
												</MudTooltip>
											</MudBadge>
										</AuthorizeView>
										break;
									default:
										<MudTooltip Arrow="true"
													Placement="Placement.Start"
													Text="Количество камер в виде">
											<MudChip T="int"
													 Variant="Variant.Outlined"
													 Color="Color.Info"
													 Size="Size.Small"
													 Icon="@Icons.Material.Filled.Camera">@presenter.CameraCount</MudChip>
										</MudTooltip>

										<MudMenu Icon="@Icons.Material.Filled.MoreVert"
												 AriaLabel="Действия с камерой"
												 Variant="Variant.Outlined"
												 Color="Color.Primary">
											<MudMenuItem OnClick="() => NavigateToDetails(presenter)"
														 Icon="@Icons.Material.Outlined.PlayCircle">Запустить просмотр
											</MudMenuItem>
											<MudMenuItem OnClick="() => ShowView(presenter)"
														 Icon="@Icons.Material.Outlined.PanoramaFishEye">Просмотр настроек
											</MudMenuItem>

											<AuthorizeView Policy="@AppPermissions.Main.CameraViews.Update.GetEnumPermissionString()"
														   Resource="@(new PolicyRequirementResource(OrganizationId, ViewId))"
														   Context="editContext">
												<MudMenuItem OnClick="() => EditView(presenter)"
															 IconColor="Color.Warning"
															 Icon="@Icons.Material.Outlined.Edit">Редактирование настроек
												</MudMenuItem>
											</AuthorizeView>
											<AuthorizeView Policy="@AppPermissions.Main.CameraViews.Delete.GetEnumPermissionString()"
														   Resource="@(new PolicyRequirementResource(OrganizationId, ViewId))"
														   Context="deleteContext">
												<MudDivider />
												<MudMenuItem OnClick="() => DeleteView(presenter)"
															 IconColor="Color.Error"
															 Icon="@Icons.Material.Outlined.Delete">Удаление вида
												</MudMenuItem>
											</AuthorizeView>
										</MudMenu>
										break;
								}
							</MudStack>
						</div>
					</Content>
				</MudTreeViewItem>
			</ItemTemplate>
		</MudTreeView>
	}
</div>