@using Teslametrics.App.Web.Components.MSEPlayer
@using Teslametrics.MediaServer.Orleans.Camera
@inherits InteractiveBaseComponent
<MudDialog Visible="_isVisible"
		   VisibleChanged="VisibilityChanged"
		   ActionsClass="mx-2"
		   Class="@($"dialog_{_guid}")"
		   ContentClass="dialog_content"
		   Options="_dialogOptions">
	<DialogContent>
		@* <Player Id="@_playerId" PathToFile="@($"streams/{_cameraId}/online/stream.m3u8")" /> *@
		@switch (_cameraResponse?.CameraStatus)
		{
			case CameraStatus.Running:
				@if (_cameraResponse.CameraStreamId.HasValue && _cameraResponse.CameraStreamId.Value != Guid.Empty)
				{
                    <MsePlayer CameraId="@_cameraResponse.CameraStreamId.Value"
                               Type="StreamType.View"
                               Autoplay="true"
                               Muted="true"/>
				}
				else
				{
					<div class="empty_cell">
						<MudIcon Icon="@Icons.Material.Filled.Block"
							Color="Color.Error" />
						<div>Ошибка</div>
					</div>
				}
				break;
			case CameraStatus.Stopped:
				<div class="empty_cell">
					<MudIcon Icon="@Icons.Material.Filled.Block"
							Color="Color.Warning" />
					<div>Камера отключена</div>
				</div>
				break;
			case CameraStatus.Starting:
				<div class="empty_cell">
					<MudProgressCircular Color="Color.Info"
										Style="height:70px;width:70px;"
										Indeterminate="true" />
					<div>Камера подключается</div>
				</div>
				break;
			case CameraStatus.Problem:
				<div class="empty_cell">
					<MudIcon Icon="@Icons.Material.Filled.Block"
							Color="Color.Error" />
					<div>Ошибка</div>
				</div>
				break;
			default:
				break;
		}

		@if (_cameraResponse is not null && !_cameraResponse.IsSuccess)
		{
			<MudText Typo="Typo.subtitle1">Не удалось получить камеру</MudText>
		}

		@if (IsLoading && (_cameraResponse is null || !_cameraResponse.IsSuccess))
		{
			<MudSkeleton SkeletonType="SkeletonType.Rectangle"
						Class="skeleton-camera" />
		}
	</DialogContent>
	<DialogActions>
		<MudButton OnClick="CancelAsync">Закрыть</MudButton>
	</DialogActions>
</MudDialog>
<style>
	@($".dialog_{_guid}")
	{
		align-items: stretch;
		justify-content: center;
		height: -webkit-fill-available !important;
		box-sizing: border-box;
		@* width: fit-content !important; *@
		padding: 0;
		margin: 0;
		@* min-width: fit-content;
		max-width: fit-content !important; *@
		display: grid !important;
		grid-template-rows: 1fr auto;
	}

	@($".dialog_{_guid} .dialog_content")
	{
		@* width: fit-content; *@
		height: -webkit-fill-available;
        padding: 0!important;
        overflow: hidden;
        @* width: inherit; *@
	}
    @@media (min-width: 767px) {
        @($".dialog_{_guid}")
        {
            height: fit-content !important;
        }
    }
</style>