using Dapper;
using FluentValidation;
using MediatR;
using System.Data;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Services.Persistence;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Features.Main.Cameras.ExportDialog;

public static class GetCameraListUseCase
{
    public record Query(Guid UserId, Guid OrganizationId, Guid? FolderId, string OrderBy, OrderDirection OrderDirection) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public List<Item> Items { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(List<Item> items)
        {
            Items = items;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Result = result;

            Items = [];
        }

        public record Item(Guid Id, string Name, TimeSpan TimeZone, Coordinates? Coordinates, string FolderName, string PublicUri, string ArchiveUri, string ViewUri, string QuotaName, bool AutoStart);
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError
    }

    public class Validator : AbstractValidator<Query>
    {
        public Validator()
        {
            RuleFor(q => q.UserId).NotEmpty();
            RuleFor(q => q.OrganizationId).NotEmpty();
            RuleFor(q => q.FolderId).NotEmpty().When(q => q.FolderId is not null);
        }
    }

    public class Handler : IRequestHandler<Query, Response>
    {
        private readonly IValidator<Query> _validator;
        private readonly IDbConnection _dbConnection;

        public Handler(IValidator<Query> validator,
                       IDbConnection dbConnection)
        {
            _validator = validator;
            _dbConnection = dbConnection;
        }

        public async Task<Response> Handle(Query request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            var allowedResources = request.UserId != SystemConsts.RootUserId
                ? await GetAllowedResourcesAsync(request.UserId)
                : [];

            var template = SqlQueryBuilder.Create()
                .Select(Db.Cameras.Props.Id)
                .Select(Db.Cameras.Props.Name)
                .Select(Db.Cameras.Props.TimeZone)
                .Select(Db.Cameras.Props.Latitude)
                .Select(Db.Cameras.Props.Longitude)
                .Select(Db.Cameras.Props.PublicUri)
                .Select(Db.Cameras.Props.ArchiveUri)
                .Select(Db.Cameras.Props.ViewUri)
                .Select(Db.Folders.Props.Name, "FolderName")
                .Select(Db.CameraQuotas.Props.Name, "QuotaName")
                .Select(Db.Cameras.Props.AutoStart)
                .InnerJoin(Db.Organizations.Table, Db.Organizations.Props.Id, Db.Cameras.Props.OrganizationId, SqlOperator.Equals)
                .InnerJoin(Db.Folders.Table, Db.Folders.Props.Id, Db.Cameras.Props.FolderId, SqlOperator.Equals)
                .LeftJoin(Db.CameraQuotas.Table, Db.CameraQuotas.Props.Id, Db.Cameras.Props.QuotaId, SqlOperator.Equals)
                .Where(Db.Cameras.Props.OrganizationId, ":OrganizationId", SqlOperator.Equals, new { request.OrganizationId })
                .WhereIf(request.FolderId is not null, Db.Cameras.Props.FolderId, ":FolderId", SqlOperator.Equals, new { request.FolderId })
                .WhereIf(request.UserId != SystemConsts.RootUserId, $"({Db.Organizations.Props.OwnerId} = :UserId OR :Wildcard = ANY(:ResourceIds) OR {Db.Cameras.Props.Id} = ANY(:ResourceIds))", new { request.UserId, Wildcard = SystemConsts.ResourceWildcardId, ResourceIds = allowedResources })
                .OrderByIf(!string.IsNullOrWhiteSpace(request.OrderBy), request.OrderBy, request.OrderDirection)
                .Build(QueryType.Standard, Db.Cameras.Table, RowSelection.AllRows);

            var cameras = await _dbConnection.QueryAsync<CameraModel>(template.RawSql, template.Parameters);

            return new Response(cameras.Select(c => new Response.Item(
                c.Id,
                c.Name,
                c.TimeZone,
                c.Latitude.HasValue && c.Longitude.HasValue
                    ? new Coordinates(c.Latitude.Value, c.Longitude.Value)
                    : null,
                c.FolderName,
                c.PublicUri,
                c.ArchiveUri,
                c.ViewUri,
                c.QuotaName ?? string.Empty,
                c.AutoStart)).ToList());
        }

        private async Task<IEnumerable<Guid>> GetAllowedResourcesAsync(Guid userId)
        {
            var template = SqlQueryBuilder.Create()
                .Select(Db.RolePermissions.Props.ResourceId)
                .InnerJoin(Db.UserRoles.Table, Db.UserRoles.Props.RoleId, Db.RolePermissions.Props.RoleId, SqlOperator.Equals)
                .Where(Db.UserRoles.Props.UserId, ":UserId", SqlOperator.Equals, new { userId })
                .Where(Db.RolePermissions.Props.Permission, ":Permission", SqlOperator.Equals, new { Permission = Fqdn<AppPermissions>.GetName(AppPermissions.Main.Cameras.Read) })
                .Build(QueryType.Standard, Db.RolePermissions.Table, RowSelection.AllRows);

            return await _dbConnection.QueryAsync<Guid>(template.RawSql, template.Parameters);
        }
    }

    public record CameraModel(Guid Id,
                              string Name,
                              TimeSpan TimeZone,
                              double? Latitude,
                              double? Longitude,
                              string PublicUri,
                              string ArchiveUri,
                              string ViewUri,
                              string FolderName,
                              string? QuotaName,
                              bool AutoStart);
}
