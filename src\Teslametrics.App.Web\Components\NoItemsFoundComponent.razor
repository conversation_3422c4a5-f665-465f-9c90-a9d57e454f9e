@if (!HasItems)
{
	<MudStack Row>
		<MudIcon Icon="@Icons.Material.Filled.SearchOff" Style="font-size: 8rem;" />
		<MudStack AlignItems="AlignItems.Start" Justify="Justify.FlexStart" Class="mud-height-full mt-5">
			<MudText Typo="Typo.body1">Ничего не найдено</MudText>
			<MudText Typo="Typo.subtitle1">Попробуйте снова позднее</MudText>
			@if (LastRefreshTime.HasValue)
			{
				<MudText Typo="Typo.subtitle2">Дата последнего обновления: <MudText Inline="true" Color="Color.Warning">@LastRefreshTime.Value.ToLocalTime()</MudText></MudText>
			}
			@if (RefreshAsync.HasDelegate)
			{
				<MudButton OnClick="@RefreshAsync" Color="Color.Primary" Variant="Variant.Outlined">Обновить</MudButton>
			}
		</MudStack>
	</MudStack>
}