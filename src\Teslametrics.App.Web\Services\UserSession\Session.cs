using System.Security.Claims;

namespace Teslametrics.App.Web.Services.UserSession;

public class Session
{
    public Session(ClaimsPrincipal claimsPrincipal)
    {
        ClaimsPrincipal = claimsPrincipal;
        Id = Guid.NewGuid();
    }

	public Session(Guid id, ClaimsPrincipal claimsPrincipal)
	{
		ClaimsPrincipal = claimsPrincipal;
		Id = id;
	}
	
	public ClaimsPrincipal ClaimsPrincipal { get; private set; }
    public Guid Id { get; init; }
}