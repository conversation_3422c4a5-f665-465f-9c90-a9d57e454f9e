using System.Data;
using Dapper;
using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Services.Persistence;

namespace Teslametrics.App.Web.Features.Main.Cameras.Archive;

public static class GetAvailableArchivesUseCase
{
	public record Query(Guid CameraId, DateTime Period) : BaseRequest<Response>;

	public record Response : BaseResponse
	{
		public IEnumerable<DateTime> AvailableDates { get; init; } // список дат, на которые имеется запись

		public Result Result { get; init; }

		public bool IsSuccess => Result == Result.Success;

		public Response(IEnumerable<DateTime> availableDates)
		{
			Result = Result.Success;
			AvailableDates = availableDates;
		}

		public Response(Result result)
		{
			if (result == Result.Success)
			{
				throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
			}

			Result = result;
			AvailableDates = [];
		}
	}

	public enum Result
	{
		Unknown = 0,
		Success,
		ValidationError
	}

    public class Validator : AbstractValidator<Query>
    {
        public Validator()
        {
            RuleFor(q => q.CameraId).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Query, Response>
    {
        private readonly IValidator<Query> _validator;
        private readonly IDbConnection _dbConnection;

        public Handler(IValidator<Query> validator,
                       IDbConnection dbConnection)
        {
            _validator = validator;
            _dbConnection = dbConnection;
        }

        public async Task<Response> Handle(Query request, CancellationToken cancellationToken = default)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            var table = $"{Db.StreamSegments.Table}_{request.CameraId.ToString("N")}";

            var sql = $@"
                SELECT DISTINCT dt::date
                FROM (
                    SELECT {Db.StreamSegments.Columns.StartTime} as dt
                    FROM {table}
                    WHERE DATE_TRUNC('month', {Db.StreamSegments.Columns.StartTime}) = DATE_TRUNC('month', :Period)
                    UNION ALL
                    SELECT {Db.StreamSegments.Columns.EndTime} as dt
                    FROM {table}
                    WHERE DATE_TRUNC('month', {Db.StreamSegments.Columns.EndTime}) = DATE_TRUNC('month', :Period)
                ) AS dates";

            var availableDates = await _dbConnection.QueryAsync<DateTime>(sql, new { request.Period });

            return new Response(availableDates);
        }
    }
}