<div class="d-flex flex-column gap-2">
	<div class="d-flex gap-2">
		<MudIcon Icon="@Icon" Size="Size.Small" Color="_titleColor" />
		<MudText Color="_titleColor">@Title</MudText>
	</div>
	<MudText Typo="Typo.subtitle2" Class="subtitle">@Subtitle</MudText>
</div>

@code {
	private Color _titleColor => Error ? Color.Error : Disabled ? Color.Surface : Color.Inherit;

	[Parameter]
	public bool Error { get; set; } = false;

	[Parameter]
	public bool Disabled { get; set; } = false;

	[Parameter]
	public string Icon { get; set; } = string.Empty;

	[Parameter]
	[EditorRequired]
	public string Title { get; set; } = string.Empty;

	[Parameter]
	[EditorRequired]
	public string Subtitle { get; set; } = string.Empty;
}
