using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Domain;
using Teslametrics.App.Web.Domain.AccessControl;
using Teslametrics.App.Web.Domain.AccessControl.Organizations;
using Teslametrics.App.Web.Services.Outbox;
using Teslametrics.App.Web.Services.TransactionManager;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Roles.Drawer.Create;

public static class CreateRoleUseCase
{
    public record Command(Guid OrganizationId, string Name, bool IsAdmin, List<ResourcePermission> ResourcePermissions) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public Guid Id { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(Guid id)
        {
            Id = id;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Id = Guid.Empty;
            Result = result;
        }
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        RoleNameAlreadyExists,
        OrganizationNotFound
    }

    public class Validator : AbstractValidator<Command>
    {
        public Validator()
        {
            RuleFor(c => c.OrganizationId).NotEmpty();
            RuleFor(c => c.Name).Length(3, 60);
            RuleFor(c => c.ResourcePermissions).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Command, Response>
    {
        private readonly IValidator<Command> _validator;
        private readonly IOrganizationRepository _organizationRepository;
        private readonly ITransactionManager _transactionManager;
        private readonly IPublisher _publisher;
        private readonly IOutbox _outbox;

        public Handler(IValidator<Command> validator,
                       IOrganizationRepository organizationRepository,
                       ITransactionManager transactionManager,
                       IPublisher publisher,
                       IOutbox outbox)
        {
            _validator = validator;
            _organizationRepository = organizationRepository;
            _transactionManager = transactionManager;
            _publisher = publisher;
            _outbox = outbox;
        }

        public async Task<Response> Handle(Command request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            AppPermissions.Validate(request.ResourcePermissions.Select(p => p.Permission.Value), request.IsAdmin);

            using var transaction = await _transactionManager.CreateTransactionAsync();

            var organization = await _organizationRepository.FindAsync(request.OrganizationId, cancellationToken);
            if (organization == null)
            {
                return new Response(Result.OrganizationNotFound);
            }

            if (organization.Roles.Any(r => r.Name == request.Name))
            {
                return new Response(Result.RoleNameAlreadyExists);
            }

            var resourcePermissions = request.ResourcePermissions;

            if (request.IsAdmin)
            {
                resourcePermissions.Insert(0, new ResourcePermission(new ResourceId(organization.Id), new Permission(Fqdn<AppPermissions>.GetName(AppPermissions.Main.AccessControl.Organizations.Read))));
            }

            var (roleId, events) = organization.CreateRole(GuidGenerator.New(), request.Name, request.IsAdmin, resourcePermissions.Select(p => (GuidGenerator.New(), p)));

            await _organizationRepository.SaveChangesAsync(cancellationToken);

            foreach (var @event in events)
            {
                await _publisher.Publish(@event, cancellationToken);
            }

            await _outbox.AddRangeAsync(events);

            await transaction.CommitAsync();

            return new Response(roleId);
        }
    }
}