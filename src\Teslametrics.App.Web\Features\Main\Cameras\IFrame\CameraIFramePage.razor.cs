using Microsoft.AspNetCore.Components;
using MudBlazor;
using System.Reactive;
using System.Reactive.Disposables;
using System.Text;
using Teslametrics.App.Web.Extensions;
using Teslametrics.App.Web.Orleans.Camera;

namespace Teslametrics.App.Web.Features.Main.Cameras.IFrame;

public partial class CameraIFramePage
{
	private string _playerId => new StringBuilder(nameof(CameraIFramePage)).Append("-").Append(CameraId).ToString();
	private string _pathToFile = string.Empty;
	private CameraStatus _status = CameraStatus.Stopped;
	private bool _subscribing;
	private DateTime _lastRefreshTime;
	private SubscribeCameraUseCase.Response? _subscriptionResult;

	[Parameter]
	public Guid? CameraId { get; set; }

	protected override async Task OnInitializedAsync()
	{
		await base.OnInitializedAsync();
		await FetchAsync();
		await SubscribeAsync();
		_lastRefreshTime = DateTime.Now;
	}
	private async Task FetchAsync()
	{
		try
		{
			if (CameraId is null) return;

			await SetLoadingAsync(true);
			_lastRefreshTime = DateTime.Now;
			var response = await ScopeFactory.MediatorSend(new GetCameraUseCase.Query(CameraId.Value));
			switch (response.Result)
			{
				case GetCameraUseCase.Result.Success:
					_status = response.CameraStatus;
					_pathToFile = "/publicaccess/" + CameraId + "/stream.m3u8";
					break;
				case GetCameraUseCase.Result.ValidationError:
					Logger.LogWarning($"ValidationError result in {nameof(GetCameraUseCase)}: {response.Result}");
					break;
				case GetCameraUseCase.Result.CameraNotFound:
					break;
				case GetCameraUseCase.Result.Unknown:
				default:
					throw new Exception($"Unexpected result in {nameof(GetCameraUseCase)}: {response.Result}");
			}
		}
		catch (Exception exc)
		{
			Logger.LogError(exc, exc.Message);
			Snackbar.Add("Не удалось получить обновление данных камеры из-за непредвиденной ошибки.", Severity.Error);
		}
		finally
		{
			await SetLoadingAsync(false);
		}
	}
	private async Task SubscribeAsync()
	{
		try
		{
			Unsubscribe();

			if (CameraId is null) return;
			await SetSubscribingAsync(true);
			_subscriptionResult = await ScopeFactory.MediatorSend(new SubscribeCameraUseCase.Request(Observer.Create<object>(OnAppEventHandler, OnError), CameraId.Value));
			await SetSubscribingAsync(false);
			switch (_subscriptionResult.Result)
			{
				case SubscribeCameraUseCase.Result.Success:
					CompositeDisposable.Add(_subscriptionResult.Subscription!);
					break;
				case SubscribeCameraUseCase.Result.ValidationError:
					Snackbar.Add("Ошибка валидации при подписке на события", Severity.Error);
					break;
				case SubscribeCameraUseCase.Result.Unknown:
				default:
					throw new Exception($"Unexpected result in {nameof(SubscribeCameraUseCase)}: {_subscriptionResult.Result}");
			}
		}
		catch (Exception ex)
		{
			await SetSubscribingAsync(false);
			Snackbar.Add("Не удалось получить подписку на события камеры из-за непредвиденной ошибки. Повторите попытку", Severity.Error);
			Logger.LogError(ex, ex.Message);
		}
	}
	private void Unsubscribe()
	{
		if (_subscriptionResult?.Subscription is not null)
		{
			CompositeDisposable.Remove(_subscriptionResult.Subscription);
			_subscriptionResult.Subscription.Dispose();
		}
	}

	protected Task SetSubscribingAsync(bool isLoading = true) => UpdateViewAsync(() =>
	{
		_subscribing = isLoading;
	});

	private async void OnAppEventHandler(object appEvent)
	{
		await FetchAsync();
		await UpdateViewAsync();
	}

	private void OnError(Exception exc)
	{
	}
}
