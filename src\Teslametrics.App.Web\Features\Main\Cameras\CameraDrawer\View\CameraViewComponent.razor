@using Teslametrics.App.Web.Extensions
@using Teslametrics.App.Web.Features.Main.Cameras.CameraDrawer.View.PublicAccessView
@using Teslametrics.App.Web.Features.Main.Cameras.CameraDrawer.View.Onvif
@using Teslametrics.MediaServer.Orleans.Camera
@using Teslametrics.Shared
@inherits InteractiveBaseComponent
<DrawerHeader>
	<MudStack Spacing="0">
		<MudText Typo="Typo.h1">Настройки камеры</MudText>
		@if (IsLoading && _model is null)
		{
			<MudSkeleton Width="30%"
						 Height="42px"></MudSkeleton>
		}
		@if (IsLoading && _model is not null && !_model.IsSuccess)
		{
			<MudText Typo="Typo.subtitle1">Не удалось получить данные о камере.</MudText>
		}
		@if (_model is not null && _model.IsSuccess)
		{
			<MudText Typo="Typo.subtitle1">@_model.Name</MudText>
		}
	</MudStack>
	@if (IsLoading)
	{
		<MudProgressCircular Color="Color.Primary"
							 Indeterminate="true" />
	}
	<MudSpacer />
	@if (IsLoading)
	{
		<MudSkeleton SkeletonType="SkeletonType.Circle"
					 Width="50px"
					 Height="50px" />
	}
	@if (!IsLoading && _model is not null && _model.IsSuccess)
	{
		@if (!_subscribing && (_subscriptionResult is null || !_subscriptionResult.IsSuccess))
		{
			<MudTooltip Arrow="true"
						Placement="Placement.Start"
						Text="Ошибка подписки на события">
				<MudIconButton OnClick="SubscribeAsync"
							   Icon="@Icons.Material.Filled.ErrorOutline"
							   Color="Color.Error" />
			</MudTooltip>
		}
		<MudIconButton OnClick="RefreshAsync"
					   Icon="@Icons.Material.Filled.Refresh"
					   Color="Color.Primary" />
		<MudTooltip Arrow="true"
					Placement="Placement.Start"
					Text="Статус подключения камеры">
			@switch (_model.CameraStatus)
			{
				case CameraStatus.Running:
					<MudChip T="string"
							 Variant="Variant.Outlined"
							 Color="Color.Success">Подключена</MudChip>
					break;
				case CameraStatus.Problem:
					<MudChip T="string"
							 Variant="Variant.Outlined"
							 Color="Color.Error">Ошибка</MudChip>
					break;
				case CameraStatus.Stopped:
					<MudChip T="string"
							 Variant="Variant.Outlined"
							 Color="Color.Warning">Отключена</MudChip>
					break;
				case CameraStatus.Starting:
					<MudChip T="string"
							 Variant="Variant.Outlined"
							 Color="Color.Info">Подключается</MudChip>
					break;
				default:
					<MudChip T="string"
							 Variant="Variant.Outlined"
							 Color="Color.Error">Неизвестно</MudChip>
					break;
			}
		</MudTooltip>
	}
</DrawerHeader>
@if (IsLoading)
{
	<MudProgressLinear Color="Color.Primary"
					   Indeterminate="true" />
}
else
{
	<div style="height: 4px;" />
}
<div class="px-4 py-4">
	@if (_model is not null && _model.IsSuccess)
	{
		<MudTabs PanelClass="mud-height-full px-4 py-4 d-flex flex-column gap-4"
				 TabHeaderClass="mt-3 mx-3"
				 KeepPanelsAlive="true">
			<MudTabPanel Text="Информация в системе">
				<FormSectionComponent Title="Описание камеры"
									  Subtitle="Настройки, которые влияют только на восприятие человеком">

					<MudTextField Value="_model.Name"
								  ReadOnly="true"
								  Label="Наименование" />
				</FormSectionComponent>
				<FormSectionComponent Title="Параметры камеры"
									  Subtitle="Данные настройки важны для работы в системе">
					<MudTextField Value="@CameraId.ToString("N")"
								  ReadOnly="true"
								  Label="ID камеры" />

					<MudTextField Value="@(_model.QuotaName ?? "Квота не задана")"
								  ReadOnly="true"
								  Label="Наименование квоты" />


					<MudTextField Value="_model.ArchiveUri"
								  InputType="InputType.Text"
								  Label="Ссылка на архивный поток"
								  HelperText="По данной ссылке медиасервер будет получать поток с камеры"
								  ReadOnly="true" />

					<MudTextField Value="_model.ViewUri"
								  InputType="InputType.Text"
								  Label="Ссылка на поток для видов"
								  HelperText="По данной ссылке медиасервер будет получать поток с камеры"
								  ReadOnly="true" />

					<MudTextField Value="_model.PublicUri"
								  InputType="InputType.Text"
								  Label="Ссылка на публичный поток"
								  HelperText="По данной ссылке медиасервер будет получать поток с камеры"
								  ReadOnly="true" />

					<MudCheckBox Value="_model.AutoStart"
								 Label="Автозапуск при перезапуске системы"
								 Color="Color.Primary"
								 Class="ml-n3"
								 ReadOnly="true" />

					<MudCheckBox T="bool"
								 Value="_model.OnvifEnabled"
								 Color="Color.Primary"
								 Class="ml-n3"
								 Label="Включить ONVIF?"
								 ReadOnly="true" />
				</FormSectionComponent>
				<FormSectionComponent Title="Статус камеры">
					<MudStack Spacing="8"
							  Row="true">
						@if (_model.IsBlocked)
						{
							<MudChip T="string"
									 Variant="Variant.Outlined"
									 Color="Color.Error">Заблокирована</MudChip>
						}
						else
						{
							@switch (_model.CameraStatus)
							{
								case CameraStatus.Running:
									<MudChip T="string"
											 Variant="Variant.Outlined"
											 Color="Color.Success">Подключена</MudChip>
									<MudSpacer />
									<AuthorizeView Policy="@AppPermissions.Main.Cameras.Disconnect.GetEnumPermissionString()"
												   Resource="new PolicyRequirementResource(OrganizationId, _model.Id)"
												   Context="innerContext">
										<MudButton OnClick="DisconnectAsync"
												   Color="Color.Warning"
												   Variant="Variant.Outlined">
											Отключить
										</MudButton>
									</AuthorizeView>
									break;
								case CameraStatus.Stopped:
									<MudChip T="string"
											 Variant="Variant.Outlined"
											 Color="Color.Warning">Отключена</MudChip>
									<MudSpacer />
									<AuthorizeView Policy="@AppPermissions.Main.Cameras.Connect.GetEnumPermissionString()"
												   Resource="new PolicyRequirementResource(OrganizationId, _model.Id)"
												   Context="innerContext">
										<MudButton OnClick="ConnectAsync"
												   Color="Color.Primary"
												   Variant="Variant.Outlined">
											Подключить
										</MudButton>
									</AuthorizeView>
									break;
								case CameraStatus.Starting:
									<MudChip T="string"
											 Variant="Variant.Outlined"
											 Color="Color.Info">Подключается</MudChip>
									<MudProgressCircular Color="Color.Info"
														 Indeterminate="true" />
									<MudSpacer />
									<AuthorizeView Policy="@AppPermissions.Main.Cameras.Disconnect.GetEnumPermissionString()"
												   Resource="new PolicyRequirementResource(OrganizationId, _model.Id)"
												   Context="innerContext">
										<MudButton OnClick="DisconnectAsync"
												   Color="Color.Warning"
												   Variant="Variant.Outlined">
											Отключить
										</MudButton>
									</AuthorizeView>
									break;
								case CameraStatus.Problem:
									<MudChip T="string"
											 Variant="Variant.Outlined"
											 Color="Color.Info">Ошибка</MudChip>
									<MudSpacer />
									<AuthorizeView Policy="@AppPermissions.Main.Cameras.Connect.GetEnumPermissionString()"
												   Resource="new PolicyRequirementResource(OrganizationId, _model.Id)"
												   Context="innerContext">
										<MudButton OnClick="DisconnectAsync"
												   Color="Color.Primary"
												   Variant="Variant.Outlined">
											Отключить
										</MudButton>
									</AuthorizeView>
									break;
								default:
									break;
							}
						}
					</MudStack>
				</FormSectionComponent>

				<FormSectionComponent Title="Публичный доступ к камере"
									  Subtitle="Адреса, с помощью которых можно получить доступ к камере">
					<CameraPublicAccessListComponent CameraId="_model.Id" />
				</FormSectionComponent>

				<FormSectionComponent Title="Местонахождение камеры">
					<TimeZoneSelector TimeZone="@_model.TimeZone"
									  Label="Часовой пояс"
									  ReadOnly="true"
									  HelperText="Часовой пояс, в котором будет расположена камера" />

					<YandexMaps ReadOnly="true"
								Coordinates="@_model.Coordinates"
								Width="calc(100% + 32px)"
								Class="ma-n4 rounded-b overflow-hidden"
								Height="400px" />
				</FormSectionComponent>
			</MudTabPanel>

			<MudTabPanel Text="ONVIF"
						 Disabled="!_model.OnvifEnabled">
				@if (_model.OnvifOptions is not null)
				{
					<OnvifComponent Onvif="_model.OnvifOptions" />
				}
			</MudTabPanel>
		</MudTabs>
	}
	<FormLoadingComponent IsLoading="IsLoading && _model is null" />
	<NotFoundComponent IsFound="_model is not null && _model.IsSuccess" />
</div>
<DrawerActions>
	<MudSpacer />
	<MudButton OnClick="Cancel">Закрыть</MudButton>
	@if (!IsLoading && _model is not null && _model.IsSuccess)
	{
		<AuthorizeView Policy="@AppPermissions.Main.Cameras.Update.GetEnumPermissionString()"
					   Resource="new PolicyRequirementResource(OrganizationId, _model.Id)"
					   Context="innerContext">
			<MudButton OnClick="Edit"
					   Color="Color.Secondary"
					   Variant="Variant.Outlined">Редактировать</MudButton>
		</AuthorizeView>
	}
	else
	{
		<MudSkeleton Width="150px"
					 Height="36.5px" />
	}
</DrawerActions>