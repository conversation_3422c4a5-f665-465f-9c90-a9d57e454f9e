using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Domain.PublicLinks.Events;

namespace Teslametrics.App.Web.Domain.PublicLinks;

public class PublicLinkAggregate : IEntity
{
    public Guid Id { get; private set; }

    public Guid CameraId { get; private set;}

    public string Name { get; private set; }

    public static (PublicLinkAggregate PublicLink, List<object> Events) Create(Guid id, Guid cameraId, string name)
    {
        return (new PublicLinkAggregate(id, cameraId, name), [new PublicLinkCreatedEvent(id, cameraId)]);
    }

    private PublicLinkAggregate()
    {
        Name = string.Empty;
    }

    public PublicLinkAggregate(Guid id, Guid cameraId, string name)
    {
        Id = id;
        CameraId = cameraId;
        Name = name;
    }

    public List<object> Update(string name)
    {
        if (Name != name)
        {
            Name = name;
            return [new PublicLinkUpdatedEvent(Id, CameraId)];
        }

        return [];
    }
}
