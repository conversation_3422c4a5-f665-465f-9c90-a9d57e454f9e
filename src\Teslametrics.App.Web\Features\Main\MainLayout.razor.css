::deep .appbar {
    padding: 0px 48px;
    background: var(--mud-palette-background);
}

.toolbar_items {
    display: flex;
    gap: 16px;
    align-items: center;
    justify-content: flex-end;
    width: 100%;
}

::deep #layout_main_content_wrapper {
    padding-bottom: 80px;
    padding-left: 48px;
    padding-right: 40px;
}

/* Отступ снизу для основного контента на мобильных устройствах */
@media (max-width: 767px) {
	::deep #layout_main_content_wrapper {
        padding-left: 0;
        padding-right: 0;
		padding-bottom: 56px; /* Высота нижней навигации + отступ */
	}
    ::deep .appbar {
        padding: 0px 8px;
    }
    .toolbar_items {
        gap: 8px;
    }
}