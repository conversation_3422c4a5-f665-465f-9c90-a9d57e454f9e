::deep #layout_sidebar {
	background: rgba(23, 24, 26, 1);
}

::deep #layout_sidebar {
	z-index: calc(var(--mud-zindex-appbar) - 1);
}

@media (min-width: 641px) {
	::deep #layout_sidebar {
		border-right: 1px solid var(--mud-palette-divider);
	}
}

::deep layout_sidebar:not(.mud-drawer-mini) .nav_menu {
	padding: 8px;
}

::deep .mud-nav-link {
	padding-left: 8px;
}

::deep .mud-nav-link .mud-nav-link-text {
	text-overflow: ellipsis;
	overflow: hidden;
	white-space: nowrap;
}

::deep .nav_link a {
	height: 48px;
	border-radius: 8px;
	padding-top: 12px;
	padding-right: 16px;
	padding-bottom: 12px;
	padding-left: 16px;
	gap: 10px;
	margin: 0;
	display: flex;
	align-items: center;
}

::deep .nav_link svg {
	width: 16px;
	height: 16px;
}

::deep .nav_link a>svg {
	font-size: 16px;
}

::deep .nav_link a .mud-nav-link-text {
	margin: 0;
}

::deep .nav_link>* {
	padding: 16px 12px;
	border-radius: 8px;
	color: rgba(255, 255, 255, 1);
}

::deep .nav_link .active {
	background: rgba(46, 48, 51, 1) !important;
}

::deep .time_chip {
	border-radius: 12px !important;
	height: 48px !important;
}