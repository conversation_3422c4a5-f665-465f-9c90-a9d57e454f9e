using ArchUnitNET.Domain;
using ArchUnitNET.Domain.Extensions;
using ArchUnitNET.Loader;
using ArchUnitNET.xUnit;
using Teslametrics.Core.Abstractions;
using static ArchUnitNET.Fluent.ArchRuleDefinition;

namespace ArchTests;

public class DomainTests
{
    private static readonly Architecture Architecture;

    private static readonly IObjectProvider<Interface> EntityInterface;

    static DomainTests()
    {
        Architecture = new ArchLoader().LoadAssemblies(
            System.Reflection.Assembly.Load("Teslametrics.App.Web")
        ).Build();

        EntityInterface = Interfaces().That().AreAssignableTo(Architecture.GetInterfaceOfType(typeof(IEntity)));
    }

    [Fact]
    public void AggregateShouldInheritFromEntity() =>
        Classes()
        .That()
        .HaveNameEndingWith("Aggregate")
        .And()
        .ResideInNamespace(@"/*.Domain./*", true)
        .Should()
        .BeAssignableTo(EntityInterface)
        .Check(Architecture);
}