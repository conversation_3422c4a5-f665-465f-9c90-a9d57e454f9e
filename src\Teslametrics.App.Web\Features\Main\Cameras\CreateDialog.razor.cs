using MudBlazor;
using Teslametrics.App.Web.Events.Cameras;

namespace Teslametrics.App.Web.Features.Main.Cameras;

public partial class CreateDialog
{
	private DialogOptions _dialogOptions = new() { CloseOnEscapeKey = true, FullWidth = true, MaxWidth = MaxWidth.Medium, CloseButton = true };
	private bool _isVisible;
	private Guid _folderId;
	private Guid _organizationId;

	protected override void OnInitialized()
	{
		base.OnInitialized();

		CompositeDisposable.Add(EventSystem.Subscribe<EntityCreateEto>(OnCreateHandler));
	}

	#region [Actions]
	private void Cancel()
	{
		_isVisible = false;
	}

	private void CreateGroup() => EventSystem.Publish(new CameraGroupCreateEto(_organizationId, _folderId));

	private void CreateCamera()
	{
		EventSystem.Publish(new CameraCreateEto(_organizationId, _folderId));
		_isVisible = false;
	}
	#endregion

	private void OnCreateHandler(EntityCreateEto eto)
	{
		_organizationId = eto.OrganizationId;
		_folderId = eto.GroupId is null ? Guid.Empty : eto.GroupId.Value;
		_isVisible = true;
		StateHasChanged();
	}
}