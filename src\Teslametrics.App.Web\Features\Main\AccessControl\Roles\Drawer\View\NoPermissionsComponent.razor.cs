using Microsoft.AspNetCore.Components;
using System.Reflection;
using System.Reflection.Metadata;
using Teslametrics.App.Web.Domain.AccessControl;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Roles.Drawer.View;

public partial class NoPermissionsComponent
{
	private bool IsFound;

	[Parameter]
	public bool IsWildcard { get; set; }

	[Parameter]
	public Type[] PermissionEnumTypes { get; set; } = null!;

	[Parameter]
	public IEnumerable<ResourcePermission> Selected { get; set; } = [];


	protected override void OnInitialized()
	{
		base.OnInitialized();

		IsFound = false;

		IEnumerable<ResourcePermission> checkList = IsWildcard ? Selected.Where(x => x.ResourceId.IsWildcard) : Selected.Where(x => !x.ResourceId.IsWildcard);
		foreach (var item in PermissionEnumTypes)
		{
			var space = GetNameSpace(item);
			if(space is null) continue;

			foreach (var value in checkList)
			{
				if(value.Permission.Value.Contains(space))
				{
					IsFound = true;
					return;
				}
			}
		}
	}

	private string? GetNameSpace(Type type)
	{
		if (!type.IsEnum) return null;
		var namespaceParts = new Stack<string>();

		while (type != null)
		{
			namespaceParts.Push(type.Name);

			if (type.GetCustomAttribute<FqdnRootAttribute>() != null)
			{
				break;
			}
			type = type.DeclaringType!;
		}

		return string.Join(".", namespaceParts) + ".";
	}
}
