using Microsoft.AspNetCore.Components;
using Teslametrics.App.Web.Shared;

namespace Teslametrics.App.Web.Features.Main.Incidents;

public partial class IncidentsPage
{
	public Guid? _selectedIncident { get; set; }
	[Parameter]
	public DateTime DateFrom { get; set; } = DateTime.Today;

	[Parameter]
	public DateTime DateTo { get; set; } = DateTime.Today.AddDays(1).AddMilliseconds(-1);
	[Parameter]
	public Guid? CityId { get; set; }
	[Parameter]
	public Guid? BuildingId { get; set; }
	[Parameter]
	public Guid? FloorId { get; set; }
	[Parameter]
	public Guid? RoomId { get; set; }
	[Parameter]
	public Guid? FridgeId { get; set; }
	[Parameter]
	public IncidentType? IncidentType { get; set; }
	[Parameter]
	public bool? IsResolved { get; set; }

	[Parameter]
	public Guid? IncidentId { get; set; }
}
