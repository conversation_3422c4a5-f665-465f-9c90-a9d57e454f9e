using Teslametrics.App.Web.Abstractions;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Quota.UserQuota;

public static class UpdateUserQuotaUseCase
{
    public record Command(Guid OrganizationId, int TotalQuota);//: BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public Guid Id { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(Guid id)
        {
            Id = id;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Id = Guid.Empty;
            Result = result;
        }
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        OrganizationNotFound
    }
}
