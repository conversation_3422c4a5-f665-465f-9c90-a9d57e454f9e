using System.Data;
using System.Reactive.Linq;
using Dapper;
using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Data.Sql;
using Teslametrics.App.Web.Domain.AccessControl.Organizations.Events;
using Teslametrics.App.Web.Domain.Cameras.Events;
using Teslametrics.App.Web.Domain.Folders.Events;
using Teslametrics.App.Web.Domain.PublicLinks.Events;
using Teslametrics.App.Web.Services.DomainEventBus;
using Teslametrics.App.Web.Services.Persistence;

namespace Teslametrics.App.Web.Features.Main.CameraPublicAccess;

public static class TreeNodeSubscribeUseCase
{
    public record Request(IObserver<UpdatedEvent> Observer, List<Guid> OrganizationIds) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public IDisposable? Subscription { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(IDisposable subscription)
        {
            Subscription = subscription;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Subscription = null;
            Result = result;
        }
    }

    public record UpdatedEvent(); // Удалены/добавлены/изменены организации/камеры/ссылки/директории

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError
    }

    public class Validator : AbstractValidator<Request>
    {
        public Validator()
        {
            RuleFor(r => r.Observer).NotEmpty();
            RuleFor(r => r.OrganizationIds).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Request, Response>
    {
        private readonly IValidator<Request> _validator;
        private readonly IDomainEventBus _domainEventBus;
        private readonly IDbConnection _dbConnection;

        public Handler(IValidator<Request> validator,
                       IDomainEventBus domainEventBus,
                       IDbConnection dbConnection)
        {
            _validator = validator;
            _domainEventBus = domainEventBus;
            _dbConnection = dbConnection;
        }

        public async Task<Response> Handle(Request request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            var template = SqlQueryBuilder.Create()
                .Select(Db.Cameras.Props.Id)
                .Where(Db.Cameras.Props.OrganizationId, ":OrganizationIds", SqlOperator.Any, new { request.OrganizationIds })
                .Build(QueryType.Standard, Db.Cameras.Table, RowSelection.AllRows);

            var cameras = await _dbConnection.QueryAsync<CameraModel>(template.RawSql, template.Parameters);

            var eventStream = await _domainEventBus.GetEventStreamAsync();

            var subscription = eventStream
                .Where(e => e switch
                {
                    OrganizationCreatedEvent => true,
                    OrganizationUpdatedEvent @event => request.OrganizationIds.Contains(@event.Id),
                    OrganizationDeletedEvent @event => request.OrganizationIds.Contains(@event.Id),
                    FolderCreatedEvent @event => request.OrganizationIds.Contains(@event.OrganizationId),
                    FolderUpdatedEvent @event => request.OrganizationIds.Contains(@event.OrganizationId),
                    FolderParentChangedEvent @event => request.OrganizationIds.Contains(@event.OrganizationId),
                    FolderDeletedEvent @event => request.OrganizationIds.Contains(@event.OrganizationId),
                    CameraCreatedEvent @event => request.OrganizationIds.Contains(@event.OrganizationId),
                    CameraUpdatedEvent @event => request.OrganizationIds.Contains(@event.OrganizationId),
                    CameraFolderChangedEvent @event => request.OrganizationIds.Contains(@event.OrganizationId),
                    CameraDeletedEvent @event => request.OrganizationIds.Contains(@event.OrganizationId),
                    PublicLinkCreatedEvent @event => cameras.Any(c => c.Id == @event.CameraId),
                    PublicLinkUpdatedEvent @event => cameras.Any(c => c.Id == @event.CameraId),
                    PublicLinkReissuedEvent @event => cameras.Any(c => c.Id == @event.CameraId),
                    PublicLinkDeletedEvent @event => cameras.Any(c => c.Id == @event.CameraId),
                    _ => false
                })
                .Select(e => new UpdatedEvent())
                .Subscribe(request.Observer);

            return new Response(subscription);
        }
    }

    public record CameraModel(Guid Id);
}