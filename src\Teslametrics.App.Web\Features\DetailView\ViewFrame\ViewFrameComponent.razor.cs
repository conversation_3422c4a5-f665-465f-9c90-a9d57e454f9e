using System.Reactive;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using Teslametrics.App.Web.Events.CameraView;
using Teslametrics.App.Web.Extensions;
using Teslametrics.App.Web.Shared;

namespace Teslametrics.App.Web.Features.DetailView.ViewFrame;

public partial class ViewFrameComponent
{
	private Guid _organizationId = Guid.Empty;
	private Guid _viewId = Guid.Empty;
	private bool _subscribing;
	private DateTime _lastRefreshTime;

	private SubscribeViewUseCase.Response? _subscriptionResult;
	private GetViewUseCase.Response? _viewResponse;
	private List<GetViewUseCase.Response.Cell> _cells = new List<GetViewUseCase.Response.Cell>();

	[Inject]
	public IJSRuntime JsRuntime { get; set; } = null!;

	[Parameter]
	[EditorRequired]
	public Guid OrganizationId { get; set; }

	[Parameter]
	[EditorRequired]
	public Guid ViewId { get; set; }

	protected override async Task OnAfterRenderAsync(bool firstRender)
	{
		await base.OnAfterRenderAsync(firstRender);
		if (firstRender)
		{
			try
			{
				var jsModule = await JsRuntime.InvokeAsync<IJSObjectReference>("import", "./Features/DetailView/ViewFrame/ViewFrameComponent.razor.js");
				if (jsModule is not null)
				{
					await jsModule.InvokeVoidAsync($"initObservers");
					await jsModule.DisposeAsync();
				}
			}
			catch (JSDisconnectedException)
			{

			}
			catch (Exception exc)
			{
				Logger.LogError(exc, exc.Message);
			}
		}
	}

	protected override async Task OnParametersSetAsync()
	{
		await base.OnParametersSetAsync();

		if (OrganizationId != _organizationId || ViewId != _viewId)
		{
			_organizationId = OrganizationId;
			_viewId = ViewId;

			await SubscribeAsync();
			await FetchAsync();
		}
	}

	private async Task FetchAsync()
	{
		await SetLoadingAsync(true);
		try
		{
			_viewResponse = await ScopeFactory.MediatorSend(new GetViewUseCase.Query(ViewId));
		}
		catch (Exception exc)
		{
			_viewResponse = null;
			Logger.LogError(exc, exc.Message);
			Snackbar.Add("Не удалось получить вид из-за непредвиденной ошибки сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
		}

		await SetLoadingAsync(false);
		if (_viewResponse is null) return;

		switch (_viewResponse.Result)
		{
			case GetViewUseCase.Result.Success:
				FillEmptyCells();
				_lastRefreshTime = DateTime.Now;
				break;
			case GetViewUseCase.Result.ViewNotFound:
				Snackbar.Add("Вид не существует", MudBlazor.Severity.Error);
				break;
			case GetViewUseCase.Result.Unknown:
				Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(ViewFrameComponent), nameof(GetViewUseCase));
				Snackbar.Add($"Не удалось получить вид из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
				break;
			default:
				Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(ViewFrameComponent), nameof(GetViewUseCase), _viewResponse.Result);
				Snackbar.Add($"Не удалось получить вид из-за непредвиденной ошибки: {_viewResponse.Result}. Обратитесь к администратору.", MudBlazor.Severity.Error);
				break;
		}
	}

	private void FillEmptyCells()
	{
		if (_viewResponse == null) return;

		int cellCount = _viewResponse.GridType switch
		{
			GridType.Grid1Plus5 => 6,
			GridType.Grid1Plus7 => 8,
			GridType.Grid1Plus12 => 13,
			GridType.Grid2Plus8 => 10,
			GridType.Grid3Plus4 => 7,
			_ => _viewResponse.RowCount * _viewResponse.ColumnCount
		};
		_cells.Clear();
		_cells.Capacity = cellCount;

		// Initialize all cells as empty
		for (short i = 0; i < cellCount; i++)
		{
			_cells.Add(new GetViewUseCase.Response.Cell(Guid.Empty, i));
		}

		// Fill in the actual cells from _viewResponse
		foreach (var cell in _viewResponse.Cells)
		{
			if (cell.CellIndex < cellCount)
			{
				_cells[cell.CellIndex] = cell;
			}
		}
	}

	private async Task SubscribeAsync()
	{
		try
		{
			Unsubscribe();

			await SetSubscribingAsync(true);
			_subscriptionResult = await ScopeFactory.MediatorSend(new SubscribeViewUseCase.Request(Observer.Create<object>(OnAppEventHandler, OnError), ViewId));
			await SetSubscribingAsync(false);
			switch (_subscriptionResult.Result)
			{
				case SubscribeViewUseCase.Result.Success:
					CompositeDisposable.Add(_subscriptionResult.Subscription!);
					break;
				case SubscribeViewUseCase.Result.ValidationError:
					Snackbar.Add("Ошибка валидации при подписке на события", MudBlazor.Severity.Error);
					break;
				case SubscribeViewUseCase.Result.Unknown:
				default:
					throw new Exception($"Unexpected result in {nameof(SubscribeViewUseCase)}: {_subscriptionResult.Result}");
			}
		}
		catch (Exception ex)
		{
			await SetSubscribingAsync(false);
			Snackbar.Add("Не удалось получить подписку на события камеры из-за непредвиденной ошибки. Повторите попытку", MudBlazor.Severity.Error);
			Logger.LogError(ex, ex.Message);
		}
	}

	private void Unsubscribe()
	{
		if (_subscriptionResult?.Subscription is not null)
		{
			CompositeDisposable.Remove(_subscriptionResult.Subscription);
			_subscriptionResult.Subscription.Dispose();
		}
	}

	protected Task SetSubscribingAsync(bool isLoading = true) => UpdateViewAsync(() =>
	{
		_subscribing = isLoading;
	});

	#region [Actions]
	private Task RefreshAsync() => FetchAsync();
	private void View() => EventSystem.Publish(new CameraViewSelectEto(OrganizationId, ViewId));
	private void Edit() => EventSystem.Publish(new CameraViewEditEto(OrganizationId, ViewId));
	private void Delete() => EventSystem.Publish(new CameraViewDeleteEto(OrganizationId, ViewId));
	#endregion

	#region [Event Handlers]
	private async void OnAppEventHandler(object appEvent)
	{
		switch (appEvent)
		{
			case SubscribeViewUseCase.DeletedEvent deleted:
				Unsubscribe();
				await FetchAsync();
				break;
			case SubscribeViewUseCase.UpdatedEvent updated:
				await FetchAsync();
				await UpdateViewAsync();
				break;
		}
	}

	private void OnError(Exception exc)
	{
		Snackbar.Add("Ошибка при подписке на события", MudBlazor.Severity.Error);
		Logger.LogError(exc, exc.Message);
	}
	#endregion [Event Handlers]
}
