﻿using MediatR;
using Microsoft.AspNetCore.Components.Authorization;
using MudBlazor;
using System.Reactive;
using Teslametrics.App.Web.Extensions;

namespace Teslametrics.App.Web.Features.Main.Profile;

public partial class Profile
{
	private GetUserProfileUseCase.Response? Model;
	private Guid _userId;

	protected override async Task OnInitializedAsync()
	{
		var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
		_userId = authState.User.GetUserId()!.Value;

		await SubscribeAsync();
		await FetchAsync();
		await base.OnInitializedAsync();
	}
	protected async Task FetchAsync()
	{
		if (IsLoading) return;

		try
		{
			await SetLoadingAsync();
			var response = await ScopeFactory.CreateScope().ServiceProvider.GetRequiredService<IMediator>().Send(new GetUserProfileUseCase.Query(_userId));
			if (response.IsSuccess)
			{
				Model = response;
				return;
			}

			switch (response.Result)
			{
				case GetUserProfileUseCase.Result.ValidationError:
					Snackbar.Add("Ошибка валидации данных", Severity.Error);
					break;
				case GetUserProfileUseCase.Result.UserNotFound:
					Snackbar.Add("Ваш профиль не найден", Severity.Error);
					break;
				case GetUserProfileUseCase.Result.Unknown:
				default:
					Snackbar.Add("Не удалось получить роль из-за непредвиденной ошибки:" + response.Result.ToString(), Severity.Error);
					break;
			}
		}
		catch (Exception ex)
		{
			Snackbar.Add("Не удалось получить выбранную роль. Повторите попытку", Severity.Error);
			Logger.LogError(ex, ex.Message);
		}
		finally
		{
			await SetLoadingAsync(false);
		}
	}
	private async Task SubscribeAsync()
	{
		var result = await ScopeFactory.CreateScope().ServiceProvider.GetRequiredService<IMediator>().Send(new SubscribeUserProfileUseCase.Request(Observer.Create<object>(OnAppEventHandler, OnError), _userId));
		if (result.IsSuccess)
		{
			CompositeDisposable.Add(result.Subscription!);
			return;
		}

		switch (result.Result)
		{
			case SubscribeUserProfileUseCase.Result.ValidationError:
				Snackbar.Add("Ошибка валидации при подписке на события", Severity.Error);
				break;

			case SubscribeUserProfileUseCase.Result.Unknown:
			default:
				Snackbar.Add("Не удалось получить подписку на обновления из-за непредвиденной ошибки:" + result.Result.ToString(), Severity.Error);
				break;
		}
	}

	#region [Event Handlers]
	private async void OnAppEventHandler(object appEvent)
	{
		if (Model is null) return;

		switch (appEvent)
		{
			case SubscribeUserProfileUseCase.UpdatedEvent updatedEto:
				await FetchAsync();
				await UpdateViewAsync();
				break;

			default:
				break;
		}
	}

	private void OnError(Exception exc)
	{
		Logger.LogError(exc, exc.Message);
		Snackbar.Add("Ошибка валидации при подписке на события", Severity.Error);
	}
	#endregion [Event Handlers]
}
