using FluentValidation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components;
using System.Reactive;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Components.Drawer;
using Teslametrics.App.Web.Domain.AccessControl;
using Teslametrics.App.Web.Eto.Roles;
using Teslametrics.App.Web.Extensions;
using Teslametrics.Shared;
using Severity = MudBlazor.Severity;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Roles.Drawer.Edit;

public partial class RoleEditComponent
{
	[CascadingParameter(Name = DrawerConsts.InstanceName)]
	private DrawerComponent Drawer { get; set; } = null!;

	private class RoleValidator : BaseFluentValidator<UpdateModel>
	{
		public RoleValidator()
		{
			RuleFor(model => model.Name)
				.Length(3, 60)
				.WithMessage("Название роли должно быть от 3 до 60 символов");

			RuleFor(model => model.Permissions)
				.NotEmpty()
				.WithMessage("Добавьте хотя бы 1 право доступа");
		}
	}
	private class UpdateModel
	{
		public Guid Id { get; set; }

		public string Name { get; set; }

		public bool IsAdmin { get; set; }

		public IEnumerable<ResourcePermission> Permissions { get; set; }

		public UpdateModel(Guid id, string name, IEnumerable<ResourcePermission> permissions)
		{
			Id = id;
			Name = name;
			Permissions = permissions;
		}
	}

	private DateTime _lastRefreshTime = DateTime.Now;

	private Guid _roleId;
	private bool _subscribing;
	private UpdateModel? _model;
	private RoleValidator _roleValidator = new();
	private bool _isValid => _model is null ? false : _roleValidator.Validate(_model).IsValid;

	private GetRoleUseCase.Response? _response;
	private SubscribeRoleUseCase.Response? _subscriptionResult;

	#region Injectables
	[Inject]
	public IAuthorizationService AuthorizationService { get; set; } = null!;
	#endregion

	#region Parameters
	[Parameter]
	[EditorRequired]
	public Guid RoleId { get; set; }

	[Parameter]
	public Guid OrganizationId { get; set; }
	#endregion

	protected override async Task OnParametersSetAsync()
	{
		if (RoleId != _roleId)
		{
			_roleId = RoleId;
			_model = null;
			await FetchAsync();
		}

		await base.OnParametersSetAsync();
	}

	protected async Task FetchAsync()
	{
		if (IsLoading) return;

		await SetLoadingAsync();
		try
		{
			_response = await ScopeFactory.MediatorSend(new GetRoleUseCase.Query(RoleId));
		}
		catch (Exception ex)
		{
			Snackbar.Add("Не удалось получить выбранную роль. Повторите попытку", Severity.Error);
			Logger.LogError(ex, ex.Message);
		}
		await SetLoadingAsync(false);
		switch (_response?.Result)
		{
			case GetRoleUseCase.Result.Success:
				_lastRefreshTime = DateTime.Now;
				_model = new(_response.Id, _response.Name, _response.Permissions)
				{
					IsAdmin = _response.IsAdmin
				};
				await SubscribeAsync();
				break;
			case GetRoleUseCase.Result.ValidationError:
				Snackbar.Add("Ошибка валидации данных", Severity.Error);
				break;
			case GetRoleUseCase.Result.RoleNotFound:
				Snackbar.Add("Роль не найден", Severity.Error);
				break;
			case GetRoleUseCase.Result.Unknown:
				Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(RoleEditComponent), nameof(GetRoleUseCase));
				Snackbar.Add($"Не удалось получить роль из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
				break;
			default:
				Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(RoleEditComponent), nameof(GetRoleUseCase), _response?.Result);
				Snackbar.Add($"Не удалось получить роль из-за ошибки: {_response?.Result}", MudBlazor.Severity.Error);
				break;
		}
	}

	private async Task SubscribeAsync()
	{
		if (_subscribing) return;
		await SetSubscribingAsync(true);

		Unsubscribe();

		try
		{
			_subscriptionResult = await ScopeFactory.MediatorSend(new SubscribeRoleUseCase.Request(Observer.Create<object>(OnAppEventHandler, OnError), RoleId));
		}
		catch (Exception exc)
		{
			Snackbar.Add("Не удалось получить подписку на события роли. Повторите попытку", Severity.Error);
			Logger.LogError(exc, exc.Message);
		}
		await SetSubscribingAsync(false);
		switch (_subscriptionResult?.Result)
		{
			case SubscribeRoleUseCase.Result.Success:
				CompositeDisposable.Add(_subscriptionResult.Subscription!);
				break;

			case SubscribeRoleUseCase.Result.ValidationError:
				Snackbar.Add("Ошибка валидации при подписке на события", Severity.Error);
				break;

			case SubscribeRoleUseCase.Result.Unknown:
				Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(RoleEditComponent), nameof(SubscribeRoleUseCase));
				Snackbar.Add($"Не удалось получить подписку на события роли из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
				break;

			default:
				Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(RoleEditComponent), nameof(SubscribeRoleUseCase), _subscriptionResult?.Result);
				Snackbar.Add($"Не удалось получить подписку на события роли из-за ошибки: {_subscriptionResult?.Result}", MudBlazor.Severity.Error);
				break;
		}
	}

	private void Unsubscribe()
	{
		if (_subscriptionResult is not null && _subscriptionResult.Subscription is not null)
		{
			CompositeDisposable.Remove(_subscriptionResult.Subscription);
			_subscriptionResult.Subscription.Dispose();
		}
	}
	protected Task SetSubscribingAsync(bool isLoading = true) => UpdateViewAsync(() =>
	{
		_subscribing = isLoading;
	});

	#region [Actions]
	private Task RefreshAsync() => FetchAsync();

	private async Task SubmitAsync()
	{
		if (!_isValid || IsLoading || _model is null) return;

		await SetLoadingAsync();
		UpdateRoleUseCase.Response? response = null;
		try
		{
			response = await ScopeFactory.MediatorSend(new UpdateRoleUseCase.Command(OrganizationId, _model.Id, _model.Name, _model.IsAdmin, _model.Permissions.ToList()));
		}
		catch (Exception ex)
		{
			Snackbar.Add("Не удалось обновить роль из-за непредвиденной ошибки, повторите попытку", Severity.Error);
			Logger.LogError(ex, ex.Message);
		}
		await SetLoadingAsync(false);

		switch (response?.Result)
		{
			case UpdateRoleUseCase.Result.Success:
				Snackbar.Add("Роль успешно обновлена", Severity.Success);
				await Drawer.HideAsync();
				break;
			case UpdateRoleUseCase.Result.ValidationError:
				Snackbar.Add("Ошибка валидации данных", Severity.Error);
				break;
			case UpdateRoleUseCase.Result.RoleNotFound:
				Snackbar.Add("Текущая роль недоступна", Severity.Error);
				break;
			case UpdateRoleUseCase.Result.InvalidPermission:
				Snackbar.Add("Выбраны недоступные права доступа", Severity.Error);
				break;
			case UpdateRoleUseCase.Result.CannotUpdateSystemRole:
				Snackbar.Add("Системную роль обновить нельзя!", Severity.Error);
				break;
			case UpdateRoleUseCase.Result.Unknown:
				Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(RoleEditComponent), nameof(UpdateRoleUseCase));
				Snackbar.Add($"Не удалось обновить роль из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
				break;
			default:
				Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(RoleEditComponent), nameof(UpdateRoleUseCase), response?.Result);
				Snackbar.Add($"Не удалось обновить роль из-за ошибки: {response?.Result}", MudBlazor.Severity.Error);
				break;
		}
	}
	private void Delete() => EventSystem.Publish(new RoleDeleteEto(OrganizationId, RoleId));
	private void Select() => EventSystem.Publish(new RoleSelectEto(OrganizationId, _model!.Id));
	private Task CancelAsync() => Drawer.HideAsync();

	private void IsAdminChanged(bool isAdmin)
	{
		if (isAdmin)
		{
			_model!.IsAdmin = true;
		}
		else
		{
			_model!.IsAdmin = false;
			var adminPermissions = AppPermissions.GetAdmin();
			_model!.Permissions = _model!.Permissions.Where(x => !adminPermissions.Contains(x.Permission.Value));
		}
	}
	#endregion

	#region [Event Handlers]
	private async void OnAppEventHandler(object appEvent)
	{
		if (_model is null) return;

		switch (appEvent)
		{
			case SubscribeRoleUseCase.UpdatedEvent updatedEto:
				await FetchAsync();
				await UpdateViewAsync();
				break;

			case SubscribeRoleUseCase.DeletedEvent deletedEto:
				Unsubscribe();
				_model = null;
				Snackbar.Add("Просматриваемая вами роль была удалена", Severity.Warning);
				await Drawer.HideAsync();
				break;

			default:
				break;
		}
	}

	private void OnError(Exception exc)
	{
		Logger.LogError(exc, exc.Message);
		Snackbar.Add("Ошибка валидации при подписке на события", Severity.Error);
	}
	#endregion [Event Handlers]
}