@using Teslametrics.App.Web.Features.Main.Incidents.Filter.Building
@using Teslametrics.App.Web.Features.Main.Incidents.Filter.Floor
@using Teslametrics.App.Web.Features.Main.Incidents.Filter.Room
@using Teslametrics.App.Web.Features.Main.Incidents.Filter.Device
@using Teslametrics.App.Web.Features.Main.IncidentsDashboard.Models
@using Teslametrics.App.Web.Features.Main.IncidentsDashboard.Filter.CityFilter
@using Teslametrics.App.Web.Features.Main.IncidentsDashboard.Filter.DateFilter

@inherits InteractiveBaseComponent
<MudStack Spacing="4">
    <MudStack Spacing="2"
              Justify="Justify.SpaceBetween"
              AlignItems="AlignItems.Center"
              Row="true">
        <DateFilterComponent DateRange="@DateRange"
                             DateRangeChanged="DateRangeChanged" />

        <div>
            <MudButton Variant="Variant.Filled"
                       Color="Color.Primary"
                       StartIcon="@Icons.Material.Filled.Download"
                       Class="download_report_btn">
                Скачать отчет
            </MudButton>
            <MudIconButton Icon="@Icons.Material.Filled.Download"
                           Color="Color.Primary"
                           Variant="Variant.Outlined"
                           Class="download_report_btn_mini" />
        </div>
    </MudStack>

    <MudStack Spacing="2"
              Row="true"
              StretchItems="StretchItems.All">
        <CitySelectorComponent City="@CityId"
                               CityChanged="CityIdChanged" />
        <BuildingSelectorComponent City="@CityId"
                                   BuildingId="BuildingId"
                                   BuildingIdChanged="BuildingIdChanged" />
        <FloorSelectorComponent CityId="@CityId"
                                BuildingId="BuildingId"
                                Floor="FloorId"
                                FloorChanged="FloorIdChanged" />
    </MudStack>

    <MudStack Spacing="2"
              Row="true"
              StretchItems="StretchItems.All">
        <RoomSelectorComponent CityId="@CityId"
                               BuildingId="BuildingId"
                               FloorId="FloorId"
                               RoomId="RoomId"
                               RoomIdChanged="RoomIdChanged" />
        <DeviceSelectorComponent CityId="@CityId"
                                 BuildingId="BuildingId"
                                 FloorId="FloorId"
                                 RoomId="RoomId"
                                 DeviceId="DeviceId"
                                 DeviceIdChanged="DeviceIdChanged" />
    </MudStack>
</MudStack>
