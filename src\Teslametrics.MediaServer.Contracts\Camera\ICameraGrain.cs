using Orleans;

namespace Teslametrics.MediaServer.Orleans.Camera;

public interface ICameraGrain : IGrainWithGuidKey
{
    public Task<CameraStatus> GetStatusAsync();

    public Task ConnectRtspAsync(ConnectRtspRequest request);

    public Task ConnectOnvifAsync(ConnectOnvifRequest request);

    public Task DisconnectAsync();

    public Task<GetCameraStreamIdResponse> GetCameraStreamIdAsync(GetCameraStreamIdRequest request);

    public Task SubscribeAsync(SubscribeRequest request);

    public Task UnsubscribeAsync(UnsubscribeRequest request);

    [GenerateSerializer]
    public record ConnectRtspRequest(string ArchiveUri, string ViewUri, string PublicUri);

    [GenerateSerializer]
    public record ConnectOnvifRequest(string Host, int Port, string Username, string Password);

    [GenerateSerializer]
    public record GetCameraStreamIdRequest(StreamType StreamType);

    [GenerateSerializer]
    public record GetCameraStreamIdResponse(Guid? CameraStreamId);

    [GenerateSerializer]
    public record SubscribeRequest(ICameraStatusObserver Observer);

    [GenerateSerializer]
    public record UnsubscribeRequest(ICameraStatusObserver Observer);
}