namespace Teslametrics.MediaServer.Orleans.Camera;

public interface ICameraGrain : IGrainWithGuidKey
{
    public Task<CameraStatus> GetStatusAsync();

    public Task ConnectRtspAsync(ConnectRtspRequest request);

    public Task ConnectOnvifAsync(ConnectOnvifRequest request);

    public Task DisconnectAsync();

    public Task<GetCameraStreamIdResponse> GetCameraStreamIdAsync(GetCameraStreamIdRequest request);

    [GenerateSerializer]
    public record ConnectRtspRequest(string ArchiveUri, string ViewUri, string PublicUri);

    [GenerateSerializer]
    public record ConnectOnvifRequest(string Host, int Port, string Username, string Password);

    [GenerateSerializer]
    public record GetCameraStreamIdRequest(StreamType StreamType);

    [GenerateSerializer]
    public record GetCameraStreamIdResponse(Guid? CameraStreamId);
}