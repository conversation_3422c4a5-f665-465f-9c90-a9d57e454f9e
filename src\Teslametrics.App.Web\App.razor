@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Localization
@using System.Globalization
@using Teslametrics.App.Web.Features.Main
<!DOCTYPE html>
<html lang="ru-RU">

<head>
    <meta charset="utf-8" />
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0" />
    <base href="/" />
    <link rel="stylesheet"
          href="app.css" />
    <link rel="stylesheet"
          href="Teslametrics.App.Web.styles.css?v=@System.Reflection.Assembly.GetExecutingAssembly().GetName().Version" />
    <link rel="icon"
          type="image/png"
          href="/favicons/favicon-96x96.png"
          sizes="96x96" />
    <link rel="icon"
          type="image/svg+xml"
          href="/favicons/favicon.svg" />
    <link rel="shortcut icon"
          href="/favicons/favicon.ico" />
    <link rel="apple-touch-icon"
          sizes="180x180"
          href="/favicons/apple-touch-icon.png" />
    <meta name="apple-mobile-web-app-title"
          content="Multimonitor" />
    <link rel="manifest"
          href="/favicons/site.webmanifest" />
    <script src="https://cdn.jsdelivr.net/npm/@@microsoft/signalr@8.0.7/dist/browser/signalr.min.js"
            crossorigin="anonymous"
            referrerpolicy="no-referrer"
            onerror="loadLocalSignalR()"
            defer></script>
    <script src="https://cdn.jsdelivr.net/npm/@@microsoft/signalr-protocol-msgpack@8.0.7/dist/browser/signalr-protocol-msgpack.min.js"
            crossorigin="anonymous"
            referrerpolicy="no-referrer"
            onerror="loadLocalSignalRMsgpack()"
            defer></script>

    <script>
        function loadLocalSignalR() {
            const fallbackScript = document.createElement('script');
            fallbackScript.src = '/js/signalr.min.js';
            fallbackScript.defer = true;
            document.head.appendChild(fallbackScript);
        }
        function loadLocalSignalRMsgpack() {
            const fallbackScript = document.createElement('script');
            fallbackScript.src = '/js/signalr-protocol-msgpack.min.js';
            fallbackScript.defer = true;
            document.head.appendChild(fallbackScript);
        }

    </script>
    <script src="https://unpkg.com/mitt/dist/mitt.umd.js"></script>
    <HeadOutlet @rendermode="InteractiveServer" />
</head>

<body class="bg-white dark:bg-black dark:text-white">
    <Routes @rendermode="InteractiveServer" />
    <script src="_framework/blazor.web.js"
            defer></script>
    <!-- additional js libs -->
    <script src="_content/MudBlazor/MudBlazor.min.js"
            defer
            asp-append-version="true"
            async></script>
    <script src="/js/imageHelper.js"
            defer
            asp-append-version="true"></script>
    <link href="_content/MudBlazor/MudBlazor.min.css"
          rel="stylesheet"
          media="print"
          onload="this.media='all'"
          asp-append-version="true" />

    <link href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/atom-one-dark.min.css"
          rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;700&display=swap"
          rel="stylesheet"
          media="print"
          onload="this.media='all'">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js"
            async></script>
    <script src="https://vjs.zencdn.net/8.16.1/video.min.js"
            async></script>
    <link href="https://vjs.zencdn.net/8.16.1/video-js.css"
          rel="stylesheet" />
    <script src="https://cdn.jsdelivr.net/npm/hls.js@latest"
            async></script>
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"
            async></script>
</body>

</html>

@code {
    [CascadingParameter]
    private HttpContext HttpContext { get; set; } = default!;

    protected override void OnInitialized()
    {
        // Всегда устанавливаем русскую локаль независимо от настроек браузера
        var russianCulture = new CultureInfo("ru-RU");
        CultureInfo.DefaultThreadCurrentCulture = russianCulture;
        CultureInfo.DefaultThreadCurrentUICulture = russianCulture;

        @* new RequestCulture(
		CultureInfo.CurrentCulture,
		CultureInfo.CurrentUICulture))); *@

        HttpContext?.Response.Cookies.Append(
        CookieRequestCultureProvider.DefaultCookieName,
        CookieRequestCultureProvider.MakeCookieValue(
        new RequestCulture(russianCulture, russianCulture)));
    }
}