@using Teslametrics.App.Web.Features.Main.Cameras.TreeView
@using Teslametrics.Shared
@attribute [Route(RouteConstants.Cameras)]
@attribute [AppAuthorize(AppPermissions.Main.Cameras.Read, AppPermissions.Main.Folders.Read)]
@inherits InteractiveBaseComponent
<PageTitle>Multimonitor | Камеры</PageTitle>
@if (!UserDeviceService.IsMobile)
{
    <ResizerComponent Class="mud-height-full overflow-hidden"
                      StorageId="cameras_list_size"
                      MinWidth="320px"
                      Orientation="@_orientation">
        <LeftPanel>
            <MudPaper Class="mud-height-full">
                <FolderViewComponent @bind-OrganizationId="@OrganizationId" />
            </MudPaper>
        </LeftPanel>
        <RightPanel>
            @if (OrganizationId is not null)
            {
                <Teslametrics.App.Web.Features.Main.Cameras.List.CamsListComponent OrganizationId="@OrganizationId.Value"
                                                                                   FolderId="Folder" />
            }
            <NoTreeElementSelectedComponent Show="OrganizationId is null"
                                            LeftIcon="@Icons.Material.Filled.Folder"
                                            RightIcon="@Icons.Material.Filled.Business"
                                            Title="Выберите элемент дерева"
                                            Subtitle="Для просмотра списка камер выберите элемент дерева" />
        </RightPanel>
    </ResizerComponent>
}
else
{
    <MudSwipeArea OnSwipeEnd="@OnSwipeEnd"
                  Class="mud-width-full mud-height-full overflow-hidden">
        <MudDrawer Open="@_drawerOpen"
                   Fixed="false"
                   OverlayAutoClose="false"
                   Overlay="false"
                   Breakpoint="Breakpoint.Always"
                   Width="100%"
                   Elevation="1"
                   Variant="@DrawerVariant.Temporary">
            <FolderViewComponent OrganizationId="@OrganizationId"
                                 OrganizationIdChanged="OnOrganizationIdChanged" />
        </MudDrawer>
        @if (OrganizationId is not null)
        {
            <Teslametrics.App.Web.Features.Main.Cameras.List.CamsListComponent OrganizationId="@OrganizationId.Value"
                                                                               FolderId="Folder" />
        }
        <NoTreeElementSelectedComponent Show="OrganizationId is null"
                                        LeftIcon="@Icons.Material.Filled.Folder"
                                        RightIcon="@Icons.Material.Filled.Business"
                                        Title="Выберите элемент дерева"
                                        Subtitle="Для просмотра списка камер выберите элемент дерева" />
    </MudSwipeArea>
}
<CreateDialog />
<Teslametrics.App.Web.Features.Main.Cameras.CameraDrawer.CameraDrawer />
<AuthorizeView Policy="@(Fqdn<AppPermissions>.GetNames(AppPermissions.Main.Cameras.Delete).Last())"
               Context="innerContext">
    <Teslametrics.App.Web.Features.Main.Cameras.DeleteCameraDialog.DeleteCameraDialog />
</AuthorizeView>
<AuthorizeView Policy="@(Fqdn<AppPermissions>.GetNames(AppPermissions.Main.Folders.Delete).Last())"
               Context="innerContext">
    <Teslametrics.App.Web.Features.Main.Cameras.DeleteFolderDialog.DeleteFolderDialog />
</AuthorizeView>
<Teslametrics.App.Web.Features.Main.Cameras.PlayerDialog.PlayerDialog />
<Teslametrics.App.Web.Features.Main.Cameras.ImportDialog.ImportDialog />
<Teslametrics.App.Web.Features.Main.Cameras.ExportDialog.ExportDialog />