@using Teslametrics.App.Web.Features.Main.Cameras.TreeView
@attribute [Route(RouteConstants.Cameras)]
@attribute [AppAuthorize(AppPermissions.Main.Cameras.Read, AppPermissions.Main.Folders.Read)]
@inherits InteractiveBaseComponent
<PageTitle>Multimonitor | Камеры</PageTitle>
<MudContainer MaxWidth="MaxWidth.False"
			  Class="mud-height-full pt-4 ">
	<MudStack Spacing="2"
			  Class="mud-height-full">
		<ResizerComponent Class="mud-height-full overflow-hidden"
						  StorageId="cameras_list_size">
			<LeftPanel>
				<FolderViewComponent @bind-OrganizationId="@OrganizationId" />
			</LeftPanel>
			<RightPanel>
				@if (OrganizationId is not null)
				{
					<Teslametrics.App.Web.Features.Main.Cameras.List.CamsListComponent OrganizationId="@OrganizationId.Value"
																					   FolderId="Folder" />
				}
				<NoTreeElementSelectedComponent Show="OrganizationId is null"
												LeftIcon="@Icons.Material.Filled.Folder"
												RightIcon="@Icons.Material.Filled.Business"
												Title="Выберите элемент дерева"
												Subtitle="Для просмотра списка камер выберите элемент дерева" />
			</RightPanel>
		</ResizerComponent>
	</MudStack>
</MudContainer>
<CreateDialog />
<Teslametrics.App.Web.Features.Main.Cameras.CameraDrawer.CameraDrawer />
<AuthorizeView Policy="@(Fqdn<AppPermissions>.GetNames(AppPermissions.Main.Cameras.Delete).Last())"
			   Context="innerContext">
	<Teslametrics.App.Web.Features.Main.Cameras.DeleteCameraDialog.DeleteCameraDialog />
</AuthorizeView>
<AuthorizeView Policy="@(Fqdn<AppPermissions>.GetNames(AppPermissions.Main.Folders.Delete).Last())"
			   Context="innerContext">
	<Teslametrics.App.Web.Features.Main.Cameras.DeleteFolderDialog.DeleteFolderDialog />
</AuthorizeView>
<Teslametrics.App.Web.Features.Main.Cameras.PlayerDialog.PlayerDialog />
<Teslametrics.App.Web.Features.Main.Cameras.ImportDialog.ImportDialog />
<Teslametrics.App.Web.Features.Main.Cameras.ExportDialog.ExportDialog />