.grid_container
{
	--height: calc(100vh - 16px);
	--width: calc(100vw - 16px);
}

.cams_list_component
{
	display: grid;
	grid-template-rows: auto 1fr;
	overflow: hidden;
}

.cameras_grid_1_5
{
	--gap: 8px;
	user-select: none;
	display: grid;
	overflow: hidden;
	gap: var(--gap);
	width: 100%;
	height: 100%;
	grid-template-areas:
		"main main small1"
		"main main small2"
		"small3 small4 small5";
}

.cameras_grid_1_5> :nth-child(1)
{
	grid-area: main;
}

.cameras_grid_1_5>*
{
	aspect-ratio: 16/9;
}

.grid
{
	--gap: 8px;
	user-select: none;
	display: grid;
	overflow: hidden;
	grid-template-columns: repeat(var(--cols), 1fr);
	grid-template-rows: repeat(var(--rows), 1fr);
	gap: var(--gap);
	width: 100%;
	margin: 0 auto;
	--calced-height: calc(var(--rows)*((var(--width) -(var(--cols) - 1)* var(--gap)) / var(--cols)* 9 / 16) +(var(--rows) - 1)* var(--gap));
	height: min(100%, var(--calced-height));
	--calced-width: calc((var(--height) -(var(--rows) - 1)* var(--gap)) / var(--rows)* 16 / 9* var(--cols) +(var(--cols) - 1)* var(--gap) + 1px);
	width: min(100%, var(--calced-width));
	overflow: hidden;
	max-height: calc(var(--height) - (var(--rows) - 1) * var(--gap) + 40px);
	max-width: calc((var(--height) - (var(--rows) - 1) * var(--gap)) / var(--rows) * 16 / 9 * var(--cols) + (var(--cols) - 1) * var(--gap) - 1px);
	overflow: visible;
}

::deep .grid>div
{
	width: 100%;
	overflow: hidden;
	height: 100%;
}

.GridCustom
{
	/* width: calc(var(--width) - (var(--cols) - 1) * var(--gap) + 16px);
		height: calc((var(--width) - (var(--cols) - 1) * var(--gap)) / var(--cols) * 9 / 16 * var(--rows) + (var(--rows) - 1) * var(--gap) + 40px); */
	max-height: calc(var(--height) - (var(--rows) - 1) * var(--gap) + 40px);
	max-width: calc((var(--height) - (var(--rows) - 1) * var(--gap)) / var(--rows) * 16 / 9 * var(--cols) + (var(--cols) - 1) * var(--gap) + 16px);
}

.Grid1Plus5
{
	grid-template-areas:
		"main main small1"
		"main main small2"
		"small3 small4 small5";
	/* margin: 0 auto;
	overflow: hidden;
	width: calc(var(--width) -(var(--cols) - 1)* var(--gap) - 16px);
	height: calc((var(--width) -(var(--cols) - 1)* var(--gap)) / var(--cols)* 9 / 16* var(--rows) +(var(--rows) - 1)* var(--gap) + 40px);
	max-height: calc(var(--height) -(var(--rows) - 1)* var(--gap) + 40px);
	max-width: calc((var(--height) -(var(--rows) - 1)* var(--gap)) / var(--rows)* 16 / 9* var(--cols) +(var(--cols) - 1)* var(--gap) + 16px); */
}

::deep.Grid1Plus5> :nth-child(1),
::deep.Grid1Plus7> :nth-child(1),
::deep.Grid1Plus12> :nth-child(1)
{
	grid-area: main;
}

.Grid1Plus7
{
	grid-template-areas:
		"main main main small1"
		"main main main small2"
		"main main main small3"
		"small4 small5 small6 small7";
	grid-template-rows: repeat(4, 1fr);
	grid-template-columns: repeat(4, 1fr);
}

.Grid1Plus12
{
	grid-template-areas:
		"main main small1 small2"
		"main main small3 small4"
		"small5 small6 small7 small8"
		"small9 small10 small11 small12";
	grid-template-rows: repeat(4, 1fr);
	grid-template-columns: repeat(4, 1fr);
}

.Grid2Plus8
{
	grid-template-areas:
		"main1 main1 main2 main2"
		"main1 main1 main2 main2"
		"small1 small2 small3 small4"
		"small5 small6 small7 small8";
	grid-template-rows: repeat(4, 1fr);
	grid-template-columns: repeat(4, 1fr);
}

::deep.Grid2Plus8> :nth-child(1)
{
	grid-area: main1;
}

::deep.Grid2Plus8> :nth-child(2)
{
	grid-area: main2;
}

.Grid3Plus4
{
	grid-template-areas:
		"main1 main1 main2 main2"
		"main1 main1 main2 main2"
		"main3 main3 small1 small2"
		"main3 main3 small3 small4";
	grid-template-rows: repeat(4, 1fr);
	grid-template-columns: repeat(4, 1fr);
}

::deep.Grid3Plus4> :nth-child(1)
{
	grid-area: main1;
}

::deep.Grid3Plus4> :nth-child(2)
{
	grid-area: main2;
}

::deep.Grid3Plus4> :nth-child(3)
{
	grid-area: main3;
}