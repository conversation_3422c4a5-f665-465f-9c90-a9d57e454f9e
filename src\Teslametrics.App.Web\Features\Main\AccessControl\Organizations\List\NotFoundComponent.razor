﻿@if (!IsFound)
{
	<MudDivider />
	<MudStack AlignItems="AlignItems.Center" Justify="Justify.Center" Class="pa-4">
		<MudIcon Icon="@Icons.Material.Filled.SearchOff" Style="font-size: 8rem;" />
		<MudText Typo="Typo.body1">Ничего не найдено</MudText>
		<MudText Typo="Typo.subtitle1">Попробуйте снова позднее</MudText>
		<MudText>Время последнего обновления: @LastRefreshTime.ToLocalTime()</MudText>
		<MudButton OnClick="RefreshAsync" FullWidth="false" Variant="Variant.Filled" Color="Color.Primary">Обновить принудительно</MudButton>
	</MudStack>
}
@code {
	[Parameter]
	[EditorRequired]
	public bool IsFound { get; set; }

	[Parameter]
	[EditorRequired]
	public DateTime LastRefreshTime { get; set; }

	[Parameter]
	[EditorRequired]
	public EventCallback RefreshAsync { get; set; }
}