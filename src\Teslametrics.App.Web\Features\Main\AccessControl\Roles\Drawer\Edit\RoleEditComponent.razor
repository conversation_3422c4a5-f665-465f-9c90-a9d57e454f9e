@using Teslametrics.App.Web.Features.Main.AccessControl.Roles.Drawer.Edit.CameraViews
@using Teslametrics.App.Web.Features.Main.AccessControl.Roles.Drawer.Edit.CamerasPermissions
@using Organizations = Teslametrics.Shared.AppPermissions.Main.AccessControl.Organizations
@using Teslametrics.Shared
@using Users = Teslametrics.Shared.AppPermissions.Main.AccessControl.Users
@using Roles = Teslametrics.Shared.AppPermissions.Main.AccessControl.Roles
@inherits InteractiveBaseComponent<RoleEditComponent>
<DrawerHeader>
	<MudStack Spacing="0">
		<MudText Typo="Typo.h1">Редактирование роли</MudText>
		@if (IsLoading)
		{
			<MudSkeleton Width="60%"
						 Height="calc(var(--mud-typography-subtitle1-lineheight) * var(--mud-typography-subtitle1-size))" />
		}
		@if (!IsLoading && _response is not null && _response.IsSuccess)
		{
			<MudText Typo="Typo.subtitle1">@_response.Name</MudText>
		}
		@if (!IsLoading && (_response is null || !_response.IsSuccess))
		{
			<MudText Typo="Typo.subtitle1">Не удалось получить настройки роли</MudText>
		}
	</MudStack>
	<MudSpacer />
	@if (!_subscribing && (_subscriptionResult is null || !_subscriptionResult.IsSuccess))
	{
		<MudTooltip Arrow="true"
					Placement="Placement.Start"
					Text="Ошибка подписки на события">
			<MudIconButton OnClick="SubscribeAsync"
						   Icon="@Icons.Material.Filled.ErrorOutline"
						   Color="Color.Error" />
		</MudTooltip>
	}
	<MudTooltip Text="@($"Время последнего обновления: {_lastRefreshTime.ToLocalTime()}")"
				Arrow="true"
				Placement="Placement.Start">
		<MudIconButton OnClick="RefreshAsync"
					   Icon="@Icons.Material.Filled.Refresh"
					   Color="Color.Primary" />
	</MudTooltip>
	@if (!IsLoading && _response is not null && _response.IsSuccess)
	{
		<MudMenu Icon="@Icons.Material.Filled.MoreVert"
				 AriaLabel="Действия с выбранной ролью"
				 Color="Color.Primary"
				 Variant="Variant.Outlined">
			<MudMenuItem OnClick="Select"
						 Icon="@Icons.Material.Outlined.PanoramaFishEye">Просмотр роль</MudMenuItem>
			<AuthorizeView Policy="@AppPermissions.Main.AccessControl.Roles.Delete.GetEnumPermissionString()"
						   Resource="new PolicyRequirementResource(OrganizationId, RoleId)"
						   Context="innerContext">
				<MudDivider Class="my-4" />
				<MudMenuItem OnClick="Delete"
							 Icon="@Icons.Material.Outlined.Delete"
							 IconColor="Color.Warning">Удалить</MudMenuItem>
			</AuthorizeView>
		</MudMenu>
	}
</DrawerHeader>
<MudForm Model="_model"
		 Validation="_roleValidator.ValidateValue"
		 Class="flex-1"
		 ValidationDelay="0"
		 OverrideFieldValidation="true"
		 Spacing="8">
	@if (!IsLoading && _model is not null && _response is not null && _response.IsSuccess)
	{
		<FormSectionComponent Title="Описание роли">
			<MudTextField @bind-Value="_model.Name"
						  Clearable="true"
						  InputType="InputType.Text"
						  Immediate="true"
						  Label="Название роли"
						  Required="true"
						  RequiredError="Поле является обязательным"
						  Class="field_login"
						  HelperText="Укажите наименование роли на латинице" />

			<MudCheckBox T="bool"
						 Value="_model.IsAdmin"
						 ValueChanged="IsAdminChanged"
						 Label="Роль администратора"
						 Color="Color.Primary"
						 Class="ml-n4" />
		</FormSectionComponent>
		<div>
			<MudStack>
				<MudText Typo="Typo.h6">Права доступа</MudText>
				<MudText Typo="Typo.body2">
					Права доступа пользователя бывают двух типов:<br />
					@if (OrganizationId == SystemConsts.RootOrganizationId)
					{
						<MudText>1. Глобальные - на все ресурсы всех организаций</MudText>
						<MudText>2. Ресурсные - на конкретный объект в определённой области</MudText>
					}
					else
					{
						<MudText>1. Общие - на все ресурсы организации</MudText>
						<MudText>2. Ресурсные - на конкретный объект в организации</MudText>
					}
				</MudText>
			</MudStack>
			<MudTabs ApplyEffectsToContainer="true"
					 PanelClass="tab_banel pt-6 d-flex flex-column gap-5"
					 TabHeaderClass="tabs"
					 KeepPanelsAlive="false">
				@if (_model.IsAdmin)
				{
					<MudTabPanel Text="Контроль доступа"
								 Icon="@Icons.Material.Outlined.Key">
						<FormSectionComponent Subtitle="@(OrganizationId == SystemConsts.RootOrganizationId ? "Глобальные" : "Общие")">
							@if (OrganizationId == SystemConsts.RootOrganizationId)
							{
								<WildcardPermissionComponents TEnum="AppPermissions.Main.AccessControl.Organizations"
															  Title="Организации"
															  @bind-Selected="_model.Permissions"
															  ShowAdminTaggedValues="_model.IsAdmin" />
							}
							<WildcardPermissionComponents TEnum="AppPermissions.Main.AccessControl.Users"
														  Title="Пользователи"
														  @bind-Selected="_model.Permissions"
														  ShowAdminTaggedValues="_model.IsAdmin" />
							<WildcardPermissionComponents TEnum="AppPermissions.Main.AccessControl.Roles"
														  Title="Роли"
														  @bind-Selected="_model.Permissions"
														  ShowAdminTaggedValues="_model.IsAdmin" />
						</FormSectionComponent>

						@if (OrganizationId == SystemConsts.RootOrganizationId)
						{
							<FormSectionComponent Subtitle="Ресурсные"
												  CardContentClass="pa-0">
								<OrganizationsConcretePermissionsComponent @bind-Selected="_model.Permissions" />
							</FormSectionComponent>
						}
					</MudTabPanel>
				}
				<MudTabPanel Text="Камеры"
							 Icon="@Icons.Material.Outlined.Camera">
					<FormSectionComponent Subtitle="@(OrganizationId == SystemConsts.RootOrganizationId ? "Глобальные" : "Общие")">
						<WildcardPermissionComponents TEnum="AppPermissions.Main.Cameras"
													  Title="Камеры"
													  @bind-Selected="_model.Permissions"
													  ShowAdminTaggedValues="_model.IsAdmin" />
						<WildcardPermissionComponents TEnum="AppPermissions.Main.Folders"
													  Title="Директории"
													  @bind-Selected="_model.Permissions"
													  ShowAdminTaggedValues="_model.IsAdmin" />
					</FormSectionComponent>

					<FormSectionComponent Subtitle="Ресурсные"
										  CardContentClass="pa-0">
						<CamerasConcretePermissionsComponent @bind-Selected="_model.Permissions"
															 OrganizationId="OrganizationId"
															 ShowAdminTaggedValues="_model.IsAdmin" />
					</FormSectionComponent>
				</MudTabPanel>
				@if (OrganizationId == SystemConsts.RootOrganizationId && _model.IsAdmin)
				{
					<MudTabPanel Text="Пресеты"
								 Icon="@Icons.Material.Outlined.Camera">
						<FormSectionComponent Subtitle="@(OrganizationId == SystemConsts.RootOrganizationId ? "Глобальные" : "Общие")">
							<WildcardPermissionComponents TEnum="AppPermissions.Main.CameraPresets"
														  Title="Пресеты камер"
														  @bind-Selected="_model.Permissions"
														  ShowAdminTaggedValues="_model.IsAdmin" />
						</FormSectionComponent>
					</MudTabPanel>
				}

				@if (OrganizationId == SystemConsts.RootOrganizationId && _model.IsAdmin)
				{
					<MudTabPanel Text="Квота"
								 Icon="@Icons.Material.Outlined.Dataset">
						<FormSectionComponent Subtitle="Глобальные">
							<WildcardPermissionComponents TEnum="AppPermissions.Main.CameraQuotas"
														  Title="Квоты камер"
														  @bind-Selected="_model.Permissions"
														  ShowAdminTaggedValues="@_model.IsAdmin" />
						</FormSectionComponent>
					</MudTabPanel>
				}
				@if (OrganizationId == SystemConsts.RootOrganizationId && _model.IsAdmin)
				{
					<MudTabPanel Text="Публичный доступ к камерам"
								 Icon="@Icons.Material.Outlined.Dataset">
						<FormSectionComponent Subtitle="Глобальные">
							<WildcardPermissionComponents TEnum="AppPermissions.Main.CameraPublicAccess"
														  Title="Публичный доступ к камерам"
														  @bind-Selected="_model.Permissions"
														  ShowAdminTaggedValues="@_model.IsAdmin" />
						</FormSectionComponent>
					</MudTabPanel>
				}

				<MudTabPanel Text="Виды"
							 Icon="@Icons.Material.Filled.ViewModule">
					<FormSectionComponent Subtitle="@(OrganizationId == SystemConsts.RootOrganizationId ? "Глобальные" : "Общие")">
						<WildcardPermissionComponents TEnum="AppPermissions.Main.CameraViews"
													  Title="Виды"
													  @bind-Selected="_model.Permissions"
													  ShowAdminTaggedValues="@_model.IsAdmin" />
					</FormSectionComponent>

					<FormSectionComponent Subtitle="@L["ResourceRights"]"
										  CardContentClass="pa-0">
						<CameraViewsConcretePermissionsComponent @bind-Selected="_model.Permissions"
																 OrganizationId="OrganizationId"
																 ShowAdminTaggedValues="_model.IsAdmin" />
					</FormSectionComponent>
				</MudTabPanel>
			</MudTabs>
		</div>
	}
	<RoleFormLoadingComponent IsLoading="IsLoading && (_response is null || !_response.IsSuccess)" />
	<NoItemsFoundComponent HasItems="_response is not null && _response.IsSuccess"
						   LastRefreshTime="_lastRefreshTime"
						   RefreshAsync="RefreshAsync" />
</MudForm>
<DrawerActions>
	<MudSpacer />
	<MudButton OnClick="CancelAsync"
			   Variant="Variant.Outlined"
			   StartIcon="@Icons.Material.Outlined.Close">Закрыть</MudButton>
	@if (IsLoading)
	{
		<MudSkeleton Width="150px"
					 Height="36.5px" />
	}
	else
	{
		<MudButton OnClick="SubmitAsync"
				   Color="Color.Secondary"
				   Variant="Variant.Outlined"
				   StartIcon="@Icons.Material.Outlined.Save"
				   Disabled="@(!_isValid)">Сохранить</MudButton>
	}
</DrawerActions>