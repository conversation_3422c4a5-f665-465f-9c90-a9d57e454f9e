using Microsoft.AspNetCore.Components;
using System.Reactive;
using System.Text;
using Teslametrics.App.Web.Components.Drawer;
using Teslametrics.App.Web.Eto.Roles;
using Teslametrics.App.Web.Eto.Users;
using Teslametrics.App.Web.Events.Quota;
using Teslametrics.App.Web.Extensions;
using Teslametrics.App.Web.Features.Main.AccessControl.Roles.Drawer.View;
using Teslametrics.App.Web.Shared;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Quota.Drawer.View;

public partial class QuotaViewComponent
{
	private DateTime _lastRefreshTime;
	private Guid _quotaId;
	private bool _subscribing;
	private string _contextMenuAuthPolicyString => new StringBuilder()
		.Append("," + AppPermissions.Main.CameraQuotas.Update.GetEnumPermissionString())
		.Append("," + AppPermissions.Main.CameraQuotas.Delete.GetEnumPermissionString())
		.ToString();// Есть вероятность, что спереди будет запятая.


	[CascadingParameter(Name = DrawerConsts.InstanceName)]
	private DrawerComponent Drawer { get; set; } = null!;

	private GetQuotaUseCase.Response? _response;
	private SubscribeQuotaUseCase.Response? _subscriptionResponse;

	[Parameter]
	public Guid OrganizationId { get; set; }

	[Parameter]
	public Guid QuotaId { get; set; }

	protected override async Task OnParametersSetAsync()
	{
		if (QuotaId != _quotaId)
		{
			_quotaId = QuotaId;
			await FetchAsync();
		}

		await base.OnParametersSetAsync();
	}
	protected async Task FetchAsync()
	{
		if (IsLoading) return;
		await SetLoadingAsync(true);
		try
		{
			_response = await ScopeFactory.MediatorSend(new GetQuotaUseCase.Query(QuotaId));
		}
		catch (Exception ex)
		{
			_response = null;
			Snackbar.Add("Не удалось получить выбранную роль из-за непредвиденной ошибки связи с сервером. Повторите попытку", MudBlazor.Severity.Error);
			Logger.LogError(ex, ex.Message);
		}

		await SetLoadingAsync(false);

		if (_response is null) return;
		switch (_response.Result)
		{
			case GetQuotaUseCase.Result.Success:
				_lastRefreshTime = DateTime.UtcNow;
				await SubscribeAsync();
				break;
			case GetQuotaUseCase.Result.ValidationError:
				Snackbar.Add("Ошибка валидации данных", MudBlazor.Severity.Error);
				break;
			case GetQuotaUseCase.Result.QuotaNotFound:
				Snackbar.Add("Квота не найдена", MudBlazor.Severity.Error);
				break;
			case GetQuotaUseCase.Result.Unknown:
				Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(QuotaViewComponent), nameof(GetQuotaUseCase));
				Snackbar.Add("Не удалось получить выбранную квоту из-за неизвестной ошибки. Повторите попытку", MudBlazor.Severity.Error);
				break;
			default:
				Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(QuotaViewComponent), nameof(GetQuotaUseCase), _response.Result);
				Snackbar.Add("Не удалось получить выбранную роль из-за непредвиденной ошибки связи с сервером. Повторите попытку", MudBlazor.Severity.Error);
				break;
		}
	}

	private async Task SubscribeAsync()
	{
		try
		{
			if (_response is null || !_response.IsSuccess) return;
			await SetSubscribingAsync(true);
			Unsubscribe();
			_subscriptionResponse = await ScopeFactory.MediatorSend(new SubscribeQuotaUseCase.Request(Observer.Create<object>(OnAppEventHandler, OnError), _response.Id));
		}
		catch (Exception exc)
		{
			Snackbar.Add("Не удалось получить подписку на события роли. Повторите попытку", MudBlazor.Severity.Error);
			Logger.LogError(exc, exc.Message);
		}
		await SetSubscribingAsync(false);

		if (_subscriptionResponse is null) return;
		switch (_subscriptionResponse.Result)
		{
			case SubscribeQuotaUseCase.Result.Success:
				CompositeDisposable.Add(_subscriptionResponse.Subscription!);
				break;

			case SubscribeQuotaUseCase.Result.ValidationError:
				Snackbar.Add("Ошибка валидации при подписке на события", MudBlazor.Severity.Error);
				break;
			case SubscribeQuotaUseCase.Result.Unknown:
				Logger.LogError("Unexpected error in {Component}, {UseCase}.", nameof(RoleViewComponent), nameof(SubscribeQuotaUseCase));
				Snackbar.Add("Не удалось получить подписку на выбранную квоту из-за неизвестной ошибки. Повторите попытку", MudBlazor.Severity.Error);
				break;
			default:
				Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(RoleViewComponent), nameof(SubscribeQuotaUseCase), _subscriptionResponse.Result);
				Snackbar.Add("Не удалось получить подписку на выбранную роль из-за непредвиденной ошибки связи с сервером. Повторите попытку", MudBlazor.Severity.Error);
				break;
		}

	}

	private void Unsubscribe()
	{
		if (_subscriptionResponse is not null && _subscriptionResponse.Subscription is not null)
		{
			CompositeDisposable.Remove(_subscriptionResponse.Subscription);
			_subscriptionResponse.Subscription.Dispose();
		}
	}
	protected Task SetSubscribingAsync(bool isLoading = true) => UpdateViewAsync(() =>
	{
		_subscribing = isLoading;
	});

	#region [Actions]
	private Task CancelAsync()
	{
		Unsubscribe();
		_response = null;
		return Drawer.HideAsync();
	}
	private Task RefreshAsync() => FetchAsync();
	private void Edit() => EventSystem.Publish(new CameraQuotaEditEto(OrganizationId, QuotaId));
	private void Delete() => EventSystem.Publish(new RoleDeleteEto(OrganizationId, QuotaId));
	#endregion

	#region [Event Handlers]
	private async void OnAppEventHandler(object appEvent)
	{
		if (_response is null) return;

		switch (appEvent)
		{
			case SubscribeRoleUseCase.UpdatedEvent updatedEto:
				await FetchAsync();
				await UpdateViewAsync();
				break;

			case SubscribeRoleUseCase.DeletedEvent deletedEto:
				Snackbar.Add("Просматриваемая вами роль была удалена", MudBlazor.Severity.Warning);
				await CancelAsync();
				break;

			default:
				break;
		}
	}

	private void OnError(Exception exc)
	{
		Logger.LogError(exc, exc.Message);
		Snackbar.Add("Ошибка валидации при подписке на события", MudBlazor.Severity.Error);
	}
	#endregion [Event Handlers]
}
