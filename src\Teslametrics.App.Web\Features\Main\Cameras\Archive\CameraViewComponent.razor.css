﻿::deep.container
{
	display: grid;
	grid-template-columns: 1fr auto;
	overflow: hidden;
	height: -webkit-fill-available;
}

::deep .sidebar
{
	background-color: var(--mud-palette-surface);
	position: relative;
	max-width: 300px;
	display: grid !important;
	grid-template-rows: auto auto 1fr auto;
	overflow: hidden;
	position: relative;
	height: 100%;
}

::deep .sidebar .header
{
	background-color: var(--mud-palette-primary);
}
::deep button.start {
	border: 1px solid;
	border-radius: 50% 0 0 50%;
	border-right: none;
	margin-right: 0;
	padding-right: 2px;
	width: 38px;
}
::deep button.end {
	border: 1px solid;
	border-radius: 0 50% 50% 0;
	border-left: none;
	margin-left: 0;
	padding-left: 2px;
	width: 38px;
}
::deep button.start.end {
	border: 1px solid;
	border-radius: 50%;
}
