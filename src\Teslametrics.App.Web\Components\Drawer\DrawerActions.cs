using Microsoft.AspNetCore.Components;

namespace Teslametrics.App.Web.Components.Drawer;

public class DrawerActions : ComponentBase, IDisposable
{
	[Parameter]
	public RenderFragment? ChildContent { get; set; }

	[Parameter]
	public string? Class { get; set; }

	[CascadingParameter(Name = DrawerConsts.InstanceName)]
	public DrawerComponent Drawer { get; set; } = null!;

	public event Action? OnChange;

	protected override void OnInitialized()
	{
		base.OnInitialized();

		Drawer.SetActions(this);
	}

	protected override void OnParametersSet()
	{
		base.OnParametersSet();

		OnChange?.Invoke();
	}

	public void Dispose()
	{
		Drawer?.SetActions(null);
	}
}