using System.Reactive;
using Microsoft.AspNetCore.Components.Authorization;
using Teslametrics.App.Web.Events.CameraPublicAccess;
using Teslametrics.App.Web.Extensions;
using Teslametrics.App.Web.Services.Authorization;
using Teslametrics.App.Web.Shared;

namespace Teslametrics.App.Web.Features.Main.CameraPublicAccess.DeleteDialog;

public partial class DeletePublicAccessDialog
{
    private bool _disposedValue;
    private bool _subscribing;
    private MudBlazor.DialogOptions _dialogOptions = new() { CloseOnEscapeKey = true, FullWidth = true, MaxWidth = MudBlazor.MaxWidth.Medium, NoHeader = true };
    private bool _isVisible;
    private Guid _accessId;
    private Guid _organizationId;
    private Guid _cameraId;

    private SubscribeCameraPublicAccessUseCase.Response? _subscriptionResult;
    private GetCameraPublicAccessUseCase.Response? _camera;

    protected override void Dispose(bool disposing)
    {
        if (!_disposedValue)
        {
            if (disposing)
            {
                AuthenticationStateProvider.AuthenticationStateChanged -= OnAuthenticationStateChanged;
            }

            _disposedValue = true;
        }

        base.Dispose(disposing);
    }

    protected override void OnInitialized()
    {
        base.OnInitialized();

        CompositeDisposable.Add(EventSystem.Subscribe<CameraPublicAccessDeleteEto>(OnDeleteHandler));
    }

    private async Task FetchAsync()
    {
        try
        {
            if (_camera is null) await SetLoadingAsync(true);
            _camera = await ScopeFactory.MediatorSend(new GetCameraPublicAccessUseCase.Query(_accessId));
            await SetLoadingAsync(false);
            switch (_camera.Result)
            {
                case GetCameraPublicAccessUseCase.Result.Success:
                    await SubscribeAsync();
                    break;
                case GetCameraPublicAccessUseCase.Result.AccessNotFound:
                    Snackbar.Add("Не удалось получить ссылку публичного доступа. Возможно уже удалена.", MudBlazor.Severity.Warning);
                    Cancel();
                    break;
                case GetCameraPublicAccessUseCase.Result.ValidationError:
                    Snackbar.Add("Не удалось получить камеру, к которой привязана ссылка из-за ошибки валидации.", MudBlazor.Severity.Error);
                    Cancel();
                    break;
                case GetCameraPublicAccessUseCase.Result.Unknown:
                default:
                    throw new Exception($"Unexpected result in {nameof(GetCameraPublicAccessUseCase)}: {_camera.Result}");
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, ex.Message);
            Snackbar.Add("Не удалось получить ссылку публичного доступа.", MudBlazor.Severity.Error);
        }
        finally
        {
            await SetLoadingAsync(false);
        }
    }
    private async Task SubscribeAsync()
    {
        try
        {
            Unsubscribe();

            await SetSubscribingAsync(true);
            _subscriptionResult = await ScopeFactory.MediatorSend(new SubscribeCameraPublicAccessUseCase.Request(Observer.Create<object>(OnAppEventHandler, OnError), _cameraId, _accessId));
            await SetSubscribingAsync(false);
            switch (_subscriptionResult.Result)
            {
                case SubscribeCameraPublicAccessUseCase.Result.Success:
                    CompositeDisposable.Add(_subscriptionResult.Subscription!);
                    break;
                case SubscribeCameraPublicAccessUseCase.Result.ValidationError:
                    Snackbar.Add("Ошибка валидации при подписке на события", MudBlazor.Severity.Error);
                    break;
                case SubscribeCameraPublicAccessUseCase.Result.Unknown:
                default:
                    throw new Exception($"Unexpected result in {nameof(SubscribeCameraPublicAccessUseCase)}: {_subscriptionResult.Result}");
            }
        }
        catch (Exception ex)
        {
            await SetSubscribingAsync(false);
            Snackbar.Add("Не удалось получить подписку на события камеры из-за непредвиденной ошибки. Повторите попытку", MudBlazor.Severity.Error);
            Logger.LogError(ex, ex.Message);
        }
    }
    private void Unsubscribe()
    {
        if (_subscriptionResult?.Subscription is not null)
        {
            CompositeDisposable.Remove(_subscriptionResult.Subscription);
            _subscriptionResult.Subscription.Dispose();
        }
    }
    protected Task SetSubscribingAsync(bool isLoading = true) => UpdateViewAsync(() =>
    {
        _subscribing = isLoading;
    });

    #region [Actions]
    private void Cancel()
    {
        _isVisible = false;
        AuthenticationStateProvider.AuthenticationStateChanged -= OnAuthenticationStateChanged;
        Unsubscribe();
    }
    private Task RefreshAsync() => FetchAsync();
    private async Task SubmitAsync()
    {
        try
        {
            Unsubscribe();
            var response = await ScopeFactory.MediatorSend(new DeletePublicLinkUseCase.Command(_accessId));
            switch (response.Result)
            {
                case DeletePublicLinkUseCase.Result.Success:
                    Snackbar.Add("Ссылка на публичный доступ к камере успешно удалена", MudBlazor.Severity.Success);
                    Cancel();
                    break;
                case DeletePublicLinkUseCase.Result.ValidationError:
                    await SubscribeAsync();
                    Snackbar.Add("Не удалось удалить ссылку на публичный доступ к камере из-за ошибки валидации", MudBlazor.Severity.Error);
                    break;
                case DeletePublicLinkUseCase.Result.Unknown:
                default:
                    await SubscribeAsync();
                    throw new Exception($"Unexpected result in {nameof(DeletePublicLinkUseCase)}: {response.Result}");
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, ex.Message);
            Snackbar.Add("Не удалось удалить ссылку на публичный доступ к камере.");
        }
    }
    #endregion


    #region [Event Handlers]
    private async void OnDeleteHandler(CameraPublicAccessDeleteEto eto)
    {
        var userAuthState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        if (userAuthState is null) return;

        var policyRequirementResource = new PolicyRequirementResource(_organizationId, eto.AccessId);
        var authorizationResult = await AuthorizationService.AuthorizeAsync(
            userAuthState.User,  // Current user from AuthenticationStateProvider
            policyRequirementResource, // The resource being authorized
            AppPermissions.Main.CameraPublicAccess.Delete.GetEnumPermissionString() // The policy name
        );

        if (authorizationResult.Succeeded)
        {
            _accessId = eto.AccessId;
            _organizationId = eto.OrganizationId;
            _cameraId = eto.CameraId;
            _isVisible = true;

            await FetchAsync();
            await SubscribeAsync();

            AuthenticationStateProvider.AuthenticationStateChanged += OnAuthenticationStateChanged;

            StateHasChanged();
        }
        else
        {
            Snackbar.Add("Недостаточно прав для удаления ссылки на публичный доступ к камере", MudBlazor.Severity.Warning);
            Cancel();
        }
    }

    private async void OnAppEventHandler(object appEvent)
    {
        switch (appEvent)
        {
            case SubscribeCameraPublicAccessUseCase.UpdatedEvent updatedEto:
                await FetchAsync();
                await UpdateViewAsync();
                break;

            case SubscribeCameraPublicAccessUseCase.CameraDeletedEvent deletedEto:
                Snackbar.Add("Камера была удалена", MudBlazor.Severity.Warning);
                Cancel();
                break;

            case SubscribeCameraPublicAccessUseCase.AccessDeletedEvent deletedEto:
                Snackbar.Add("Ссылка на публичный доступ к камере была удалена", MudBlazor.Severity.Warning);
                Cancel();
                break;

            case SubscribeCameraPublicAccessUseCase.AccessReissuedEvent reissuedEto:
                Snackbar.Add("Ссылка на публичный доступ к камере была перезапущена", MudBlazor.Severity.Warning);
                _accessId = reissuedEto.Id;
                await FetchAsync();
                await SubscribeAsync();
                await UpdateViewAsync();
                break;

            default:
                Snackbar.Add("Было получено непредвиденное событие.", MudBlazor.Severity.Warning);
                await FetchAsync();
                await UpdateViewAsync();
                Logger.LogWarning("Unexpected event in {UseCase}: {Event}", nameof(SubscribeCameraPublicAccessUseCase), nameof(appEvent));
                break;
        }
    }

    private void OnError(Exception exc)
    {
        Snackbar.Add("Ошибка при подписке на события", MudBlazor.Severity.Error);
        Logger.LogError(exc, exc.Message);
    }

    private async void OnAuthenticationStateChanged(Task<AuthenticationState> authenticationState)
    {
        var userAuthState = await authenticationState;
        if (userAuthState.User is null || userAuthState.User.Identity is null || !userAuthState.User.Identity.IsAuthenticated)
        {
            Cancel();
            return;
        }

        var policyRequirementResource = new PolicyRequirementResource(_organizationId, _accessId);
        var authorizationResult = await AuthorizationService.AuthorizeAsync(
            userAuthState.User,  // Current user from AuthenticationStateProvider
            policyRequirementResource, // The resource being authorized
            AppPermissions.Main.CameraPublicAccess.Update.GetEnumPermissionString() // The policy name
        );

        if (!authorizationResult.Succeeded)
        {
            Snackbar.Add("Недостаточно прав для перевыпуска ссылки на публичный доступ к камере", MudBlazor.Severity.Warning);
            Cancel();
        }
    }
    #endregion [Event Handlers]
}
