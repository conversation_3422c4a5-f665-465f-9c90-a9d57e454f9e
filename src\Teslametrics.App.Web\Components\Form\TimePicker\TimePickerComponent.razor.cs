﻿using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Web;
using MudBlazor.Utilities;
using System.Globalization;
using System.Reflection.Metadata;
using MudBlazor;

namespace Teslametrics.App.Web.Components.Form.TimePicker;

/// <summary>
/// Данный компонент был сделан по образу и подобию MudTimePicker. Исходный компонент не подходит в связи с необходимостью использовать TimeOnly вместо TimeSpan
/// За дизайн picker`а был взят пикер из Material Design 3 https://m3.material.io/components/time-pickers/accessibility
/// </summary>
public partial class TimePickerComponent : MudPicker<TimeOnly?>
{
	#region Private FIELDS
	private MudTextField<int?> _minutesInputField = null!;

	private OpenTo _currentView;

	#region Time
	private const string format24Hours = "HH:mm";
	private const string format12Hours = "hh:mm tt";
	private string _timeFormat = string.Empty;

	private readonly SetTime _timeSet = new();
	private int _initialHour; // ushort, мб? час => 24, минут => 60?
	private int _initialMinute;

	private bool _amPm = false;
	private bool IsAm => _timeSet.Hour >= 00 && _timeSet.Hour < 12; // am is 00:00 to 11:59
	private bool IsPm => _timeSet.Hour >= 12 && _timeSet.Hour < 24; // pm is 12:00 to 23:59
	#endregion
	#endregion

	#region internal FIELDS
	internal TimeOnly? TimeIntermediate { get; private set; }
	#endregion

	#region Protected FIELDS
	#region Class Styles
	protected new string ToolbarClass => new CssBuilder(base.ToolbarClass)
		.AddClass("mud-picker-timepicker-toolbar")
		.AddClass($"mud-picker-timepicker-toolbar-landscape", Orientation == Orientation.Landscape && PickerVariant == PickerVariant.Static)
		.AddClass("pt-4 pb-6")
		.AddClass("ds_toolbar")
		.AddClass(Class)
		.Build();
	#endregion
	#endregion

	#region PARAMETERS
	[Parameter]
	public string Id { get; set; } = Guid.NewGuid().ToString();

	#region Edit Mode
	/// <summary>
	/// First view to show in the MudDatePicker.
	/// </summary>
	[Parameter]
	[Category(CategoryTypes.FormComponent.PickerBehavior)]
	public OpenTo OpenTo { get; set; } = OpenTo.Hours;

	[Parameter]
	public TimePickerMode PickerMode { get; set; } = TimePickerMode.Select;
	[Parameter]
	public EventCallback<TimePickerMode> PickerModeChanged { get; set; }

	[Parameter]
	public bool LockPickerMode { get; set; } = false;
	/// <summary>
	/// Choose the edition mode. By default, you can edit hours and minutes.
	/// </summary>
	[Parameter]
	[Category(CategoryTypes.FormComponent.PickerBehavior)]
	public TimeEditMode TimeEditMode { get; set; } = TimeEditMode.Normal;

	/// <summary>
	/// If AutoClose is set to true and PickerActions are defined, the hour and the minutes can be defined without any action.
	/// </summary>
	[Parameter]
	[Category(CategoryTypes.FormComponent.PickerBehavior)]
	public bool AutoClose { get; set; }

	/// <summary>
	/// Sets the amount of time in milliseconds to wait before closing the picker. This helps the user see that the time was selected before the popover disappears.
	/// </summary>
	[Parameter]
	[Category(CategoryTypes.FormComponent.PickerBehavior)]
	public int ClosingDelay { get; set; } = 200;
	#endregion

	#region Time
	/// <summary>
	/// If true, sets 12 hour selection clock.
	/// </summary>
	[Parameter]
	[Category(CategoryTypes.FormComponent.Behavior)]
	public bool AmPm { get; set; }

	/// <summary>
	/// String Format for selected time view
	/// </summary>
	[Parameter]
	[Category(CategoryTypes.FormComponent.Behavior)]
	public string TimeFormat { get; set; } = string.Empty;

	[Parameter]
	public TimeOnly? Time { get; set; }
	[Parameter]
	public EventCallback<TimeOnly?> TimeChanged { get; set; }
	#endregion

	#region Texts Parameters
	#region Heading
	[Parameter]
	public string SelectTimeModeText { get; set; } = "Select time";

	[Parameter]
	public string InputTimeModeText { get; set; } = "Enter time";
	#endregion Heading

	#region Actions
	[Parameter]
	public string SubmitButtonText { get; set; } = "OK";

	[Parameter]
	public string CancelButtonText { get; set; } = "Cancel";
	#endregion Actions

	#region Text fields
	[Parameter]
	public string HourLabelText { get; set; } = "Hour";
	[Parameter]
	public string MinuteLabelText { get; set; } = "Minute";
	#endregion
	#endregion Texts
	#endregion

	public bool MouseDown { get; set; }

	public TimePickerComponent() : base(new DefaultConverter<TimeOnly?>())
	{
		Converter.GetFunc = OnGet;
		Converter.SetFunc = OnSet;
		((DefaultConverter<TimeOnly?>)Converter).Format = format24Hours;
		AdornmentIcon = Icons.Material.Filled.AccessTime;
		AdornmentAriaLabel = "Open Time Picker";
	}

	#region Protected METHODS
	protected override void OnInitialized()
	{
		base.OnInitialized();
		UpdateTimeSetFromTime();
		InitDefaultActions();

		_currentView = OpenTo;
		_initialHour = _timeSet.Hour;
		_initialMinute = _timeSet.Minute;

		if (!UserAttributes.ContainsKey("id"))
		{
			UserAttributes.Add("id", Id);
		}
	}

	protected override async Task OnParametersSetAsync()
	{
		if (AmPm != _amPm)
		{
			_amPm = AmPm;

			if (Converter is DefaultConverter<TimeOnly?> defaultConverter && string.IsNullOrWhiteSpace(_timeFormat))
			{
				defaultConverter.Format = AmPm ? format12Hours : format24Hours;
			}

			Touched = true;
			await SetTextAsync(Converter.Set(_value), false);
		}

		if (TimeFormat != _timeFormat)
		{
			_timeFormat = TimeFormat;

			if (Converter is DefaultConverter<TimeOnly?> defaultConverter)
				defaultConverter.Format = _timeFormat;

			Touched = true;
			await SetTextAsync(Converter.Set(_value), false);
		}

		if (Time != _value)
		{
			await SetTimeAsync(Time, true);
		}
	}

	protected override Task OnPickerClosedAsync()
	{
		if (Time != TimeIntermediate)
		{
			TimeIntermediate = Time;
			UpdateTimeSetFromTime();
		}
		return base.OnPickerClosedAsync();
	}

	protected async Task SetTimeAsync(TimeOnly? time, bool updateValue)
	{
		if (_value == time)
		{
			return;
		}

		Touched = true;
		TimeIntermediate = time;
		_value = time;
		if (updateValue)
			await SetTextAsync(Converter.Set(_value), true);
		UpdateTimeSetFromTime();
		await TimeChanged.InvokeAsync(_value);
		await BeginValidateAsync();
		FieldChanged(_value);
	}

	protected async Task SubmitAndClose()
	{
		if (PickerActions == null || AutoClose)
		{
			await SubmitAsync();

			if (PickerVariant != PickerVariant.Static)
			{
				await Task.Delay(ClosingDelay);
				await CloseAsync(false);
			}
		}
	}

	protected override async Task SubmitAsync()
	{
		if (ReadOnly)
			return;

		await SetTimeAsync(TimeIntermediate, true);
		await base.SubmitAsync();
	}
	#endregion

	#region Private METHODS
	private void UpdateTimeSetFromTime()
	{
		if (TimeIntermediate == null)
		{
			_timeSet.Hour = 0;
			_timeSet.Minute = 0;
			return;
		}
		_timeSet.Hour = TimeIntermediate.Value.Hour;
		_timeSet.Minute = TimeIntermediate.Value.Minute;
	}

	private static string GetTransform(double angle, double radius, double offsetX, double offsetY)
	{
		angle = angle / 180 * Math.PI;
		var x = (Math.Sin(angle) * radius + offsetX).ToString("F3", CultureInfo.InvariantCulture);
		var y = ((Math.Cos(angle) + 1) * radius + offsetY).ToString("F3", CultureInfo.InvariantCulture);
		return $"transform: translate({x}px, {y}px);";
	}

	private string GetNumberColor(int value)
	{
		if (_currentView == OpenTo.Hours)
		{
			var h = _timeSet.Hour;
			if (AmPm)
			{
				h = _timeSet.Hour % 12;
				if (_timeSet.Hour % 12 == 0)
					h = 12;
			}
			if (h == value)
				return $"mud-clock-number mud-theme-{Color.ToDescriptionString()}";
		}
		else if (_currentView == OpenTo.Minutes && _timeSet.Minute == value)
		{
			return $"mud-clock-number mud-theme-{Color.ToDescriptionString()}";
		}
		return $"mud-clock-number";
	}

	private string GetHourString()
	{
		if (TimeIntermediate == null)
			return "--";
		var h = AmPm ? Math.Abs(24 - 12 - TimeIntermediate.Value.Hour) : TimeIntermediate.Value.Hour;
		return $"{h:D2}";
	}

	private string GetMinuteString()
	{
		if (TimeIntermediate == null)
			return "--";

		return $"{TimeIntermediate.Value.Minute:D2}";
	}

	private Task UpdateTimeAsync()
	{
		TimeIntermediate = new TimeOnly(_timeSet.Hour, _timeSet.Minute);
		if ((PickerVariant == PickerVariant.Static && PickerActions == null) || (PickerActions != null && AutoClose))
		{
			return SubmitAsync();
		}

		return Task.CompletedTask;
	}

	#region InputActions
	private async Task OnHourInput(int? hour)
	{
		if (hour is null)
		{
			return;
		}

		if (hour > 23)
		{
			hour = 23;
		}

		if (hour == _timeSet.Hour)
		{
			return;
		}

		_timeSet.Hour = hour.Value;
		await UpdateTimeAsync();

		// Проверяем для автофокуса
		if (TimeEditMode == TimeEditMode.Normal && _timeSet.Hour.ToString().Length == 2)
		{
			await _minutesInputField.FocusAsync();
			_currentView = OpenTo.Minutes;
		}
	}
	private async Task OnMinuteInput(int? minutes)
	{
		if (minutes is null)
		{
			return;
		}

		if (minutes > 59)
		{
			minutes = 59;
		}
		if (minutes == _timeSet.Minute)
		{
			return;
		}

		_timeSet.Minute = minutes.Value;
		await UpdateTimeAsync();
		StateHasChanged();
	}
	#endregion

	#region Mouse Events
	/// <summary>
	/// Sets Mouse Down bool to true if mouse is inside the clock mask.
	/// </summary>
	private void OnMouseDown(MouseEventArgs e)
	{
		MouseDown = true;
	}

	/// <summary>
	/// Sets Mouse Down bool to false if mouse is inside the clock mask.
	/// </summary>
	private async Task OnMouseUp(MouseEventArgs e)
	{
		if (MouseDown && _currentView == OpenTo.Minutes && _timeSet.Minute != _initialMinute || _currentView == OpenTo.Hours && _timeSet.Hour != _initialHour && TimeEditMode == TimeEditMode.OnlyHours)
		{
			MouseDown = false;
			await SubmitAndClose();
		}

		MouseDown = false;

		if (_currentView == OpenTo.Hours && _timeSet.Hour != _initialHour && TimeEditMode == TimeEditMode.Normal)
		{
			_currentView = OpenTo.Minutes;
		}
	}

	/// <summary>
	/// On click for the hour "sticks", sets the hour.
	/// </summary>
	private async Task OnMouseClickHourAsync(int value)
	{
		var h = value;
		if (AmPm)
		{
			if (IsAm && value == 12)
				h = 0;
			else if (IsPm && value < 12)
				h = value + 12;
		}
		_timeSet.Hour = h;
		await UpdateTimeAsync();

		if (TimeEditMode == TimeEditMode.Normal)
		{
			_currentView = OpenTo.Minutes;
		}
		else if (TimeEditMode == TimeEditMode.OnlyHours)
		{
			await SubmitAndClose();
		}
	}

	/// <summary>
	/// If MouseDown is true enables "dragging" effect on the clock pin/stick.
	/// </summary>
	private Task OnMouseOverHourAsync(int value)
	{
		if (MouseDown)
		{
			_timeSet.Hour = value;
			return UpdateTimeAsync();
		}

		return Task.CompletedTask;
	}
	#endregion Mouse Events

	#region Converter Methods
	private string OnSet(TimeOnly? timespan)
	{
		if (timespan == null)
			return string.Empty;

		//var time = DateTime.Today.Add(timespan.Value);
		return timespan?.ToString(((DefaultConverter<TimeOnly?>)Converter).Format) ?? string.Empty;
	}

	private TimeOnly? OnGet(string? value)
	{
		if (string.IsNullOrEmpty(value))
			return null;

		//var m = Regex.Match(value, "AM|PM", RegexOptions.IgnoreCase);
		return TimeOnly.Parse(value);
		//if (m.Success)
		//{
		//	return DateTime.ParseExact(value, format12Hours, CultureInfo.InvariantCulture).TimeOfDay;
		//}
		//else
		//{
		//	return DateTime.ParseExact(value, format24Hours, CultureInfo.InvariantCulture).TimeOfDay;
		//}
	}
	#endregion
	#endregion Private METHODS
}
