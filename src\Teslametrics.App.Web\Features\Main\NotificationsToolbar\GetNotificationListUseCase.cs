using System.Data;
using Dapper;
using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.Core.Services.Persistence;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Features.Main.NotificationsToolbar;

public static class GetNotificationListUseCase
{
    public record Query(Guid UserId) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public List<Incident> Incidents { get; init; }
        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(List<Incident> incidents)
        {
            Incidents = incidents;

            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Incidents = [];
            Result = result;
        }

        public record Incident
        {
            public Guid Id { get; init; }
            public string Device { get; init; }
            public DateTimeOffset Date { get; init; }
            public string Time => Date.ToString("HH:mm");
            public IncidentType IncidentType { get; init; }
            public bool IsResolved { get; init; }

            public Incident(Guid id,
                            string device,
                            DateTimeOffset date,
                            IncidentType incidentType,
                            bool isResolved)
            {
                Id = id;
                Device = device;
                Date = date;
                IncidentType = incidentType;
                IsResolved = isResolved;
            }
        }
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError
    }

    public class Validator : AbstractValidator<Query>
    {
        public Validator()
        {
            RuleFor(q => q.UserId).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Query, Response>
    {
        private readonly IValidator<Query> _validator;
        private readonly IDbConnection _dbConnection;

        public Handler(IValidator<Query> validator,
                       IDbConnection dbConnection)
        {
            _validator = validator;
            _dbConnection = dbConnection;
        }

        public async Task<Response> Handle(Query request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            var template = SqlQueryBuilder.Create()
                .Select(Db.Incidents.Props.Id)
                .Select(Db.Incidents.Props.Device)
                .Select(Db.Incidents.Props.CreatedAt)
                .Select(Db.Incidents.Props.IncidentType)
                .Select(Db.Incidents.Props.ResolvedAt)
                .InnerJoin(Db.Incidents.Table, Db.Incidents.Props.Id, Db.IncidentNotifications.Props.IncidentId, SqlOperator.Equals)
                .Where(Db.IncidentNotifications.Props.UserId, ":UserId", SqlOperator.Equals, new { request.UserId })
                .Build(QueryType.Standard, Db.IncidentNotifications.Table, RowSelection.AllRows);

            var incidentModels = await _dbConnection.QueryAsync<IncidentModel>(template.RawSql, template.Parameters);

            var incidents = incidentModels.Select(i => new Response.Incident(
                i.Id,
                i.Device,
                i.CreatedAt,
                i.IncidentType,
                i.ResolvedAt.HasValue
            )).ToList();

            return new Response(incidents);
        }
    }

    public record IncidentModel(Guid Id, string Device, DateTimeOffset CreatedAt, IncidentType IncidentType, DateTimeOffset? ResolvedAt);
}
