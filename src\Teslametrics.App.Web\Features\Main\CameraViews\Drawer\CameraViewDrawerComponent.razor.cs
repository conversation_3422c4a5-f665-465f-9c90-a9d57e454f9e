using Teslametrics.App.Web.Events;
using Teslametrics.App.Web.Events.CameraView;

namespace Teslametrics.App.Web.Features.Main.CameraViews.Drawer;

public partial class CameraViewDrawerComponent
{
    private Guid? _organizationId;
    private Guid? _viewId;
    private DrawerMode _mode;

    public bool IsOpened => _mode != DrawerMode.Hidden;

    public enum DrawerMode
    {
        Hidden,
        Create,
        Edit,
        View
    }

    protected override void OnInitialized()
    {
        CompositeDisposable.Add(EventSystem.Subscribe<CameraViewCreateEto>(OnEventHandler));
        CompositeDisposable.Add(EventSystem.Subscribe<CameraViewSelectEto>(OnEventHandler));
        CompositeDisposable.Add(EventSystem.Subscribe<CameraViewEditEto>(OnEventHandler));
        base.OnInitialized();
    }

    public void ShowCreate(Guid organizationId)
    {
        _organizationId = organizationId;
        _viewId = null;
        _mode = DrawerMode.Create;
        StateHasChanged();
    }

    public void ShowEdit(Guid organizationId, Guid viewId)
    {
        _organizationId = organizationId;
        _viewId = viewId;
        _mode = DrawerMode.Edit;
        StateHasChanged();
    }
    public void ShowView(Guid organizationId, Guid viewId)
    {
        _organizationId = organizationId;
        _viewId = viewId;
        _mode = DrawerMode.View;
        StateHasChanged();
    }

    public void Show(DrawerMode mode, Guid organizationId, Guid viewId)
    {
        if (mode == DrawerMode.Hidden || mode == DrawerMode.Create)
        {
            throw new ArgumentException($"DrawerMode Edit or View expected {mode.ToString()} provided");
        }

        _mode = mode;
        _organizationId = organizationId;
        _viewId = viewId;
    }

    public void Close()
    {
        _mode = DrawerMode.Hidden;
        _organizationId = null;
        _viewId = null;
        StateHasChanged();
    }

    private void Show()
    {
        _mode = DrawerMode.Create;
        StateHasChanged();
    }

    #region [Event Handlers]
    private void OnEventHandler(BaseEto eto)
    {
        switch (eto)
        {
            case CameraViewCreateEto createEto:
                ShowCreate(createEto.OrganizationId);
                break;
            case CameraViewEditEto editEto:
                ShowEdit(editEto.OrganizationId, editEto.ViewId);
                break;
            case CameraViewSelectEto selectEto:
                ShowView(selectEto.OrganizationId, selectEto.ViewId);
                break;
            default:
                throw new ArgumentException("Unknown event type", nameof(eto));
        }
    }

    private void OnOpenChanged(bool opened)
    {
        if (opened)
        {
            if (_mode != DrawerMode.Hidden && _viewId.HasValue && _organizationId.HasValue)
            {
                Show(_mode, _organizationId.Value, _viewId.Value);
            }
            else
            {
                Show();
            }
        }
        else
        {
            Close();
        }
    }
    #endregion [Event Handlers]
}