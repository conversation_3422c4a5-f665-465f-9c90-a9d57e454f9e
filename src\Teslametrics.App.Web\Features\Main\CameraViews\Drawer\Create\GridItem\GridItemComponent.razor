﻿@using Teslametrics.App.Web.Features.Main.CameraViews.Drawer.Create.GridItem.Preview
@inherits InteractiveBaseComponent
<MudCard>
	<MudCardContent>
		<MudAutocomplete T="CameraViewCell"
						 AdornmentIcon="@Icons.Material.Filled.Search"
						 AdornmentColor="Color.Primary"
						 Adornment="Adornment.Start"
						 SearchFunc="@SearchAsync"
						 Label="Камера"
						 ToStringFunc="@(e => e == null ? null : e.Name)"
						 Value="Camera"
						 Clearable="true"
						 ResetValueOnEmptyText="true"
						 ValueChanged="OnSelectedValudeChanged"
						 OpenChanged="OnOpenChangedAsync"
						 @ref="_ref" />
		<Preview CameraId="@Camera?.CameraId" />
	</MudCardContent>
</MudCard>
