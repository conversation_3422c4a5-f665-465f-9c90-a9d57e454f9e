﻿::deep .tabs
{
	background: transparent;
}

.layout
{
    display: grid;
    grid-template-rows: 1fr;
    grid-template-columns: 0.3fr 0.7fr;
    overflow: visible;
    height: 100%;
    gap: 8px;
}

.mobile_layout
{
    height: 100%;
    overflow: hidden;
}

::deep .mud-tabs {
    gap: 24px;
}

@media screen and (max-width: 767px) {
    .layout {
        grid-template-rows: 0.3fr 0.7fr;
        grid-template-columns: 1fr;
        overflow: hidden;
        height: 100%;
    }
    ::deep .panel {
        margin-left: 8px;
        margin-bottom: 8px;
        margin-right: 8px;
    }
    ::deep .mud-tabs {
        gap: 8px;
    }
}