using System.Reactive;
using FluentValidation;
using Microsoft.AspNetCore.Components;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Events.Presets;
using Teslametrics.App.Web.Events.Quota;
using Teslametrics.App.Web.Extensions;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Quota.CameraQuotaList;

public partial class PresetListComponent
{
	private bool _isValid = true;
	private Guid _organizationId;
	private class QuotaItem(Guid id, string name, int UsedQuota, int totalQuota, int retentionPeriodDays, int storageLimitMb)
	{
		public Guid Id { get; } = id;
		public string Name { get; set; } = name;
		public int RemainingQuota => TotalQuota - UsedQuota;
		public int TotalQuota { get; } = totalQuota;
		public int UsedQuota { get; init; } = UsedQuota;

		public int NewQuota { get; set; } = totalQuota;

		public int RetentionPeriodDays { get; set; } = retentionPeriodDays;
		public int StorageLimitMb { get; set; } = storageLimitMb;
	}

	private readonly FluentValueValidator<int> _validator = new(x => x.Must(limit => limit == -1 || limit >= 1).WithMessage("Значение не должно быть равно нулю"));
	private bool _subscribing;
	private SubscribeCameraQuotaListUseCase.Response? _subscriptionResult;
	private GetCameraQuotaListUseCase.Response? _response;
	private List<QuotaItem> _items = [];

	private int _currentPage => Offset / Limit;

	#region Parameters
	[Parameter]
	public int Offset { get; set; } = 0;
	[Parameter]
	public EventCallback<int> OffsetChanged { get; set; }

	[Parameter]
	public int Limit { get; set; } = 25;
	[Parameter]
	public EventCallback<int> LimitChanged { get; set; }

	[Parameter]
	[EditorRequired]
	public Guid OrganizationId { get; set; }

	[Parameter]
	public string SearchString { get; set; } = string.Empty;

	[Parameter]
	public EventCallback<string> SearchStringChanged { get; set; }
	#endregion [Parameter]

	protected override async Task OnParametersSetAsync()
	{
		if (OrganizationId != _organizationId)
		{
			_organizationId = OrganizationId;
			await LoadDataAsync();
			await SubscribeAsync();
		}

		await base.OnParametersSetAsync();
	}

	private async Task LoadDataAsync()
	{
		try
		{
			_response = await ScopeFactory.MediatorSend(new GetCameraQuotaListUseCase.Query(OrganizationId, Offset, Limit, SearchString));
			switch (_response.Result)
			{
				case GetCameraQuotaListUseCase.Result.Success:
					_items = _response.Items.Select(item => new QuotaItem(item.Id, item.Name, item.UsedQuota, item.TotalQuota, item.RetentionPeriodDays, item.StorageLimitMb)).ToList();
					break;
				case GetCameraQuotaListUseCase.Result.ValidationError:
					Snackbar.Add("Ошибка валидации при получении списка пресетов", MudBlazor.Severity.Error);
					break;
				case GetCameraQuotaListUseCase.Result.Unknown:
				default:
					throw new Exception($"Unexpected result in {nameof(GetCameraQuotaListUseCase)}: {_response.Result}");
			}
		}
		catch (Exception exc)
		{
			Logger.LogError(exc, exc.Message);
			Snackbar.Add("Не удалось получить список квот камеры из-за непредвиденной ошибки.", MudBlazor.Severity.Error);
		}
	}

	private async Task SubscribeAsync()
	{
		try
		{
			await SetSubscribingAsync(true);
			Unsubscribe();
			_subscriptionResult = await ScopeFactory.MediatorSend(new SubscribeCameraQuotaListUseCase.Request(OrganizationId, Observer.Create<SubscribeCameraQuotaListUseCase.UpdatedEvent>(OnAppEventHandler, OnError)));
			switch (_subscriptionResult.Result)
			{
				case SubscribeCameraQuotaListUseCase.Result.Success:
					CompositeDisposable.Add(_subscriptionResult.Subscription!);
					break;
				case SubscribeCameraQuotaListUseCase.Result.ValidationError:
					Snackbar.Add("Ошибка валидации при подписке на события", MudBlazor.Severity.Error);
					break;
				case SubscribeCameraQuotaListUseCase.Result.Unknown:
				default:
					throw new Exception($"Unexpected result in {nameof(SubscribeCameraQuotaListUseCase)}: {_subscriptionResult.Result}");
			}
		}
		catch (Exception exc)
		{
			Logger.LogError(exc, exc.Message);
			Snackbar.Add("Не удалось подписаться на события из-за непредвиденной ошибки.", MudBlazor.Severity.Error);
		}
	}

	private void Unsubscribe()
	{
		if (_subscriptionResult is not null && _subscriptionResult.Subscription is not null)
		{
			CompositeDisposable.Remove(_subscriptionResult.Subscription);
			_subscriptionResult.Subscription.Dispose();
		}
	}

	private Task SetSubscribingAsync(bool isSubscribing)
	{
		return InvokeAsync(() => _subscribing = isSubscribing);
	}

	#region [Actions]
	private void Add() => EventSystem.Publish(new CameraQuotaCreateEto(OrganizationId));

	private void Select(QuotaItem item) => EventSystem.Publish(new CameraQuotaSelectEto(OrganizationId, item.Id));
	private void Edit(QuotaItem item) => EventSystem.Publish(new CameraQuotaEditEto(OrganizationId, item.Id));

	private async Task SaveAsync(QuotaItem item)
	{
		try
		{
			if (_response is null || !_response.IsSuccess)
				return;

			var response = await ScopeFactory.MediatorSend(new UpdateCameraQuotaUseCase.Command(OrganizationId, item.Id, item.Name, item.NewQuota, item.RetentionPeriodDays, item.StorageLimitMb));
			switch (response.Result)
			{
				case UpdateCameraQuotaUseCase.Result.Success:
					Snackbar.Add("Квоты успешно обновлены", MudBlazor.Severity.Success);
					await LoadDataAsync();
					break;
				case UpdateCameraQuotaUseCase.Result.ValidationError:
					Snackbar.Add("Ошибка валидации при обновлении квоты", MudBlazor.Severity.Error);
					break;
				case UpdateCameraQuotaUseCase.Result.OrganizationNotFound:
					Snackbar.Add("Указанной организации не существует", MudBlazor.Severity.Error);
					break;
				case UpdateCameraQuotaUseCase.Result.LimitCannotBeLowerThanCurrent:
					Snackbar.Add("Лимит не может быть меньше текущего значения", MudBlazor.Severity.Error);
					break;
				case UpdateCameraQuotaUseCase.Result.Unknown:
				default:
					throw new Exception($"Unexpected result in {nameof(UpdateCameraQuotaUseCase)}: {response.Result}");
			}
		}
		catch (Exception exc)
		{
			Logger.LogError(exc, exc.Message);
			Snackbar.Add("Не удалось обновить квоты камеры из-за непредвиденной ошибки.", MudBlazor.Severity.Error);
		}
	}

	private async Task DeleteAsync(QuotaItem item)
	{
		try
		{
			var response = await ScopeFactory.MediatorSend(new DeleteCameraQuotaUseCase.Command(OrganizationId, item.Id));
			switch (response.Result)
			{
				case DeleteCameraQuotaUseCase.Result.Success:
					Snackbar.Add("Квота успешно удалена", MudBlazor.Severity.Success);
					await LoadDataAsync();
					break;
				case DeleteCameraQuotaUseCase.Result.ValidationError:
					Snackbar.Add("Ошибка валидации при удалении квоты", MudBlazor.Severity.Error);
					break;
				case DeleteCameraQuotaUseCase.Result.OrganizationNotFound:
					Snackbar.Add("Указанной организации не существует", MudBlazor.Severity.Error);
					break;
				case DeleteCameraQuotaUseCase.Result.Unknown:
				default:
					throw new Exception($"Unexpected result in {nameof(DeleteCameraQuotaUseCase)}: {response.Result}");
			}
		}
		catch (Exception exc)
		{
			Logger.LogError(exc, exc.Message);
			Snackbar.Add("Не удалось обновить квоты камеры из-за непредвиденной ошибки.", MudBlazor.Severity.Error);
		}
	}

	private Task RefreshAsync() => LoadDataAsync();
	#endregion

	private MudBlazor.Color GetQuotaColor(QuotaItem item)
	{
		var remainingQuota = item.TotalQuota - item.UsedQuota;
		if (remainingQuota <= 0)
			return MudBlazor.Color.Error;

		if ((remainingQuota < 5 && item.TotalQuota < 50) ||
			(remainingQuota <= item.TotalQuota * 0.1 && item.TotalQuota >= 50))
			return MudBlazor.Color.Warning;

		return MudBlazor.Color.Default;
	}

	#region [Event Handlers]
	private Task RowsPerPageChanged(int limit)
	{
		Limit = limit;
		if (LimitChanged.HasDelegate)
		{
			return LimitChanged.InvokeAsync(limit);
		}
		return Task.CompletedTask;
	}

	private async Task OnSearchChanged(string _searchString)
	{
		SearchString = _searchString;
		if (SearchStringChanged.HasDelegate)
			await SearchStringChanged.InvokeAsync(SearchString);

		await RefreshAsync();
	}

	protected async void OnAppEventHandler(SubscribeCameraQuotaListUseCase.UpdatedEvent appEvent)
	{
		await RefreshAsync();
		await UpdateViewAsync();
	}

	protected void OnError(Exception exc)
	{
		Logger.LogError(exc, exc.Message);
		Snackbar.Add("Ошибка валидации при подписке на события", MudBlazor.Severity.Error);
	}
	#endregion
}
