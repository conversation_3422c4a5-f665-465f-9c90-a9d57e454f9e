﻿using MediatR;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using MudBlazor;
using Teslametrics.App.Web.Events.Users;
using Teslametrics.App.Web.Extensions;

namespace Teslametrics.App.Web.Features.DetailView.AuthToolbarComponent;

public partial class AuthToolbarComponent
{
	private class User(Guid id, string username)
	{
		public Guid UserId { get; init; } = id;
		public string Username { get; init; } = username;
	}

	private User? _user;

	#region [Injectables]
	[Inject]
	private NavigationManager _navigationManager { get; set; } = null!;
	#endregion

	protected override async Task OnInitializedAsync()
	{
		try
		{
			var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
			if (authState.User.Identity?.IsAuthenticated ?? false)
			{
				var userId = authState.User.GetUserId()!.Value;
				await FetchAsync(userId);
			}
		}
		catch (Exception exc)
		{
			_navigationManager.NavigateTo(RouteConstants.LogoutPage, true);
			Logger.LogError(exc, exc.Message);
		}
	}

	private async Task FetchAsync(Guid id)
	{
		if (IsLoading) return;
		GetUserUseCase.Response? response = null;
		await SetLoadingAsync(true);
		try
		{
			response = await ScopeFactory.CreateScope().ServiceProvider.GetRequiredService<IMediator>().Send(new GetUserUseCase.Query(id));
		}
		catch (Exception ex)
		{
			response = null;
			Snackbar.Add("Не удалось получить выбранного пользователя из-за ошибки сервера. Повторите попытку позже.", Severity.Error);
			Logger.LogError(ex, ex.Message);
			_navigationManager.NavigateTo(RouteConstants.LogoutPage, true);
		}
		await SetLoadingAsync(false);

		if (response is null) return;

		switch (response.Result)
		{
			case GetUserUseCase.Result.Success:
				_user = new(response.Id, response.Username);
				break;
			case GetUserUseCase.Result.ValidationError:
				Snackbar.Add("Ошибка валидации данных", Severity.Error);
				_navigationManager.NavigateTo(RouteConstants.LogoutPage, true);
				break;
			case GetUserUseCase.Result.UserNotFound:
				Snackbar.Add("Пользователь не найден", Severity.Error);
				_navigationManager.NavigateTo(RouteConstants.LogoutPage, true);
				break;
			case GetUserUseCase.Result.Unknown:
				Snackbar.Add($"Не удалось получить пользователя из-за ошибки сервера. Обратитесь к администратору.", Severity.Error);
				Logger.LogError("Unexpected result in {Component}.{Method} using {UseCase}: {Result}", nameof(AuthToolbarComponent), nameof(FetchAsync), nameof(GetUserUseCase), response.Result);
				_navigationManager.NavigateTo(RouteConstants.LogoutPage, true);
				break;
			default:
				Snackbar.Add($"Не удалось получить пользователя из-за непредвиденной ошибки: {response.Result}", Severity.Error);
				Logger.LogError("Unexpected result in {Component}.{Method} using {UseCase}: {Result}", nameof(AuthToolbarComponent), nameof(FetchAsync), nameof(GetUserUseCase), response.Result);
				_navigationManager.NavigateTo(RouteConstants.LogoutPage, true);
				break;
		}
	}

	private void SignOut()
	{
		_navigationManager.NavigateTo(RouteConstants.LogoutPage, true);
	}

	private void ChangePasswordAsync() => EventSystem.Publish(new ChangeCurrentUserPassword());
}
