using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using MudBlazor;
using System.Reactive;
using System.Text;
using Teslametrics.App.Web.Eto.Roles;
using Teslametrics.App.Web.Extensions;
using Teslametrics.App.Web.Services.Authorization;
using Teslametrics.App.Web.Shared;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Roles.DeleteDialog;

public partial class DeleteDialog
{
	private bool _disposedValue;

	private Guid _id;
	private Guid _organizationId;

	private bool _subscribing;
	private DialogOptions _deleteDialogOptions = new() { CloseOnEscapeKey = true, FullWidth = true, MaxWidth = MaxWidth.Medium, CloseButton = true, NoHeader = true };
	private StringBuilder _confirmationMessage => new StringBuilder("Удалить ").AppendIf(_model?.Name ?? string.Empty, _model is not null);
	private string _input = string.Empty;
	private bool _isVisible;

	private GetRoleUseCase.Response? _model;
	private SubscribeRoleUseCase.Response? _subscriptionResult;

	protected override void Dispose(bool disposing)
	{
		if (!_disposedValue)
		{
			if (disposing)
			{
				AuthenticationStateProvider.AuthenticationStateChanged -= OnAuthenticationStateChanged;
			}

			_disposedValue = true;
		}

		base.Dispose(disposing);
	}

	protected override void OnInitialized()
	{
		CompositeDisposable.Add(EventSystem.Subscribe<RoleDeleteEto>(OnRoleDeleteHandler));

		base.OnInitialized();
	}

	private async Task FetchAsync()
	{
		if (IsLoading) return;

		try
		{
			await SetLoadingAsync();
			_model = await ScopeFactory.MediatorSend(new GetRoleUseCase.Query(_id));
			switch (_model.Result)
			{
				case GetRoleUseCase.Result.Success:
					await SubscribeAsync();
					break;
				case GetRoleUseCase.Result.ValidationError:
					Snackbar.Add("Ошибка валидации данных", Severity.Error);
					break;
				case GetRoleUseCase.Result.RoleNotFound:
					Snackbar.Add("Запрошенная роль не найдена", Severity.Error);
					break;
				case GetRoleUseCase.Result.Unknown:
				default:
					throw new Exception($"Unexpected result in {nameof(GetRoleUseCase)}: {_model.Result}");
			}
		}
		catch (Exception ex)
		{
			Snackbar.Add("Не удалось получить выбранную роль. Повторите попытку", Severity.Error);
			Logger.LogError(ex, ex.Message);
		}
		finally
		{
			await SetLoadingAsync(false);
		}
	}

	#region [Actions]
	private Task RefreshAsync() => FetchAsync();
	private Task CancelAsync() => UpdateViewAsync(() =>
	{
		Unsubscribe();

		_isVisible = false;
		_model = null;

		AuthenticationStateProvider.AuthenticationStateChanged -= OnAuthenticationStateChanged;
	});

	private async Task SubmitAsync()
	{
		if (IsLoading || _model is null) return;

		try
		{
			await SetLoadingAsync();

			Unsubscribe();
			var result = await ScopeFactory.MediatorSend(new DeleteRoleUseCase.Command(_organizationId, _model.Id));
			switch (result.Result)
			{
				case DeleteRoleUseCase.Result.Success:
					Snackbar.Add("Роль успешно удалена.", Severity.Success);
					await CancelAsync();
					break;
				case DeleteRoleUseCase.Result.ValidationError:
					await SubscribeAsync();
					Snackbar.Add("Ошибка валидации данных", Severity.Error);
					break;
				case DeleteRoleUseCase.Result.CannotDeleteRoleAssignedToUser:
					await SubscribeAsync();
					Snackbar.Add("Нельзя удалить роль, привязанную к пользователю", Severity.Error);
					break;
				case DeleteRoleUseCase.Result.CannotDeleteSystemRole:
					await SubscribeAsync();
					Snackbar.Add("Системную роль удалить нельзя!", Severity.Error);
					break;
				case DeleteRoleUseCase.Result.OrganizationNotFound:
					await SubscribeAsync();
					Snackbar.Add("Организация не найдена.", Severity.Error);
					return;
				case DeleteRoleUseCase.Result.Unknown:
				default:
					await SubscribeAsync();
					throw new Exception($"Unexpected result in {nameof(DeleteRoleUseCase)}: {result.Result}");
			}
		}
		catch (Exception ex)
		{
			Snackbar.Add("Не удалось удалить выбранную роль. Повторите попытку", Severity.Error);
			Logger.LogError(ex, ex.Message);
		}
		finally
		{
			await SetLoadingAsync(false);
		}
	}
	#endregion [Actions]

	private async Task SubscribeAsync()
	{
		Unsubscribe();
		if (_model is null || !_model.IsSuccess) return;

		await SetSubscribingAsync(true);
		_subscriptionResult = await ScopeFactory.MediatorSend(new SubscribeRoleUseCase.Request(Observer.Create<object>(OnAppEventHandler, OnError), _model.Id));
		await SetSubscribingAsync(false);
		switch (_subscriptionResult.Result)
		{
			case SubscribeRoleUseCase.Result.Success:
				CompositeDisposable.Add(_subscriptionResult.Subscription!);
				break;
			case SubscribeRoleUseCase.Result.ValidationError:
				Snackbar.Add("Ошибка валидации при подписке на события", Severity.Error);
				break;

			case SubscribeRoleUseCase.Result.Unknown:
			default:
				throw new Exception($"Unexpected result in {nameof(SubscribeRoleUseCase)}: {_subscriptionResult.Result}");
		}
	}
	private void Unsubscribe()
	{
		if (_subscriptionResult?.Subscription is not null)
		{
			CompositeDisposable.Remove(_subscriptionResult.Subscription);
			_subscriptionResult.Subscription.Dispose();
		}
	}
	protected Task SetSubscribingAsync(bool isLoading = true) => UpdateViewAsync(() =>
	{
		_subscribing = isLoading;
	});

	private void VisibilityChanged(bool isVisible)
	{
		_isVisible = isVisible;
		if (!isVisible)
		{
			Unsubscribe();
			_model = null;
		}
	}

	#region [Event Handlers]
	private async void OnAppEventHandler(object appEvent)
	{
		switch (appEvent)
		{
			case SubscribeRoleUseCase.UpdatedEvent updatedEto:
				await FetchAsync();
				await UpdateViewAsync();
				break;

			case SubscribeRoleUseCase.DeletedEvent deleteEto:
				Snackbar.Add("Роль была удалена.", Severity.Error);
				await CancelAsync();
				break;

			default:
				break;
		}
	}

	private void OnError(Exception exc)
	{
		Logger.LogError(exc, exc.Message);
		Snackbar.Add("Ошибка валидации при подписке на события", Severity.Error);
	}

	private async Task OnRoleDeleteHandler(RoleDeleteEto eto)
	{

		var userAuthState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
		if (userAuthState is null) return;

		var policyRequirementResource = new PolicyRequirementResource(eto.OrganizationId, eto.RoleId);
		var authorizationResult = await AuthorizationService.AuthorizeAsync(
			userAuthState.User,  // Current user from AuthenticationStateProvider
			policyRequirementResource, // The resource being authorized
			AppPermissions.Main.AccessControl.Roles.Delete.GetEnumPermissionString() // The policy name
		);

		if (authorizationResult.Succeeded)
		{
			_organizationId = eto.OrganizationId;
			_id = eto.RoleId;
			_isVisible = true;
			_input = string.Empty;

			await FetchAsync();
			await SubscribeAsync();

			AuthenticationStateProvider.AuthenticationStateChanged += OnAuthenticationStateChanged;

			StateHasChanged();
		}
		else
		{
			Snackbar.Add("Недостаточно прав для удаления роли", MudBlazor.Severity.Warning);
			await CancelAsync();
		}
	}

	private async void OnAuthenticationStateChanged(Task<AuthenticationState> authenticationState)
	{
		var userAuthState = await authenticationState;
		if (userAuthState.User is null || userAuthState.User.Identity is null || !userAuthState.User.Identity.IsAuthenticated)
		{
			await CancelAsync();
			return;
		}

		var policyRequirementResource = new PolicyRequirementResource(_organizationId, _id);
		var authorizationResult = await AuthorizationService.AuthorizeAsync(
			userAuthState.User,  // Current user from AuthenticationStateProvider
			policyRequirementResource, // The resource being authorized
			AppPermissions.Main.AccessControl.Roles.Delete.GetEnumPermissionString() // The policy name
		);

		if (!authorizationResult.Succeeded)
		{
			Snackbar.Add("Недостаточно прав для удаления роли", MudBlazor.Severity.Warning);
			await CancelAsync();
		}
	}
	#endregion
}
