﻿using Microsoft.AspNetCore.Components;
using MudBlazor;

namespace Teslametrics.App.Web.Features.DatabaseMigrations;

public partial class DatabaseMigrationsLayout : LayoutComponentBase
{
    [Inject] public MudTheme CustomTheme { get; set; } = null!;

    #region Theme mode
    private bool _isDarkMode;
    private MudThemeProvider? _mudThemeProvider;
    #endregion

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            #region Theme mode
            if (_mudThemeProvider is not null)
            {
                _isDarkMode = await _mudThemeProvider.GetSystemDarkModeAsync();
            }
            #endregion
            StateHasChanged();
        }
    }
}