FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
ENV TZ=Europe/Moscow
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# Установка SSH-сервера
#RUN apt-get update && apt-get install -y openssh-server && mkdir /var/run/sshd

# Создание пользователя и установка пароля
#RUN echo "root:Veresk13(&" | chpasswd

# Разрешить вход по паролю
#RUN sed -i 's/#PasswordAuthentication yes/PasswordAuthentication yes/' /etc/ssh/sshd_config && sed -i 's/#PermitRootLogin yes/PermitRootLogin yes/' /etc/ssh/sshd_config

WORKDIR /app
EXPOSE 80
EXPOSE 443
#EXPOSE 22

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src
#COPY ["src/Teslametrics.App.Web/Teslametrics.App.Web.csproj", "Teslametrics.App.Web/"]
#COPY ["src/FFMpegNET/FFMpegNET.csproj", "FFMpegNET/"]
#COPY ["src/Orleans.Streams.Kafka/Orleans.Streams.Kafka.csproj", "Orleans.Streams.Kafka/"]
#RUN dotnet restore "Teslametrics.App.Web/Teslametrics.App.Web.csproj"
COPY src/ .
WORKDIR "/src/Teslametrics.App.Web"
#RUN dotnet build "Teslametrics.App.Web.csproj" -c Debug -o /app/build

FROM build AS publish
RUN dotnet publish "Teslametrics.App.Web.csproj" -c Release -o /app/publish -p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .

# COPY entrypoint.sh .
RUN chmod +x entrypoint.sh

ENTRYPOINT ["./entrypoint.sh"]
