using Microsoft.AspNetCore.Components;
using Teslametrics.App.Web.Extensions;

namespace Teslametrics.App.Web.Features.Main.Cameras.Archive;

public partial class EventListComponent
{
	private Guid _cameraId;
	private DateTime _selectedDate;

	private GetEventsUseCase.Response? _archiveEvents;

	[Parameter]
	public string Class { get; set; } = string.Empty;

	[Parameter]
	public Guid CameraId { get; set; }

	[Parameter]
	public DateTime SelectedDate { get; set; }

	[Parameter]
	public (DateTime Start, DateTime End)? TimeLine { get; set; }

	[Parameter]
	public EventCallback<DateTime> EventSelected { get; set; }

	protected override async Task OnParametersSetAsync()
	{
		if (_cameraId != CameraId || _selectedDate != SelectedDate)
		{
			_cameraId = CameraId;
			_selectedDate = SelectedDate;
			await LoadEventsAsync();
		}
	}

	private async Task LoadEventsAsync()
	{
		try
		{
			var date = new DateTimeOffset(SelectedDate, TimeSpan.Zero); // Убеждаемся, что offset = 0
			_archiveEvents = await ScopeFactory.MediatorSend(new GetEventsUseCase.Query(CameraId, date));
		}
		catch (Exception ex)
		{
			_archiveEvents = null;
			Logger.LogError(ex, ex.Message);
			Snackbar.Add("Ошибка при запросе событий от сервера.");
		}

		if (_archiveEvents is null) return;

		await SetLoadingAsync(false);
		switch (_archiveEvents.Result)
		{
			case GetEventsUseCase.Result.Success:
				break;
			case GetEventsUseCase.Result.ValidationError:
				Snackbar.Add("Не удалось получить список событий архива.", MudBlazor.Severity.Error);
				break;
			case GetEventsUseCase.Result.Unknown:
				Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(EventListComponent), nameof(GetEventsUseCase));
				Snackbar.Add($"Не удалось получить список событий архива из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
				break;
			default:
				Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(EventListComponent), nameof(GetEventsUseCase), _archiveEvents.Result);
				Snackbar.Add($"Не удалось получить список событий архива из-за ошибки: {_archiveEvents.Result}", MudBlazor.Severity.Error);
				break;
		}
	}

	private async Task OnEventSelected(GetEventsUseCase.Response.Event @event)
	{
		if (EventSelected.HasDelegate)
		{
			await EventSelected.InvokeAsync(@event.StartTime.ToLocalTime().LocalDateTime);
		}
	}
}
