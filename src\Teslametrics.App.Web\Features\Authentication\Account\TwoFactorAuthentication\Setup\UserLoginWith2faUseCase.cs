using System;
using System.Security.Claims;
using FluentValidation;
using MediatR;
using Microsoft.AspNetCore.Components.Authorization;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.Core.Domain.AccessControl;
using Teslametrics.Core.Domain.AccessControl.Users;
using Teslametrics.App.Web.Services.Authentication;
using Teslametrics.App.Web.Services.Cookies;
using Teslametrics.Core.Services.TransactionManager;
using Teslametrics.App.Web.Services.Authentication.TwoFactorAuth;
using Teslametrics.App.Web.Services.UserSession;

namespace Teslametrics.App.Web.Features.Authentication.Account.TwoFactorAuthentication.Setup;

public static class UserLoginWith2faUseCase
{
    public record Command(string Username, string Code) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public Result Result { get; private set; }
        public bool IsSuccess => Result == Result.Success;
        public Guid? SessionId { get; private set; }
        public ClaimsPrincipal? ClaimsPrincipal { get; private set; }

        public Response(ClaimsPrincipal claimsPrincipal, Guid? sessionId, Result result)
        {
            ClaimsPrincipal = claimsPrincipal;
            SessionId = sessionId;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            Result = result;
        }
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        UserNotFound,
        UserLockedout,
        ForceChangePassword,
        WrongCode,
        FailedToCreateSession
    }

    public class Validator : AbstractValidator<Command>
    {
        public Validator()
        {
            RuleFor(c => c.Username).NotEmpty();
            RuleFor(c => c.Code).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Command, Response>
    {
        private readonly IValidator<Command> _validator;
        private readonly IUserRepository _userRepository;
        private readonly ITransactionManager _transactionManager;
        private readonly LogInDomainService _logInDomainService;
        private readonly ILogger<Handler> _logger;
        private readonly TwoFactorAuthService _twoFactorAuthService;
        private readonly ISessionProvider _sessionProvider;
        private readonly CookieStorageAccessor _cookieStorageAccessor;
        private readonly IHostEnvironmentAuthenticationStateProvider _authStateProvider;

        public Handler(IValidator<Command> validator,
                       IUserRepository userRepository,
                       ITransactionManager transactionManager,
                       LogInDomainService logInDomainService,
                       ILogger<Handler> logger,
                       TwoFactorAuthService twoFactorAuthService,
                       ISessionProvider sessionProvider,
                       CookieStorageAccessor cookieStorageAccessor,
                       IHostEnvironmentAuthenticationStateProvider authStateProvider)
        {
            _validator = validator;
            _userRepository = userRepository;
            _transactionManager = transactionManager;
            _logInDomainService = logInDomainService;
            _logger = logger;
            _twoFactorAuthService = twoFactorAuthService;
            _sessionProvider = sessionProvider;
            _cookieStorageAccessor = cookieStorageAccessor;
            _authStateProvider = authStateProvider;
        }

        public async Task<Response> Handle(Command request, CancellationToken cancellationToken)
        {
            try
            {
                if (!_validator.Validate(request).IsValid)
                {
                    return new Response(Result.ValidationError);
                }
                using var transaction = await _transactionManager.CreateTransactionAsync();
                var user = await _userRepository.FindByUsernameAsync(request.Username, cancellationToken);

                if (user is null)
                {
                    return new Response(Result.UserNotFound);
                }

                if (user.LockoutEnabled)
                {
                    return new Response(Result.UserLockedout);
                }

                if (user.ForcePasswordChange)
                {
                    return new Response(Result.ForceChangePassword);
                }

                if (!_twoFactorAuthService.VerifyTotpCode(user.SecretKey, request.Code))
                {
                    return new Response(Result.WrongCode);
                }

                user.CompleteSetup2FA();

                await _userRepository.SaveChangesAsync(cancellationToken);

                var permissions = await _logInDomainService.LogInAsync(user, cancellationToken);

                var claims = new List<Claim>();
                claims.AddRange(permissions.Select(x => new Claim(x.Item1, x.Item2)));

                Guid userId = Guid.Parse(claims.Single(x => x.Type == ClaimTypes.NameIdentifier).Value);

                Guid? sessionId = _sessionProvider.GetSessionByUserId(userId)?.Id;
                ClaimsPrincipal? userPrincipal = new(new ClaimsIdentity(claims, "Custom"));

                if (sessionId is null)
                {
                    SessionResult? sessionResult = _sessionProvider.CreateSession(userPrincipal);
                    if (!sessionResult.IsSuccess)
                        return new Response(Result.FailedToCreateSession);
                    sessionId = sessionResult.SessionId;
                }

                // await _cookieStorageAccessor.SetValueAsync(AuthenticationStorageNames.SessionId, sessionId);
                //_authStateProvider.SetAuthenticationState(Task.FromResult(new AuthenticationState(userPrincipal)));

                await transaction.CommitAsync();

                return new Response(userPrincipal, sessionId, Result.Success);
            }
            catch (Exception exc)
            {
                _logger.LogError(exc, exc.Message);
                return new Response(Result.Unknown);
            }

        }
    }
}
