@using Teslametrics.App.Web.Features.Main.Cameras.CameraDrawer.Create.PresetField
@using Teslametrics.App.Web.Features.Main.Cameras.CameraDrawer.Create.Onvif
@inherits InteractiveBaseComponent
<DrawerHeader>
    <MudText Typo="Typo.h3">Создание камеры</MudText>
</DrawerHeader>
<MudForm Model="_model"
         Validation="_validator.ValidateValue"
         @bind-IsValid="_isValid"
         OverrideFieldValidation="true">
    <MudTabs PanelClass="mud-height-full px-4 py-4 d-flex flex-column gap-4"
             TabHeaderClass="mt-3 mx-3"
             KeepPanelsAlive="true">
        <MudTabPanel Text="Информация в системе">
            <FormSectionComponent Title="Описание камеры"
                                  Subtitle="Настройки, которые влияют только на восприятие человеком">
                <MudTextField @bind-Value="_model.Name"
                              For="@(() => _model.Name)"
                              Clearable="true"
                              InputType="InputType.Text"
                              Immediate="true"
                              Label="Наименование"
                              HelperText="Необходимо для идентификации камеры человеком"
                              RequiredError="Данное поле обязательно"
                              Required="true" />
            </FormSectionComponent>
            <FormSectionComponent Title="Параметры камеры"
                                  Subtitle="Данные настройки важны для работы в системе">
                <PresetFieldComponent @bind-Selected="_model.Preset"
                                      For="() => _model.Preset"
                                      OrganizationId="OrganizationId" />

                <MudTextField T="string"
                              Value="_model.ArchiveUri"
                              ValueChanged="OnArchiveUriChanged"
                              For="@(() => _model.ArchiveUri)"
                              InputType="InputType.Text"
                              Immediate="true"
                              Label="Ссылка на архивный поток"
                              HelperText="По данной ссылке медиасервер будет получать поток с камеры"
                              @ref="@_archiveUriRef" />

                <MudTextField T="string"
                              Value="_model.ViewUri"
                              ValueChanged="OnViewUriChanged"
                              For="@(() => _model.ViewUri)"
                              InputType="InputType.Text"
                              Immediate="true"
                              Label="Ссылка на поток для видов"
                              HelperText="По данной ссылке медиасервер будет получать поток с камеры"
                              @ref="@_viewUriRef" />

                <MudTextField T="string"
                              Value="_model.PublicUri"
                              ValueChanged="OnPublicUriChanged"
                              For="@(() => _model.PublicUri)"
                              InputType="InputType.Text"
                              Immediate="true"
                              Label="Ссылка на публичный поток"
                              HelperText="По данной ссылке медиасервер будет получать поток с камеры"
                              @ref="@_publicUriRef" />

                <MudCheckBox @bind-Value="_model.StartOnCreate"
                             Label="Запуск после создания"
                             Color="Color.Primary"
                             Class="ml-n3" />

                <MudCheckBox @bind-Value="_model.AutoStart"
                             Label="Автозапуск при перезапуске системы"
                             Color="Color.Primary"
                             Class="ml-n3" />

                <MudCheckBox T="bool"
                             @bind-Value="_model.OnvifEnabled"
                             Color="Color.Primary"
                             Class="ml-n3"
                             Label="Включить ONVIF?" />

            </FormSectionComponent>

            <FormSectionComponent Title="Местонахождение камеры">
                <TimeZoneSelector @bind-TimeZone="@_model.TimeZone"
                                  Label="Часовой пояс"
                                  HelperText="Часовой пояс, в котором будет расположена камера" />

                <YandexMaps ReadOnly="false"
                            @bind-Coordinates="@_model.Coordinates"
                            Width="calc(100% + 32px)"
                            Class="ma-n4 rounded-b overflow-hidden"
                            Height="400px" />
            </FormSectionComponent>
        </MudTabPanel>

        <MudTabPanel Text="ONVIF"
                     Disabled="!_model.OnvifEnabled">
            <OnvifComponent Onvif="_model.OnvifSettings" />
        </MudTabPanel>
    </MudTabs>
</MudForm>
<DrawerActions>
    <MudSpacer />
    <MudButton OnClick="CancelAsync">Закрыть</MudButton>
    <MudButton OnClick="SubmitAsync"
               Disabled="@(!_isValid)"
               Color="Color.Secondary"
               Variant="Variant.Outlined">Сохранить</MudButton>
</DrawerActions>