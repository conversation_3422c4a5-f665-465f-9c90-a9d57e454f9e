const mapsStorage = new WeakMap();

export function Initialize(containerId, apiKey, locale, coordinates, readonly, singleMarkerMode, objRef) {
	return new Promise((resolve, reject) => {
		const container = document.getElementById(containerId);
		if (!container) {
			reject(new Error('Map container not found'));
			return;
		}

		const existingConfig = mapsStorage.get(container);
		if (existingConfig && existingConfig.map) {
			existingConfig.map.destroy();
			mapsStorage.delete(container);
		}

		if (window.ymaps) {
			try {
				initMap(container, coordinates, readonly, singleMarkerMode, objRef);
				resolve();
			} catch (error) {
				reject(error);
			}
			return;
		}

		const script = document.createElement('script');
		script.src = `https://api-maps.yandex.ru/2.1/?apikey=${apiKey}&lang=${locale}`;
		script.async = true;
		script.onload = () => {
			ymaps.ready(() => {
				try {
					initMap(container, coordinates, readonly, singleMarkerMode, objRef);
					resolve();
				} catch (error) {
					reject(error);
				}
			});
		};
		script.onerror = (error) => reject(error);
		document.head.appendChild(script);
	});
}

function initMap(container, coordinates, readonly, singleMarkerMode, objRef) {
	const mapCenter = coordinates?.length > 0
		? [coordinates[0].latitude, coordinates[0].longitude]
		: [55.76, 37.64];
	const zoom = coordinates?.length > 0 ? 19 : 10;

	const map = new ymaps.Map(container, {
		center: mapCenter,
		zoom: zoom,
		controls: ['zoomControl', 'geolocationControl', 'fullscreenControl', 'searchControl']
	}, {
		suppressMapOpenBlock: true,
		yandexMapDisablePoiInteractivity: true,
		restrictMapArea: false
	});

	const markers = [];

	if (coordinates && coordinates.length > 0) {
		for (const coord of coordinates) {
			const marker = createMarker(coord, readonly);
			map.geoObjects.add(marker);
			markers.push(marker);

			if (!readonly) {
				attachMarkerEvents(marker, container, objRef);
			}
		}
	}

	const mapConfig = {
		map,
		markers,
		objRef,
		readonly,
		singleMarkerMode
	};
	mapsStorage.set(container, mapConfig);

	if (!readonly) {
		map.events.add('click', function (e) {
			const coords = e.get('coords');
			const config = mapsStorage.get(container);

			if (config.singleMarkerMode) {
				config.markers.forEach(m => config.map.geoObjects.remove(m));
				config.markers = [];
			}

			const newMarker = createMarker({ latitude: coords[0], longitude: coords[1] }, false);
			config.map.geoObjects.add(newMarker);
			config.markers.push(newMarker);

			attachMarkerEvents(newMarker, container, objRef);
			updateMarkersList(config, objRef);
		});
	}
}

function createMarker(coord, readonly) {
	return new ymaps.Placemark([coord.latitude, coord.longitude], {}, {
		draggable: !readonly,
		iconLayout: 'default#image',
		iconImageHref: '/map_pointer.svg', // путь к картинке
		iconImageSize: [20, 24]
	});
}

function attachMarkerEvents(marker, container, objRef) {
	marker.events.add('dragend', function () {
		const config = mapsStorage.get(container);
		updateMarkersList(config, objRef);
	});

	marker.events.add('click', function (e) {
		e.stopPropagation();
		const config = mapsStorage.get(container);
		config.map.geoObjects.remove(marker);
		const index = config.markers.indexOf(marker);
		if (index !== -1) {
			config.markers.splice(index, 1);
		}
		updateMarkersList(config, objRef);
	});
}

function updateMarkersList(config, objRef) {
	if (config.singleMarkerMode) {
		if (config.markers.length > 0) {
			const marker = config.markers[0];
			const coords = marker.geometry.getCoordinates();
			objRef.invokeMethodAsync('OnMarkerPositionChanged', coords[0], coords[1]);
		} else {
			objRef.invokeMethodAsync('OnMarkerPositionChanged', null, null);
		}
	} else {
		const coordsList = config.markers.map(m => {
			const coords = m.geometry.getCoordinates();
			return { latitude: coords[0], longitude: coords[1] };
		});
		objRef.invokeMethodAsync('OnMarkersUpdated', coordsList);
	}
}

export function setMarkers(containerId, coordinates) {
	const container = document.getElementById(containerId);
	if (!container) throw new Error('Map container not found');

	const config = mapsStorage.get(container);
	if (!config) throw new Error('Map is not initialized');

	// Удаляем старые маркеры
	config.markers.forEach(marker => {
		config.map.geoObjects.remove(marker);
	});
	config.markers = [];

	if (!coordinates || coordinates.length === 0) {
		return;
	}

	if (config.singleMarkerMode && coordinates.length > 1) {
		// В режиме одной метки игнорируем все кроме первой
		coordinates = [coordinates[0]];
	}

	for (const coord of coordinates) {
		const marker = createMarker(coord, config.readonly);
		config.map.geoObjects.add(marker);
		config.markers.push(marker);

		if (!config.readonly) {
			attachMarkerEvents(marker, container, config.objRef);
		}
	}
}