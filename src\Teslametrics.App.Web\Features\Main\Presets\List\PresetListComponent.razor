@using Teslametrics.Shared
@inherits InteractiveBaseComponent
<MudDataGrid T="GetCameraPresetListUseCase.Response.Item"
			 Items="@(_response?.Items ?? [])"
			 Filterable="false"
			 SortMode="SortMode.None"
			 Outlined="false"
			 Elevation="0"
			 CurrentPage="CurrentPage"
			 RowsPerPage="Limit"
			 RowsPerPageChanged="RowsPerPageChanged"
			 Loading="IsLoading"
			 Striped="true"
			 Hover="true"
			 RowClick="Select">
	<ToolBarContent>
		<MudStack Row="true"
				  Class="mud-width-full"
				  AlignItems="AlignItems.Start">
			<MudText Typo="Typo.h6"
					 Class="mt-4">Пресеты камер</MudText>
			<MudSpacer />
			@if (_subResponse is null || !_subResponse.IsSuccess)
			{
				<MudTooltip Arrow="true"
							Placement="Placement.Start"
							Text="Ошибка подписки на события">
					<MudIconButton OnClick="SubscribeAsync"
								   Icon="@Icons.Material.Filled.ErrorOutline"
								   Color="Color.Error" />
				</MudTooltip>
			}
			<AuthorizeView Policy="@AppPermissions.Main.CameraPresets.Create.GetEnumPermissionString()"
						   Context="innerContext">
				<MudButton StartIcon="@Icons.Material.Outlined.Add"
						   OnClick="Create"
						   Variant="Variant.Outlined"
						   Color="Color.Primary"
						   Class="mt-2">
					Создать
				</MudButton>
			</AuthorizeView>
			<MudTextField Value="_searchString"
						  Placeholder="Поиск"
						  Adornment="Adornment.Start"
						  AdornmentIcon="@Icons.Material.Filled.Search"
						  IconSize="Size.Medium"
						  Class="mt-3"
						  Immediate="true"
						  DebounceInterval="500"
						  OnDebounceIntervalElapsed="@(async () => await OnSearchAsync(_searchString))" />
		</MudStack>
	</ToolBarContent>
	<Columns>
		<PropertyColumn Property="x => x.Name"
						Title="Наименование пресета" />
		<TemplateColumn CellClass="d-flex justify-end">
			<HeaderTemplate>
				<MudSpacer />
				<MudTooltip Text="@($"Время последнего обновления: {_lastRefreshTime.ToLocalTime()}")"
							Arrow="true"
							Placement="Placement.Left">
					<MudIconButton OnClick="RefreshAsync"
								   Icon="@Icons.Material.Outlined.Refresh" />
				</MudTooltip>
			</HeaderTemplate>
			<CellTemplate>
				<MudButtonGroup Color="Color.Primary"
								Variant="Variant.Outlined">
					<AuthorizeView Policy="@AppPermissions.Main.CameraPresets.Update.GetEnumPermissionString()"
								   Resource="new PolicyRequirementResource(Guid.Empty, context.Item.Id)"
								   Context="innerContext">
					   <Authorized>
							<MudButton OnClick="() => Edit(context.Item.Id)">Редактировать</MudButton>
					   </Authorized>
					   <NotAuthorized>
							<MudButton OnClick="() => Select(context.Item.Id)">Просмотр</MudButton>
					   </NotAuthorized>
					</AuthorizeView>
					<MudMenu Icon="@Icons.Material.Filled.ArrowDropDown"
							 Style="align-self: auto;">
						<MudMenuItem OnClick="() => Select(context.Item.Id)"
									 Icon="@Icons.Material.Outlined.PanoramaFishEye">Просмотр</MudMenuItem>
						<AuthorizeView Policy="@AppPermissions.Main.CameraPresets.Update.GetEnumPermissionString()"
									   Resource="new PolicyRequirementResource(Guid.Empty, context.Item.Id)"
									   Context="innerContext">
							<MudMenuItem OnClick="() => Edit(context.Item.Id)"
										 Icon="@Icons.Material.Outlined.Edit">Редактировать</MudMenuItem>
						</AuthorizeView>
						<AuthorizeView Policy="@AppPermissions.Main.CameraPresets.Delete.GetEnumPermissionString()"
									   Resource="new PolicyRequirementResource(Guid.Empty, context.Item.Id)"
									   Context="innerContext">
							<MudDivider Class="my-4" />
							<MudMenuItem OnClick="() => Delete(context.Item.Id)"
										 Icon="@Icons.Material.Outlined.Delete"
										 IconColor="Color.Warning">Удалить</MudMenuItem>
						</AuthorizeView>
					</MudMenu>
				</MudButtonGroup>
			</CellTemplate>
		</TemplateColumn>
	</Columns>
	<NoRecordsContent>
		<NoItemsFoundComponent HasItems="@(_response is not null && _response.Result == GetCameraPresetListUseCase.Result.Success && _response.Items.Count > 0)"
							   LastRefreshTime="_lastRefreshTime"
							   RefreshAsync="RefreshAsync" />
		<ErrorReceivedComponent Result="_response?.Result"
								LastRefreshTime="_lastRefreshTime"
								RefreshAsync="RefreshAsync" />
	</NoRecordsContent>
	<PagerContent>
		<MudDataGridPager T="GetCameraPresetListUseCase.Response.Item"
						  InfoFormat="{first_item}-{last_item} из {all_items}"
						  RowsPerPageString="Строк на страницу:" />
	</PagerContent>
</MudDataGrid>