@using Microsoft.AspNetCore.Components.Authorization
@using Teslametrics.Shared
@attribute [StreamRendering]
@inherits InteractiveBaseComponent<UserListComponent>
<div class="d_contents">
	<MudDataGrid T="GetUserListUseCase.Response.Item"
				 Items="@(_response?.Items ?? [])"
				 MultiSelection="true"
				 Filterable="false"
				 SortMode="SortMode.None"
				 QuickFilter="@_quickFilter"
				 Outlined="false"
				 Elevation="0"
				 Loading="IsLoading"
				 Virtualize="true"
				 FixedHeader="true"
				 Height="100%"
				 CurrentPage="CurrentPage"
				 RowsPerPage="Limit"
				 Striped="true"
				 Hover="true"
				 RowsPerPageChanged="RowsPerPageChanged"
				 RowClick="Select">
		<Columns>
			<PropertyColumn Property="x => x.Username">
				<HeaderTemplate>
					<MudText Typo="Typo.h6">Список пользователей</MudText>
				</HeaderTemplate>
			</PropertyColumn>
			<TemplateColumn CellClass="d-flex justify-end">
				<HeaderTemplate>
					<MudSpacer />
					<MudTooltip Text="@($"Время последнего обновления: {_lastRefreshTime.ToLocalTime()}")"
								Arrow="true"
								Placement="Placement.Left">
						<MudIconButton OnClick="RefreshAsync"
									   Icon="@Icons.Material.Outlined.Refresh" />
					</MudTooltip>
					<MudStack Row="true"
							  AlignItems="AlignItems.Center"
							  Justify="Justify.Center	">
						<div class="d_contents">
							<MudTextField @bind-Value="_searchString"
										  Placeholder="Поиск"
										  Adornment="Adornment.Start"
										  AdornmentIcon="@Icons.Material.Filled.Search"
										  IconSize="Size.Medium"
										  Immediate="true" />
						</div>
						<AuthorizeView Policy="@(Fqdn<AppPermissions>.GetNames(AppPermissions.Main.AccessControl.Users.Create).Last())"
									   Resource="new PolicyRequirementResource(OrganizationId)"
									   Context="innerContext">
							<MudButton OnClick="CreateUser"
									   Color="Color.Primary"
									   Variant="Variant.Outlined">Добавить пользователя</MudButton>
						</AuthorizeView>
					</MudStack>
				</HeaderTemplate>
				<CellTemplate>
					<MudButtonGroup Color="Color.Primary"
									Variant="Variant.Outlined">
						@if (!context.Item.IsSystem)
						{
							<AuthorizeView Policy="@(Fqdn<AppPermissions>.GetNames(AppPermissions.Main.AccessControl.Users.Update).Last())" Resource="new PolicyRequirementResource(OrganizationId, context.Item.Id)" Context="innerContext">
								<Authorized>
									<MudButton OnClick="() => Edit(context.Item)" >Редактировать</MudButton>
								</Authorized>
								<NotAuthorized>
									<MudButton OnClick="() => Select(context.Item)">Просмотр</MudButton>
								</NotAuthorized>
							</AuthorizeView>
						}
						else
						{
							<MudButton OnClick="() => Select(context.Item)">Просмотр</MudButton>
						}
						<MudMenu Icon="@Icons.Material.Filled.ArrowDropDown"
								 Style="align-self: auto;">
							<MudMenuItem OnClick="() => Select(context.Item)"
										 Icon="@Icons.Material.Outlined.PanoramaFishEye">Просмотр</MudMenuItem>
							@if (!context.Item.IsSystem)
							{
								<AuthorizeView Policy="@(Fqdn<AppPermissions>.GetNames(AppPermissions.Main.AccessControl.Users.Update).Last())"
											   Resource="new PolicyRequirementResource(OrganizationId, context.Item.Id)"
											   Context="innerContext">
									<MudMenuItem OnClick="() => Edit(context.Item)"
												 Icon="@Icons.Material.Outlined.Edit">Редактировать</MudMenuItem>
									<MudMenuItem OnClick="() => ChangeUserPassword(context.Item)"
												 Icon="@Icons.Material.Filled.Login"
												 IconColor="Color.Warning">Смена пароля пользователя</MudMenuItem>
								</AuthorizeView>
							}
							<AuthorizeView Policy="@(Fqdn<AppPermissions>.GetNames(AppPermissions.Main.AccessControl.Users.ForceChangePassword).Last())"
										   Resource="new PolicyRequirementResource(OrganizationId, context.Item.Id)"
										   Context="innerContext">
								<MudMenuItem OnClick="() => LoginpasswordChange(context.Item)"
											 Icon="@Icons.Material.Filled.Login"
											 IconColor="Color.Warning">Смена пароля при входе</MudMenuItem>
							</AuthorizeView>
							@if (!context.Item.IsSystem)
							{
								<AuthorizeView Policy="@(Fqdn<AppPermissions>.GetNames(AppPermissions.Main.AccessControl.Users.Delete).Last())"
											   Resource="new PolicyRequirementResource(OrganizationId, context.Item.Id)"
											   Context="innerContext">
									<MudDivider Class="my-4" />
									<MudMenuItem OnClick="() => Delete(context.Item)"
												 Icon="@Icons.Material.Outlined.Delete"
												 IconColor="Color.Warning">Удалить</MudMenuItem>
								</AuthorizeView>
							}
						</MudMenu>
					</MudButtonGroup>
				</CellTemplate>
			</TemplateColumn>
		</Columns>
		<RowLoadingContent>
			<MudSkeleton />
		</RowLoadingContent>
		<NoRecordsContent>
			<MudStack Class="mud-width-full"
					  AlignItems="AlignItems.Center"
					  Justify="Justify.Center">
				<NoUsersFoundComponent />
				<MudButton OnClick="RefreshAsync"
						   Variant="Variant.Filled"
						   Color="Color.Primary">Обновить</MudButton>
			</MudStack>
		</NoRecordsContent>
		<PagerContent>
			<MudDataGridPager T="GetUserListUseCase.Response.Item"
							  InfoFormat="{first_item}-{last_item} из {all_items}"
							  RowsPerPageString="Строк на страницу:" />
		</PagerContent>
	</MudDataGrid>
</div>