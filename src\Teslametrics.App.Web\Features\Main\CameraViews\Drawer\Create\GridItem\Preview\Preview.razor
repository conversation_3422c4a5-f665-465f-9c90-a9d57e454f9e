﻿@using Teslametrics.MediaServer.Orleans.Camera
@inherits InteractiveBaseComponent
@if (CameraId.HasValue && CameraId.Value != Guid.Empty)
{
    @switch (_response?.CameraStatus)
    {
        case CameraStatus.Running:
            <ImageComponent ImageUrl="@($"/streams/{CameraId}/{_response.CameraStreamId}/view/preview?t={DateTime.UtcNow.ToLocalTime()}")" />
            break;
        case CameraStatus.Stopped:
            <div class="empty_cell">
                <MudIcon Icon="@Icons.Material.Filled.Block"
                         Color="Color.Warning" />
                <div>Камера отключена</div>
            </div>
            break;
        case CameraStatus.Starting:
            <div class="empty_cell">
                <MudProgressCircular Color="Color.Info"
                                     Style="height:70px;width:70px;"
                                     Indeterminate="true" />
                <div>Камера подключается</div>
            </div>
            break;
        case CameraStatus.Problem:
            <div class="empty_cell">
                <MudIcon Icon="@Icons.Material.Filled.Block"
                         Color="Color.Error" />
                <div>Ошибка</div>
            </div>
            break;
        default:
            break;
    }

    @if (_response is not null && !_response.IsSuccess)
    {
        <MudText Typo="Typo.subtitle1">Не удалось получить камеру</MudText>
    }

    @if (IsLoading && (_response is null || !_response.IsSuccess))
    {
        <MudSkeleton SkeletonType="SkeletonType.Rectangle"
                     Class="skeleton-camera" />
    }
}
else
{
    <MudText Typo="Typo.subtitle1">Пустая ячейка</MudText>
    <div class="empty_icon_container">
        <MudIcon Icon="@Icons.Material.Filled.VideocamOff"
                 Style="font-size: 4rem;" />
    </div>
}