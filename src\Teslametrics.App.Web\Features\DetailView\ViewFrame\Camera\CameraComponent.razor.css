.stream_content
{
	aspect-ratio: 16/9;
	display: flex;
	align-items: center;
	align-content: center;
	justify-content: center;
}

.empty_cell
{
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	color: var(--mud-palette-text-secondary);
	gap: 8px;
	aspect-ratio: 16 / 9;
}

.empty_cell ::deep .mud-icon-root
{
	font-size: 2rem;
}

::deep .camera_cell
{
	position: relative;
	width: 100%;
	overflow: hidden;
	height: 100%;
	display: flex;
	justify-content: center;
}

.camera_name
{
	background: var(--mud-palette-background-gray);
	position: absolute;
	padding: 4px 8px;
	border-radius: 4px;
	font-size: 0.875rem;
	opacity: 0;
	transition: opacity 0.2s ease-in-out;
	z-index: 1;
	top: 8px;
	left: 8px;
}

.camera_cell:hover .camera_name
{
	opacity: 1;
}