@using Teslametrics.App.Web.Components.MSEPlayer
@using Teslametrics.App.Web.Orleans.Camera
@inherits InteractiveBaseComponent
<div class="d_contents">
    <MudCard class="camera_cell">
        @if (_viewResponse is not null && _viewResponse.IsSuccess)
        {
            <div class="camera_name">@_viewResponse.Name</div>
            <div class="stream_content">
                @if (_viewResponse.IsBlocked)
                {
                    <div class="empty_cell">
                        <MudIcon Icon="@Icons.Material.Filled.Block"
                                 Color="Color.Error" />
                        <div>Камера заблокирована</div>
                    </div>
                }
                else
                {
                    @switch (_viewResponse.CameraStatus)
                    {
                        case CameraStatus.Running:
                            @* <Player Id="@_playerId"
									CameraId="@_viewResponse.Id" /> *@
                            @if (_viewResponse.CameraStreamId.HasValue && _viewResponse.CameraStreamId.Value != Guid.Empty)
                            {
                                <MsePlayer CameraId="@_viewResponse.CameraStreamId.Value"
                                           Type="StreamType.View"
                                           Autoplay="true"
                                           Muted="true" />
                            }
                            else
                            {
                                <div class="empty_cell">
                                    <MudIcon Icon="@Icons.Material.Filled.Block"
                                             Color="Color.Error" />
                                    <div>Ошибка</div>
                                </div>
                            }
                            break;
                        case CameraStatus.Stopped:
                            <div class="empty_cell">
                                <MudIcon Icon="@Icons.Material.Filled.Block"
                                         Color="Color.Warning" />
                                <div>Камера отключена</div>
                            </div>
                            break;
                        case CameraStatus.Starting:
                            <div class="empty_cell">
                                <MudProgressCircular Color="Color.Info"
                                                     Style="height:70px;width:70px;"
                                                     Indeterminate="true" />
                                <div>Камера подключается</div>
                            </div>
                            break;
                        case CameraStatus.Problem:
                            <div class="empty_cell">
                                <MudIcon Icon="@Icons.Material.Filled.Block"
                                         Color="Color.Error" />
                                <div>Ошибка</div>
                            </div>
                            break;
                        default:
                            break;
                    }
                }
            </div>
        }
        else
        {
            <div class="empty_cell">
                <MudIcon Icon="@Icons.Material.Filled.VideocamOff" />
                <div>No Camera Selected</div>
            </div>
        }
    </MudCard>
</div>