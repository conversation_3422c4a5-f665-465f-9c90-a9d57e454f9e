using System.Linq.Expressions;
using Microsoft.AspNetCore.Components;
using MudBlazor;
using Teslametrics.App.Web.Extensions;
using Teslametrics.App.Web.Shared;

namespace Teslametrics.App.Web.Features.Main.Cameras.CameraDrawer.Create.PresetField;

public partial class PresetFieldComponent : IDisposable
{
	private GetPresetStreamConfigUseCase.Response? _streamConfigResponse = null;
	private CancellationTokenSource? _tooltipCts;

	public record Preset(Guid Id, string Title);

	#region [Parameters]
	[Parameter]
	[Category(CategoryTypes.FormComponent.Validation)]
	public Expression<Func<Preset>>? For { get; set; }

	[Parameter]
	public Preset? Selected { get; set; }

	[Parameter]
	public EventCallback<Preset> SelectedChanged { get; set; }

	[Parameter]
	[EditorRequired]
	public Guid OrganizationId { get; set; }
	#endregion

	private async Task<IEnumerable<Preset>> SearchAsync(string value, CancellationToken token)
	{
		GetPresetListUseCase.Response? response = null;
		try
		{
			await SetLoadingAsync(true);
			response = await ScopeFactory.MediatorSend(new GetPresetListUseCase.Query(OrganizationId, 0, 25, value), token);
		}
		catch (Exception exc)
		{
			response = null;
			Logger.LogError(exc, exc.Message);
			Snackbar.Add("Не удалось получить список пресетов камеры из-за непредвиденной ошибки.", Severity.Error);
			return [];
		}

		if (response is null) return [];
		switch (response.Result)
		{
			case GetPresetListUseCase.Result.Success:
				return response.Items.Select(item => new Preset(item.Id, item.Name));
			case GetPresetListUseCase.Result.Unknown:
				Snackbar.Add("Не удалось получить список пресетов камеры из-за непредвиденной ошибки.", Severity.Error);
				Logger.LogError("Unexpected result in {Component}, {UseCase}", nameof(PresetFieldComponent), nameof(GetPresetStreamConfigUseCase));
				break;
			default:
				Snackbar.Add("Не удалось получить список пресетов камеры из-за непредвиденной ошибки.", Severity.Error);
				Logger.LogError("Unexpected result in {Component}, {UseCase}: {Result}", nameof(PresetFieldComponent), nameof(GetPresetListUseCase), response.Result);
				break;
		}
		return [];
	}

	#region Event Handlers
	private async Task OnSelectedValudeChanged(Preset? preset)
	{
		if (SelectedChanged.HasDelegate)
		{
			await SelectedChanged.InvokeAsync(preset);
		}
		else
		{
			Selected = preset;
		}
	}

	private async Task OnTooltipVisibleChanged(bool visible, Guid id)
	{
		_tooltipCts?.Cancel();
		_tooltipCts?.Dispose();
		_tooltipCts = null;

		if (!visible)
		{
			return;
		}

		_tooltipCts = new CancellationTokenSource();
		try
		{
			await SetLoadingAsync(true);
			_streamConfigResponse = await ScopeFactory.MediatorSend(
				new GetPresetStreamConfigUseCase.Query(id),
				_tooltipCts.Token
			);
		}
		catch (OperationCanceledException)
		{
			// Запрос был отменен, игнорируем
		}
		catch (Exception exc)
		{
			_streamConfigResponse = null;
			Logger.LogError(exc, exc.Message);
			Snackbar.Add("Не удалось получить конфигурацию пресетов камеры из-за непредвиденной ошибки.", Severity.Error);
		}
		finally
		{
			await SetLoadingAsync(false);
		}

		if (_streamConfigResponse is null) return;

		switch (_streamConfigResponse.Result)
		{
			case GetPresetStreamConfigUseCase.Result.Success:
				break;
			case GetPresetStreamConfigUseCase.Result.ValidationError:
				Snackbar.Add("Ошибка валидации при запросе конфигурации пресетов камеры.", Severity.Error);
				break;
			case GetPresetStreamConfigUseCase.Result.Unknown:
				Snackbar.Add("Неизвестная ошибка при запросе конфигурации пресетов камеры.", Severity.Error);
				Logger.LogError("Unexpected result in {Component}, {UseCase}", nameof(PresetFieldComponent), nameof(GetPresetStreamConfigUseCase));
				break;
			default:
				Snackbar.Add("Неизвестная ошибка при запросе конфигурации пресетов камеры.", Severity.Error);
				Logger.LogError("Unexpected result in {Component}, {UseCase}: {Result}", nameof(PresetFieldComponent), nameof(GetPresetStreamConfigUseCase), _streamConfigResponse.Result);
				break;
		}
	}
	#endregion

	public void Dispose()
	{
		_tooltipCts?.Cancel();
		_tooltipCts?.Dispose();
	}
}
