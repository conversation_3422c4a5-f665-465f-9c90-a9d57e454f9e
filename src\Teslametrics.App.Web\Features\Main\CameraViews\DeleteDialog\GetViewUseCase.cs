using Dapper;
using FluentValidation;
using MediatR;
using System.Data;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Data.Sql;
using Teslametrics.App.Web.Services.Persistence;

namespace Teslametrics.App.Web.Features.Main.CameraViews.DeleteDialog;

public static class GetViewUseCase
{
    public record Query(Guid Id) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public Guid Id { get; init; }

        public string Name { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(Guid id, string name)
        {
            Id = id;
            Name = name;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Result = result;

            Id = Guid.Empty;
            Name = string.Empty;
        }
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        ViewNotFound
    }

    public class Validator : AbstractValidator<Query>
    {
        public Validator()
        {
            RuleFor(q => q.Id).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Query, Response>
    {
        private readonly IValidator<Query> _validator;
        private readonly IDbConnection _dbConnection;

        public Handler(IValidator<Query> validator,
                       IDbConnection dbConnection)
        {
            _validator = validator;
            _dbConnection = dbConnection;
        }

        public async Task<Response> Handle(Query request, CancellationToken cancellationToken = default)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            var template = SqlQueryBuilder.Create()
                .Select(Db.CameraViews.Props.Id)
                .Select(Db.CameraViews.Props.Name)
                .Where(Db.CameraViews.Props.Id, ":Id", SqlOperator.Equals, new { request.Id })
                .Build(QueryType.Standard, Db.CameraViews.Table, RowSelection.AllRows);

            var view = await _dbConnection.QuerySingleOrDefaultAsync<CameraViewModel>(template.RawSql, template.Parameters);
            if (view is null)
            {
                return new Response(Result.ViewNotFound);
            }

            return new Response(view.Id, view.Name);
        }
    }

    public record CameraViewModel(Guid Id, string Name);
}
