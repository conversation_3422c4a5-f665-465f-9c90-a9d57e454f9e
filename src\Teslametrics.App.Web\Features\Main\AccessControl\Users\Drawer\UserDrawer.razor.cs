using Microsoft.AspNetCore.Components;
using Teslametrics.App.Web.Eto.Users;
using Teslametrics.App.Web.Events;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Users.Drawer;

public partial class UserDrawer
{
	private DrawerMode _mode;
	public Guid? _userId;

	public bool IsOpened => _mode != DrawerMode.Hidden;

	#region Parameters
	[Parameter]
	public Guid OrganizationId { get; set; }
	#endregion

	public enum DrawerMode
	{
		Hidden,
		Create,
		Edit,
		View
	}
	protected override void OnInitialized()
	{
		CompositeDisposable.Add(EventSystem.Subscribe<UserCreateEto>(OnUserHandler));
		CompositeDisposable.Add(EventSystem.Subscribe<UserSelectEto>(OnUserHandler));
		CompositeDisposable.Add(EventSystem.Subscribe<UserEditEto>(OnUserHandler));
		base.OnInitialized();
	}

	public Task ShowCreateAsync() => InvokeAsync(() =>
	{
		_userId = null;
		_mode = DrawerMode.Create;
		StateHasChanged();
	});

	public Task ShowEditAsync(Guid userId) => InvokeAsync(() =>
	{
		_userId = userId;
		_mode = DrawerMode.Edit;
		StateHasChanged();
	});

	public Task ShowViewAsync(Guid userId) => InvokeAsync(() =>
	{
		_userId = userId;
		_mode = DrawerMode.View;
		StateHasChanged();
	});

	public Task ShowAsync(DrawerMode mode, Guid userId) => InvokeAsync(() =>
	{
		if (mode == DrawerMode.Hidden || mode == DrawerMode.Create)
		{
			throw new ArgumentException($"DrawerMode Edit or View expected {mode.ToString()} provided");
		}

		_mode = mode;
		_userId = userId;
		StateHasChanged();
	});

	public Task CloseAsync() => InvokeAsync(() =>
	{
		_mode = DrawerMode.Hidden;
		_userId = null;
		StateHasChanged();
	});

	private Task ShowAsync() => InvokeAsync(() =>
	{
		_mode = DrawerMode.Create;
		StateHasChanged();
	});

	#region [Event Handlers]
	private async void OnUserHandler(BaseEto eto)
	{
		switch (eto)
		{
			case UserEditEto editEto:
				await ShowEditAsync(editEto.UserId);
				break;

			case UserCreateEto createEto:
				await ShowCreateAsync();
				break;

			case UserSelectEto selectEto:
				await ShowViewAsync(selectEto.UserId);
				break;

			default:
				await CloseAsync();
				break;
		}
	}

	private Task OnOpenChanged(bool opened)
	{
		if (opened)
		{
			if (_mode != DrawerMode.Hidden && _userId.HasValue)
			{
				return ShowAsync(_mode, _userId.Value);
			}
			else
			{
				return ShowAsync();
			}
		}
		else
		{
			return CloseAsync();
		}
	}
	#endregion [Event Handlers]
}
