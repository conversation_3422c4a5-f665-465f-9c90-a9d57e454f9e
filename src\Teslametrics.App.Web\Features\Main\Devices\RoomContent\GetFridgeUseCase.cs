using System.Data;
using System.Text.Json;
using Dapper;
using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Data.Sql;
using Teslametrics.App.Web.Services.Persistence;
using Teslametrics.App.Web.Shared;

namespace Teslametrics.App.Web.Features.Main.Devices.RoomContent;

public static class GetFridgeUseCase
{
    public record Query(Guid FridgeId) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public FridgeModel Fridge { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(FridgeModel fridge)
        {
            Fridge = fridge;

            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Result = result;
            Fridge = new();
        }

        public record FridgeModel
        {
            public Guid Id { get; set; }
            public string Name { get; set; } = string.Empty;
            public List<ISensorModel> Sensors { get; set; } = [];
            public bool HasIncidents => Sensors.Any(s => s.Incident is not null);
        }

        public interface ISensorModel
        {
            public Guid Id { get; set; }
            public string Name { get; set; } // TODO: Название топика
            public string? DisplayName { get; set; }
            public Incident? Incident { get; set; }
        }

        public class TemperatureModel : ISensorModel
        {
            public Guid Id { get; set; }
            public string Name { get; set; } // TODO: Название топика
            public string? DisplayName { get; set; }
            public Incident? Incident { get; set; }
            public TemperatureModel(Guid id, string name, string? displayName, Incident? incident = null)
            {
                Id = id;
                Name = name;
                DisplayName = displayName;
                Incident = incident;
            }
        }

        public class DoorModel : ISensorModel
        {
            public Guid Id { get; set; }
            public string Name { get; set; } // TODO: Название топика
            public string? DisplayName { get; set; }
            public Incident? Incident { get; set; }
            public DoorModel(Guid id, string name, string? displayName, Incident? incident = null)
            {
                Id = id;
                Name = name;
                DisplayName = displayName;
                Incident = incident;
            }
        }

        public class HumidityModel : ISensorModel
        {
            public Guid Id { get; set; }
            public string Name { get; set; } // TODO: Название топика
            public string? DisplayName { get; set; }
            public Incident? Incident { get; set; }
            public HumidityModel(Guid id, string name, string? displayName, Incident? incident = null)
            {
                Id = id;
                Name = name;
                DisplayName = displayName;
                Incident = incident;
            }
        }

        public class LeakModel : ISensorModel
        {
            public Guid Id { get; set; }
            public string Name { get; set; } // TODO: Название топика
            public string? DisplayName { get; set; }
            public Incident? Incident { get; set; }
            public LeakModel(Guid id, string name, string? displayName, Incident? incident = null)
            {
                Id = id;
                Name = name;
                DisplayName = displayName;
                Incident = incident;
            }
        }

        public class PowerModel : ISensorModel
        {
            public Guid Id { get; set; }
            public string Name { get; set; } // TODO: Название топика
            public string? DisplayName { get; set; }
            public Incident? Incident { get; set; }
            public PowerModel(Guid id, string name, string? displayName, Incident? incident = null)
            {
                Id = id;
                Name = name;
                DisplayName = displayName;
                Incident = incident;
            }
        }

        public record Incident // Инцидент с датчиком
        {
            public DateTimeOffset TriggeredAt { get; set; } // Когда он произошел
        }
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        PlanNotFound,
        FridgeNotFound
    }

    public class Validator : AbstractValidator<Query>
    {
        public Validator()
        {
            RuleFor(q => q.FridgeId).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Query, Response>
    {
        private readonly IValidator<Query> _validator;
        private readonly IDbConnection _dbConnection;

        public Handler(IValidator<Query> validator, IDbConnection dbConnection)
        {
            _validator = validator;
            _dbConnection = dbConnection;
        }

        public async Task<Response> Handle(Query request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            if (!await CheckTableExistsAsync())
            {
                return new Response(Result.PlanNotFound);
            }

            var template = SqlQueryBuilder.Create()
                .Select(Db.Plans.Props.Page)
                .Build(QueryType.Standard, Db.Plans.Table, RowSelection.AllRows);

            var pageJson = await _dbConnection.ExecuteScalarAsync<string?>(template.RawSql);

            if (string.IsNullOrEmpty(pageJson))
            {
                return new Response(Result.PlanNotFound);
            }

            var page = JsonSerializer.Deserialize<PageModel>(pageJson)!;

            var fridge = page.GetFridge(request.FridgeId);

            if (fridge is null)
            {
                return new Response(Result.FridgeNotFound);
            }

            template = SqlQueryBuilder.Create()
                .Select(Db.Incidents.Props.SensorId)
                .Select(Db.Incidents.Props.CreatedAt)
                .Where(Db.Incidents.Props.DeviceId, ":DeviceId", SqlOperator.Equals, new { DeviceId = request.FridgeId })
                .Where(Db.Incidents.Props.ResolvedAt, SqlOperator.IsNull)
                .Build(QueryType.Standard, Db.Incidents.Table, RowSelection.AllRows);

            var incidents = await _dbConnection.QueryAsync<IncidentModel>(template.RawSql, template.Parameters);

            var fridgeModel = new Response.FridgeModel { Id = fridge.Id, Name = fridge.Name, Sensors = fridge.Sensors.Select(s => GetSensorModel(s, incidents.Where(i => i.SensorId == s.Id))).ToList() };

            return new Response(fridgeModel);
        }

        private Response.ISensorModel GetSensorModel(ISensorModel sensor, IEnumerable<IncidentModel> incidents)
        {
            var incident = incidents.OrderByDescending(i => i.CreatedAt).FirstOrDefault();

            var model = sensor switch
            {
                TemperatureModel => (Response.ISensorModel)new Response.TemperatureModel(sensor.Id, sensor.Name, sensor.DisplayName),
                DoorModel => new Response.DoorModel(sensor.Id, sensor.Name, sensor.DisplayName),
                HumidityModel => new Response.HumidityModel(sensor.Id, sensor.Name, sensor.DisplayName),
                LeakModel => new Response.LeakModel(sensor.Id, sensor.Name, sensor.DisplayName),
                PowerModel => new Response.PowerModel(sensor.Id, sensor.Name, sensor.DisplayName),
                _ => throw new ArgumentException($"Unknown sensor type: {sensor.GetType()}")
            };

            model.Incident = incident is null ? null : new Response.Incident { TriggeredAt = incident.CreatedAt };

            return model;
        }

        private async Task<bool> CheckTableExistsAsync()
        {
            // Check if table exists
            var tableExists = await _dbConnection.ExecuteScalarAsync<int>(
                "SELECT COUNT(*) FROM information_schema.tables " +
                "WHERE table_schema = 'public' AND table_name = @TableName",
                new { TableName = Db.Plans.Table });

            return tableExists > 0;
        }
    }

    public record IncidentModel(Guid SensorId, DateTimeOffset CreatedAt);
}