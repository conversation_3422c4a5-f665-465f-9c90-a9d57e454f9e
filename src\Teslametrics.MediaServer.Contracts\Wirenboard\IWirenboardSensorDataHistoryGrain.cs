using Orleans;

namespace Teslametrics.MediaServer.Orleans.Wirenboard;

public interface IWirenboardSensorDataHistoryGrain : IGrainWithGuidKey
{
    /// <summary>
    /// Запускает сбор истории для указанного топика
    /// </summary>
    /// <param name="request">Запрос на запуск сбора истории</param>
    Task StartAsync(StartRequest request);

    /// <summary>
    /// Останавливает сбор истории
    /// </summary>
    Task StopAsync();

    /// <summary>
    /// Метод для поддержания активности грейна, предотвращающий его деактивацию
    /// </summary>
    /// <returns>Задача, представляющая асинхронную операцию</returns>
    Task PingAsync();

    [GenerateSerializer]
    public record StartRequest(string BrokerAddress, int Port, HistoryTopicInfo TopicInfo);
}