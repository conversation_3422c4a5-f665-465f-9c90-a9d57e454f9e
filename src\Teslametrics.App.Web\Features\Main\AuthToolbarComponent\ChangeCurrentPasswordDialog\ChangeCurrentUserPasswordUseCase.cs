using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.Core.Domain.AccessControl.Users;
using Teslametrics.App.Web.Services;
using Teslametrics.Core.Services.TransactionManager;

namespace Teslametrics.App.Web.Features.Main.AuthToolbarComponent.ChangeCurrentPasswordDialog;

public static class ChangeCurrentUserPasswordUseCase
{
    public record Command(Guid Id, string CurrentPassword, string NewPassword) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public Result Result { get; init; }
        public bool IsSuccess => Result == Result.Success;

        public Response(Result result)
        {
            Result = result;
        }
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        NewPasswordEqualsOld,
        WrongPassword,
        UserNotFound
    }

    public class Validator : AbstractValidator<Command>
    {
        public Validator()
        {
            RuleFor(c => c.Id).NotEmpty();
            RuleFor(c => c.CurrentPassword).NotEmpty();
            RuleFor(c => c.NewPassword).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Command, Response>
    {
        private readonly IValidator<Command> _validator;
        private readonly IUserRepository _userRepository;
        private readonly ITransactionManager _transactionManager;

        public Handler(IValidator<Command> validator,
                       IUserRepository userRepository,
                       ITransactionManager transactionManager)
        {
            _validator = validator;
            _userRepository = userRepository;
            _transactionManager = transactionManager;
        }

        public async Task<Response> Handle(Command request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            using var transaction = await _transactionManager.CreateTransactionAsync();

            var user = await _userRepository.FindAsync(request.Id, cancellationToken);
            if (user is null)
            {
                return new Response(Result.UserNotFound);
            }

            if (PasswordHasher.VerifyHashedPassword(user.Password, request.CurrentPassword) is PasswordVerificationResult.Failed)
            {
                return new Response(Result.WrongPassword);
            }

            if (request.CurrentPassword == request.NewPassword)
            {
                return new Response(Result.NewPasswordEqualsOld);
            }

            var hashedPassword = PasswordHasher.HashPassword(request.NewPassword);
            user.ChangePassword(hashedPassword);

            await _userRepository.SaveChangesAsync(cancellationToken);

            await transaction.CommitAsync();

            return new Response(Result.Success);
        }
    }
}