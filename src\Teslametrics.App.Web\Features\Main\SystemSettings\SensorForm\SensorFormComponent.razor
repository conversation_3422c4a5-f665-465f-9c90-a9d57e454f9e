@using System.ComponentModel.DataAnnotations
<MudStack Row="true" AlignItems="AlignItems.Center" Justify="Justify.SpaceBetween" Class="pa-4">
	<div class="sensor_form_container gap-2">
		<SelectEnumComponent TEnum="SensorType" SelectedValue="_selectedSensorType" SelectedValueChanged="OnSensorTypeChanged" Label="Тип датчика"
			Required="true" />
		<LeakSensorFormComponent SensorModel="@_selectedSensor" />
		<HumiditySensorFormComponent SensorModel="@_selectedSensor" />
		<DoorSensorFormComponent SensorModel="@_selectedSensor" />
		<PowerSensorFormComponent SensorModel="@_selectedSensor" />
		<TemperatureSensorFormComponent SensorModel="@_selectedSensor" />
	</div>
	<MudButton StartIcon="@Icons.Material.Filled.Add" OnClick="@AddSensor" Variant="Variant.Outlined" Color="Color.Primary" Class="add_button">
		Сохранить сенсор
	</MudButton>
</MudStack>

@code {
	private ISensorModel _selectedSensor = new TemperatureModel();
	private SensorType _selectedSensorType = SensorType.Temperature;

	[Parameter]
	[EditorRequired]
	public IList<ISensorModel> Sensors { get; set; } = null!;

	private void AddSensor()
	{
		_selectedSensor.Id = Guid.NewGuid();
		if (Sensors is null) return;

		if (!string.IsNullOrWhiteSpace(_selectedSensor.Name))
		{
			Sensors.Add(_selectedSensor);
			_selectedSensorType = SensorType.Temperature;
			_selectedSensor = CreateSensor(SensorType.Temperature);
		}
	}

	private void OnSensorTypeChanged(SensorType type)
	{
		_selectedSensorType = type;
		_selectedSensor = CreateSensor(type, _selectedSensor.Id, _selectedSensor.Name, _selectedSensor.DisplayName);
		StateHasChanged();
	}

	private ISensorModel CreateSensor(SensorType type, Guid? id = null, string name = "", string? displayName = null)
	{
		id ??= Guid.NewGuid();
		return type switch
		{
			SensorType.Temperature => new TemperatureModel(id.Value, name, displayName)
			{
				MinTemp = -30,
				MaxTemp = 30
			},
			SensorType.Door => new DoorModel(id.Value, name, displayName, 30),
			SensorType.Humidity => new HumidityModel(id.Value, name, displayName),
			SensorType.Leak => new LeakModel(id.Value, name, displayName),
			SensorType.Power => new PowerModel(id.Value, name, displayName),
			_ => throw new ArgumentException($"Неизвестный тип датчика: {type}")
		};
	}
}