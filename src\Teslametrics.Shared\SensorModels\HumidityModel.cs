using Orleans;

namespace Teslametrics.Shared;

[GenerateSerializer]
public class HumidityModel : BaseSensorModel
{
    [Id(3)]
    public float MinHumidity { get; set; }
    [Id(4)]
    public float MaxHumidity { get; set; }

    // Конструктор без параметров для System.Text.Json
    public HumidityModel()
        : base(Guid.Empty, string.Empty, null)
    {
        MinHumidity = 0;
        MaxHumidity = 100;
    }

    public HumidityModel(string name = "", string? displayName = null, float minHumidity = 0, float maxHumidity = 100)
        : this(GuidGenerator.New(), name, displayName, minHumidity, maxHumidity)
    {
    }

    public HumidityModel(Guid id, string name, string? displayName, float minHumidity = 0, float maxHumidity = 100)
        : base(id, name, displayName)
    {
        MinHumidity = minHumidity;
        MaxHumidity = maxHumidity;
    }
}
