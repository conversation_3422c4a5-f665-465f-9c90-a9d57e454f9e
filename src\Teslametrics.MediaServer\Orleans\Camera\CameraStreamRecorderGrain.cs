using Dapper;
using FFMpegNET;
using Microsoft.Extensions.Options;
using System.Data;
using System.Globalization;
using Teslametrics.Core.Services.Persistence;
using Teslametrics.MediaServer.Contracts;
using Teslametrics.Shared;
using static Teslametrics.MediaServer.Orleans.Camera.ICameraStreamRecorderGrain;

namespace Teslametrics.MediaServer.Orleans.Camera;

public class CameraStreamRecorderGrain : Grain, ICameraStreamRecorderGrain
{
    private MicroSegmentAccumulator _accumulator;
    private readonly ILogger<CameraStreamRecorderGrain> _logger;
    private readonly IServiceScopeFactory _serviceScopeFactory;
    private readonly IFileStorage _fileStorage;
    // private StreamSubscriptionHandle<MicroSegment>? _liveStreamSub;
    private bool _isStarted;
    private Task? _runTask;

    public CameraStreamRecorderGrain(ILogger<CameraStreamRecorderGrain> logger,
                                     ILogger<MicroSegmentAccumulator> accumulatorLogger,
                                     IServiceScopeFactory serviceScopeFactory,
                                     IFileStorage fileStorage)
    {
        _logger = logger;
        _serviceScopeFactory = serviceScopeFactory;
        _fileStorage = fileStorage;
        _accumulator = new MicroSegmentAccumulator(accumulatorLogger, Options.Create(MicroSegmentAccumulator.Options.Default));
    }

    public override Task OnActivateAsync(CancellationToken cancellationToken)
    {
        return base.OnActivateAsync(cancellationToken);
    }

    public override async Task OnDeactivateAsync(DeactivationReason reason, CancellationToken cancellationToken)
    {
        await StopAsync();
        _accumulator.Dispose();

        await base.OnDeactivateAsync(reason, cancellationToken);
    }

    public async Task StartAsync()
    {
        if (_isStarted)
        {
            return;
        }

        var cameraId = this.GetPrimaryKey();

        await CreateTableIfNotExistsAsync(cameraId);

        var retentionDays = await GetRetentionDaysAsync(cameraId);
        await _fileStorage.CreateDirectoryIfNotExistsAsync(cameraId.ToString("N"), retentionDays);

        // var streamProvider = this.GetStreamProvider(StreamNames.VideoLiveStream);

        // var streamId = StreamId.Create(StreamNamespaces.CameraStreams, cameraStreamId);
        // var stream = streamProvider.GetStream<MicroSegment>(streamId);

        // _liveStreamSub = await stream.SubscribeAsync((segment, token) =>
        // {
        //     try
        //     {
        //         var microSegment = new FFMpegNET.MicroSegment(segment.Payload, segment.StartTime, segment.Duration);
        //         _accumulator.AddMicroSegment(microSegment);
        //     }
        //     catch (Exception ex)
        //     {
        //         _logger.LogError(ex, "Error processing segment of archive camera stream. CameraId: {CameraId}", cameraId);
        //     }

        //     return Task.CompletedTask;
        // });

        _runTask = _accumulator.Run(async segment =>
        {
            var startTime = segment.StartTime;
            var endTime = startTime.AddSeconds(segment.Duration);

            var tags = new Dictionary<string, string>
            {
                { "StartTime", startTime.ToUnixTimeMilliseconds().ToString() },
                { "EndTime", endTime.ToUnixTimeMilliseconds().ToString() },
                { "Duration", segment.Duration.ToString(CultureInfo.InvariantCulture) }
            };

            var fileName = segment.StartTime.ToString("dd-MM-yyyy_HH-mm-ss-fff") + ".ts";

            try
            {
                segment.Stream.Position = 0;

                // Upload the file with the memory stream that has a known length
                await _fileStorage.UploadFileAsync(cameraId.ToString("N"), fileName, segment.Stream, tags);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading segment to storage: {FileName}", fileName);
            }
        }, CancellationToken.None);

        _isStarted = true;
    }

    public async Task StopAsync()
    {
        if (_isStarted)
        {
            _accumulator.RequestGracefulStop();

            await _runTask!;
            _runTask = null;

            // await _liveStreamSub!.UnsubscribeAsync();

            _isStarted = false;
        }
    }

    private async Task CreateTableIfNotExistsAsync(Guid cameraId)
    {
        var tableName = $"{Db.StreamSegments.Table}_{cameraId.ToString("N")}";

        using var scope = _serviceScopeFactory.CreateScope();
        using var dbConnection = scope.ServiceProvider.GetRequiredService<IDbConnection>();

        if (dbConnection.State != ConnectionState.Open)
        {
            dbConnection.Open();
        }

        var exists = await dbConnection.ExecuteScalarAsync<bool>("SELECT EXISTS (" +
                                                                    "SELECT FROM pg_tables " +
                                                                    "WHERE schemaname = 'public' " +
                                                                    "AND tablename = @tableName" +
                                                                ")", new { tableName });

        if (!exists)
        {
            using var transaction = dbConnection.BeginTransaction(IsolationLevel.Serializable);
            try
            {
                await dbConnection.ExecuteAsync($"create table {tableName} " +
                                                $"({Db.StreamSegments.Columns.SegmentIndex} BIGSERIAL, " +
                                                $"{Db.StreamSegments.Columns.FileName} text, " +
                                                $"{Db.StreamSegments.Columns.StartTime} timestamptz PRIMARY KEY, " +
                                                $"{Db.StreamSegments.Columns.EndTime} timestamptz)");

                // Создаем индексы до преобразования в гипертаблицу
                await dbConnection.ExecuteAsync($"CREATE INDEX idx_{tableName}_filename ON {tableName} ({Db.StreamSegments.Columns.FileName})");

                // Создаем составной индекс по времени
                // EndTime используется в условиях WHERE чаще, поэтому ставим его первым
                await dbConnection.ExecuteAsync($"CREATE INDEX idx_{tableName}_time ON {tableName} ({Db.StreamSegments.Columns.EndTime}, {Db.StreamSegments.Columns.StartTime})");

                // Преобразуем в гипертаблицу после создания индексов
                await dbConnection.ExecuteAsync($"SELECT create_hypertable('{tableName}', '{Db.StreamSegments.Columns.StartTime}')");

                transaction.Commit();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to create table {TableName} and hypertable", tableName);
                transaction.Rollback();
                throw;
            }
        }
    }

    private async Task<int> GetRetentionDaysAsync(Guid cameraId)
    {
        using var scope = _serviceScopeFactory.CreateScope();
        using var dbConnection = scope.ServiceProvider.GetRequiredService<IDbConnection>();

        if (dbConnection.State != ConnectionState.Open)
        {
            dbConnection.Open();
        }

        var retentionPeriodTemplate = SqlQueryBuilder.Create()
            .Select(Db.CameraQuotas.Columns.RetentionPeriodDays)
            .InnerJoin(Db.Cameras.Table, Db.Cameras.Props.QuotaId, Db.CameraQuotas.Props.Id, SqlOperator.Equals)
            .Where(Db.Cameras.Props.Id, ":Id", SqlOperator.Equals, new { Id = cameraId })
            .Build(QueryType.Standard, Db.CameraQuotas.Table, RowSelection.AllRows);

        return await dbConnection.ExecuteScalarAsync<int>(retentionPeriodTemplate.RawSql, retentionPeriodTemplate.Parameters);
    }

    public Task SendSegment(SendSegmentRequest request)
    {
        try
        {
            var segment = request.MicroSegment;

            var microSegment = new FFMpegNET.MicroSegment(segment.Payload, segment.StartTime, segment.Duration);
            _accumulator.AddMicroSegment(microSegment);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing segment of archive camera stream. CameraId: {CameraId}", this.GetPrimaryKey());
        }

        return Task.CompletedTask;
    }
}