let incidentsChart = null;
/**
 * Initialize the incidents bar chart
 * @param {Object} data - The chart data
 */
export function initIncidentsChart(data) {
    if (incidentsChart) {
        incidentsChart.destroy();
    }

    const options = {
        chart: {
            type: 'bar',
            height: 300,
            toolbar: {
                show: false
            }
        },
        plotOptions: {
            bar: {
                borderRadius: 4,
                columnWidth: '40%',
            }
        },
        dataLabels: {
            enabled: false
        },
        colors: ['#FF5722'],
        xaxis: {
            categories: data.categories,
            axisBorder: {
                show: false
            },
            axisTicks: {
                show: false
            }
        },
        yaxis: {
            min: 0,
            max: Math.max(...data.series[0].data) * 1.2,
            tickAmount: 4
        },
        grid: {
            xaxis: {
                lines: {
                    show: true
                }
            },
            yaxis: {
                lines: {
                    show: true
                }
            }
        },
        series: data.series
    };

    incidentsChart = new ApexCharts(document.querySelector("#incidents-chart"), options);
    incidentsChart.render();
}