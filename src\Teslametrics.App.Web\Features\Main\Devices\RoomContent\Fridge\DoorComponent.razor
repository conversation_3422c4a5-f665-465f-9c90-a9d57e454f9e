﻿@using Teslametrics.MediaServer.Orleans.Wirenboard

@inherits InteractiveBaseComponent
@implements ISensorObserver

<InfoCardComponent Icon="@Icons.Material.Rounded.DoorFront"
                   Title="@(_value ? "Открыта" : "Закрыта")"
                   Subtitle="Состояние двери"
                   Error="@_value" />

@code {
    private bool _value;

    [Parameter]
    [EditorRequired]
    public string? TopicName { get; set; } = null;

    protected override void OnInitialized()
    {
        base.OnInitialized();
    }

    public Task ReceiveData(ISensorData SensorData)
    {
        if (SensorData is SensorBoolData data)
        {
            _value = data.Value;
            StateHasChanged();
        }

        return Task.CompletedTask;
    }

    private Task SubscribeAsync()
    {
        if (string.IsNullOrEmpty(TopicName)) return Task.CompletedTask;

        return ScopeFactory.MediatorSend(new SubscribeFridgeSensorUseCase.Request(this, TopicName));
    }
}
