using Microsoft.AspNetCore.Components;
using Teslametrics.App.Web.Exceptions;
using Teslametrics.App.Web.Extensions;

namespace Teslametrics.App.Web.Features.Main.Incidents.SelectedEvent;

public partial class SelectedEventComponent
{
	private IEnumerable<GetIncidentUseCase.Response.DoorModel>? _door => _response?.Fridge?.Sensors.OfType<GetIncidentUseCase.Response.DoorModel>();
	private IEnumerable<GetIncidentUseCase.Response.LeakModel>? _leak => _response?.Fridge?.Sensors.OfType<GetIncidentUseCase.Response.LeakModel>();
	private IEnumerable<GetIncidentUseCase.Response.PowerModel>? _power => _response?.Fridge?.Sensors.OfType<GetIncidentUseCase.Response.PowerModel>();

	private readonly List<(GetIncidentUseCase.Response.TemperatureModel? Temperature, GetIncidentUseCase.Response.HumidityModel? Humidity)> _doubledCards = [];

	private GetIncidentUseCase.Response? _response;

	[Parameter]
	public Guid IncidentId { get; set; }

	protected override async Task OnParametersSetAsync()
	{
		await FetchDataAsync();
		await MarkIncidentAsReadedAsync();
		await base.OnParametersSetAsync();
	}

	private async Task FetchDataAsync()
	{
		if (IsLoading) return;
		try
		{
			await SetLoadingAsync(true);
			_response = await ScopeFactory.MediatorSend(new GetIncidentUseCase.Query(IncidentId));
		}
		catch (Exception ex)
		{
			_response = null;
			Logger.LogError(ex, "Failed to fetch incident data");
			Snackbar.Add($"Не удалось получить данные происшествия из-за ошибки сообщения с сервером. Повторите попытку позже.", MudBlazor.Severity.Error);
		}

		await SetLoadingAsync(false);
		if (_response is null) return;

		switch (_response.Result)
		{
			case GetIncidentUseCase.Result.Success:
				_doubledCards.Clear();
				if (_response.Fridge is null || _response.Fridge.Sensors is null) return;
				// Ищем Temperature и Humidity
				var temperatures = _response.Fridge.Sensors.OfType<GetIncidentUseCase.Response.TemperatureModel>().ToList();
				var humidities = _response.Fridge.Sensors.OfType<GetIncidentUseCase.Response.HumidityModel>().ToList();

				// Определяем максимальное количество карточек для пары Температура+Влажность
				int maxPairs = Math.Max(temperatures.Count, humidities.Count);

				for (int i = 0; i < maxPairs; i++)
				{
					var temp = i < temperatures.Count ? temperatures[i] : null;
					var hum = i < humidities.Count ? humidities[i] : null;

					_doubledCards.Add(new(temp, hum));
				}
				break;
			case GetIncidentUseCase.Result.Unknown:
				Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(SelectedEventComponent), nameof(GetIncidentUseCase));
				Snackbar.Add($"Не удалось получить данные происшествия из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
				break;
			default:
				Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(SelectedEventComponent), nameof(GetIncidentUseCase), _response.Result);
				Snackbar.Add($"Не удалось получить данные происшествия из-за ошибки: {_response.Result}", MudBlazor.Severity.Error);
				break;
		}
	}

	private async Task MarkIncidentAsReadedAsync()
	{
		MarkIncidentAsReadedUseCase.Response? response;
		try
		{
			var userId = await GetCurrentUserIdAsync() ?? throw new NotAuthorizedException();
			response = await ScopeFactory.MediatorSend(new MarkIncidentAsReadedUseCase.Command(IncidentId, userId));
		}
		catch (Exception ex)
		{
			response = null;
			Logger.LogError(ex, "Failed to mark incident as read");
			Snackbar.Add($"Не удалось получить данные происшествия из-за ошибки сообщения с сервером. Повторите попытку позже.", MudBlazor.Severity.Error);
		}

		if (response is null) return;

		switch (response.Result)
		{
			case MarkIncidentAsReadedUseCase.Result.Success:
				break;
			case MarkIncidentAsReadedUseCase.Result.Unknown:
				Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(SelectedEventComponent), nameof(MarkIncidentAsReadedUseCase));
				Snackbar.Add($"Не удалось получить данные происшествия из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
				break;
			default:
				Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(SelectedEventComponent), nameof(MarkIncidentAsReadedUseCase), response.Result);
				Snackbar.Add($"Не удалось получить данные происшествия из-за ошибки: {response.Result}", MudBlazor.Severity.Error);
				break;
		}
	}
}
