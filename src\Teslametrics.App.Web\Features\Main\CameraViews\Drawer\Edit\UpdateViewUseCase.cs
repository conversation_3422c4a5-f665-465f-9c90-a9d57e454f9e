using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.Core.Domain.CameraViews;
using Teslametrics.Core.Services.Outbox;
using Teslametrics.Core.Services.TransactionManager;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Features.Main.CameraViews.Drawer.Edit;

public static class UpdateViewUseCase
{
    public record Command(Guid Id, string Name, short ColumnCount, short RowCount, GridType GridType, List<(Guid CameraId, short CellIndex)> Cells) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public Guid Id { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(Guid id)
        {
            Id = id;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Id = Guid.Empty;
            Result = result;
        }
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        CameraViewNotFound,
        CameraViewNameAlreadyExists
    }

    public class Validator : AbstractValidator<Command>
    {
        public Validator()
        {
            RuleFor(c => c.Id).NotEmpty();
            RuleFor(c => c.Name).Length(3, 60);
            RuleFor(c => c.ColumnCount).GreaterThan((short)0);
            RuleFor(c => c.RowCount).GreaterThan((short)0);
            RuleFor(c => c.Cells).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Command, Response>
    {
        private readonly IValidator<Command> _validator;
        private readonly ICameraViewRepository _cameraViewRepository;
        private readonly ITransactionManager _transactionManager;
        private readonly IPublisher _publisher;
        private readonly IOutbox _outbox;


        public Handler(IValidator<Command> validator,
                       ICameraViewRepository cameraViewRepository,
                       ITransactionManager transactionManager,
                       IPublisher publisher,
                       IOutbox outbox)
        {
            _validator = validator;
            _cameraViewRepository = cameraViewRepository;
            _transactionManager = transactionManager;
            _publisher = publisher;
            _outbox = outbox;
        }

        public async Task<Response> Handle(Command request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            using var transaction = await _transactionManager.CreateTransactionAsync();

            var cameraView = await _cameraViewRepository.FindAsync(request.Id, cancellationToken);
            if (cameraView is null)
            {
                return new Response(Result.CameraViewNotFound);
            }

            if (cameraView.Name != request.Name)
            {
                if (await _cameraViewRepository.IsNameExistsAsync(request.Name, cameraView.OrganizationId, cancellationToken))
                {
                    return new Response(Result.CameraViewNameAlreadyExists);
                }
            }

            var events = cameraView.Update(request.Name, request.ColumnCount, request.RowCount, request.GridType, request.Cells);

            await _cameraViewRepository.SaveChangesAsync(cancellationToken);

            foreach (var @event in events)
            {
                await _publisher.Publish(@event, cancellationToken);
            }

            await _outbox.AddRangeAsync(events);

            await transaction.CommitAsync();

            return new Response(cameraView.Id);
        }
    }
}