﻿@inherits InteractiveBaseComponent
<DrawerHeader>
	<MudStack Spacing="0">
		<MudText Typo="Typo.h1">Просмотр квоты</MudText>
		@if (IsLoading)
		{
			<MudSkeleton Width="60%"
						 Height="calc(var(--mud-typography-subtitle1-lineheight) * var(--mud-typography-subtitle1-size))" />
		}
		@if (!IsLoading && _response is not null && _response.IsSuccess)
		{
			<MudText Typo="Typo.subtitle1">@_response.Name</MudText>
		}
		@if (!IsLoading && (_response is null || !_response.IsSuccess))
		{
			<MudText Typo="Typo.subtitle1">Не удалось получить настройки квоты</MudText>
		}
	</MudStack>
	<MudSpacer />
	@if (!_subscribing && (_subscriptionResponse is null || !_subscriptionResponse.IsSuccess))
	{
		<MudTooltip Arrow="true"
					Placement="Placement.Start"
					Text="Ошибка подписки на события">
			<MudIconButton OnClick="SubscribeAsync"
						   Icon="@Icons.Material.Filled.ErrorOutline"
						   Color="Color.Error" />
		</MudTooltip>
	}
	<TimePassedComponent InputTime="_lastRefreshTime" />
	@if (!IsLoading && _response is not null && _response.IsSuccess)
	{
		<AuthorizeView Policy="@_contextMenuAuthPolicyString"
					   Resource="new PolicyRequirementResource(OrganizationId, QuotaId)"
					   Context="menuContext">
			<MudMenu Icon="@Icons.Material.Filled.MoreVert"
					 AriaLabel="Действия с выбранной квотой"
					 Color="Color.Primary"
					 Variant="Variant.Outlined">
				<AuthorizeView Policy="@(Fqdn<AppPermissions>.GetNames(AppPermissions.Main.CameraQuotas.Update).Last())"
							   Resource="new PolicyRequirementResource(OrganizationId, QuotaId)"
							   Context="innerContext">
					<MudMenuItem OnClick="Edit"
								 Icon="@Icons.Material.Outlined.PanoramaFishEye">Редактировать</MudMenuItem>
				</AuthorizeView>
				<AuthorizeView Policy="@(Fqdn<AppPermissions>.GetNames(AppPermissions.Main.CameraQuotas.Delete).Last())"
							   Resource="new PolicyRequirementResource(OrganizationId, QuotaId)"
							   Context="innerContext">
					<MudDivider Class="my-4" />
					<MudMenuItem OnClick="Delete"
								 Icon="@Icons.Material.Outlined.Delete"
								 IconColor="Color.Warning">Удалить</MudMenuItem>
				</AuthorizeView>
			</MudMenu>
		</AuthorizeView>
	}
</DrawerHeader>
@if (IsLoading)
{
	<MudProgressLinear Color="Color.Primary"
					   Indeterminate="true" />
}
else
{
	<div style="height: 4px;" />
}

<MudStack Spacing="8"
		  Class="px-4 pt-4">
	@if (_response is not null && _response.IsSuccess && _response is not null)
	{
		<MudTextField Value="_response.Name"
					  Variant="Variant.Text"
					  Label="Наименование"
					  ReadOnly="true" />

		<MudTextField Value="_response.PresetName"
					  Variant="Variant.Text"
					  Label="Пресет"
					  ReadOnly="true" />

		<MudCheckBox T="bool"
					 Value="_response.TotalQuota == -1"
					 Label="Количество камер неограничено"
					 LabelPosition="LabelPosition.End"
					 Class="ml-n4"
					 ReadOnly="true" />
		@if (_response.TotalQuota != -1)
		{
			<MudNumericField T="int"
							 Value="_response.TotalQuota"
							 ReadOnly="true"
							 Label="Количество камер" />
		}

		<MudNumericField T="int"
						 Value="_response.StorageLimitMb"
						 Label="Расчётный объём хранимых записей, мб"
						 ReadOnly="true" />

		<MudNumericField T="int"
						 Value="_response.RetentionPeriodDays"
						 Label="Глубина хранения записей, дней"
						 Variant="Variant.Text"
						 ReadOnly="true" />
	}
	<FormLoadingComponent IsLoading="IsLoading && _response is null" />
	<NoItemsFoundComponent HasItems="!IsLoading && _response is not null && _response.IsSuccess" />
</MudStack>

<DrawerActions>
	<MudSpacer />
	<MudButton OnClick="CancelAsync">Закрыть</MudButton>
	<AuthorizeView Policy="@AppPermissions.Main.CameraQuotas.Update.GetEnumPermissionString()"
				   Context="innerContext"
				   Resource="new PolicyRequirementResource(OrganizationId, QuotaId)">
		<MudButton OnClick="Edit"
				   Color="Color.Secondary"
				   Variant="Variant.Outlined">Редактировать</MudButton>
	</AuthorizeView>
</DrawerActions>