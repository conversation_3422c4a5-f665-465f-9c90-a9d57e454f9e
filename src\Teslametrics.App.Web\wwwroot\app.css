/*@import url('open-iconic/font/css/open-iconic-bootstrap.min.css');*/

@import './scrollbar.css';
@import './blazor-error.css';
@import './MudBlazorOverrides.css';
@import './multimonitor.css';

html,
body {
	font-family: 'Inter', sans-serif;
}

body {
	height: 100vh;
	margin: 0;
	padding: 0;
}

h1:focus {
	outline: none;
}

/* Global Styles */
.height_fill_available {
	height: 100vh;
	/* Fallback for other browsers */
	height: -webkit-fill-available;
	/* For WebKit-based browsers */
}

.d_contents {
	display: contents;
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
textarea:-webkit-autofill,
textarea:-webkit-autofill:hover,
textarea:-webkit-autofill:focus,
select:-webkit-autofill,
select:-webkit-autofill:hover,
select:-webkit-autofill:focus {
	border: 1px solid transparent !important;
	-webkit-box-shadow: 0 0 0px 1000px transparent inset !important;
	transition: background-color 5000s ease-in-out 0s !important;
}

input:-webkit-autofill {
	-webkit-box-shadow: 0 0 0 50px var(--mud-palette-surface) inset;
	/* Change the color to your own background color */
	-webkit-text-fill-color: var(--mud-palette-text-primary);
}

input:-webkit-autofill:focus {
	-webkit-box-shadow: 0 0 0 50px var(--mud-palette-surface) inset, 0 0 0 50px transparent inset;
	-webkit-text-fill-color: var(--mud-palette-text-primary);
}

input:-webkit-autofill,
input:-webkit-autofill:focus {
	transition: background-color 0s 600000s, color 0s 600000s !important;
}

input:-internal-autofill-selected {
	appearance: menulist-button;
	background-image: none !important;
	color: fieldtext !important;
}

/* .mud-input-input-control:not(:has(.mud-input-control-helper-container))::after {
	content: " ";
	display: block;
	height: 20px;
} */

.mud-input-input-control .mud-input-control-helper-container {
	min-height: 20px;
}

.mud-input-control .mud-typography-input {
	min-height: 34px;
}

.quota_select_item_tooltip {
	z-index: calc(var(--mud-zindex-drawer) + var(--mud-zindex-tooltip) + 1) !important;
}

/* Общая ширина скроллбара */
::-webkit-scrollbar {
	width: 8px;
}

/* Фон трека (дорожки) */
::-webkit-scrollbar-track {
	background: transparent;
	border-radius: 4px;
}

/* Ползунок */
::-webkit-scrollbar-thumb {
	background-color: rgba(0, 0, 0, 0.1);
	/* светлый, почти прозрачный */
	border-radius: 4px;
}

/* При наведении */
::-webkit-scrollbar-thumb:hover {
	background-color: rgba(0, 0, 0, 0.2);
}