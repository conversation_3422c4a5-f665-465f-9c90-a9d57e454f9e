using Dapper;
using FluentValidation;
using MediatR;
using System.Data;
using Teslametrics.Core.Services.Persistence;
using Teslametrics.Shared;
using Teslametrics.App.Web.Abstractions;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Organizations.Drawer.Edit;

public static class GetOrganizationUseCase
{
    public record Query(Guid Id) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public Guid Id { get; init; } // ID органзиации
        public Guid OwnerId { get; init; }
        public string Name { get; init; } // Наименование организации
        public string Owner { get; init; } // Владелец организации

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(Guid id, Guid ownerId, string name, string owner)
        {
            Id = id;
            OwnerId = ownerId;
            Name = name;
            Owner = owner;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Result = result;

            Id = Guid.Empty;
            OwnerId = Guid.Empty;
            Name = string.Empty;
            Owner = string.Empty;
        }
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        OrganizationNotFound
    }

    public class Validator : AbstractValidator<Query>
    {
        public Validator()
        {
            RuleFor(q => q.Id).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Query, Response>
    {
        private readonly IValidator<Query> _validator;
        private readonly IDbConnection _dbConnection;

        public Handler(IValidator<Query> validator,
                       IDbConnection dbConnection)
        {
            _validator = validator;
            _dbConnection = dbConnection;
        }

        public async Task<Response> Handle(Query request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            var template = SqlQueryBuilder.Create()
                .Select(Db.Organizations.Props.Id)
                .Select(Db.Organizations.Props.OwnerId)
                .Select(Db.Organizations.Props.Name)
                .Select(Db.Users.Props.Name, "Owner")
                .InnerJoin(Db.Users.Table, Db.Users.Props.Id, Db.Organizations.Props.OwnerId, SqlOperator.Equals)
                .Where(Db.Organizations.Props.Id, ":Id", SqlOperator.Equals, new { request.Id })
                .Build(QueryType.Standard, Db.Organizations.Table, RowSelection.AllRows);

            var organization = await _dbConnection.QuerySingleOrDefaultAsync<OrganizationModel>(template.RawSql, template.Parameters);
            if (organization is null)
            {
                return new Response(Result.OrganizationNotFound);
            }

            return new Response(organization.Id,
                                organization.OwnerId,
                                organization.Name,
                                organization.Owner);
        }
    }

    public record OrganizationModel(Guid Id, Guid OwnerId, string Name, string Owner);
}