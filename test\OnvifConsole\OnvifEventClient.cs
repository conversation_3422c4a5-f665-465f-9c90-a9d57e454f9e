using OnvifProxy;
using System.ServiceModel;
using System.ServiceModel.Channels;
using System.Net;
using System.Xml;
using System.Reactive.Subjects;
using System.Reactive.Linq;
/// <summary>
/// Класс-обертка для работы с событиями ONVIF
/// </summary>
public class OnvifEventClient : IDisposable
{
    private readonly string _deviceUrl;
    private readonly string _username;
    private readonly string _password;
    private readonly CustomBinding _binding;
    private EventPortTypeClient? _eventClient;
    private PullPointSubscriptionClient? _pullPointClient;
    private SubscriptionManagerClient? _subscriptionManagerClient;
    private string? _subscriptionUrl;
    private CancellationTokenSource? _pollingCts;
    private Task? _pollingTask;
    private bool _isPolling;

    // Subject для реализации IObservable
    private Subject<OnvifEventArgs> _motionEventsSubject = new Subject<OnvifEventArgs>();

    /// <summary>
    /// Observable для событий движения
    /// </summary>
    public IObservable<OnvifEventArgs> MotionEvents => _motionEventsSubject.AsObservable();

    /// <summary>
    /// Создает новый Subject и обновляет подписчиков
    /// </summary>
    private void RecreateSubject()
    {
        // Создаем новый Subject для продолжения работы
        var newSubject = new Subject<OnvifEventArgs>();

        // Атомарно заменяем старый Subject на новый
        Interlocked.Exchange(ref _motionEventsSubject, newSubject);
    }

    /// <summary>
    /// Конструктор клиента событий ONVIF
    /// </summary>
    /// <param name="host">Хост или IP-адрес устройства</param>
    /// <param name="port">Порт устройства</param>
    /// <param name="username">Имя пользователя для аутентификации</param>
    /// <param name="password">Пароль для аутентификации</param>
    /// <param name="useSsl">Использовать SSL (HTTPS)</param>
    public OnvifEventClient(string host, int port, string username, string password, bool useSsl = false)
    {
        string protocol = useSsl ? "https" : "http";
        _deviceUrl = $"{protocol}://{host}:{port}/onvif/events";
        _username = username;
        _password = password;

        // Создание привязки для SOAP 1.2
        _binding = new CustomBinding();
        var textBindingElement = new TextMessageEncodingBindingElement(MessageVersion.Soap12WSAddressing10, System.Text.Encoding.UTF8);
        var httpBindingElement = new HttpTransportBindingElement
        {
            AuthenticationScheme = AuthenticationSchemes.Digest,
            MaxReceivedMessageSize = 1000000
        };

        _binding.Elements.Add(textBindingElement);
        _binding.Elements.Add(httpBindingElement);

        // Увеличиваем таймауты
        _binding.SendTimeout = TimeSpan.FromMinutes(10);
        _binding.ReceiveTimeout = TimeSpan.FromMinutes(10);
        _binding.OpenTimeout = TimeSpan.FromMinutes(1);
        _binding.CloseTimeout = TimeSpan.FromMinutes(1);

        // Инициализация клиента
        InitializeEventClient();
    }

    /// <summary>
    /// Инициализация клиента событий
    /// </summary>
    private void InitializeEventClient()
    {
        var endpoint = new EndpointAddress(_deviceUrl);
        _eventClient = new EventPortTypeClient(_binding, endpoint);

        // Установка учетных данных для аутентификации
        var factory = _eventClient.ChannelFactory as ChannelFactory;
        if (factory != null)
        {
            factory.Credentials.HttpDigest.ClientCredential = new NetworkCredential(_username, _password);
        }
    }

    /// <summary>
    /// Получение возможностей ONVIF-устройства
    /// </summary>
    /// <returns>Объект с возможностями устройства</returns>
    public async Task<Capabilities> GetCapabilitiesAsync()
    {
        if (_eventClient == null)
        {
            throw new InvalidOperationException("Event client is not initialized");
        }
        return await _eventClient.GetServiceCapabilitiesAsync();
    }

    /// <summary>
    /// Создание подписки на события движения
    /// </summary>
    /// <returns>URL подписки</returns>
    public async Task<string> CreateSubscriptionAsync()
    {
        if (_eventClient == null)
        {
            throw new InvalidOperationException("Event client is not initialized");
        }

        var createPullPointSubscriptionRequest = new CreatePullPointSubscriptionRequest();

        // Создаем XML-документ для фильтра
        XmlDocument doc = new XmlDocument();

        // Создаем корневой элемент TopicExpression
        XmlElement topicExpressionElement = doc.CreateElement("wsnt", "TopicExpression", "http://docs.oasis-open.org/wsn/b-2");

        // Устанавливаем атрибуты для TopicExpression
        topicExpressionElement.SetAttribute("Dialect", "http://www.onvif.org/ver10/tev/topicExpression/ConcreteSet");

        // Устанавливаем значение для фильтрации только событий движения
        // topicExpressionElement.InnerText = "tns1:VideoSource/MotionAlarm tns1:RuleEngine/CellMotionDetector/Motion";
        topicExpressionElement.InnerText = "tns1:VideoSource/MotionAlarm";

        // Создаем фильтр и добавляем в него TopicExpression
        createPullPointSubscriptionRequest.Filter = new FilterType
        {
            Any = new XmlElement[] { topicExpressionElement }
        };

        var response = await _eventClient.CreatePullPointSubscriptionAsync(createPullPointSubscriptionRequest);

        // Получаем оригинальную ссылку на подписку
        string subscriptionUrl = response.SubscriptionReference.Address.Value;

        // Исправляем ссылку на подписку, заменяя внутренний IP на внешний
        Uri originalUri = new Uri(subscriptionUrl);
        string path = originalUri.PathAndQuery;

        // Извлекаем хост из оригинального URL устройства
        Uri deviceUri = new Uri(_deviceUrl);
        string externalHost = $"{deviceUri.Host}:{deviceUri.Port}";

        // Создаем исправленную ссылку
        _subscriptionUrl = $"http://{externalHost}{path}";

        // Инициализация клиентов для работы с подпиской
        InitializePullPointClient();
        InitializeSubscriptionManagerClient();

        return _subscriptionUrl;
    }

    /// <summary>
    /// Инициализация клиента для получения событий
    /// </summary>
    private void InitializePullPointClient()
    {
        if (_subscriptionUrl == null)
        {
            throw new InvalidOperationException("Subscription URL is not initialized");
        }

        _pullPointClient = new PullPointSubscriptionClient(_binding, new EndpointAddress(_subscriptionUrl));
        var pullPointFactory = _pullPointClient.ChannelFactory as ChannelFactory;
        if (pullPointFactory != null)
        {
            pullPointFactory.Credentials.HttpDigest.ClientCredential = new NetworkCredential(_username, _password);
        }
    }

    /// <summary>
    /// Инициализация клиента для управления подпиской
    /// </summary>
    private void InitializeSubscriptionManagerClient()
    {
        if (_subscriptionUrl == null)
        {
            throw new InvalidOperationException("Subscription URL is not initialized");
        }

        _subscriptionManagerClient = new SubscriptionManagerClient(_binding, new EndpointAddress(_subscriptionUrl));
        var subscriptionManagerFactory = _subscriptionManagerClient.ChannelFactory as ChannelFactory;
        if (subscriptionManagerFactory != null)
        {
            subscriptionManagerFactory.Credentials.HttpDigest.ClientCredential = new NetworkCredential(_username, _password);
        }
    }

    /// <summary>
    /// Получение сообщений из очереди событий
    /// </summary>
    /// <param name="timeout">Таймаут в секундах</param>
    /// <param name="messageLimit">Максимальное количество сообщений</param>
    /// <returns>Список сообщений</returns>
    public async Task<NotificationMessageHolderType[]> PullMessagesAsync(int timeout = 5, int messageLimit = 10)
    {
        if (_pullPointClient == null)
        {
            throw new InvalidOperationException("PullPoint client is not initialized");
        }

        var pullMessagesRequest = new PullMessagesRequest
        {
            Timeout = $"PT{timeout}S", // Формат таймаута в соответствии с ISO 8601
            MessageLimit = messageLimit
        };

        var response = await _pullPointClient.PullMessagesAsync(pullMessagesRequest);
        return response.NotificationMessage;
    }

    /// <summary>
    /// Отмена подписки на события
    /// </summary>
    public async Task UnsubscribeAsync()
    {
        // Сначала останавливаем получение событий, если оно запущено
        await StopEventPollingAsync();

        if (_subscriptionManagerClient == null)
        {
            throw new InvalidOperationException("Subscription manager client is not initialized");
        }

        var unsubscribe = new Unsubscribe();
        await _subscriptionManagerClient.UnsubscribeAsync(unsubscribe);
    }

    /// <summary>
    /// Запуск получения событий
    /// </summary>
    /// <param name="pollingInterval">Интервал опроса в миллисекундах</param>
    /// <returns>Задача, представляющая асинхронную операцию</returns>
    public async Task StartEventPollingAsync(int pollingInterval = 1000)
    {
        if (_pullPointClient == null)
        {
            throw new InvalidOperationException("PullPoint client is not initialized");
        }

        _isPolling = true;
        _pollingCts = new CancellationTokenSource();

        // Запускаем задачу опроса в отдельном потоке
        _pollingTask = Task.Run(async () =>
        {
            try
            {
                while (!_pollingCts.Token.IsCancellationRequested)
                {
                    try
                    {
                        var messages = await PullMessagesAsync();
                        if (messages != null && messages.Length > 0)
                        {
                            // Вызываем событие для каждого полученного сообщения
                            foreach (var message in messages)
                            {
                                try
                                {
                                    // Получаем время события с устройства
                                    var deviceTime = DateTimeOffset.Now;
                                    var isMotion = false;

                                    if (message.Message != null)
                                    {
                                        // Получение времени события с устройства
                                        var utcTimeAttr = message.Message.GetAttribute("UtcTime", "");
                                        if (!string.IsNullOrEmpty(utcTimeAttr) && DateTimeOffset.TryParse(utcTimeAttr, out var parsedTime))
                                        {
                                            deviceTime = parsedTime;
                                        }

                                        // Определяем тему события
                                        string? topic = null;
                                        if (message.Topic?.Any != null && message.Topic.Any.Length > 0)
                                        {
                                            topic = message.Topic.Any[0]?.InnerText;
                                        }

                                        // Извлечение состояния движения
                                        if (topic == "tns1:VideoSource/MotionAlarm")
                                        {
                                            // Поиск элемента SimpleItem с именем State
                                            var dataNodes = message.Message.GetElementsByTagName("tt:Data");
                                            if (dataNodes.Count > 0 && dataNodes[0] is XmlElement dataElement)
                                            {
                                                var simpleItems = dataElement.GetElementsByTagName("tt:SimpleItem");
                                                foreach (var itemNode in simpleItems)
                                                {
                                                    if (itemNode is XmlElement item &&
                                                        item.GetAttribute("Name") == "State")
                                                    {
                                                        isMotion = item.GetAttribute("Value").ToLower() == "true";
                                                        break;
                                                    }
                                                }
                                            }
                                        }
                                        else if (topic == "tns1:RuleEngine/CellMotionDetector/Motion")
                                        {
                                            // Поиск элемента SimpleItem с именем IsMotion
                                            var dataNodes = message.Message.GetElementsByTagName("tt:Data");
                                            if (dataNodes.Count > 0 && dataNodes[0] is XmlElement dataElement)
                                            {
                                                var simpleItems = dataElement.GetElementsByTagName("tt:SimpleItem");
                                                foreach (var itemNode in simpleItems)
                                                {
                                                    if (itemNode is XmlElement item &&
                                                        item.GetAttribute("Name") == "IsMotion")
                                                    {
                                                        isMotion = item.GetAttribute("Value").ToLower() == "true";
                                                        break;
                                                    }
                                                }
                                            }
                                        }
                                    }

                                    // Создаем объект аргументов события
                                    var eventArgs = new OnvifEventArgs(deviceTime, isMotion);

                                    // Отправляем событие через Subject
                                    _motionEventsSubject.OnNext(eventArgs);
                                }
                                catch (Exception ex)
                                {
                                    // Отправляем ошибку через OnError
                                    _motionEventsSubject.OnError(ex);

                                    // Создаем новый Subject для продолжения работы
                                    RecreateSubject();

                                    // Прерываем текущую итерацию
                                    break;
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        // Отправляем ошибку через OnError
                        _motionEventsSubject.OnError(ex);

                        // Создаем новый Subject для продолжения работы
                        RecreateSubject();
                    }

                    // Ждем указанное время перед следующим запросом
                    await Task.Delay(pollingInterval, _pollingCts.Token);
                }
            }
            catch (OperationCanceledException)
            {
                // Нормальное завершение при отмене задачи
            }
            catch (Exception ex)
            {
                // Отправляем ошибку через OnError
                _motionEventsSubject.OnError(ex);

                // Создаем новый Subject для продолжения работы
                RecreateSubject();
            }
            finally
            {
                _isPolling = false;
            }
        });

        await Task.CompletedTask;
    }

    /// <summary>
    /// Остановка получения событий
    /// </summary>
    /// <returns>Задача, представляющая асинхронную операцию</returns>
    public async Task StopEventPollingAsync()
    {
        if (!_isPolling || _pollingCts == null || _pollingTask == null)
        {
            return; // Не запущено
        }

        try
        {
            // Отменяем токен и ждем завершения задачи
            _pollingCts.Cancel();
            await _pollingTask;
        }
        catch (Exception)
        {
            // Игнорируем ошибки при остановке
        }
        finally
        {
            _pollingCts.Dispose();
            _pollingCts = null;
            _pollingTask = null;
            _isPolling = false;
        }
    }

    /// <summary>
    /// Освобождает ресурсы
    /// </summary>
    public void Dispose()
    {
        _pollingCts?.Cancel();
        _pollingCts?.Dispose();
        _pollingCts = null;
        _pollingTask = null;
        _isPolling = false;

        // Завершаем работу Subject'а
        _motionEventsSubject.OnCompleted();
        _motionEventsSubject.Dispose();
    }
}
