using System.Data;
using Dapper;
using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Data.Sql;
using Teslametrics.App.Web.Services.Persistence;
using Teslametrics.App.Web.Shared;

namespace Teslametrics.App.Web.Features.Main.Incidents.IncidentsList;

public static class GetIncidentListUseCase
{
    public record Query(Guid UserId, DateTimeOffset DateFrom, DateTimeOffset DateTo, Guid? CityId, Guid? BuildingId, Guid? FloorId, Guid? RoomId, Guid? FridgeId, IncidentType? IncidentType, bool? IsResolved) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public List<Incident> Incidents { get; init; }
        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(List<Incident> incidents)
        {
            Incidents = incidents;

            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Incidents = [];
            Result = result;
        }

        public record Incident
        {
            public Guid Id { get; init; }
            public string City { get; init; }
            public string Address { get; init; }
            public int Floor { get; init; }
            public string Room { get; init; }
            public string Device { get; init; }
            public DateTimeOffset Date { get; init; }
            public string Time => Date.ToString("HH:mm");
            public IncidentType IncidentType { get; init; }
            public bool IsResolved { get; init; }
            public bool IsViewed { get; set; }
            public Incident(Guid id,
                            string city,
                            string address,
                            int floor,
                            string room,
                            string device,
                            DateTimeOffset date,
                            IncidentType incidentType,
                            bool isResolved,
                            bool isViewed)
            {
                Id = id;
                City = city;
                Address = address;
                Floor = floor;
                Room = room;
                Device = device;
                Date = date;
                IncidentType = incidentType;
                IsResolved = isResolved;
                IsViewed = isViewed;
            }
        }
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError
    }

    public class Validator : AbstractValidator<Query>
    {
        public Validator()
        {
            RuleFor(q => q.UserId).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Query, Response>
    {
        private readonly IValidator<Query> _validator;
        private readonly IDbConnection _dbConnection;

        public Handler(IValidator<Query> validator,
                       IDbConnection dbConnection)
        {
            _validator = validator;
            _dbConnection = dbConnection;
        }

        public async Task<Response> Handle(Query request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            var template = SqlQueryBuilder.Create()
                .Select(Db.Incidents.Props.Id)
                .Select(Db.Incidents.Props.City)
                .Select(Db.Incidents.Props.Building)
                .Select(Db.Incidents.Props.Floor)
                .Select(Db.Incidents.Props.Room)
                .Select(Db.Incidents.Props.Device)
                .Select(Db.Incidents.Props.CreatedAt)
                .Select(Db.Incidents.Props.IncidentType)
                .Select(Db.Incidents.Props.ResolvedAt)
                .Select("CASE WHEN " + Db.IncidentNotifications.Props.Id + " IS NOT NULL THEN false ELSE true END AS IsViewed")
                .LeftJoin($"{Db.IncidentNotifications.Table} ON {Db.IncidentNotifications.Props.IncidentId} = {Db.Incidents.Props.Id} AND {Db.IncidentNotifications.Props.UserId} = :UserId",
                          new { request.UserId })
                .Where(Db.Incidents.Props.CreatedAt, ":DateFrom", SqlOperator.GreaterThanOrEqual, new { request.DateFrom })
                .Where(Db.Incidents.Props.CreatedAt, ":DateTo", SqlOperator.LessThanOrEqual, new { request.DateTo })
                .WhereIf(request.CityId is not null, Db.Incidents.Props.CityId, ":CityId", SqlOperator.Equals, new { request.CityId })
                .WhereIf(request.BuildingId is not null, Db.Incidents.Props.BuildingId, ":BuildingId", SqlOperator.Equals, new { request.BuildingId })
                .WhereIf(request.FloorId is not null, Db.Incidents.Props.FloorId, ":FloorId", SqlOperator.Equals, new { request.FloorId })
                .WhereIf(request.RoomId is not null, Db.Incidents.Props.RoomId, ":RoomId", SqlOperator.Equals, new { request.RoomId })
                .WhereIf(request.FridgeId is not null, Db.Incidents.Props.DeviceId, ":FridgeId", SqlOperator.Equals, new { request.FridgeId })
                .WhereIf(request.IncidentType is not null, Db.Incidents.Props.IncidentType, ":IncidentType", SqlOperator.Equals, new { IncidentType = request.IncidentType.ToString() })
                .WhereIf(request.IsResolved is not null && request.IsResolved.Value, Db.Incidents.Props.ResolvedAt, SqlOperator.IsNotNull)
                .WhereIf(request.IsResolved is not null && !request.IsResolved.Value, Db.Incidents.Props.ResolvedAt, SqlOperator.IsNull)
                .Build(QueryType.Standard, Db.Incidents.Table, RowSelection.AllRows);

            var incidentModels = await _dbConnection.QueryAsync<IncidentModel>(template.RawSql, template.Parameters);

            var incidents = incidentModels.OrderByDescending(i => i.CreatedAt).Select(i => new Response.Incident(
                i.Id,
                i.City,
                i.Building,
                i.Floor,
                i.Room,
                i.Device,
                i.CreatedAt.ToLocalTime(),
                i.IncidentType,
                i.ResolvedAt.HasValue,
                i.IsViewed
            )).ToList();

            return new Response(incidents);
        }
    }

    public record IncidentModel(Guid Id, string City, string Building, int Floor, string Room, string Device, DateTimeOffset CreatedAt, IncidentType IncidentType, DateTimeOffset? ResolvedAt, bool IsViewed);
}