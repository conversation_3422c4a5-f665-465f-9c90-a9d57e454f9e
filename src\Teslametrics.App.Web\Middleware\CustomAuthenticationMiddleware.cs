using Microsoft.AspNetCore.Components.Authorization;
using Teslametrics.App.Web.Services.Authentication;
using Teslametrics.App.Web.Services.Cookies;
using Teslametrics.App.Web.Services.UserSession;

namespace Teslametrics.App.Web.Middleware;

public class CustomAuthenticationMiddleware(IHostEnvironmentAuthenticationStateProvider stateProvider,
											ISessionProvider sessionProvider,
											CookieStorageAccessor cookieStorageAccessor) : IMiddleware
{
	private readonly IHostEnvironmentAuthenticationStateProvider _stateProvider = stateProvider;
	private readonly ISessionProvider _sessionProvider = sessionProvider;
	private readonly CookieStorageAccessor _cookieStorageAccessor = cookieStorageAccessor;

	public async Task InvokeAsync(HttpContext context, RequestDelegate next)
	{
		if (!HttpMethods.IsGet(context.Request.Method))
		{
			await next.Invoke(context);
			return;
		}

		Guid? sessionId = await _cookieStorageAccessor.GetValueAsync<Guid?>(AuthenticationStorageNames.SessionId);
		if (sessionId is null)
		{
			await next.Invoke(context);
			return;
		}

		Session? session = _sessionProvider.GetSessionBySessionId(sessionId.Value);
		if (session is null)
		{
			await _cookieStorageAccessor.RemoveAsync(AuthenticationStorageNames.SessionId);

			await next.Invoke(context);
			return;
		}

		var newState = new AuthenticationState(session.ClaimsPrincipal);
		_stateProvider.SetAuthenticationState(Task.FromResult(newState));

		await next.Invoke(context);
	}
}