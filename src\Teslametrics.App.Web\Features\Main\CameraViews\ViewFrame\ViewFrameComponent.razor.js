// Объявляем переменные в модульной области видимости
let resizeObserver = null;
let intersectionObserver = null;
let mutationObserver = null;
let isObserverInitialized = false;
let resizeInProgress = false;

/**
 * Инициализирует ResizeObserver для отслеживания изменений размера grid_container
 * @param {HTMLElement} gridContainer - Элемент для отслеживания размеров
 */
function initResizeObserver(gridContainer) {
	if (!gridContainer || resizeObserver) return;

	resizeObserver = new ResizeObserver(entries => {
		if (resizeInProgress) return;

		for (const entry of entries) {
			resizeInProgress = true;
			const { width, height } = entry.contentRect;

			gridContainer.style.setProperty('--width', `${width}px`);
			gridContainer.style.setProperty('--height', `${height}px`);
			resizeInProgress = false;
		}
	});

	resizeObserver.observe(gridContainer);
}

/**
 * Инициализирует IntersectionObserver для отслеживания видимости grid_container
 * @param {HTMLElement} gridContainer - Элемент для отслеживания видимости
 */
function initIntersectionObserver(gridContainer) {
	if (!gridContainer || intersectionObserver) return;

	intersectionObserver = new IntersectionObserver(entries => {
		const isElementVisible = entries[0].isIntersecting;
		if (!isElementVisible && intersectionObserver) {
			intersectionObserver.disconnect();
			console.log('Элемент пропал со страницы. Остановка отслеживания размера.');
		}
	}, { threshold: 0.1 });

	intersectionObserver.observe(gridContainer);
}

/**
 * Очищает все наблюдатели и сбрасывает состояние
 */
function cleanupObservers() {
	if (resizeObserver) {
		resizeObserver.disconnect();
		resizeObserver = null;
	}
	if (intersectionObserver) {
		intersectionObserver.disconnect();
		intersectionObserver = null;
	}
	if (mutationObserver) {
		mutationObserver.disconnect();
		mutationObserver = null;
	}
	isObserverInitialized = false;
	resizeInProgress = false;
}

/**
 * Инициализирует MutationObserver для отслеживания изменений в DOM
 */
function initMutationObserver() {
	if (mutationObserver) {
		cleanupObservers();
	}

	const camsList = document.querySelector('.cams_list_component');
	if (!camsList) {
		console.error('.cams_list_component не найден');
		return;
	}

	mutationObserver = new MutationObserver(() => {
		const gridContainer = document.querySelector('.grid_container');

		if (gridContainer && !isObserverInitialized) {
			initResizeObserver(gridContainer);
			initIntersectionObserver(gridContainer);
			isObserverInitialized = true;
		} else if (!gridContainer && isObserverInitialized) {
			cleanupObservers();
			console.log('Элемент .grid_container исчез, остановлено отслеживание');
		}
	});

	mutationObserver.observe(camsList, { childList: true, subtree: true });

	// Проверяем наличие .grid_container при первой инициализации
	const gridContainer = document.querySelector('.grid_container');
	if (gridContainer) {
		initResizeObserver(gridContainer);
		initIntersectionObserver(gridContainer);
		isObserverInitialized = true;
	}
}

export { initMutationObserver }