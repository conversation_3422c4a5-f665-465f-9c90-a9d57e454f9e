using Microsoft.AspNetCore.Components;
using MudBlazor;
using System.Reactive.Disposables;
using System.Reactive;
using Teslametrics.App.Web.Domain.AccessControl;
using Teslametrics.App.Web.Extensions;
using Teslametrics.Shared;
using Dapper;
using Microsoft.AspNetCore.Components.Authorization;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Roles.Drawer.Edit.CamerasPermissions;

public partial class CamerasConcretePermissionsComponent
{
	#region [Type Declarations]
	private enum ItemType
	{
		Organization,
		Folder,
		Camera
	}

	private class TreeItemPresenter : TreeItemData<Guid>
	{
		public int CameraCount { get; set; }
		public override bool Expandable => Type != ItemType.Camera;
		public bool ReadOnly => Type == ItemType.Camera;
		public bool IsFolder => Type == ItemType.Folder;
		public bool IsCamera => Type == ItemType.Camera;
		public bool IsOrganization => Type == ItemType.Organization;
		public Guid OrganizationId { get; set; }
		public Guid Id => Value;
		public Guid ParentId { get; init; }
		public ItemType Type { get; init; }

		public TreeItemPresenter(Guid id, Guid parentId, string title, ItemType type) : base(id)
		{
			Text = title;
			ParentId = parentId;
			Type = type;
			Icon = type switch
			{
				ItemType.Folder => Icons.Material.Outlined.Folder,
				ItemType.Camera => Icons.Material.Outlined.Camera,
				ItemType.Organization => Icons.Material.Outlined.Business,
				_ => Icons.Material.Outlined.QuestionMark
			};
		}
	}
	#endregion [Type Declarations]

	private bool _disposedValue;
	private record InheritStatus(bool Inherited, IEnumerable<string> InheritedFrom);
	private bool _subscribing;
	private IEnumerable<AppPermissions.Main.Cameras> _cameraValues = [];
	private IEnumerable<AppPermissions.Main.Folders> _folderValues = [];

	// Track parameter changes for proper synchronization
	private Guid _organizationId;

	// Performance optimization: Use HashSet for O(1) lookups and efficient modifications
	private readonly HashSet<ResourcePermission> _selectedPermissions = [];
	private readonly Dictionary<(Guid resourceId, string permission), bool> _permissionLookupCache = [];
	private readonly Dictionary<(Guid resourceId, string permission), InheritStatus> _inheritStatusCache = [];
	private bool _isUpdatingPermissions = false;

	private IEnumerable<ResourcePermission> _wildcardPermissions => _selectedPermissions.Where(x => x.ResourceId.IsWildcard);

	private SubscribeTreeUseCase.Response? _subscriptionResult;
	private readonly List<TreeItemPresenter> _items = [];

	#region [Parameters]
	[Parameter]
	public Guid OrganizationId { get; set; }

	[Parameter]
	[EditorRequired]
	public IEnumerable<ResourcePermission> Selected { get; set; } = [];

	[Parameter]
	public EventCallback<IEnumerable<ResourcePermission>> SelectedChanged { get; set; }

	[Parameter]
	public bool ShowAdminTaggedValues { get; set; }
	#endregion [Parameters]

	protected override void OnInitialized()
	{
		AuthenticationStateProvider.AuthenticationStateChanged += OnAuthenticationStateChanged;

		// Initialize internal HashSet with parameter value
		SyncSelectedToHashSet();

		base.OnInitialized();
	}

	protected override async Task OnParametersSetAsync()
	{
		if (OrganizationId != _organizationId)
		{
			_organizationId = OrganizationId;

			IEnumerable<Task> tasks = [
				FetchAsync(),
				SubscribeAsync(),
			];

			await Task.WhenAll(tasks);
		}
		await base.OnParametersSetAsync();
	}

	protected override void Dispose(bool disposing)
	{
		if (!_disposedValue)
		{
			if (disposing)
			{
				AuthenticationStateProvider.AuthenticationStateChanged -= OnAuthenticationStateChanged;
			}

			_disposedValue = true;
		}

		base.Dispose(disposing);
	}

	protected override void OnParametersSet()
	{
		// Sync Selected parameter to internal HashSet
		if (!_isUpdatingPermissions)
		{
			SyncSelectedToHashSet();
		}

		RefreshPermissionLists();

		base.OnParametersSet();
	}

	private void SyncSelectedToHashSet()
	{
		// Clear and repopulate HashSet from parameter
		_selectedPermissions.Clear();
		if (Selected != null)
		{
			_selectedPermissions.UnionWith(Selected);
		}
		ClearCaches();
	}

	private async Task FetchAsync()
	{
		if (IsLoading) return;

		await SetLoadingAsync(true);
		GetTreeUseCase.Response? response = null;
		try
		{
			var userId = await GetCurrentUserIdAsync() ?? throw new UnauthorizedAccessException();
			response = await ScopeFactory.MediatorSend(new GetTreeUseCase.Query(OrganizationId, userId));
		}
		catch (Exception ex)
		{
			Snackbar.Add("Не удалось получить список камер. Повторите попытку", Severity.Error);
			Logger.LogError(ex, ex.Message);
		}

		await SetLoadingAsync(false);

		switch (response?.Result)
		{
			case GetTreeUseCase.Result.Success:
				var buffer = ConvertResponseToTreeItemPresenters(response);
				TransferExpandedState(_items, buffer);
				_items.Clear();
				_items.AddRange(buffer);
				buffer.Clear();
				buffer = null;
				break;
			case GetTreeUseCase.Result.ValidationError:
				Snackbar.Add("Не удалось получить список камер. Повторите попытку", Severity.Error);
				break;
			case GetTreeUseCase.Result.Unknown:
				Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(CamerasConcretePermissionsComponent), nameof(GetTreeUseCase));
				Snackbar.Add($"Не удалось получить список камер из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
				break;
			default:
				Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(CamerasConcretePermissionsComponent), nameof(GetTreeUseCase), response?.Result);
				Snackbar.Add("Не удалось получить список камер. Повторите попытку", Severity.Error);
				break;
		}
	}
	private async Task SubscribeAsync()
	{
		try
		{
			if (_subscribing) return;

			Unsubscribe();

			await SetSubscribingAsync(true);
			var orgIds = _items.Select(x => x.Value).ToList();
			_subscriptionResult = await ScopeFactory.MediatorSend(new SubscribeTreeUseCase.Request(Observer.Create<object>(OnAppEventHandler, OnError), OrganizationId));
			await SetSubscribingAsync(false);
		}
		catch (Exception ex)
		{
			Snackbar.Add("Не удалось получить подписку на события дерева из-за непредвиденной ошибки. Повторите попытку", Severity.Error);
			Logger.LogError(ex, ex.Message);
		}
		await SetSubscribingAsync(false);

		switch (_subscriptionResult?.Result)
		{
			case SubscribeTreeUseCase.Result.Success:
				CompositeDisposable.Add(_subscriptionResult.Subscription!);
				break;
			case SubscribeTreeUseCase.Result.ValidationError:
				Snackbar.Add("Ошибка валидации при подписке на события", Severity.Error);
				break;
			case SubscribeTreeUseCase.Result.Unknown:
				Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(CamerasConcretePermissionsComponent), nameof(SubscribeTreeUseCase));
				Snackbar.Add($"Не удалось получить подписку на события дерева из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
				break;
			default:
				Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(CamerasConcretePermissionsComponent), nameof(SubscribeTreeUseCase), _subscriptionResult?.Result);
				Snackbar.Add("Не удалось получить подписку на события дерева. Повторите попытку", Severity.Error);
				break;
		}
	}
	private void Unsubscribe()
	{
		if (_subscriptionResult?.Subscription is not null)
		{
			CompositeDisposable.Remove(_subscriptionResult.Subscription);
			_subscriptionResult.Subscription.Dispose();
		}
	}
	private Task SetSubscribingAsync(bool isLoading = true) => UpdateViewAsync(() =>
	{
		_subscribing = isLoading;
	});

	#region [Actions]
	private Task RefreshAsync() => FetchAsync();

	private async Task OnChangedHandler(bool isChecked, Enum value, TreeItemPresenter presenter)
	{
		_isUpdatingPermissions = true;
		try
		{
			string permissionName = value.GetEnumPermissionString();
			var permission = new ResourcePermission(new(presenter.Value), new(permissionName));

			if (isChecked)
			{
				// Use HashSet.Add for O(1) operation instead of LINQ.Any + Append
				_selectedPermissions.Add(permission);

				// Check if the value is a camera permission
				if (value is AppPermissions.Main.Cameras)
				{
					string folderPermissionName = AppPermissions.Main.Folders.Read.GetEnumPermissionString();
					var folderPermission = new ResourcePermission(new(presenter.ParentId), new(folderPermissionName));

					// Check if folder permission doesn't already exist
					bool folderPermissionExists = _selectedPermissions.Any(x =>
						x.Permission.Value == folderPermissionName &&
						(x.ResourceId.Value == presenter.ParentId || x.ResourceId == ResourceId.Wildcard));

					if (!folderPermissionExists)
					{
						_selectedPermissions.Add(folderPermission);
					}
				}

				// Handle inherited flags for cameras
				if (value is AppPermissions.Main.Cameras cameraFlags)
				{
					foreach (AppPermissions.Main.Cameras flag in Enum.GetValues(typeof(AppPermissions.Main.Cameras)))
					{
						if (cameraFlags.HasFlag(flag) && flag != AppPermissions.Main.Cameras.Invalid)
						{
							string inheritedPermissionName = flag.GetEnumPermissionString();
							var inheritedPermission = new ResourcePermission(new(presenter.Value), new(inheritedPermissionName));
							_selectedPermissions.Add(inheritedPermission);
						}
					}
				}

				// Handle inherited flags for folders
				if (value is AppPermissions.Main.Folders folderFlags)
				{
					foreach (AppPermissions.Main.Folders flag in Enum.GetValues(typeof(AppPermissions.Main.Folders)))
					{
						if (folderFlags.HasFlag(flag) && flag != AppPermissions.Main.Folders.Invalid)
						{
							string inheritedPermissionName = flag.GetEnumPermissionString();
							var inheritedPermission = new ResourcePermission(new(presenter.Value), new(inheritedPermissionName));
							_selectedPermissions.Add(inheritedPermission);
						}
					}
				}
			}
			else
			{
				// Use HashSet.Remove for O(1) operation instead of LINQ.Where + Except
				_selectedPermissions.Remove(permission);
			}

			// Clear caches after modifications
			ClearCaches();

			// Update the Selected parameter and notify parent
			Selected = [.. _selectedPermissions];
			if (SelectedChanged.HasDelegate)
				await SelectedChanged.InvokeAsync(Selected);
		}
		finally
		{
			_isUpdatingPermissions = false;
		}
	}
	#endregion [Actions]

	#region [Event Hanndlers]
	private async void OnAppEventHandler(object appEvent)
	{
		await RefreshAsync();
		await UpdateViewAsync();
	}

	private void OnError(Exception exc)
	{
		Snackbar.Add("Ошибка при подписке на события", Severity.Error);
		Logger.LogError(exc, exc.Message);
	}

	// todo:: мб вынести в базовый класс? Это довольно часто случается
	private async void OnAuthenticationStateChanged(Task<AuthenticationState> authState)
	{
		await FetchAsync();
	}
	#endregion [Event Handlers]

	#region [Casts]
	private static List<TreeItemPresenter> ConvertResponseToTreeItemPresenters(GetTreeUseCase.Response response) // Нужно, т.к. treeview от mudblazor`а привередливый и жрет только TreeItemData<TYPE>
	{
		var treeItems = new List<TreeItemPresenter>();

		foreach (var folder in response.Items)
		{
			ConvertFolderToTreeItems(folder, folder.Id, treeItems);
		}

		return treeItems;
	}

	private static void ConvertFolderToTreeItems(GetTreeUseCase.Response.Folder folder, Guid parentId, List<TreeItemPresenter> items)
	{
		// Добавляем папку
		var folderPresenter = new TreeItemPresenter(
			folder.Id,
			parentId,
			folder.Name,
			ItemType.Folder
		);

		folderPresenter.Children ??= [];

		items.Add(folderPresenter);

		// Обрабатываем камеры, вложенные в папку
		foreach (var camera in folder.Cameras)
		{
			var cameraPresenter = new TreeItemPresenter(
				camera.Id,
				folder.Id,
				camera.Name,
				ItemType.Camera
			);
			folderPresenter.Children.Add(cameraPresenter);
		}
	}

	// Опять-таки проблема широкого списка против глубокого. Если элементов немного, то лучше оставить рекурсивный поиск. Если поменяется - добавить словарь.
	private static void TransferExpandedState<T>(List<T> sourceTree, List<T> targetTree) where T : TreeItemData<Guid>
	{
		foreach (var sourceElement in sourceTree)
		{
			if (!sourceElement.Expanded)
				continue;

			// Находим соответствующий элемент во втором дереве
			var targetElement = FindElementById(targetTree, sourceElement.Value);

			if (targetElement != null)
			{
				// Переносим значение Expanded
				targetElement.Expanded = sourceElement.Expanded;
			}

			// Рекурсивно обрабатываем вложенные элементы
			if (sourceElement.Children?.Count > 0 && targetElement?.Children?.Count > 0)
			{
				TransferExpandedState(sourceElement.Children, targetElement.Children);
			}
		}
	}

	private static TreeItemData<Guid>? FindElementById(IEnumerable<TreeItemData<Guid>> tree, Guid id)
	{
		foreach (var element in tree)
		{
			if (element.Value == id)
				return element;

			if (element.Children?.Count > 0)
			{
				var found = FindElementById(element.Children, id);
				if (found != null)
					return found;
			}
		}

		return null;
	}
	#endregion [Casts]

	private InheritStatus GetInheritStatus<TEnum>(TEnum permission, TreeItemPresenter itemPresenter) where TEnum : Enum
	{
		var key = (itemPresenter.Value, permission.GetEnumPermissionString());

		if (_inheritStatusCache.TryGetValue(key, out InheritStatus? cachedStatus))
		{
			return cachedStatus;
		}

		List<string> _inheritedFrom = [];
		var enumType = typeof(TEnum);
		var permissionString = permission.GetEnumPermissionString();

		// Use HashSet lookup instead of LINQ Where for better performance
		foreach (var perm in _selectedPermissions)
		{
			if (perm.ResourceId.Value != itemPresenter.Value) continue;

			var lastDotIndex = perm.Permission.Value.LastIndexOf('.');
			var enumName = lastDotIndex >= 0 ? perm.Permission.Value.AsSpan(lastDotIndex + 1) : perm.Permission.Value.AsSpan();

			if (Enum.TryParse(enumType, enumName, out var enumValueObj) && enumValueObj is TEnum enumValue)
			{
				bool hasReadPermission = enumValue.HasFlag(permission);
				if (hasReadPermission)
				{
					_inheritedFrom.Add(enumValue.GetEnumPermissionString());
				}
			}
		}

		if (_inheritedFrom.Count == 1)
		{
			var result = new InheritStatus(false, []);
			_inheritStatusCache[key] = result;
			return result;
		}

		_inheritedFrom.Remove(permissionString);
		var finalResult = new InheritStatus(_inheritedFrom.Count > 0, _inheritedFrom);
		_inheritStatusCache[key] = finalResult;
		return finalResult;
	}

	private void ClearCaches()
	{
		_permissionLookupCache.Clear();
		_inheritStatusCache.Clear();
	}

	private bool ContainsPermission(Enum value, Guid resourceId)
	{
		var key = (resourceId, value.GetEnumPermissionString());

		if (_permissionLookupCache.TryGetValue(key, out bool cachedResult))
		{
			return cachedResult;
		}

		var result = _selectedPermissions.Any(x => x.ResourceId.Value == resourceId && x.Permission.Value == key.Item2);
		_permissionLookupCache[key] = result;
		return result;
	}

	private void RefreshPermissionLists()
	{
		List<AppPermissions.Main.Cameras> cameraEnums = [];
		List<AppPermissions.Main.Folders> folderEnumValues = [];
		if (ShowAdminTaggedValues)
		{
			cameraEnums = ((AppPermissions.Main.Cameras[])Enum.GetValues(typeof(AppPermissions.Main.Cameras)))
				.Where(item => item != AppPermissions.Main.Cameras.Invalid && !_wildcardPermissions.Any(x => x.Permission.Value == item.GetEnumPermissionString()))
				.AsList();

			folderEnumValues = ((AppPermissions.Main.Folders[])Enum.GetValues(typeof(AppPermissions.Main.Folders)))
				.AsList();

			folderEnumValues = folderEnumValues.Where(item => item != AppPermissions.Main.Folders.Invalid && !_wildcardPermissions.Any(x => x.Permission.Value == item.GetEnumPermissionString()))
				.AsList();
		}
		else
		{
			cameraEnums = ((AppPermissions.Main.Cameras[])Enum.GetValues(typeof(AppPermissions.Main.Cameras)))
				.Where(item => item != AppPermissions.Main.Cameras.Invalid && !item.IsAdminTagged() && !_wildcardPermissions.Any(x => x.Permission.Value == item.GetEnumPermissionString()))
				.AsList();

			folderEnumValues = ((AppPermissions.Main.Folders[])Enum.GetValues(typeof(AppPermissions.Main.Folders)))
				.Where(item => item != AppPermissions.Main.Folders.Invalid && !item.IsAdminTagged() && !_wildcardPermissions.Any(x => x.Permission.Value == item.GetEnumPermissionString()))
				.AsList();
		}

		//cameraEnums.Remove(AppPermissions.Main.Cameras.Create);
		cameraEnums.Remove(AppPermissions.Main.Cameras.Invalid);
		folderEnumValues.Remove(AppPermissions.Main.Folders.Invalid);
		folderEnumValues.Remove(AppPermissions.Main.Folders.Create);

		_cameraValues = cameraEnums;
		_folderValues = folderEnumValues;
	}
}