using FluentValidation;
using Microsoft.AspNetCore.Components;
using System.Reactive;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Components.Drawer;
using Teslametrics.App.Web.Events.Quota;
using Teslametrics.App.Web.Extensions;
using Teslametrics.App.Web.Features.Main.AccessControl.Quota.Drawer.Edit.PresetField;
using Teslametrics.App.Web.Features.Main.AccessControl.Quota.Drawer.View;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Quota.Drawer.Edit;

public partial class QuotaEditComponent
{
	private class Model(Guid id, string name, PresetFieldComponent.Preset? preset, int totalQuota, int retentionPeriodDays, int storageLimitMb)
	{
		public Guid Id { get; set; } = id;
		public string Name { get; set; } = name;
		public PresetFieldComponent.Preset? Preset { get; set; } = preset;
		public int TotalQuota { get; set; } = totalQuota;
		public int RetentionPeriodDays { get; set; } = retentionPeriodDays;
		public int StorageLimitMb { get; set; } = storageLimitMb;
	}

	private class Validator : BaseFluentValidator<Model>
	{
		public Validator()
		{
			RuleFor(model => model.Name)
				.Length(3, 60).WithMessage("наименование должно быть длиной от 3 до 60 символов");
		}
	}

	private bool _subscribing;
	private bool _isValid;
	private Model? _model = new(Guid.Empty, string.Empty, null, 0, 0, 0);
	private Validator _validator = new();

	[CascadingParameter(Name = DrawerConsts.InstanceName)]
	private DrawerComponent Drawer { get; set; } = null!;

	private GetQuotaUseCase.Response? _response;
	private SubscribeQuotaUseCase.Response? _subscriptionResponse;

	[Parameter]
	public Guid OrganizationId { get; set; }

	[Parameter]
	public Guid QuotaId { get; set; }
	protected override async Task OnParametersSetAsync()
	{
		await base.OnParametersSetAsync();

		if (_response is null || QuotaId != _response.Id)
		{
			_model = null;
			await FetchAsync();
			await SubscribeAsync();
		}
	}

	private async Task FetchAsync()
	{
		if (IsLoading) return;
		try
		{
			await SetLoadingAsync(true);
			_response = await ScopeFactory.MediatorSend(new GetQuotaUseCase.Query(QuotaId));
		}
		catch (Exception ex)
		{
			_response = null;
			Logger.LogError(ex, ex.Message);
			Snackbar.Add($"Не удалось получить квоту из-за ошибки сообщения с сервером. Повторите попытку позже.", MudBlazor.Severity.Error);
		}

		await SetLoadingAsync(false);
		if (_response is null) return;

		switch (_response.Result)
		{
			case GetQuotaUseCase.Result.Success:
				PresetFieldComponent.Preset? preset = null;
				if (_response.PresetId.HasValue)
				{
					preset = new PresetFieldComponent.Preset(_response.PresetId.Value, _response.PresetName!);
				}
				_model ??= new Model(_response.Id, _response.Name, preset, _response.TotalQuota, _response.RetentionPeriodDays, _response.StorageLimitMb);
				break;
			case GetQuotaUseCase.Result.QuotaNotFound:
				Snackbar.Add("Не удалось получить квоту. Возможно камера уже удалена.", MudBlazor.Severity.Warning);
				Cancel();
				break;
			case GetQuotaUseCase.Result.ValidationError:
				Snackbar.Add("Не удалось получить квоту из-за ошибки валидации.", MudBlazor.Severity.Error);
				Cancel();
				break;
			case GetQuotaUseCase.Result.Unknown:
				Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(QuotaEditComponent), nameof(GetQuotaUseCase));
				Snackbar.Add($"Не удалось получить квоту из-за неизвестной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
				break;
			default:
				Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(QuotaEditComponent), nameof(GetQuotaUseCase), _response.Result);
				Snackbar.Add($"Не удалось получить квоту из-за ошибки: {_response.Result}", MudBlazor.Severity.Error);
				break;
		}
	}

	private async Task SubscribeAsync()
	{
		try
		{
			Unsubscribe();
			await SetSubscribingAsync(true);
			_subscriptionResponse = await ScopeFactory.MediatorSend(new SubscribeQuotaUseCase.Request(Observer.Create<object>(OnAppEventHandler, OnError), QuotaId));
		}
		catch (Exception ex)
		{
			_subscriptionResponse = null;
			Snackbar.Add($"Не удалось подписаться на события квоты из-за ошибки сообщения с сервером. Повторите попытку позже.", MudBlazor.Severity.Error);
			Logger.LogError(ex, ex.Message);
		}
		finally
		{
			await SetSubscribingAsync(false);
		}

		if (_subscriptionResponse is null) return;

		switch (_subscriptionResponse.Result)
		{
			case SubscribeQuotaUseCase.Result.Success:
				CompositeDisposable.Add(_subscriptionResponse.Subscription!);
				break;
			case SubscribeQuotaUseCase.Result.ValidationError:
				Snackbar.Add("Ошибка валидации при подписке на события", MudBlazor.Severity.Error);
				break;
			case SubscribeQuotaUseCase.Result.QuotaNotFound:
				Snackbar.Add("Ошибка подписки на события квоты", MudBlazor.Severity.Error);
				break;
			case SubscribeQuotaUseCase.Result.Unknown:
				Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(QuotaEditComponent), nameof(SubscribeQuotaUseCase));
				Snackbar.Add($"Не удалось подписаться на события квоты из-за неизвестной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
				break;
			default:
				Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(QuotaEditComponent), nameof(SubscribeQuotaUseCase), _subscriptionResponse.Result);
				Snackbar.Add($"Не удалось подписаться на события квоты из-за ошибки: {_subscriptionResponse.Result}", MudBlazor.Severity.Error);
				break;
		}
	}
	private void Unsubscribe()
	{
		if (_subscriptionResponse?.Subscription is not null)
		{
			CompositeDisposable.Remove(_subscriptionResponse.Subscription);
			_subscriptionResponse.Subscription.Dispose();
		}
	}

	protected Task SetSubscribingAsync(bool isLoading = true) => UpdateViewAsync(() =>
	{
		_subscribing = isLoading;
	});

	#region [Actions]
	private async Task SubmitAsync()
	{
		if (_model is null)
		{
			Snackbar.Add("Камера не найдена. Невозможно применить изменения!", MudBlazor.Severity.Error);
			return;
		}

		UpdateQuotaUseCase.Response? response = null;
		try
		{
			await SetLoadingAsync(true);
			response = await ScopeFactory.MediatorSend(new UpdateQuotaUseCase.Command(OrganizationId, _model.Id, _model.Name, _model.TotalQuota, _model.RetentionPeriodDays, _model.StorageLimitMb));
		}
		catch (Exception ex)
		{
			response = null;
			Logger.LogError(ex, ex.Message);
			Snackbar.Add($"Не удалось сохранить изменения из-за ошибки сообщения с сервером. Повторите попытку позже.", MudBlazor.Severity.Error);
		}

		if (response is null) return;
		switch (response.Result)
		{
			case UpdateQuotaUseCase.Result.Success:
				Snackbar.Add("Камера успешно сохранена", MudBlazor.Severity.Success);
				EventSystem.Publish(new CameraQuotaSelectEto(OrganizationId, _model.Id));
				break;
			case UpdateQuotaUseCase.Result.ValidationError:
				Snackbar.Add("Не удалось сохранить изменения из-за ошибки валидации. Проверьте правильность заполнения полей", MudBlazor.Severity.Error);
				break;
			case UpdateQuotaUseCase.Result.CameraPresetNotFound:
				Snackbar.Add("Не удалось сохранить изменения. Пресет не найден.", MudBlazor.Severity.Error);
				Cancel();
				break;
			case UpdateQuotaUseCase.Result.CameraQuotaNotFound:
				Snackbar.Add("Не удалось сохранить изменения. Квота не найдена. Выберите другую квоту", MudBlazor.Severity.Error);
				Cancel();
				break;

			case UpdateQuotaUseCase.Result.Unknown:
				Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(QuotaEditComponent), nameof(UpdateQuotaUseCase));
				Snackbar.Add($"Не удалось сохранить изменения из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
				break;

			default:
				Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(QuotaEditComponent), nameof(UpdateQuotaUseCase), response.Result);
				Snackbar.Add($"Не удалось сохранить изменения из-за ошибки: {response.Result}", MudBlazor.Severity.Error);
				break;
		}
	}
	private Task RefreshAsync() => FetchAsync();
	private void Cancel() => EventSystem.Publish(new CameraQuotaSelectEto(OrganizationId, QuotaId));
	private void Delete() => EventSystem.Publish(new CameraQuotaDeleteEto(OrganizationId, QuotaId));
	#endregion

	#region [Event Handlers]
	private async void OnAppEventHandler(object appEvent)
	{
		switch (appEvent)
		{
			case SubscribeQuotaUseCase.UpdatedEvent updatedEto:
				await FetchAsync();
				await UpdateViewAsync();
				break;

			case SubscribeQuotaUseCase.DeletedEvent deletedEto:
				Snackbar.Add("Просматриваемая вами камеры была удалена", MudBlazor.Severity.Warning);
				await Drawer.HideAsync();
				break;

			default:
				Snackbar.Add("Было получено непредвиденное событие.", MudBlazor.Severity.Warning);
				await FetchAsync();
				await UpdateViewAsync();
				Logger.LogWarning("Unexpected event in {UseCase}: {Event}", nameof(SubscribeQuotaUseCase), nameof(appEvent));
				break;
		}
	}

	private void OnError(Exception exc)
	{
		Snackbar.Add("Ошибка при подписке на события", MudBlazor.Severity.Error);
		Logger.LogError(exc, exc.Message);
	}
	#endregion [Event Handlers]
}
