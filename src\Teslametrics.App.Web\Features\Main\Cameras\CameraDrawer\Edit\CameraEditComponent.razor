@using Teslametrics.App.Web.Features.Main.Cameras.CameraDrawer.Edit.PresetField
@using Teslametrics.App.Web.Features.Main.Cameras.CameraDrawer.Edit.Onvif
@inherits InteractiveBaseComponent
<DrawerHeader>
	<MudStack Spacing="0">
		<MudText Typo="Typo.h1">Редактирование камеры</MudText>
		@if (IsLoading && _camera is null)
		{
			<MudSkeleton Width="30%"
						 Height="42px"></MudSkeleton>
		}
		@if (IsLoading && _camera is not null && !_camera.IsSuccess)
		{
			<MudText Typo="Typo.subtitle1">Не удалось получить данные о камере.</MudText>
		}
		@if (_camera is not null && _camera.IsSuccess)
		{
			<MudText Typo="Typo.subtitle1">@_camera.Name</MudText>
		}
	</MudStack>
	@if (IsLoading)
	{
		<MudProgressCircular Color="Color.Primary"
							 Indeterminate="true" />
	}
	<MudSpacer />
	@if (IsLoading)
	{
		<MudSkeleton SkeletonType="SkeletonType.Circle"
					 Width="50px"
					 Height="50px" />
	}
	@if (!IsLoading && _camera is not null && _camera.IsSuccess)
	{
		<MudSpacer />
		@if (!_subscribing && (_subscriptionResult is null || !_subscriptionResult.IsSuccess))
		{
			<MudTooltip Arrow="true"
						Placement="Placement.Start"
						Text="Ошибка подписки на события">
				<MudIconButton OnClick="SubscribeAsync"
							   Icon="@Icons.Material.Filled.ErrorOutline"
							   Color="Color.Error" />
			</MudTooltip>
		}
		<MudIconButton OnClick="RefreshAsync"
					   Icon="@Icons.Material.Filled.Refresh"
					   Color="Color.Primary" />
	}
</DrawerHeader>
@if (IsLoading)
{
	<MudProgressLinear Color="Color.Primary"
					   Indeterminate="true" />
}
else
{
	<div style="height: 4px;" />
}
<div class="px-4 py-4">
	@if (_camera is not null && _camera.IsSuccess && _model is not null)
	{
		<MudForm Model="_model"
				 Validation="_validator.ValidateValue"
				 @bind-IsValid="_isValid"
				 OverrideFieldValidation="true"
				 UserAttributes="@(new Dictionary<string, object>() { { "autocomplete", "off" }, { "aria-autocomplete", "none" }, { "role", "presentation" } })">
			<MudTabs PanelClass="mud-height-full px-4 py-4 d-flex flex-column gap-4"
					 TabHeaderClass="mt-3 mx-3"
					 KeepPanelsAlive="true">
				<MudTabPanel Text="Информация в системе">
					<FormSectionComponent Title="Описание камеры"
										  Subtitle="Настройки, которые влияют только на восприятие человеком">
						<MudTextField @bind-Value="_model.Name"
									  For="@(() => _model.Name)"
									  Clearable="true"
									  InputType="InputType.Text"
									  Immediate="true"
									  Label="Наименование"
									  HelperText="Необходимо для идентификации камеры человеком"
									  RequiredError="Данное поле обязательно"
									  Required="true" />
					</FormSectionComponent>

					<FormSectionComponent Title="Параметры камеры"
										  Subtitle="Данные настройки важны для работы в системе">
						<MudTextField Value="@CameraId.ToString("N")"
									  ReadOnly="true"
									  Label="ID камеры" />

						<PresetFieldComponent @bind-Selected="@_model.Preset"
											  For="() => _model.Preset"
											  OrganizationId="OrganizationId" />

						<MudTextField T="string"
									  Value="_model.ArchiveUri"
									  ValueChanged="OnArchiveUriChanged"
									  For="@(() => _model.ArchiveUri)"
									  InputType="InputType.Text"
									  Immediate="true"
									  Label="Ссылка на архивный поток"
									  HelperText="По данной ссылке медиасервер будет получать поток с камеры"
									  @ref="@_archiveUriRef" />

						<MudTextField T="string"
									  Value="_model.ViewUri"
									  ValueChanged="OnViewUriChanged"
									  For="@(() => _model.ViewUri)"
									  InputType="InputType.Text"
									  Immediate="true"
									  Label="Ссылка на поток для видов"
									  HelperText="По данной ссылке медиасервер будет получать поток с камеры"
									  @ref="@_viewUriRef" />

						<MudTextField T="string"
									  Value="_model.PublicUri"
									  ValueChanged="OnPublicUriChanged"
									  For="@(() => _model.PublicUri)"
									  InputType="InputType.Text"
									  Immediate="true"
									  Label="Ссылка на публичный поток"
									  HelperText="По данной ссылке медиасервер будет получать поток с камеры"
									  @ref="@_publicUriRef" />

						<MudCheckBox @bind-Value="_model.AutoStart"
									 Label="Автозапуск при перезапуске системы"
									 Color="Color.Primary"
									 Class="ml-n3" />

						<MudCheckBox T="bool"
									 @bind-Value="_model.OnvifEnabled"
									 Color="Color.Primary"
									 Class="ml-n3"
									 Label="Включить ONVIF?" />
					</FormSectionComponent>

					<FormSectionComponent Title="Местонахождение камеры">
						<TimeZoneSelector @bind-TimeZone="@_model.TimeZone"
										  Label="Часовой пояс"
										  HelperText="Часовой пояс, в котором будет расположена камера" />

						<YandexMaps @bind-Coordinates="@_model.Coordinates"
									Width="calc(100% + 32px)"
									ReadOnly="false"
									For="@(() => _model.Coordinates)"
									Class="ma-n4 rounded-b overflow-hidden"
									Height="400px" />
					</FormSectionComponent>
				</MudTabPanel>

				<MudTabPanel Text="ONVIF"
							 Disabled="!_model.OnvifEnabled">
					<OnvifComponent Onvif="_model.OnvifSettings" />
				</MudTabPanel>
			</MudTabs>
		</MudForm>
	}
	<FormLoadingComponent IsLoading="IsLoading && _camera is null" />
	<NotFoundComponent IsFound="!IsLoading && _camera is not null && _camera.IsSuccess" />
</div>
<DrawerActions>
	<MudSpacer />
	<MudButton OnClick="Cancel">Отменить</MudButton>
	@if (_camera is not null && _model is not null)
	{
		<AuthorizeView Policy="@AppPermissions.Main.Cameras.Update.GetEnumPermissionString()"
					   Resource="new PolicyRequirementResource(OrganizationId, _model.Id)"
					   Context="innerContext">
			<MudButton OnClick="SubmitAsync"
					   Disabled="@(!_isValid)"
					   Color="Color.Secondary"
					   Variant="Variant.Outlined">Сохранить</MudButton>
		</AuthorizeView>
	}
</DrawerActions>