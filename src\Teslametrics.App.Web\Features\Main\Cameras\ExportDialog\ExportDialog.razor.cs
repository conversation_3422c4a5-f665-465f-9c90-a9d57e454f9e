using System.Globalization;
using Blazor.DownloadFileFast.Interfaces;
using CsvHelper;
using CsvHelper.Configuration;
using Microsoft.AspNetCore.Components;
using Teslametrics.Shared;
using Teslametrics.App.Web.Events.Cameras;
using Teslametrics.App.Web.Exceptions;
using Teslametrics.App.Web.Extensions;

namespace Teslametrics.App.Web.Features.Main.Cameras.ExportDialog;

public partial class ExportDialog
{
    private GetCameraListUseCase.Response? _response = null;
    private MudBlazor.DialogOptions _dialogOptions = new() { CloseOnEscapeKey = true, FullWidth = true, MaxWidth = MudBlazor.MaxWidth.Medium, NoHeader = true };
    private bool _isVisible;
    private Guid _organizationId;
    private Guid? _folderId;
    private string _orderBy = "Name";
    private OrderDirection _orderDirection = OrderDirection.Ascending;
    private DateTime _lastRefreshTime = DateTime.Now;
    private int ActivePanelIndex;
    private string _searchString = string.Empty;
    private readonly HashSet<string> _exportFields = [
        nameof(GetCameraListUseCase.Response.Item.Name),
        nameof(GetCameraListUseCase.Response.Item.QuotaName),
        nameof(GetCameraListUseCase.Response.Item.FolderName),
        nameof(GetCameraListUseCase.Response.Item.ViewUri),
        nameof(GetCameraListUseCase.Response.Item.PublicUri),
        nameof(GetCameraListUseCase.Response.Item.ArchiveUri),
        nameof(GetCameraListUseCase.Response.Item.AutoStart)
    ];

    [Inject]
    private IBlazorDownloadFileService _blazorDownloadFileService { get; set; } = null!;

    protected override void OnInitialized()
    {
        base.OnInitialized();

        CompositeDisposable.Add(EventSystem.Subscribe<CameraListExportEto>(OnExportHandler));
    }

    protected async Task FetchDataAsync()
    {
        try
        {
            await SetLoadingAsync(true);
            _lastRefreshTime = DateTime.Now;
            var userId = await GetCurrentUserIdAsync() ?? throw new NotAuthorizedException();
            _response = await ScopeFactory.MediatorSend(new GetCameraListUseCase.Query(userId, _organizationId, _folderId, _orderBy, _orderDirection));
            switch (_response.Result)
            {
                case GetCameraListUseCase.Result.Success:
                    break;
                case GetCameraListUseCase.Result.Unknown:
                default:
                    throw new Exception($"Unexpected result in {nameof(GetCameraListUseCase)}: {_response.Result}");
            }
        }
        catch (Exception exc)
        {
            Logger.LogError(exc, exc.Message);
            Snackbar.Add("Не удалось получить список камер из-за непредвиденной ошибки.", MudBlazor.Severity.Error);
        }
        finally
        {
            await SetLoadingAsync(false);
        }
    }

    private Func<GetCameraListUseCase.Response.Item, bool> _quickFilter => x =>
    {
        if (string.IsNullOrWhiteSpace(_searchString))
            return true;

        if (x.QuotaName.Contains(_searchString, StringComparison.OrdinalIgnoreCase))
            return true;

        if (x.Name.Contains(_searchString, StringComparison.OrdinalIgnoreCase))
            return true;

        if (x.FolderName.Contains(_searchString, StringComparison.OrdinalIgnoreCase))
            return true;

        return false;
    };

    protected void OnError(Exception exc)
    {
        Snackbar.Add($"Ошибка при экспорте списка камер: {exc.Message}", MudBlazor.Severity.Error, key: exc.GetHashCode().ToString());
    }

    private class ExportMap : ClassMap<GetCameraListUseCase.Response.Item>
    {
        public ExportMap(HashSet<string> exportFields)
        {
            Map(m => m.Name).Name("Название").Ignore(!exportFields.Contains(nameof(GetCameraListUseCase.Response.Item.Name)));
            Map(m => m.FolderName).Name("Директория").Ignore(!exportFields.Contains(nameof(GetCameraListUseCase.Response.Item.FolderName)));
            Map(m => m.QuotaName).Name("Квота").Ignore(!exportFields.Contains(nameof(GetCameraListUseCase.Response.Item.QuotaName)));
            Map(m => m.ViewUri).Name("Поток для видов").Ignore(!exportFields.Contains(nameof(GetCameraListUseCase.Response.Item.ViewUri)));
            Map(m => m.PublicUri).Name("Публичный поток").Ignore(!exportFields.Contains(nameof(GetCameraListUseCase.Response.Item.PublicUri)));
            Map(m => m.ArchiveUri).Name("Поток для архива").Ignore(!exportFields.Contains(nameof(GetCameraListUseCase.Response.Item.ArchiveUri)));
            Map(m => m.AutoStart).Name("Автозапуск при перезапуске системы").Ignore(!exportFields.Contains(nameof(GetCameraListUseCase.Response.Item.AutoStart)));
            Map(m => m.Coordinates).Name("Координаты").Ignore(!exportFields.Contains(nameof(GetCameraListUseCase.Response.Item.Coordinates)));
            Map(m => m.TimeZone).Name("Часовой пояс").Ignore(!exportFields.Contains(nameof(GetCameraListUseCase.Response.Item.TimeZone)));
        }
    }

    private void CheckedChanged(string fieldName)
    {
        if (!_exportFields.Add(fieldName))
        {
            _exportFields.Remove(fieldName);
        }
    }

    private Task ConfirmFields()
    {
        ActivePanelIndex = 1;
        return FetchDataAsync();
    }

    private async Task SubmitAsync()
    {
        if (_response is null || !_response.IsSuccess) return;
        try
        {
            using var memoryStream = new MemoryStream();
            using var streamWriter = new StreamWriter(memoryStream);
            using var csvWriter = new CsvWriter(streamWriter, CultureInfo.InvariantCulture);
            csvWriter.Context.RegisterClassMap(new ExportMap(_exportFields));
            csvWriter.WriteRecords(_response.Items);
            streamWriter.Flush();

            await _blazorDownloadFileService.DownloadFileAsync("export.csv", memoryStream.ToArray());
        }
        catch (Exception exc)
        {
            OnError(exc);
        }
    }

    private void OnExportHandler(CameraListExportEto eto)
    {
        _isVisible = true;
        _organizationId = eto.OrganizationId;
        _folderId = eto.FolderId;
        StateHasChanged();
    }
}