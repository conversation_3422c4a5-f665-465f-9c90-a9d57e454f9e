@using Teslametrics.Shared
@if (_model is not null)
{
	<MudTextField @bind-Value="_model.DisplayName" Label="Наименование датчика" Clearable="true" />
	<MudTextField @bind-Value="_model.Name" Label="Наименование топика" />
	<MudTextField @bind-Value="_model.MinTemp" Label="Минимальная температура" Clearable="true" />
	<MudTextField @bind-Value="_model.MaxTemp" Label="Максимальная Температура" Clearable="true" />
}

@code {
	private TemperatureModel? _model => SensorModel as TemperatureModel;

	[Parameter]
	public ISensorModel? SensorModel { get; set; }
}
