﻿<MudSelect T="TimeSpan"
		   Value="TimeZone"
		   ValueChanged="TimeZoneChanged"
		   Label="@Label"
		   HelperText="@HelperText"
		   ReadOnly="@ReadOnly"
		   Immediate="true"
		   AnchorOrigin="Origin.BottomCenter">
	@foreach (var offset in _availableTimeZones)
	{
		<MudSelectItem Value="@offset"
					   @key="@offset">
			UTC @(offset.TotalHours >= 0 ? "+" : "") @offset.TotalHours:00
		</MudSelectItem>
	}
</MudSelect>