using System.Reactive.Linq;
using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Domain.Incidents.Events;
using Teslametrics.App.Web.Exceptions;
using Teslametrics.App.Web.Features.Main.SystemSettings;
using Teslametrics.App.Web.Services.DomainEventBus;
using Teslametrics.Domain.Incidents.Events;

namespace Teslametrics.App.Web.Features.Main.Devices.RoomContent;

public static class SubscribeFridgeUseCase
{
    public record Request(IObserver<UpdatedEvent> Observer, Guid FridgeId) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public IDisposable? Subscription { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(IDisposable subscription)
        {
            Subscription = subscription;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Subscription = null;
            Result = result;
        }
    }

    // События, на которые можно подписаться
    public record UpdatedEvent;

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        RoomNotFound
    }

    public class Validator : AbstractValidator<Request>
    {
        public Validator()
        {
            RuleFor(r => r.Observer).NotEmpty();
            RuleFor(r => r.FridgeId).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Request, Response>
    {
        private readonly IValidator<Request> _validator;
        private readonly IDomainEventBus _domainEventBus;

        public Handler(IValidator<Request> validator,
                       IDomainEventBus domainEventBus)
        {
            _validator = validator;
            _domainEventBus = domainEventBus;
        }

        public async Task<Response> Handle(Request request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            var eventStream = await _domainEventBus.GetEventStreamAsync();

            // Хотелось бы узнавать:
            // 1. В холодильник добавлен датчик
            // 2. В холодильнике удалён датчик
            // 3. В одном из датчиков холодильника случилось происшествие
            // 4. В одном из датчиков холодильника было решено происшествие
            // 5. Изминилась информация о холодильнике (название и т.д.)
            // 6. Холодильник был удалён <- Вот это прям отдельным эвентом

            var subscription = eventStream
                .Where(e => e switch
                {
                    PlanUpdatedEvent => true,
                    IncidentCreatedEvent @event => @event.DeviceId == request.FridgeId,
                    IncidentResolvedEvent @event => @event.DeviceId == request.FridgeId,
                    _ => false
                })
                .Select(e => e switch
                {
                    PlanUpdatedEvent => new UpdatedEvent(),
                    IncidentCreatedEvent => new UpdatedEvent(),
                    IncidentResolvedEvent => new UpdatedEvent(),
                    _ => throw new AppException("Invalid event type")
                })
                .Subscribe(request.Observer);

            return new Response(subscription);
        }
    }
}
