﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>Library</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
    <Nullable>disable</Nullable>
  </PropertyGroup>

  <!-- vendor packages -->
  <ItemGroup>
    <PackageReference Include="Confluent.SchemaRegistry.Serdes.Avro" Version="2.10.1" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="9.0.6" />
    <PackageReference Include="Confluent.Kafka" Version="2.10.1" />
    <PackageReference Include="Microsoft.Orleans.Sdk" Version="9.1.2" />
    <PackageReference Include="Microsoft.Orleans.Core" Version="9.1.2" />
    <PackageReference Include="Microsoft.Orleans.Streaming" Version="9.1.2" />
    <PackageReference Include="Orleans.Streams.Utils" Version="14.0.0" />
  </ItemGroup>

</Project>