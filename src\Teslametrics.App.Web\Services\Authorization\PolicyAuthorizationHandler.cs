using Microsoft.AspNetCore.Authorization;
using Teslametrics.App.Web.Shared;

namespace Teslametrics.App.Web.Services.Authorization;

/// <summary>
/// Handles resource authorization by checking user claims against required permissions.
/// </summary>
public class PolicyAuthorizationHandler : AuthorizationHandler<PolicyRequirement>
{
	private static readonly Type _permissionsType = typeof(AppPermissions);
	private record ClaimPermission(string OrganizationId, string Permission, string ResourceId);

	/// <summary>
	/// Handles the authorization requirement by checking user claims against the required permissions.
	/// </summary>
	protected override Task HandleRequirementAsync(AuthorizationHandlerContext context, PolicyRequirement requirement)
	{
		if (!context.User.Identity?.IsAuthenticated == true)
		{
			context.Fail();
			return Task.CompletedTask;
		}

		var permissionClaims = context.User.Claims
			.Where(claim => claim.Type == "Permission")
			.Select(claim =>
			{
				var parts = claim.Value.Split("/");
				return new ClaimPermission(parts[0], parts[1], parts[2]);
			});

		foreach (var permission in requirement.Permissions)
		{
			var parts = permission.Split('.');
			var resourceRequirementPermissionValue = parts[^1];
			var resourceRequirementPermissionType = Fqdn<AppPermissions>.GetValueType(permission);

			// Check if the resource requirement permission type is valid and an enum.
			if (resourceRequirementPermissionType == null || !resourceRequirementPermissionType.IsEnum)
				continue;

			// Attempt to parse the permission value into an enum of the required type.
			if (!Enum.TryParse(resourceRequirementPermissionType, resourceRequirementPermissionValue, out object? enumParseResult))
				continue;

			// Cast the parsed result to an Enum for further comparison.
			Enum resourcePermissionEnum = (Enum)enumParseResult;

			// If the context resource is of type Resource, perform additional checks.
			if (context.Resource is PolicyRequirementResource resource)
			{
				if (permissionClaims.Any(claim =>
					(claim.OrganizationId == "*" || (Guid.TryParse(claim.OrganizationId, out Guid orgId) && orgId == resource.OrganizationId)) &&
					(claim.Permission == "*" || claim.Permission == permission || CheckIfPermissionAcceptable(claim.Permission, resourcePermissionEnum)) &&
					(claim.ResourceId == "*" || (Guid.TryParse(claim.ResourceId, out Guid resId) && resId == resource.ResourceId))))
				{
					context.Succeed(requirement);
					return Task.CompletedTask;
				}
			}
			else
			{
				if (permissionClaims.Any(claim =>
					claim.Permission == "*" || claim.Permission == permission || CheckIfPermissionAcceptable(claim.Permission, resourcePermissionEnum)))
				{
					context.Succeed(requirement);
					return Task.CompletedTask;
				}
			}
		}

		return Task.CompletedTask;
	}

	/// <summary>
	/// Checks if a given user permission is acceptable for the required resource permission.
	/// </summary>
	private static bool CheckIfPermissionAcceptable(string permission, Enum resourcePermissionEnum)
	{
		var userClaimParts = permission.Split('.');
		var userTypeNameValue = userClaimParts[^1];
		var userTypeName = $"{_permissionsType.FullName}+{string.Join("+", userClaimParts.Take(userClaimParts.Length - 1))}";
		var userTypeNameType = _permissionsType.Assembly.GetType(userTypeName)!;

		if (userTypeNameType == null || userTypeNameType != resourcePermissionEnum.GetType())
			return false;

		var userEnum = (Enum)Enum.Parse(userTypeNameType, userTypeNameValue);

		return userEnum.HasFlag(resourcePermissionEnum);
	}
}