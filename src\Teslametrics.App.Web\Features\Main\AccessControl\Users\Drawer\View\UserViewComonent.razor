@inherits InteractiveBaseComponent<UserViewComonent>
<DrawerHeader>
	<MudStack Spacing="0">
		@if (IsLoading)
		{
			<MudSkeleton Width="40%"
						 Height="calc(var(--mud-typography-subtitle1-lineheight) * var(--mud-typography-subtitle1-size))" />
			<MudSkeleton Width="70%"
						 Height="calc(var(--mud-typography-body2-lineheight) * var(--mud-typography-body2-size))" />
		}
		@if (!IsLoading && _model is not null && _model.IsSuccess)
		{
			<MudText Typo="Typo.h1">@_model.Username</MudText>
			@if (_model.LastLoginTime is null)
			{
				<MudText Typo="Typo.body2">
					Пользователь не входил в систему
				</MudText>
			}
			else
			{
				<MudText Typo="Typo.body2">
					Последний вход: @_model.LastLoginTime?.ToLocalTime().ToString("dd/MM/yyyy HH:mm")
				</MudText>
			}
		}
		@if (!IsLoading && (_model is null || !_model.IsSuccess))
		{
			<MudText Typo="Typo.subtitle1">Не удалось получить пользователя</MudText>
		}
	</MudStack>
	<MudSpacer />
	@if (!IsLoading && _model is not null && _model.IsSuccess)
	{
		<MudStack Row="true">
			<SubscriptionErrorComponent Show="!_subscribing && (_subscriptionResult is null || !_subscriptionResult.IsSuccess)"
										RetrySubscribe="SubscribeAsync" />
			<LockedStatusComponent LockedoutEnabled="@_model.LockedoutEnabled" />
			<AuthorizeView Policy="@_contextMenuAuthPolicyString"
						   Resource="new PolicyRequirementResource(OrganizationId, UserId)"
						   Context="menuContext">
				<MudMenu Icon="@Icons.Material.Filled.MoreVert"
						 Color="Color.Primary"
						 Variant="Variant.Outlined">
					@if (!_model.IsSystem)
					{
						<AuthorizeView Policy="@AppPermissions.Main.AccessControl.Users.Update.GetEnumPermissionString()"
									   Resource="new PolicyRequirementResource(OrganizationId, UserId)"
									   Context="editContext">
							<MudMenuItem OnClick="Edit"
										 Icon="@Icons.Material.Outlined.Edit">Редактировать</MudMenuItem>
							@if (!_sameUser)
							{
								<MudMenuItem OnClick="ChangeUserPassword"
											 Icon="@Icons.Material.Filled.Login"
											 IconColor="Color.Warning">Смена пароля пользователя</MudMenuItem>
							}
						</AuthorizeView>
						@if (!_sameUser)
						{
							@if (_model.LockedoutEnabled)
							{
								<AuthorizeView Policy="@AppPermissions.Main.AccessControl.Users.Unlock.GetEnumPermissionString()"
											   Resource="new PolicyRequirementResource(OrganizationId, UserId)"
											   Context="unlockContext">
									<MudMenuItem OnClick="UnLockAsync"
												 Icon="@Icons.Material.Filled.LockOpen"
												 IconColor="Color.Warning">Разблокировать</MudMenuItem>
								</AuthorizeView>
							}
							else
							{
								<AuthorizeView Policy="@AppPermissions.Main.AccessControl.Users.Lock.GetEnumPermissionString()"
											   Resource="new PolicyRequirementResource(OrganizationId, UserId)"
											   Context="lockContext">
									<MudMenuItem OnClick="LockAsync"
												 Icon="@Icons.Material.Filled.Lock"
												 IconColor="Color.Warning">Заблокировать</MudMenuItem>
								</AuthorizeView>
							}
						}
					}
					<AuthorizeView Policy="@AppPermissions.Main.AccessControl.Users.ForceChangePassword.GetEnumPermissionString()"
								   Resource="new PolicyRequirementResource(OrganizationId, UserId)"
								   Context="forceChangePasswordContext">
						<MudMenuItem OnClick="LoginpasswordChange"
									 Icon="@Icons.Material.Filled.Login"
									 IconColor="Color.Warning">Смена пароля при входе</MudMenuItem>
					</AuthorizeView>
					@if (!_model.IsSystem)
					{
						<AuthorizeView Policy="@AppPermissions.Main.AccessControl.Users.Delete.GetEnumPermissionString()"
									   Resource="new PolicyRequirementResource(OrganizationId, UserId)"
									   Context="deleteContext">
							<MudDivider Class="my-4" />
							<MudMenuItem OnClick="Delete"
										 Icon="@Icons.Material.Outlined.Delete"
										 IconColor="Color.Warning">Удалить</MudMenuItem>
						</AuthorizeView>
					}
				</MudMenu>
			</AuthorizeView>
		</MudStack>
	}
</DrawerHeader>
<div class="d_contents">
	@if (!IsLoading && _model is not null && _model.IsSuccess)
	{
		<MudStack Spacing="8">
			<FormSectionComponent Title="Описание пользователя"
								  Subtitle="Настройки, которые влияют только на восприятие человеком">
				<MudTextField Value="_model.Username"
							  Disabled="true"
							  ReadOnly="true"
							  InputType="InputType.Text"
							  Label="Логин" />
			</FormSectionComponent>

			<FormSectionComponent Title="Параметры пользователя"
								  Subtitle="Данные настройки важны для работы в системе">
				@if (_model.IsSystem)
				{
					<MudStack AlignItems="AlignItems.Center"
							  Justify="Justify.Center"
							  Class="pa-8">
						<MudIcon Icon="@Icons.Material.Filled.WarningAmber"
								 Style="font-size: 8rem;" />
						<MudText Typo="Typo.body1">Системный пользователь</MudText>
						<MudText Typo="Typo.subtitle1">Доступны все элементы</MudText>
					</MudStack>
				}
				else
				{
					<MudText Typo="Typo.subtitle1"
							 Class="pb-2"><b>Роли пользователя</b></MudText>
					<MudStack Class="list">
						@foreach (var item in _model.Roles)
						{
							<MudPaper Class="mud-width-full"
									  Outlined="true"
									  @key="item">
								<MudChip T="string"
										 Variant="Variant.Outlined"
										 Class="item"
										 Color="Color.Default">@item.Name</MudChip>
							</MudPaper>
						}

						@if (_model.Roles.Count == 0)
						{
							<MudPaper Outlined="true"
									  Class="mud-width-full">
								<MudText Color="Color.Info">Пользователь не имеет назначенных ролей в данной организации
								</MudText>
							</MudPaper>
						}

						<IsOwnerComponent IsOwner="_model.IsOwner" />
					</MudStack>
				}
			</FormSectionComponent>
		</MudStack>
	}
	@if (IsLoading)
	{
		<UserFormLoadingComponent />
	}
	@if (!IsLoading && (_model is null || !_model.IsSuccess))
	{
		<NoUserFoundComponent />
	}
</div>

<DrawerActions>
	<MudSpacer />
	<MudButton OnClick="CancelAsync"
			   Variant="Variant.Outlined"
			   StartIcon="@Icons.Material.Outlined.Close">Закрыть</MudButton>
	@if (IsLoading)
	{
		<MudSkeleton Width="150px"
					 Height="36.5px" />
	}
	else
	{
		@if (_model is not null && !_model.IsSystem)
		{
			<AuthorizeView Policy="@AppPermissions.Main.AccessControl.Users.Update.GetEnumPermissionString()"
						   Resource="new PolicyRequirementResource(OrganizationId, UserId)"
						   Context="editContext">
				<MudButton OnClick="Edit"
						   Color="Color.Secondary"
						   Variant="Variant.Outlined">Редактировать</MudButton>
			</AuthorizeView>
		}
	}
</DrawerActions>