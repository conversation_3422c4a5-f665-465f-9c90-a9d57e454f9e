using System.Reactive;
using Microsoft.AspNetCore.Components;
using Teslametrics.App.Web.Components.Drawer;
using Teslametrics.App.Web.Extensions;

namespace Teslametrics.App.Web.Features.Main.CameraPublicAccess.Drawer.View;

public partial class ViewComponent
{
    private bool _subscribing;
    private GetCameraPublicAccessUseCase.Response? _model;
    private SubscribeCameraPublicAccessUseCase.Response? _subscriptionResult;

    #region Parameters
    [Parameter]
    [EditorRequired]
    public Guid OrganizationId { get; set; }

    [Parameter]
    [EditorRequired]
    public Guid CameraId { get; set; }

    [Parameter]
    [EditorRequired]
    public Guid AccessId { get; set; }

    [CascadingParameter(Name = DrawerConsts.InstanceName)]
    private DrawerComponent Drawer { get; set; } = null!;
    #endregion

    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();

        _model = null;
        await FetchAsync();
        await SubscribeAsync();
    }

    protected async Task FetchAsync()
    {
        try
        {
            await SetLoadingAsync(true);
            _model = await ScopeFactory.MediatorSend(new GetCameraPublicAccessUseCase.Query(AccessId));
            await SetLoadingAsync(false);
            switch (_model.Result)
            {
                case GetCameraPublicAccessUseCase.Result.Success:
                    break;
                case GetCameraPublicAccessUseCase.Result.ValidationError:
                    Snackbar.Add("Ошибка валидации данных", MudBlazor.Severity.Error);
                    break;
                case GetCameraPublicAccessUseCase.Result.Unknown:
                default:
                    throw new Exception($"Unexpected result in {nameof(GetCameraPublicAccessUseCase)}: {_model.Result}");
            }
        }
        catch (Exception ex)
        {
            await SetLoadingAsync(false);
            Snackbar.Add("Не удалось получить камеру из-за непредвиденной ошибки. Повторите попытку", MudBlazor.Severity.Error);
            Logger.LogError(ex, ex.Message);
        }
    }
    private async Task SubscribeAsync()
    {
        try
        {
            Unsubscribe();

            await SetSubscribingAsync(true);
            _subscriptionResult = await ScopeFactory.MediatorSend(new SubscribeCameraPublicAccessUseCase.Request(Observer.Create<object>(OnAppEventHandler, OnError), CameraId, AccessId));
            await SetSubscribingAsync(false);
            switch (_subscriptionResult.Result)
            {
                case SubscribeCameraPublicAccessUseCase.Result.Success:
                    CompositeDisposable.Add(_subscriptionResult.Subscription!);
                    break;
                case SubscribeCameraPublicAccessUseCase.Result.ValidationError:
                    Snackbar.Add("Ошибка валидации при подписке на события", MudBlazor.Severity.Error);
                    break;
                case SubscribeCameraPublicAccessUseCase.Result.Unknown:
                default:
                    throw new Exception($"Unexpected result in {nameof(SubscribeCameraPublicAccessUseCase)}: {_subscriptionResult.Result}");
            }
        }
        catch (Exception ex)
        {
            await SetSubscribingAsync(false);
            Snackbar.Add("Не удалось получить подписку на события камеры из-за непредвиденной ошибки. Повторите попытку", MudBlazor.Severity.Error);
            Logger.LogError(ex, ex.Message);
        }
    }
    private void Unsubscribe()
    {
        if (_subscriptionResult?.Subscription is not null)
        {
            CompositeDisposable.Remove(_subscriptionResult.Subscription);
            _subscriptionResult.Subscription.Dispose();
        }
    }

    protected Task SetSubscribingAsync(bool isLoading = true) => UpdateViewAsync(() =>
    {
        _subscribing = isLoading;
    });

    #region [Action]
    private Task RefreshAsync() => FetchAsync();

    private Task CancelAsync() => Drawer.HideAsync();
    #endregion


    #region [Event Handlers]
    private async void OnAppEventHandler(object appEvent)
    {
        switch (appEvent)
        {
            case SubscribeCameraPublicAccessUseCase.UpdatedEvent updatedEto:
                await FetchAsync();
                await UpdateViewAsync();
                break;

            case SubscribeCameraPublicAccessUseCase.AccessReissuedEvent reissuedEto:
                AccessId = reissuedEto.Id;
                await FetchAsync();
                await SubscribeAsync();
                await UpdateViewAsync();
                break;

            case SubscribeCameraPublicAccessUseCase.AccessDeletedEvent deletedEto:
                Snackbar.Add("Просматриваемый вами публичный доступ к камере был удалён", MudBlazor.Severity.Warning);
                await Drawer.HideAsync();
                break;

            case SubscribeCameraPublicAccessUseCase.CameraDeletedEvent deletedEto:
                Snackbar.Add("Просматриваемая вами камера была удалена", MudBlazor.Severity.Warning);
                await Drawer.HideAsync();
                break;

            default:
                Snackbar.Add("Было получено непредвиденное событие.", MudBlazor.Severity.Warning);
                await FetchAsync();
                await UpdateViewAsync();
                Logger.LogWarning($"Unexpected event in {nameof(SubscribeCameraPublicAccessUseCase)}: {nameof(appEvent)}");
                break;
        }
    }

    private void OnError(Exception exc)
    {
        Snackbar.Add("Ошибка при подписке на события", MudBlazor.Severity.Error);
        Logger.LogError(exc, exc.Message);
    }
    #endregion [Event Handlers]
}
