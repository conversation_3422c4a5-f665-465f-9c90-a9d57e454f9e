using MudBlazor;
using System.Reactive;
using System.Text;
using Teslametrics.App.Web.Events.Cameras;
using Teslametrics.App.Web.Extensions;

namespace Teslametrics.App.Web.Features.Main.Cameras.DeleteFolderDialog;

public partial class DeleteFolderDialog
{
	private bool _subscribing;
	private string _input = string.Empty;
	private StringBuilder _confirmationMessage => new StringBuilder("Удалить ").AppendIf(_model?.Name ?? string.Empty, _model is not null);
	private DialogOptions _dialogOptions = new() { CloseOnEscapeKey = true, FullWidth = true, MaxWidth = MaxWidth.Medium, NoHeader = true }; // https://github.com/MudBlazor/MudBlazor/issues/1043
	private bool _isVisible;
	private Guid _id;
	private Guid _organizationId;

	private SubscribeFolderUseCase.Response? _subscriptionResult;
	private GetFolderUseCase.Response? _model;

	protected override void OnInitialized()
	{
		CompositeDisposable.Add(EventSystem.Subscribe<CameraGroupDeleteEto>(OnDeleteHandler));

		base.OnInitialized();
	}

	private async Task FetchAsync(Guid id)
	{
		if (IsLoading) return;

		try
		{
			await SetLoadingAsync();
			_id = id;
			_model = await ScopeFactory.MediatorSend(new GetFolderUseCase.Query(_id));
			switch (_model.Result)
			{
				case GetFolderUseCase.Result.Success:
					await SubscribeAsync();
					break;
				case GetFolderUseCase.Result.ValidationError:
					Snackbar.Add("Ошибка валидации данных", Severity.Error);
					break;
				case GetFolderUseCase.Result.FolderNotFound:
					Snackbar.Add("Запрошенная директория не найдена", Severity.Error);
					break;
				case GetFolderUseCase.Result.Unknown:
				default:
					throw new Exception($"Unexpected result in {nameof(GetFolderUseCase)}: {_model.Result}");
			}
		}
		catch (Exception ex)
		{
			Snackbar.Add("Не удалось получить директорию из-за непредвиденной ошибки. Повторите попытку", Severity.Error);
			Logger.LogError(ex, ex.Message);
		}
		finally
		{
			await SetLoadingAsync(false);
		}
	}

	private Task CancelAsync() => UpdateViewAsync(() =>
	{
		_isVisible = false;
		_model = null;
		Unsubscribe();
	});

	private Task RefreshAsync() => FetchAsync(_id);

	private async Task SubmitAsync()
	{
		if (IsLoading || _model is null) return;

		try
		{
			await SetLoadingAsync();

			var result = await ScopeFactory.MediatorSend(new DeleteFolderUseCase.Command(_model.Id));
			if (result.IsSuccess)
			{
				Snackbar.Add("Директория успешно удалена.", Severity.Success);
				await CancelAsync();
				return;
			}

			switch (result.Result)
			{
				case DeleteFolderUseCase.Result.ValidationError:
					Snackbar.Add("Ошибка валидации данных", Severity.Error);
					break;
				case DeleteFolderUseCase.Result.CannotDeleteNotEmptyFolder:
					Snackbar.Add("Нельзя удалить не пустую директорию", Severity.Error);
					break;
				case DeleteFolderUseCase.Result.Unknown:
				default:
					throw new Exception($"Unexpected result in {nameof(DeleteFolderUseCase)}: {result.Result}");
			}
		}
		catch (Exception ex)
		{
			Snackbar.Add("Не удалось получить выбранную роль. Повторите попытку", Severity.Error);
			Logger.LogError(ex, ex.Message);
		}
		finally
		{
			await SetLoadingAsync(false);
		}
	}

	private async Task SubscribeAsync()
	{
		try
		{
			if (_model is null) return;
			await SetSubscribingAsync(true);
			Unsubscribe();
			_subscriptionResult = await ScopeFactory.MediatorSend(new SubscribeFolderUseCase.Request(Observer.Create<object>(OnAppEventHandler, OnError), _model.Id));

			switch (_subscriptionResult.Result)
			{
				case SubscribeFolderUseCase.Result.Success:
					CompositeDisposable.Add(_subscriptionResult.Subscription!);
					break;
				case SubscribeFolderUseCase.Result.ValidationError:
					Snackbar.Add("Ошибка валидации при подписке на события", Severity.Error);
					break;

				case SubscribeFolderUseCase.Result.Unknown:
				default:
					throw new Exception($"Unexpected result in {nameof(SubscribeFolderUseCase)}: {_subscriptionResult.Result}");
			}
		}
		catch (Exception exc)
		{
			Snackbar.Add("Не удалось получить подписку на события с директорией. Повторите попытку", Severity.Error);
			Logger.LogError(exc, exc.Message);
		}
		finally
		{
			await SetSubscribingAsync(false);
		}
	}
	private void Unsubscribe()
	{
		if (_subscriptionResult is not null && _subscriptionResult.Subscription is not null)
		{
			CompositeDisposable.Remove(_subscriptionResult.Subscription);
			_subscriptionResult.Subscription.Dispose();
		}
	}

	private void VisibilityChanged(bool isVisible)
	{
		_isVisible = isVisible;
		if (!isVisible)
		{
			Unsubscribe();
			_model = null;
		}
	}

	protected Task SetSubscribingAsync(bool isLoading = true) => UpdateViewAsync(() =>
	{
		_subscribing = isLoading;
	});

	#region [Event Handlers]
	private async void OnAppEventHandler(object appEvent)
	{
		switch (appEvent)
		{
			case SubscribeFolderUseCase.UpdatedEvent updatedEto:
				await FetchAsync(updatedEto.Id);
				await UpdateViewAsync();
				break;

			case SubscribeFolderUseCase.DeletedEvent deleteEto:
				Snackbar.Add("Директория была удалена.", Severity.Error);
				await CancelAsync();
				break;

			default:
				break;
		}
	}

	private void OnError(Exception exc)
	{
		Logger.LogError(exc, exc.Message);
		Snackbar.Add("Ошибка валидации при подписке на события", Severity.Error);
	}

	private async Task OnDeleteHandler(CameraGroupDeleteEto eto)
	{
		if (_model is not null && eto.GroupId == _model.Id)
		{
			return;
		}

		await UpdateViewAsync(() =>
		{
			_input = string.Empty;
			_isVisible = true;
			_organizationId = eto.OrganizationId;
			_id = eto.GroupId;
		});

		await FetchAsync(eto.GroupId);
	}
	#endregion
}
