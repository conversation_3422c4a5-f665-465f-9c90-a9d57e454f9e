using MudBlazor;
using System;
using System.Collections.Concurrent;
using System.Reactive;
using System.Security.Claims;
using Teslametrics.App.Web.Extensions;

namespace Teslametrics.App.Web.Services.UserSession;

public class DefaultSessionProvider : ISessionProvider, IDisposable
{
	private object _locker = new();
	private bool _disposedValue;
	private IDisposable? _permissionsUpdateSubscriptions;

	private readonly IServiceScopeFactory _serviceScopeFactory;

	//private readonly Dictionary<Guid, HashSet<IObserver<Guid>>> _publicObserversByUserId;
	private readonly ConcurrentDictionary<Guid, HashSet<IObserver<Guid>>> _publicObserversByUserId;
	private readonly ConcurrentDictionary<Guid, Session> _sessions; // UserId, Sesssion
	private readonly ILogger<DefaultSessionProvider> _logger;

	public IEnumerable<Guid> Users => _sessions.Keys;

	public DefaultSessionProvider(IServiceScopeFactory serviceScopeFactory, ILogger<DefaultSessionProvider> logger)
	{
		_sessions = [];
		_publicObserversByUserId = [];
		_serviceScopeFactory = serviceScopeFactory;
		_logger = logger;
	}

	public async Task InitializeAsync()
	{
		var response = await _serviceScopeFactory.MediatorSend(new SubscribeSessionFunction.Request(Observer.Create<object>(OnAppEventHandler, OnError)));
		if (!response.IsSuccess || response.Subscription is null)
			throw new ApplicationException($"Failed subscribe to user claims update events. Unexpected result in {nameof(SubscribeSessionFunction)}: {response.Result}");

		_permissionsUpdateSubscriptions = response.Subscription;
	}

	public SessionResult CreateSession(ClaimsPrincipal principal)
	{
		var userIdString = principal.Claims.Single(x => x.Type == ClaimTypes.NameIdentifier).Value;
		if (string.IsNullOrWhiteSpace(userIdString))
		{
			return new SessionResult(Guid.Empty, false);
		}
		else
		{
			var userId = Guid.Parse(userIdString);
			if (_sessions.TryGetValue(userId, out Session? session))
			{
				if (session != null)
				{
					return new SessionResult(session.Id, true);
				}
				else
				{
					return new SessionResult(Guid.Empty, false);
				}
			}

			session = new Session(principal);
			if (_sessions.TryAdd(userId, session))
			{
				return new SessionResult(session.Id, true);
			}
			else
			{
				return new SessionResult(Guid.Empty, false);
			}
		}
	}

	public Session? GetSessionBySessionId(Guid sessionId)
	{
		return _sessions.Values.SingleOrDefault(x => x.Id == sessionId);
	}

	public Session? GetSessionByUserId(Guid userId)
	{
		_sessions.TryGetValue(userId, out Session? session);
		return session;
	}

	public SessionResult KillSession(Guid sessionId)
	{
		var session = _sessions.Values.SingleOrDefault(x => x.Id == sessionId);
		if (session is null)
		{
			return new SessionResult(sessionId, false);
		}

		var userId = session.ClaimsPrincipal.GetUserId()!;
		if (_sessions.TryRemove(userId.Value, out Session? removedSession))
		{
			Notify(userId.Value);
		}
		//var userToNotify = _sessions.Where(x => x.Id == sessionId).Select(x => x.ClaimsPrincipal.GetUserId()!.Value).ToList();
		//var result = _sessions.RemoveAll(x => x.Id == sessionId);
		//if (result > 0)
		//{
		//	foreach (var userId in usersToNotify)
		//	{
		//		Notify(userId);
		//	}

		//	return new SessionResult(sessionId, true);
		//}

		return new SessionResult(sessionId, false);
	}

	public SessionResult UpdateSession(ClaimsPrincipal principal)
	{
		var userId = principal.GetUserId()!;

		if (_sessions.TryRemove(userId.Value, out Session? removedSession))
		{
			var newSession = new Session(removedSession.Id, principal);
			_sessions.TryAdd(userId.Value, newSession);
			Notify(userId.Value);

			return new SessionResult(newSession.Id, false);
		}

		return new SessionResult(Guid.Empty, false);
		//var session = _sessions.FirstOrDefault(x => x.ClaimsPrincipal.GetUserId() == principal.GetUserId());
		//if (session is null)
		//	return new SessionResult(Guid.Empty, false);

		//_sessions.Remove(session);

		//var newSession = new Session(session.Id, principal);
		//_sessions.Add(newSession);

		//Notify(principal.GetUserId()!.Value);

		//return new SessionResult(newSession.Id, false);
	}

	public IDisposable Subscribe(Guid userId, IObserver<Guid> observer)
	{
		try
		{
			lock (_locker)
			{
				if (!_publicObserversByUserId.TryGetValue(userId, out HashSet<IObserver<Guid>>? value))
				{
					value = ([]);
					_publicObserversByUserId[userId] = value;
				}

				value.Add(observer);
			}

			return new Unsubscriber(_publicObserversByUserId, userId, observer, _locker);
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, ex.Message);
			return new Unsubscriber(_publicObserversByUserId, userId, observer, _locker);
		}
	}

	public void Dispose()
	{
		// Do not change this code. Put cleanup code in 'Dispose(bool disposing)' method
		Dispose(disposing: true);
		GC.SuppressFinalize(this);
	}

	protected virtual void Dispose(bool disposing)
	{
		if (!_disposedValue)
		{
			if (disposing)
			{
				_permissionsUpdateSubscriptions?.Dispose();
				_publicObserversByUserId.Clear(); // не уверен, что так правильно
			}

			_disposedValue = true;
		}
	}

	private void Notify(Guid userId)
	{
		if (!_publicObserversByUserId.TryGetValue(userId, out HashSet<IObserver<Guid>>? value))
		{
			return;
		}

		lock (_locker)
		{
			var notifyIng = value.ToList();
			foreach (var observer in notifyIng)
			{
				observer.OnNext(userId);
			}
		}
	}

	private class Unsubscriber(ConcurrentDictionary<Guid, HashSet<IObserver<Guid>>> observersById, Guid id, IObserver<Guid> observer, object locker) : IDisposable
	{
		private readonly object _locker = locker;
		private readonly ConcurrentDictionary<Guid, HashSet<IObserver<Guid>>> _observersById = observersById;
		private readonly Guid _id = id;
		private readonly IObserver<Guid> _observer = observer;

		public void Dispose()
		{
			if (_observersById.TryGetValue(_id, out HashSet<IObserver<Guid>>? value))
			{
				lock (_locker)
				{
					if (value is not null && value.Contains(_observer))
					{
						value.Remove(_observer);
					}
				}
			}
		}
	}


	private async void OnAppEventHandler(object appEvent)
	{
		switch (appEvent)
		{
			case SubscribeSessionFunction.UpdatedEvent updatedEto:
				if (Users.Contains(updatedEto.UserId))
				{
					var claimsResponse = await _serviceScopeFactory.MediatorSend(new GetUserClaimsFunction.Command(updatedEto.UserId));
					switch (claimsResponse.Result)
					{
						case GetUserClaimsFunction.Result.Success:
							ClaimsPrincipal? userPrincipal = new(new ClaimsIdentity(claimsResponse.Permissions.Select(x => new Claim(x.Item1, x.Item2)), "Custom"));
							UpdateSession(userPrincipal);
							break;
						case GetUserClaimsFunction.Result.ValidationError:
						case GetUserClaimsFunction.Result.UserLockedout:
						case GetUserClaimsFunction.Result.UserNotFound:
						default:
							if (_sessions.TryRemove(updatedEto.UserId, out Session? session))
							{
								Notify(updatedEto.UserId);
							}
							//_sessions.TryRemove(session => session.ClaimsPrincipal.GetUserId() == updatedEto.UserId);
							//Notify(updatedEto.UserId);
							_logger.LogWarning($"Unexpected result in {nameof(SubscribeSessionFunction)}: {appEvent.GetType().ToString()}");
							break;
					}
				}
				break;

			case SubscribeSessionFunction.LockedEvent lockedEto:
				if (Users.Contains(lockedEto.UserId))
				{
					if (_sessions.TryRemove(lockedEto.UserId, out Session? session))
					{
						Notify(lockedEto.UserId);
					}
					//_sessions.RemoveAll(session => session.ClaimsPrincipal.GetUserId() == lockedEto.UserId);
					//Notify(lockedEto.UserId);
				}
				break;

			case SubscribeSessionFunction.DeletedEvent deletedEto:
				if (Users.Contains(deletedEto.UserId))
				{
					if (_sessions.TryRemove(deletedEto.UserId, out Session? session))
					{
						Notify(deletedEto.UserId);
					}
					//_sessions.RemoveAll(session => session.ClaimsPrincipal.GetUserId() == deletedEto.UserId);
					//Notify(deletedEto.UserId);
				}
				break;

			default:
				_logger.LogWarning($"Unexpected result in {nameof(SubscribeSessionFunction)}: {appEvent.GetType().ToString()}");
				break;
		}
	}

	private void OnError(Exception exc)
	{
		throw exc;
	}

	// TODO: override finalizer only if 'Dispose(bool disposing)' has code to free unmanaged resources
	// ~DefaultSessionProvider()
	// {
	//     // Do not change this code. Put cleanup code in 'Dispose(bool disposing)' method
	//     Dispose(disposing: false);
	// }
}
