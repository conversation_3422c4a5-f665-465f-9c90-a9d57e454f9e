﻿<div>
    <MudPaper Class="pa-1 d-flex align-center gap-2 quick-range"
              Elevation="0">

        <!--  Быстрые пресеты  -->
        <MudToggleGroup T="FilterDate"
                        Value="@_quick"
                        Color="Color.Default"
                        Outlined="false"
                        Delimiters="false"
                        Class="toggle_group"
                        ValueChanged="OnQuickChanged">
            <MudToggleItem T="FilterDate"
                           Value="@FilterDate.Today">Сегодня</MudToggleItem>
            <MudToggleItem T="FilterDate"
                           Value="@FilterDate.Yesterday">Вчера</MudToggleItem>
            <MudToggleItem T="FilterDate"
                           Value="@FilterDate.Week">Неделя</MudToggleItem>
            <MudToggleItem T="FilterDate"
                           Value="@FilterDate.Month">Месяц</MudToggleItem>
        </MudToggleGroup>

        <!--  Произвольный диапазон  -->
        <MudMenu Dense="true"
                 AnchorOrigin="Origin.TopCenter"
                 TransformOrigin="Origin.TopCenter">
            <ActivatorContent>
                <MudButton Variant="Variant.Text"
                           Class="@(_quick == FilterDate.Custom ? "range-btn active" : "range-btn")">
                    @_rangeText
                    <MudIcon Icon="@Icons.Material.Filled.KeyboardArrowDown"
                             Size="Size.Small"
                             Class="ml-1" />
                </MudButton>
            </ActivatorContent>

            <ChildContent>
                <MudDateRangePicker DateRange="_range"
                                    DisplayMonths="2"
                                    DateRangeChanged="OnDateRangeChanged"
                                    PickerVariant="PickerVariant.Static" />
            </ChildContent>
        </MudMenu>
    </MudPaper>
</div>