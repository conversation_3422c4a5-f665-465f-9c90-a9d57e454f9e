using System.Reactive;
using Dapper;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using MudBlazor;
using Teslametrics.App.Web.Domain.AccessControl;
using Teslametrics.App.Web.Extensions;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Roles.Drawer.Edit.CameraViews;

public partial class CameraViewsConcretePermissionsComponent
{
    private bool _disposedValue;

    private bool _subscribing;
    private DateTime _lastRefreshTime = DateTime.Now;
    private string _searchString = string.Empty;
    private IEnumerable<AppPermissions.Main.CameraViews> _viewValues = [];

    private IEnumerable<ResourcePermission> _wildcardPermissions => Selected.Where(x => x.ResourceId.IsWildcard);

    private SubscribeCameraViewListUseCase.Response? _subscriptionResult;
    private GetCameraViewListUseCase.Response? _response;

    [Parameter]
    public Guid OrganizationId { get; set; }

    [Parameter]
    [EditorRequired]
    public IEnumerable<ResourcePermission> Selected { get; set; } = [];

    [Parameter]
    public EventCallback<IEnumerable<ResourcePermission>> SelectedChanged { get; set; }

    [Parameter]
    public bool ShowAdminTaggedValues { get; set; }

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();

        await FetchAsync();
        await SubscribeAsync();

        AuthenticationStateProvider.AuthenticationStateChanged += OnAuthenticationStateChanged;
    }

    protected override void Dispose(bool disposing)
    {
        if (!_disposedValue)
        {
            if (disposing)
            {
                AuthenticationStateProvider.AuthenticationStateChanged -= OnAuthenticationStateChanged;
            }

            _disposedValue = true;
        }

        // Вызов базового метода Dispose
        base.Dispose(disposing);
    }

    protected override void OnParametersSet()
    {
        base.OnParametersSet();
        List<AppPermissions.Main.CameraViews> viewEnums = [];
        if (ShowAdminTaggedValues)
        {
            viewEnums = ((AppPermissions.Main.CameraViews[])Enum.GetValues(typeof(AppPermissions.Main.CameraViews)))
                .Where(item => item != AppPermissions.Main.CameraViews.Invalid && !_wildcardPermissions.Any(x => x.Permission.Value == item.GetEnumPermissionString()))
                .AsList();
        }
        else
        {
            viewEnums = ((AppPermissions.Main.CameraViews[])Enum.GetValues(typeof(AppPermissions.Main.CameraViews)))
                .Where(item => item != AppPermissions.Main.CameraViews.Invalid && !item.IsAdminTagged() && !_wildcardPermissions.Any(x => x.Permission.Value == item.GetEnumPermissionString()))
                .AsList();
        }

        viewEnums.Remove(AppPermissions.Main.CameraViews.Create);
        viewEnums.Remove(AppPermissions.Main.CameraViews.Invalid);

        _viewValues = viewEnums;
    }

    private async Task FetchAsync()
    {
        if (IsLoading) return;

        try
        {
            await SetLoadingAsync(true);
            var userId = await GetCurrentUserIdAsync() ?? throw new UnauthorizedAccessException();
            _response = await ScopeFactory.MediatorSend(new GetCameraViewListUseCase.Query(OrganizationId, userId));
            switch (_response.Result)
            {
                case GetCameraViewListUseCase.Result.Success:
                    _lastRefreshTime = DateTime.Now;
                    break;
                case GetCameraViewListUseCase.Result.ValidationError:
                    Snackbar.Add("Не удалось получить список видов организации. Повторите попытку", Severity.Error);
                    break;
                case GetCameraViewListUseCase.Result.Unknown:
                default:
                    Snackbar.Add("Не удалось получить список видов организации. Повторите попытку", Severity.Error);
                    break;
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add("Не удалось получить список видов организаций. Повторите попытку", Severity.Error);
            Logger.LogError(ex, ex.Message);
        }
        finally
        {
            await SetLoadingAsync(false);
        }
    }
    private async Task SubscribeAsync()
    {
        try
        {
            Unsubscribe();

            await SetSubscribingAsync(true);
            _subscriptionResult = await ScopeFactory.MediatorSend(new SubscribeCameraViewListUseCase.Request(Observer.Create<object>(OnAppEventHandler, OnError), OrganizationId));
            await SetSubscribingAsync(false);
            switch (_subscriptionResult.Result)
            {
                case SubscribeCameraViewListUseCase.Result.Success:
                    CompositeDisposable.Add(_subscriptionResult.Subscription!);
                    break;
                case SubscribeCameraViewListUseCase.Result.ValidationError:
                    Snackbar.Add("Ошибка валидации при подписке на события", Severity.Error);
                    break;
                case SubscribeCameraViewListUseCase.Result.Unknown:
                default:
                    throw new Exception($"Unexpected result in {nameof(SubscribeCameraViewListUseCase)}: {_subscriptionResult.Result}");
            }
        }
        catch (Exception ex)
        {
            await SetSubscribingAsync(false);
            Snackbar.Add("Не удалось получить подписку на события дерева из-за непредвиденной ошибки. Повторите попытку", Severity.Error);
            Logger.LogError(ex, ex.Message);
        }
    }
    private void Unsubscribe()
    {
        if (_subscriptionResult?.Subscription is not null)
        {
            CompositeDisposable.Remove(_subscriptionResult.Subscription);
            _subscriptionResult.Subscription.Dispose();
        }
    }
    private Task SetSubscribingAsync(bool isLoading = true) => UpdateViewAsync(() =>
    {
        _subscribing = isLoading;
    });

    #region [Actions]
    private Task RefreshAsync() => FetchAsync();

    private async Task OnChangedHandler(bool isChecked, Enum value, GetCameraViewListUseCase.Response.Item presenter)
    {
        string permissionName = value.GetEnumPermissionString();
        if (isChecked)
        {
            if (!Selected.Any(x => x.Permission.Value == permissionName && x.ResourceId.Value == presenter.Id))
            {
                Selected = Selected.Append(new ResourcePermission(new(presenter.Id), new(permissionName)));
            }

            // Handle inherited flags for cameras
            if (value is AppPermissions.Main.CameraViews cameraFlags)
            {
                foreach (AppPermissions.Main.CameraViews flag in Enum.GetValues(typeof(AppPermissions.Main.CameraViews)))
                {
                    if (cameraFlags.HasFlag(flag) && flag != AppPermissions.Main.CameraViews.Invalid)
                    {
                        string inheritedPermissionName = flag.GetEnumPermissionString();
                        if (!Selected.Any(x => x.Permission.Value == inheritedPermissionName && (x.ResourceId.Value == presenter.Id || x.ResourceId == ResourceId.Wildcard)))
                        {
                            Selected = Selected.Append(new ResourcePermission(new(presenter.Id), new(inheritedPermissionName)));
                        }
                    }
                }
            }
        }
        else
        {
            var toRemove = Selected.Where(x => x.Permission.Value == permissionName && x.ResourceId.Value == presenter.Id);
            Selected = Selected.Except(toRemove);
        }

        if (SelectedChanged.HasDelegate)
            await SelectedChanged.InvokeAsync(Selected);
    }
    #endregion [Actions]

    #region [Event Hanndlers]
    private async void OnAppEventHandler(object appEvent)
    {
        await RefreshAsync();
        await UpdateViewAsync();
    }

    private void OnError(Exception exc)
    {
        Snackbar.Add("Ошибка при подписке на события", Severity.Error);
        Logger.LogError(exc, exc.Message);
    }

    private async void OnAuthenticationStateChanged(Task<AuthenticationState> authState)
    {
        await FetchAsync();
    }
    #endregion [Event Handlers]

    private bool ContainsPermission(Enum value, Guid resourceId)
    {
        return Selected.Any(x => x.ResourceId.Value == resourceId && x.Permission.Value == value.GetEnumPermissionString());
    }
}
