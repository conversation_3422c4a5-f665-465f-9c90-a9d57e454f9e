﻿@inherits InteractiveBaseComponent

<YandexMaps ReadOnly="true"
            Class="rounded-b overflow-hidden"
            Height="100%"
            Width="100%"
            Markers="@(_response?.Buildings?.Where(x => x.Coordinates is not null).Select(x => x.Coordinates!.Value).ToList() ?? [])"
            SingleMarkerMode="false" />

@code {
    private GetAddressListUseCase.Response? _response;

    [Parameter]
    [EditorRequired]
    public IEnumerable<Guid> Buildings { get; set; } = [];

    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();
        await FetchDataAsync();
    }

    private async Task FetchDataAsync()
    {
        await SetLoadingAsync(true);
        try
        {
            _response = await ScopeFactory.MediatorSend(new GetAddressListUseCase.Query(Buildings.ToList()));
        }
        catch (Exception ex)
        {
            _response = null;
            Logger.LogError(ex, "Ошибка при загрузке списка адресов");
            Snackbar.Add("Произошла ошибка при загрузке списка адресов", Severity.Error);
        }

        await SetLoadingAsync(false);
        if (_response is null) return;
        switch (_response.Result)
        {
            case GetAddressListUseCase.Result.Success:
                break;
            case GetAddressListUseCase.Result.PlanNotFound:
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(MapComponent), nameof(GetAddressListUseCase), _response.Result);
                Snackbar.Add($"Не удалось получить список адресов из-за ошибки: {_response.Result}", MudBlazor.Severity.Error);
                break;
            case GetAddressListUseCase.Result.Unknown:
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(MapComponent), nameof(GetAddressListUseCase));
                Snackbar.Add($"Не удалось получить список адресов из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
                break;
            default:
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(MapComponent), nameof(GetAddressListUseCase), _response.Result);
                Snackbar.Add($"Не удалось получить список адресов из-за ошибки: {_response.Result}", MudBlazor.Severity.Error);
                break;
        }
    }
}
