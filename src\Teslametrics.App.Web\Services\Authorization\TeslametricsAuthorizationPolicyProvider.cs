using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Options;
using Teslametrics.App.Web.Shared;

namespace Teslametrics.App.Web.Services.Authorization;

public class TeslametricsAuthorizationPolicyProvider(IOptions<AuthorizationOptions> options) : IAuthorizationPolicyProvider
{
	// Default provider for fallback
	private readonly DefaultAuthorizationPolicyProvider _fallbackPolicyProvider = new (options);

	public Task<AuthorizationPolicy> GetDefaultPolicyAsync() =>
		_fallbackPolicyProvider.GetDefaultPolicyAsync();

	public Task<AuthorizationPolicy?> GetFallbackPolicyAsync() =>
		_fallbackPolicyProvider.GetFallbackPolicyAsync();

	public Task<AuthorizationPolicy?> GetPolicyAsync(string policyName)
	{
		// Check if the policyName contains your permissions
		// Split the permissions by comma
		var permissions = policyName.Split(',', StringSplitOptions.RemoveEmptyEntries)
									.Select(p => p.Trim())
									.Where(p => Fqdn<AppPermissions>.GetValueType(p) is not null);

		if (permissions.Any())
		{
			var policyBuilder = new AuthorizationPolicyBuilder();
			policyBuilder.Requirements.Add(new PolicyRequirement(permissions));

			if (policyBuilder.Requirements.Count > 0)
			{
				var policy = policyBuilder.Build();
				return Task.FromResult<AuthorizationPolicy?>(policy);
			}
		}

		// Fallback to the default policy provider
		return _fallbackPolicyProvider.GetPolicyAsync(policyName);
	}
}