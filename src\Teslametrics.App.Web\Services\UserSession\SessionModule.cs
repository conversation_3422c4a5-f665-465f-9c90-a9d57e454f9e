namespace Teslametrics.App.Web.Services.UserSession;

public static class SessionModule
{
	public static void Install(IServiceCollection services)
	{
		services.AddSingleton<ISessionProvider, DefaultSessionProvider>();
		services.AddHostedService<SessionInitializer>();
	}

	public static void Install(IServiceCollection services, ISessionProvider sessionProvider)
	{
		services.AddSingleton<ISessionProvider>(sessionProvider);
		services.AddHostedService<SessionInitializer>();
	}
}
