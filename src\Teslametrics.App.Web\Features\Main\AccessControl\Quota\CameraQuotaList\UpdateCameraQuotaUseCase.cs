using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Domain.AccessControl.Organizations;
using Teslametrics.App.Web.Services.Outbox;
using Teslametrics.App.Web.Services.TransactionManager;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Quota.CameraQuotaList;

public static class UpdateCameraQuotaUseCase
{
    public record Command(Guid OrganizationId, Guid QuotaId, string Name, int Limit, int RetentionPeriodDays, int StorageLimitMb) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public Guid Id { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(Guid id)
        {
            Id = id;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Id = Guid.Empty;
            Result = result;
        }
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        OrganizationNotFound,
        LimitCannotBeLowerThanCurrent,
        RetentionPeriodDaysCannotBeLowerThanCurrent,
        StorageLimitMbCannotBeLowerThanCurrent
    }

    public class Validator : AbstractValidator<Command>
    {
        public Validator()
        {
            RuleFor(x => x.OrganizationId).NotEmpty();
            RuleFor(x => x.QuotaId).NotEmpty();
            RuleFor(x => x.Name).NotEmpty();
            RuleFor(x => x.Limit).Must(l => l == -1 || l >= 1);
            RuleFor(c => c.RetentionPeriodDays).InclusiveBetween(1, 365);
            RuleFor(c => c.StorageLimitMb).InclusiveBetween(1, 100_000_000);
        }
    }

    public class Handler : IRequestHandler<Command, Response>
    {
        private readonly IValidator<Command> _validator;
        private readonly IOrganizationRepository _organizationRepository;
        private readonly ITransactionManager _transactionManager;
        private readonly IPublisher _publisher;
        private readonly IOutbox _outbox;

        public Handler(IValidator<Command> validator,
                       IOrganizationRepository organizationRepository,
                       ITransactionManager transactionManager,
                       IPublisher publisher,
                       IOutbox outbox)
        {
            _validator = validator;
            _organizationRepository = organizationRepository;
            _transactionManager = transactionManager;
            _publisher = publisher;
            _outbox = outbox;
        }

        public async Task<Response> Handle(Command request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            using var transaction = await _transactionManager.CreateTransactionAsync();

            var organization = await _organizationRepository.FindAsync(request.OrganizationId, cancellationToken);

            if (organization is null)
            {
                return new Response(Result.OrganizationNotFound);
            }

            // TODO: Пока запрещаем ставить лимит меньше текущего значения
            var quota = organization.CameraQuotas.Single(q => q.Id == request.QuotaId);
            if (request.Limit != -1 && (quota.Limit > request.Limit || quota.Limit == -1))
            {
                return new Response(Result.LimitCannotBeLowerThanCurrent);
            }

            if (quota.RetentionPeriodDays > request.RetentionPeriodDays)
            {
                return new Response(Result.RetentionPeriodDaysCannotBeLowerThanCurrent);
            }

            if (quota.StorageLimitMb > request.StorageLimitMb)
            {
                return new Response(Result.StorageLimitMbCannotBeLowerThanCurrent);
            }

            var events = organization.UpdateCameraQuota(request.QuotaId, request.Name, request.Limit, request.RetentionPeriodDays, request.StorageLimitMb);

            await _organizationRepository.SaveChangesAsync(cancellationToken);

            foreach (var @event in events)
            {
                await _publisher.Publish(@event, cancellationToken);
            }

            await _outbox.AddRangeAsync(events);

            await transaction.CommitAsync();

            return new Response(request.QuotaId);
        }
    }
}