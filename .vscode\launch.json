{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Docker .NET Attach (Preview)",
            "type": "docker",
            "request": "attach",
            "platform": "netCore",
            "sourceFileMap": {
                "/src": "${workspaceFolder}"
            }
        },
        {
            "name": "Windsurf Launch Debug",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build debug",
            "program": "${workspaceFolder}/src/Teslametrics.App.Web/bin/Debug/net8.0/Teslametrics.App.Web.dll",
            "args": [],
            "cwd": "${workspaceFolder}/src/Teslametrics.App.Web",
            "stopAtEntry": false,
            "console": "integratedTerminal",
            "serverReadyAction": {
                "action": "openExternally",
                "pattern": "\\bNow listening on:\\s+(https?://\\S+)"
            },
            "env": {
                "ASPNETCORE_ENVIRONMENT": "Local"
            },
            "sourceFileMap": {
                "/Views": "${workspaceFolder}/Views"
            },
            "pipeTransport": {
                "pipeCwd": "${workspaceFolder}",
                "pipeProgram": "cmd",
                "pipeArgs": ["/c"],
                "debuggerPath": "C:\\\"Windsurf\"\\netcoredbg\\netcoredbg.exe",
                "quoteArgs": true
            }
        },
        {
            "name": "Windsurf Launch Debug (without browser)",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build debug",
            "program": "${workspaceFolder}/src/Teslametrics.App.Web/bin/Debug/net8.0/Teslametrics.App.Web.dll",
            "args": [],
            "cwd": "${workspaceFolder}/src/Teslametrics.App.Web",
            "stopAtEntry": false,
            "console": "integratedTerminal",
            "env": {
                "ASPNETCORE_ENVIRONMENT": "Local"
            },
            "sourceFileMap": {
                "/Views": "${workspaceFolder}/Views"
            },
            "pipeTransport": {
                "pipeCwd": "${workspaceFolder}",
                "pipeProgram": "cmd",
                "pipeArgs": ["/c"],
                "debuggerPath": "C:\\\"Windsurf\"\\netcoredbg\\netcoredbg.exe",
                "quoteArgs": true
            }
        },
        {
            "name": "Windsurf Launch Release",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build release",
            "program": "${workspaceFolder}/src/Teslametrics.App.Web/bin/Release/net8.0/Teslametrics.App.Web.dll",
            "args": [],
            "cwd": "${workspaceFolder}/src/Teslametrics.App.Web",
            "stopAtEntry": false,
            "console": "integratedTerminal",
            "serverReadyAction": {
                "action": "openExternally",
                "pattern": "\\bNow listening on:\\s+(https?://\\S+)"
            },
            "env": {
                "ASPNETCORE_ENVIRONMENT": "Local"
            },
            "sourceFileMap": {
                "/Views": "${workspaceFolder}/Views"
            },
            "pipeTransport": {
                "pipeCwd": "${workspaceFolder}",
                "pipeProgram": "cmd",
                "pipeArgs": ["/c"],
                "debuggerPath": "C:\\\"Windsurf\"\\netcoredbg\\netcoredbg.exe",
                "quoteArgs": true
            }
        },
        {
            "name": " Windsurf Launch Release (without browser)",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build release",
            "program": "${workspaceFolder}/src/Teslametrics.App.Web/bin/Release/net8.0/Teslametrics.App.Web.dll",
            "args": [],
            "cwd": "${workspaceFolder}/src/Teslametrics.App.Web",
            "stopAtEntry": false,
            "console": "integratedTerminal",
            "env": {
                "ASPNETCORE_ENVIRONMENT": "Local"
            },
            "sourceFileMap": {
                "/Views": "${workspaceFolder}/Views"
            },
            "pipeTransport": {
                "pipeCwd": "${workspaceFolder}",
                "pipeProgram": "cmd",
                "pipeArgs": ["/c"],
                "debuggerPath": "C:\\\"Windsurf\"\\netcoredbg\\netcoredbg.exe",
                "quoteArgs": true
            }
        },
        {
            "name": "Launch Debug",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build debug",
            "program": "${workspaceFolder}/src/Teslametrics.App.Web/bin/Debug/net8.0/Teslametrics.App.Web.dll",
            "args": [],
            "cwd": "${workspaceFolder}/src/Teslametrics.App.Web",
            "stopAtEntry": false,
            "console": "integratedTerminal",
            "serverReadyAction": {
                "action": "openExternally",
                "pattern": "\\bNow listening on:\\s+(https?://\\S+)"
            },
            "env": {
                "ASPNETCORE_ENVIRONMENT": "Local"
            },
            "sourceFileMap": {
                "/Views": "${workspaceFolder}/Views"
            }
        },
        {
            "name": "Launch Debug (without browser)",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build debug",
            "program": "${workspaceFolder}/src/Teslametrics.App.Web/bin/Debug/net8.0/Teslametrics.App.Web.dll",
            "args": [],
            "cwd": "${workspaceFolder}/src/Teslametrics.App.Web",
            "stopAtEntry": false,
            "console": "integratedTerminal",
            "env": {
                "ASPNETCORE_ENVIRONMENT": "Local"
            },
            "sourceFileMap": {
                "/Views": "${workspaceFolder}/Views"
            }
        },
        {
            "name": "Launch Release",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build release",
            "program": "${workspaceFolder}/src/Teslametrics.App.Web/bin/Release/net8.0/Teslametrics.App.Web.dll",
            "args": [],
            "cwd": "${workspaceFolder}/src/Teslametrics.App.Web",
            "stopAtEntry": false,
            "console": "integratedTerminal",
            "serverReadyAction": {
                "action": "openExternally",
                "pattern": "\\bNow listening on:\\s+(https?://\\S+)"
            },
            "env": {
                "ASPNETCORE_ENVIRONMENT": "Local"
            },
            "sourceFileMap": {
                "/Views": "${workspaceFolder}/Views"
            }
        },
        {
            "name": "Launch Release (without browser)",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build release",
            "program": "${workspaceFolder}/src/Teslametrics.App.Web/bin/Release/net8.0/Teslametrics.App.Web.dll",
            "args": [],
            "cwd": "${workspaceFolder}/src/Teslametrics.App.Web",
            "stopAtEntry": false,
            "console": "integratedTerminal",
            "env": {
                "ASPNETCORE_ENVIRONMENT": "Local"
            },
            "sourceFileMap": {
                "/Views": "${workspaceFolder}/Views"
            }
        }
    ]
}