@using Teslametrics.Shared
@inherits InteractiveBaseComponent
<DrawerComponent Open="IsOpened"
				 OpenChanged="OnOpenChanged">
	<CascadingValue IsFixed="true"
					Value="this">
		@switch (_mode)
		{
			case DrawerMode.View:
				@if (_resourceId.HasValue)
				{
					<AuthorizeView Policy="@AppPermissions.Main.CameraPresets.Read.GetEnumPermissionString()"
								   Resource="new PolicyRequirementResource(_organizationId, _resourceId.Value)">
						<Teslametrics.App.Web.Features.Main.Presets.Drawer.View.PresetViewComponent PresetId="_resourceId.Value" />
					</AuthorizeView>
				}
				break;

			case DrawerMode.Edit:
				@if (_resourceId.HasValue)
				{
					<AuthorizeView Policy="@AppPermissions.Main.CameraPresets.Update.GetEnumPermissionString()"
								   Resource="new PolicyRequirementResource(_organizationId, _resourceId.Value)">
						<Teslametrics.App.Web.Features.Main.Presets.Drawer.Edit.PresetEditComponent PresetId="_resourceId.Value" />
					</AuthorizeView>
				}
				break;

			case DrawerMode.Create:
				<AuthorizeView Policy="@AppPermissions.Main.CameraPresets.Create.GetEnumPermissionString()"
							   Resource="new PolicyRequirementResource(_organizationId, null)">
					<Teslametrics.App.Web.Features.Main.Presets.Drawer.Create.PresetCreateComponent />
				</AuthorizeView>
				break;
		}
	</CascadingValue>
</DrawerComponent>