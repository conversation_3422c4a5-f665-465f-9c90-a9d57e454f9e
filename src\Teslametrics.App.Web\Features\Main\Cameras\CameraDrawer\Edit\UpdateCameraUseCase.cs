using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Domain.AccessControl.Organizations;
using Teslametrics.App.Web.Domain.Cameras;
using Teslametrics.App.Web.Orleans.Camera;
using Teslametrics.App.Web.Services.Outbox;
using Teslametrics.App.Web.Services.TransactionManager;
using Teslametrics.App.Web.Shared;

namespace Teslametrics.App.Web.Features.Main.Cameras.CameraDrawer.Edit;

public static class UpdateCameraUseCase
{
    public record Command(Guid Id,
                          Guid QuotaId,
                          string Name,
                          TimeSpan TimeZone,
                          Coordinates? Coordinates,
                          string ArchiveUri,
                          string ViewUri,
                          string PublicUri,
                          bool AutoStart,
                          bool OnvifEnabled,
                          Command.OnvifSettings? OnvifOptions) : BaseRequest<Response>
    {
        public record OnvifSettings(string Host, int Port, string Username, string Password);
    };

    public record Response : BaseResponse
    {
        public Guid Id { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(Guid id)
        {
            Id = id;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Id = Guid.Empty;
            Result = result;
        }
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        CameraNotFound,
        CameraPresetNotFound,
        CameraQuotaNotFound,
        CameraQuotaLimitReached
    }

    public class Validator : AbstractValidator<Command>
    {
        public Validator()
        {
            RuleFor(c => c.Id).NotEmpty();
            RuleFor(c => c.QuotaId).NotEmpty();
            RuleFor(c => c.Name).Length(3, 60);
            RuleFor(c => c.ArchiveUri).NotEmpty();
            RuleFor(c => c.ViewUri).NotEmpty();
            RuleFor(c => c.PublicUri).NotEmpty();
            RuleFor(c => c.OnvifOptions).NotNull().When(c => c.OnvifEnabled);
        }
    }

    public class Handler : IRequestHandler<Command, Response>
    {
        private readonly IValidator<Command> _validator;
        private readonly ICameraRepository _cameraRepository;
        private readonly IOrganizationRepository _organizationRepository;
        private readonly IClusterClient _clusterClient;
        private readonly ITransactionManager _transactionManager;
        private readonly IPublisher _publisher;
        private readonly IOutbox _outbox;

        public Handler(IValidator<Command> validator,
                       ICameraRepository cameraRepository,
                       IOrganizationRepository organizationRepository,
                       IClusterClient clusterClient,
                       ITransactionManager transactionManager,
                       IPublisher publisher,
                       IOutbox outbox)
        {
            _validator = validator;
            _cameraRepository = cameraRepository;
            _organizationRepository = organizationRepository;
            _clusterClient = clusterClient;
            _transactionManager = transactionManager;
            _publisher = publisher;
            _outbox = outbox;
        }

        public async Task<Response> Handle(Command request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            using var transaction = await _transactionManager.CreateTransactionAsync();

            var camera = await _cameraRepository.FindAsync(request.Id, cancellationToken);
            if (camera is null)
            {
                return new Response(Result.CameraNotFound);
            }

            var organization = await _organizationRepository.FindAsync(camera.OrganizationId, cancellationToken);

            var cameraQuota = organization!.CameraQuotas.SingleOrDefault(cq => cq.Id == request.QuotaId);
            if (cameraQuota is null)
            {
                return new Response(Result.CameraQuotaNotFound);
            }

            var cameraCount = await _cameraRepository.GetCameraCountByQuotaAsync(request.QuotaId, cancellationToken);

            if (cameraQuota.Limit != -1 && cameraQuota.Limit <= cameraCount)
            {
                return new Response(Result.CameraQuotaLimitReached);
            }

            bool isUriChanged = request.ArchiveUri != camera.ArchiveUri || request.ViewUri != camera.ViewUri || request.PublicUri != camera.PublicUri;

            var events = camera.Update(request.QuotaId,
                                       request.Name,
                                       request.TimeZone,
                                       request.Coordinates?.Latitude,
                                       request.Coordinates?.Longitude,
                                       request.ArchiveUri,
                                       request.ViewUri,
                                       request.PublicUri,
                                       request.OnvifEnabled,
                                       ToOnvifSettingsValueObject(request.OnvifOptions),
                                       request.AutoStart);

            await _cameraRepository.SaveChangesAsync(cancellationToken);

            foreach (var @event in events)
            {
                await _publisher.Publish(@event, cancellationToken);
            }

            await _outbox.AddRangeAsync(events);

            await transaction.CommitAsync();

            var grain = _clusterClient.GetGrain<IMediaServerGrain>(Guid.Empty);

            var status = await grain.GetStatusAsync(new IMediaServerGrain.GetCameraStatusRequest(camera.Id));

            if (status is CameraStatus.Running && isUriChanged)
            {
                await grain.DisconnectAsync(new IMediaServerGrain.CameraDisconnectRequest(camera.Id));
                await grain.ConnectRtspAsync(new IMediaServerGrain.CameraConnectRtspRequest(camera.Id, camera.ArchiveUri, camera.ViewUri, camera.PublicUri));
                if (request.OnvifEnabled)
                {
                    await grain.ConnectOnvifAsync(new IMediaServerGrain.CameraConnectOnvifRequest(camera.Id, camera.OnvifSettings!.Host, camera.OnvifSettings.Port, camera.OnvifSettings.Username, camera.OnvifSettings.Password));
                }
            }

            return new Response(camera.Id);
        }

        private static OnvifSettingsValueObject? ToOnvifSettingsValueObject(Command.OnvifSettings? onvifSettings)
        {
            if (onvifSettings is null)
            {
                return null;
            }

            return new OnvifSettingsValueObject(onvifSettings.Host, onvifSettings.Port, onvifSettings.Username, onvifSettings.Password);
        }
    }
}