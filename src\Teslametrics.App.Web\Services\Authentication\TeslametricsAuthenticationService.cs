using MediatR;
using Microsoft.AspNetCore.Components.Authorization;
using System.Security.Claims;
using Teslametrics.App.Web.Extensions;
using Teslametrics.App.Web.Features.Authentication.Account;
using Teslametrics.App.Web.Services.Cookies;
using Teslametrics.App.Web.Services.UserSession;

namespace Teslametrics.App.Web.Services.Authentication;

// TODO Не используется
public class TeslametricsAuthenticationService : IAuthenticationService
{
	private readonly IHostEnvironmentAuthenticationStateProvider _authStateProvider;
	private readonly IServiceScopeFactory _scopeFactory;
	private readonly CookieStorageAccessor _cookieStorageAccessor;
	private readonly ISessionProvider _sessionProvider;
	private readonly ILogger<TeslametricsAuthenticationService> _logger;

	public TeslametricsAuthenticationService(IHostEnvironmentAuthenticationStateProvider authStateProvider,
											 IServiceScopeFactory scopeFactory,
											 CookieStorageAccessor cookieStorageAccessor,
											 ISessionProvider sessionProvider,
											 ILogger<TeslametricsAuthenticationService> logger)
	{
		_authStateProvider = authStateProvider;
		_scopeFactory = scopeFactory;
		_cookieStorageAccessor = cookieStorageAccessor;
		_sessionProvider = sessionProvider;
		_logger = logger;
	}

	public async Task<AuthenticationResult> SignInAsync(string login, string password)
	{
		try
		{
			var response = await _scopeFactory.MediatorSend(new UserLoginUseCase.Command(login, password));

			if (!response.IsSuccess)
				return AuthenticationResult.Failure((AuthenticationResult.ResultCode)response.Result);

			var claims = new List<Claim>();
			//claims.AddRange(response.Permissions.Select(x => new Claim(x.Item1, x.Item2)));


			Guid userId = Guid.Parse(claims.Single(x => x.Type == ClaimTypes.NameIdentifier).Value);

			Guid? sessionId = _sessionProvider.GetSessionByUserId(userId)?.Id;
			ClaimsPrincipal? userPrincipal = new(new ClaimsIdentity(claims, "Custom"));
			if (sessionId is null)
			{
				SessionResult? sessionResult = _sessionProvider.CreateSession(userPrincipal);
				if (!sessionResult.IsSuccess)
					return AuthenticationResult.Failure(AuthenticationResult.ResultCode.FailedToCreateSession);
				sessionId = sessionResult.SessionId;
			}

			await _cookieStorageAccessor.SetValueAsync(AuthenticationStorageNames.SessionId, sessionId);
			_authStateProvider.SetAuthenticationState(Task.FromResult(new AuthenticationState(userPrincipal)));

			return AuthenticationResult.Success();
		}
		catch (AuthStateSubscriptionErrorException exc)
		{
			_logger.LogError(exc, exc.Message);
			return AuthenticationResult.Failure(AuthenticationResult.ResultCode.Unknown);
		}
		catch (Exception exc)
		{
			_logger.LogError(exc, exc.Message);
			return AuthenticationResult.Failure(AuthenticationResult.ResultCode.Unknown);
		}
	}

	public async Task SignOutAsync()
	{
		Guid? sessionId = await _cookieStorageAccessor.GetValueAsync<Guid?>(AuthenticationStorageNames.SessionId);
		if (sessionId is null)
		{
			return;
		}

		_sessionProvider.KillSession(sessionId.Value);
		await _cookieStorageAccessor.RemoveAsync(AuthenticationStorageNames.SessionId);
		_authStateProvider.SetAuthenticationState(Task.FromResult(new AuthenticationState(new ClaimsPrincipal(new ClaimsIdentity()))));
	}
}
