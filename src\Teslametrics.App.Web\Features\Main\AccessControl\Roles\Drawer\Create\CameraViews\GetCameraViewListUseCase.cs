using System.Data;
using Dapper;
using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Services.Persistence;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Roles.Drawer.Create.CameraViews;

public static class GetCameraViewListUseCase // Хочу получить список видов в организации для пользователя
{
    public record Query(Guid OrganizationId, Guid UserId) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public List<Item> Items { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(List<Item> items)
        {
            Items = items;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Result = result;

            Items = [];
        }

        public record Item(Guid Id, string Name);
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError
    }

    public class Validator : AbstractValidator<Query>
    {
        public Validator()
        {
            RuleFor(q => q.OrganizationId).NotEmpty();
            RuleFor(q => q.UserId).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Query, Response>
    {
        private readonly IValidator<Query> _validator;
        private readonly IDbConnection _dbConnection;

        public Handler(IValidator<Query> validator,
                       IDbConnection dbConnection)
        {
            _validator = validator;
            _dbConnection = dbConnection;
        }

        public async Task<Response> Handle(Query request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            var allowedResources = request.UserId != SystemConsts.RootUserId
                ? await GetAllowedResourcesAsync(request.UserId)
                : [];

            var cameraViewsTemplate = SqlQueryBuilder.Create()
                .Select(Db.CameraViews.Props.Id)
                .Select(Db.CameraViews.Props.Name)
                .InnerJoin(Db.Organizations.Table, Db.Organizations.Props.Id, Db.CameraViews.Props.OrganizationId, SqlOperator.Equals)
                .Where(Db.CameraViews.Props.OrganizationId, ":OrganizationId", SqlOperator.Equals, new { request.OrganizationId })
                .WhereIf(request.UserId != SystemConsts.RootUserId, $"({Db.Organizations.Props.OwnerId} = :UserId OR :Wildcard = ANY(:ResourceIds) OR {Db.CameraViews.Props.Id} = ANY(:ResourceIds))", new
                {
                    request.UserId,
                    Wildcard = SystemConsts.ResourceWildcardId,
                    ResourceIds = allowedResources
                })
                .Build(QueryType.Standard, Db.CameraViews.Table, RowSelection.AllRows);

            var cameraViewModels = await _dbConnection.QueryAsync<CameraViewModel>(cameraViewsTemplate.RawSql, cameraViewsTemplate.Parameters);

            return new Response(cameraViewModels.Select(c => new Response.Item(c.Id, c.Name)).ToList());
        }

        private async Task<IEnumerable<Guid>> GetAllowedResourcesAsync(Guid userId)
        {
            var template = SqlQueryBuilder.Create()
                .Select(Db.RolePermissions.Props.ResourceId)
                .InnerJoin(Db.UserRoles.Table, Db.UserRoles.Props.RoleId, Db.RolePermissions.Props.RoleId, SqlOperator.Equals)
                .Where(Db.RolePermissions.Props.Permission, ":ViewPermission", SqlOperator.Equals, new
                {
                    ViewPermission = Fqdn<AppPermissions>.GetName(AppPermissions.Main.CameraViews.Read)
                })
                .Where(Db.UserRoles.Props.UserId, ":UserId", SqlOperator.Equals, new { userId })
                .Build(QueryType.Standard, Db.RolePermissions.Table, RowSelection.AllRows);

            return await _dbConnection.QueryAsync<Guid>(template.RawSql, template.Parameters);
        }
    }

    public record CameraViewModel(Guid Id, string Name);
}