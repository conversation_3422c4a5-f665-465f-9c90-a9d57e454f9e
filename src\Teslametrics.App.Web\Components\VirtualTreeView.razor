﻿@using MudBlazor.Utilities
@typeparam T
<ul class="@(TreeViewClassname())">
    <Virtualize Items="viewList"
                ItemSize="40"
                Context="viewListItem">
        <li @key="@(viewListItem.Key)"
            style="flex-shrink: 0; margin-left: @(viewListItem.Level * 32)pt; height: 30pt;"
            class="@(ListItemClassname())">
            <div class="@(ContentClassname(false))">
                <MudTreeViewItemToggleButton Visible="ChildrenSelector?.Invoke(viewListItem.Item)?.Count > 0"
                                             Expanded="@viewListItem.IsExpanded"
                                             ExpandedChanged="newIsExpanded => OnClick(newIsExpanded, viewListItem)" />
                @if (ItemTemplate == null)
                {
                    @viewListItem.Item
                }
                else
                {
                    @ItemTemplate(viewListItem.Item)
                }
            </div>
        </li>
    </Virtualize>
</ul>

@code {
    //TODO: Вынести в отдельный класс, стилизовать под MudBlazor.
    //TOOD: Переделать MudTreeView для поддержки виртуализации
    protected string ListItemClassname() =>
    new CssBuilder("mud-treeview-item")
    .Build();

    protected string ContentClassname(bool isSelected) =>
    new CssBuilder("mud-treeview-item-content")
    .AddClass("cursor-pointer", false)
    .AddClass("mud-treeview-item-selected", isSelected)
    .Build();

    protected string TreeViewClassname() =>
    new CssBuilder("mud-treeview")
    .Build();

    readonly record struct ViewItem(T Item, string Key, bool IsExpanded, int Level);

    private List<ViewItem> viewList = new();

    [Parameter]
    public RenderFragment<T>? ItemTemplate { get; set; }

    [Parameter]
    public IReadOnlyList<T>? Items { get; set; }

    [Parameter]
    public Func<T, IReadOnlyList<T>>? ChildrenSelector { get; set; }

    protected override void OnParametersSet()
    {
        base.OnParametersSet();
        SynchronizeViewList();
    }

    private void SynchronizeViewList()
    {
        viewList.Clear();
        if (Items is { } items)
        {
            viewList.AddRange(items.Select((x, i) => new ViewItem(x, $"{i}", false, 0)));
        }
    }

    private void OnClick(bool newIsExpanded, ViewItem item)
    {
        var viewListIndex = viewList.FindIndex(x => EqualityComparer<ViewItem>.Default.Equals(x, item));
        if (viewListIndex >= 0)
        {
            viewList[viewListIndex] = item with { IsExpanded = newIsExpanded };
            var children = ChildrenSelector?.Invoke(item.Item) ?? [];
            if (newIsExpanded)
            {
                viewList.InsertRange(viewListIndex + 1, children.Select((x, i) => new ViewItem(x, $"{item.Key}-{i}", false, item.Level +
                1)));
            }
            else
            {
                var cnt = children.Count;
                cnt += viewList.Skip(viewListIndex + 1 + cnt).TakeWhile(x => x.Key.StartsWith(item.Key)).Count();
                viewList.RemoveRange(viewListIndex + 1, cnt);
            }
        }
    }
}