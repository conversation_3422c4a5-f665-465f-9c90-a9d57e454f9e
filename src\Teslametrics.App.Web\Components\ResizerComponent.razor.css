/* Горизонтальный режим (по умолчанию) */
.resizer_container {
    display: flex;
    gap: 16px;
}

.left_panel {
    width: 300px;
    min-width: 150px;
    max-width: 70%;
}

.right_panel {
    flex: 1;
}

.divider {
    width: 5px;
    cursor: col-resize;
}

.divider.readonly {
    cursor: default;
}

/* Вертикальный режим */
.resizer_container.vertical {
    flex-direction: column;
}

.top_panel {
    height: 300px;
    min-height: 150px;
    max-height: 70%;
}

.bottom_panel {
    flex: 1;
    overflow: hidden;
    height: 100%;
}

.resizer_container.vertical .divider {
    width: auto;
    height: 5px;
    cursor: row-resize;
}

.resizer_container.vertical .divider.readonly {
    cursor: default;
}

/* Режим только для чтения */
.resizer_container.readonly .divider {
    cursor: default;
}

/* Стили для иконки */
::deep .handle {
    rotate: 90deg;
    top: 50%;
    left: -9px;
}

.resizer_container.vertical ::deep .handle {
    rotate: 0deg;
    top: -9px;
    left: 50%;
    transform: translateX(-50%);
}