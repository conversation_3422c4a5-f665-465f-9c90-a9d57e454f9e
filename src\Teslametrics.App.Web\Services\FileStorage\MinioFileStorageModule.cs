using Microsoft.Extensions.Options;
using Minio;
using Minio.DataModel.Args;
using Minio.DataModel.ILM;
using Minio.DataModel.Notification;
using Minio.DataModel.Tags;

namespace Teslametrics.App.Web.Services.FileStorage;

public static class MinioFileStorageModule
{
    public static void Install(IServiceCollection services, IConfigurationManager configuration)
    {
        services.Configure<Settings>(
            configuration.GetSection(Settings.SectionName));
        services.AddSingleton<IFileStorage, MinioFileStorage>();
    }

    public class Settings
    {
        public const string SectionName = "Minio";

        public string? Endpoint { get; set; }
        public string? AccessKey { get; set; }
        public string? SecretKey { get; set; }
    }

    public class MinioFileStorage : IFileStorage
    {
        private readonly ILogger<MinioFileStorage> _logger;
        private readonly IMinioClient? _minio;


        public MinioFileStorage(ILogger<MinioFileStorage> logger, IOptions<Settings> options)
        {
            _logger = logger;

            try
            {
                var settings = options.Value;
                ArgumentNullException.ThrowIfNull(settings.Endpoint, nameof(settings.Endpoint));
                ArgumentNullException.ThrowIfNull(settings.AccessKey, nameof(settings.AccessKey));
                ArgumentNullException.ThrowIfNull(settings.SecretKey, nameof(settings.SecretKey));

                _minio = new MinioClient()
                    .WithEndpoint(settings.Endpoint)
                    .WithCredentials(settings.AccessKey, settings.SecretKey)
                    .Build();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to initialize MinIO client");
            }
        }

        public async Task CreateDirectoryIfNotExistsAsync(string bucketName, int retentionDays)
        {
            try
            {
                if (!await BucketExistsAsync(bucketName))
                {
                    var mbArgs = new MakeBucketArgs()
                        .WithBucket(bucketName);
                    await _minio!.MakeBucketAsync(mbArgs);

                    await ConfigureBucketNotificationsAsync(bucketName);
                }

                await ConfigureBucketLifecycle(bucketName, retentionDays);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to create bucket {BucketName}", bucketName);
                throw;
            }
        }

        public async Task<Stream> GetFileAsync(string bucketName, string fileName)
        {
            try
            {
                var tcs = new TaskCompletionSource<Stream>();

                var getObjectArgs = new GetObjectArgs()
                    .WithBucket(bucketName)
                    .WithObject(fileName)
                    .WithCallbackStream((stream) =>
                    {
                        var streamCopy = new MemoryStream();
                        stream.CopyTo(streamCopy);
                        streamCopy.Position = 0;
                        tcs.SetResult(streamCopy);
                    });

                await _minio!.GetObjectAsync(getObjectArgs);
                return await tcs.Task;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get file {FileName} from bucket {BucketName}", fileName, bucketName);
                throw;
            }
        }

        public async Task UploadFileAsync(string bucketName, string objectName, Stream stream, IDictionary<string, string> tags)
        {
            try
            {
                var contentType = GetContentType(Path.GetExtension(objectName));

                var putObjectArgs = new PutObjectArgs()
                    .WithBucket(bucketName)
                    .WithObject(objectName)
                    .WithObjectSize(stream.Length)
                    .WithStreamData(stream)
                    .WithTagging(Tagging.GetObjectTags(tags))
                    .WithContentType(contentType);

                var response = await _minio!.PutObjectAsync(putObjectArgs);

                if (response.ResponseStatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    throw new Exception("Bucket not found");
                }

                _logger.LogInformation("Successfully uploaded file {ObjectName} to bucket {BucketName}", objectName, bucketName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to upload file {ObjectName} to bucket {BucketName}", objectName, bucketName);
                throw;
            }
        }

        private static string GetContentType(string extension)
        {
            return extension.ToLower() switch
            {
                ".jpg" => "image/jpeg",
                ".png" => "image/png",
                ".mp4" => "video/mp4",
                ".m3u8" => "application/vnd.apple.mpegurl",
                ".ts" => "video/mp2t",
                _ => "application/octet-stream",
            };
        }

        public async Task DeleteFilesAsync(string bucketName, List<string> names)
        {
            try
            {
                var objArgs = new RemoveObjectsArgs()
                    .WithBucket(bucketName)
                    .WithObjects(names);
                foreach (var objDeleteError in await _minio!.RemoveObjectsAsync(objArgs))
                {
                    _logger.LogWarning("Failed to delete object {ObjectKey} from bucket {BucketName}. Reason: {ErrorMessage}",
                        objDeleteError.Key,
                        bucketName,
                        objDeleteError.Message);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to delete {Count} objects from bucket {BucketName}", names.Count, bucketName);
                throw;
            }
        }

        public async Task ClearDirectoryAsync(string bucketName)
        {
            try
            {
                if (await BucketExistsAsync(bucketName))
                {
                    var files = new List<string>();

                    var listArgs = new ListObjectsArgs()
                        .WithBucket(bucketName);

                    await foreach (var item in _minio!.ListObjectsEnumAsync(listArgs))
                    {
                        files.Add(item.Key);
                    }

                    await DeleteFilesAsync(bucketName, files);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to clear bucket {BucketName}", bucketName);
                throw;
            }
        }

        private async Task ConfigureBucketNotificationsAsync(string bucketName)
        {
            try
            {
                // Создаем правила для событий
                var queueConfig = new QueueConfig
                {
                    Events =
                    [
                        EventType.ObjectCreatedPut,
                        EventType.ObjectRemovedDelete
                    ],
                    Queue = "arn:minio:sqs::PRIMARY:kafka"
                };

                // Создаем конфигурацию уведомлений
                var config = new BucketNotification
                {
                    QueueConfigs = [queueConfig]
                };

                // Применяем конфигурацию к бакету
                var args = new SetBucketNotificationsArgs()
                    .WithBucket(bucketName)
                    .WithBucketNotificationConfiguration(config);

                await _minio!.SetBucketNotificationsAsync(args);

                _logger.LogInformation("Successfully configured Kafka notifications for bucket {BucketName}", bucketName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to configure Kafka notifications for bucket {BucketName}", bucketName);
                throw;
            }
        }

        private async Task ConfigureBucketLifecycle(string bucketName, int retentionDays)
        {
            try
            {
                var rules = new List<LifecycleRule>();

                // Создаем правило с удалением через 1 день после создания объекта
                var expiration = new Expiration { Days = retentionDays };

                // Создаем правило с удалением через 1 день после создания объекта
                var rule = new LifecycleRule(
                    abortIncompleteMultipartUpload: null,
                    id: "delete-after-retention-period",
                    expiration: expiration,
                    noncurrentVersionExpiration: null,
                    filter: new RuleFilter(null, null, null),
                    transition: null,
                    noncurrentVersionTransition: null,
                    status: LifecycleRule.LifecycleRuleStatusEnabled
                );

                rules.Add(rule);
                var lfc = new LifecycleConfiguration(rules);

                await _minio!.SetBucketLifecycleAsync(
                    new SetBucketLifecycleArgs()
                        .WithBucket(bucketName)
                        .WithLifecycleConfiguration(lfc));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to configure lifecycle rule for bucket {BucketName}. Retention period: {RetentionDays} days", bucketName, retentionDays);
                throw;
            }
        }

        private Task<bool> BucketExistsAsync(string bucketName)
        {
            var beArgs = new BucketExistsArgs()
                .WithBucket(bucketName);
            return _minio!.BucketExistsAsync(beArgs);
        }

        public async Task<IDictionary<string, string>> GetFileTagsAsync(string bucketName, string objectName)
        {
            try
            {
                var tagging = await _minio!.GetObjectTagsAsync(
                    new GetObjectTagsArgs()
                        .WithBucket(bucketName)
                        .WithObject(objectName)
                );

                return tagging.Tags;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get tags for object {ObjectName} from bucket {BucketName}", objectName, bucketName);
                throw;
            }
        }
    }
}