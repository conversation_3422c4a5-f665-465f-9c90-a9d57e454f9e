﻿@inherits InteractiveBaseComponent
<DrawerHeader>
	<MudStack>
		<MudText Typo="Typo.h3">Просмотр параметров ссылки публичного доступа</MudText>
		@if (IsLoading)
		{
			<MudSkeleton Width="60%"></MudSkeleton>
		}
		else
		{
			<MudText Typo="Typo.subtitle1">@_model?.CameraName</MudText>
		}
	</MudStack>
	<MudSpacer />
	@if (!_subscribing && (_subscriptionResult is null || !_subscriptionResult.IsSuccess))
	{
		<MudTooltip Arrow="true"
					Placement="Placement.Start"
					Text="Ошибка подписки на события">
			<MudIconButton OnClick="SubscribeAsync"
						   Icon="@Icons.Material.Filled.ErrorOutline"
						   Color="Color.Error" />
		</MudTooltip>
		<MudIconButton OnClick="RefreshAsync"
					   Icon="@Icons.Material.Filled.Refresh"
					   Color="Color.Primary" />
	}
</DrawerHeader>
<MudForm Model="_model"
		 Class="flex-1"
		 OverrideFieldValidation="true"
		 Spacing="8">
	@if (IsLoading)
	{
		<MudProgressLinear Color="Color.Primary"
						   Indeterminate="true"
						   Class="mt-n4" />
	}
	@if (!IsLoading && (_model is null || !_model.IsSuccess))
	{

		<MudStack AlignItems="AlignItems.Center"
				  Justify="Justify.Center"
				  Class="mud-height-full">
			<MudIcon Icon="@Icons.Material.Filled.PersonOff"
					 Style="font-size: 8rem;" />
			<MudText Typo="Typo.body1">Ничего не найдено</MudText>
			<MudText Typo="Typo.subtitle1">Попробуйте снова позднее</MudText>
		</MudStack>
	}
	else
	{
		<FormSectionComponent title="Описание доступа к камере"
							  Subtitle="Настройки, которые влияют только на восприятие человеком">
			@if (IsLoading)
			{

				<MudSkeleton Width="60%"
							 Height="52px" />
			}
			@if (!IsLoading && _model is not null && _model.IsSuccess)
			{
				<MudTextField Value="_model!.AccessName"
							  ReadOnly="true"
							  InputType="InputType.Text"
							  Immediate="true"
							  Label="Наименование"
							  RequiredError="Данное поле обязательно"
							  Required="true" />
			}
		</FormSectionComponent>

		<FormSectionComponent Title="Доступ к камере"
							  Subtitle="С помощью данных ссылок можно получить доступ к камере">
			@if (IsLoading)
			{

				<MudSkeleton Width="60%"
							 Height="52px" />
			}
			@if (!IsLoading && _model is not null && _model.IsSuccess)
			{
				<CameraPublicAccessComponent AccessId="_model!.AccessId" />
			}
		</FormSectionComponent>
	}
</MudForm>
<DrawerActions>
	<MudSpacer />
	<MudButton OnClick="CancelAsync"
			   Variant="Variant.Outlined"
			   StartIcon="@Icons.Material.Outlined.Close">Закрыть</MudButton>
</DrawerActions>