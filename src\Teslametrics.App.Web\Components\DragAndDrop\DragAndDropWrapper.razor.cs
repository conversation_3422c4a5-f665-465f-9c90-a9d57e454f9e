using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Components.Web;
using MudBlazor.Utilities;

namespace Teslametrics.App.Web.Components.DragAndDrop;

// TODO:: Сделать возможность кастомизации перетаскиваемого итема.
// TODO:: Сделать ограничение по зоне перетаскивания
// TODO:: Посмотреть реализации smooth-dnd и dndkit.com, чтобы получить нечто среднее.
//		  Так же можно глянуть https://codepen.io/wilbo/pen/mWdwvy?editors=1010
public partial class DragAndDropWrapper<T> where T : class
{
	private bool _disposedValue;
	private bool _canDrop = false;
	private bool _onDragOver = false;

	private bool _draggable;

	protected string ElementClass => new CssBuilder("element mud-width-full mud-height-full")
		.AddClass("dropable", _onDragOver && _canDrop)
		.AddClass("no_dropable", _onDragOver && !_canDrop)
		.AddClass(Class, Class is not null)
		.Build();

	[CascadingParameter]
	private DragAndDropContainer<T> Container { get; set; } = null!;

	[Parameter]
	public string? Class { get; set; }

	[Parameter]
	[EditorRequired]
	public T Value { get; set; } = default!;

	[Parameter]
	public RenderFragment? ChildContent { get; set; }

	protected override async Task OnInitializedAsync()
	{
		await base.OnInitializedAsync();
		_draggable = await Container.IsDraggableAsync(Value);
		AuthenticationStateProvider.AuthenticationStateChanged += OnAuthenticationStateChanged;
	}

	//@onmouseenter="OnHover"
	//private async Task OnHover()
	//{
	//	if (Container.IsDragging) return;
	//	_draggable = await Container.IsDraggableAsync(Value);
	//}

	private async Task OnDragOverHandler(DragEventArgs eventArgs)
	{
		_onDragOver = true;
		var canDropResult = await Container.IsDroppableAsync(Value);
		if (_onDragOver) // Состояние могло со спокойной душой сменится, пока мы думали.
		{
			_canDrop = canDropResult;
		}
	}

	private void OnDragLeaveHandler(DragEventArgs eventArgs)
	{
		_canDrop = false;
		_onDragOver = false;
	}

	private void OnDragStartHandler(DragEventArgs eventArgs) => Container.DragStart(eventArgs, Value);
	private async Task OnDropHandler(DragEventArgs eventArgs)
	{
		await Container.DropAsync(eventArgs, Value);
		await InvokeAsync(() =>
		{
			_canDrop = false;
			_onDragOver = false;
			StateHasChanged();
		});
	}

	private async void OnAuthenticationStateChanged(Task<AuthenticationState> authState)
	{
		_draggable = await Container.IsDraggableAsync(Value);
	}

	protected override void Dispose(bool disposing)
	{
		if (!_disposedValue)
		{
			if (disposing)
			{
				AuthenticationStateProvider.AuthenticationStateChanged -= OnAuthenticationStateChanged;
			}

			_disposedValue = true;
		}

		// Вызов базового метода Dispose
		base.Dispose(disposing);
	}
}
