@inherits InteractiveBaseComponent
<div class="tree-table-container">
	<MudTable T="FlatTreeItem"
			  Items="@FlattenedItems.Where(item => item.Parent is null || item.Parent.IsExpanded)"
			  Hover="true"
			  Breakpoint="Breakpoint.None"
			  Loading="@IsLoading"
			  Striped="true"
			  OnRowClick="OnRowClick"
			  CurrentPage="CurrentPage"
			  RowsPerPage="Limit"
			  RowsPerPageChanged="RowsPerPageChanged"
			  LoadingProgressColor="Color.Info">
		<HeaderContent>
			<MudTh>Название</MudTh>
			<MudTh style="width: 200px">Количество камер</MudTh>
			<MudTh style="width: 280px">Количество публичных доступов</MudTh>
			<MudTh style="width: 220px"></MudTh>
		</HeaderContent>
		<ToolBarContent>
			<MudText Typo="Typo.h6">Ссылки публичного доступа</MudText>
			<MudSpacer />
			<div>
				@if (!_subscribing && (_subscriptionResult is null || !_subscriptionResult.IsSuccess))
				{
					<MudTooltip Arrow="true"
								Placement="Placement.Start"
								Text="Ошибка подписки на события">
						<MudIconButton OnClick="SubscribeAsync"
									   Icon="@Icons.Material.Filled.ErrorOutline"
									   Color="Color.Error" />
					</MudTooltip>
				}
				<TimePassedComponent InputTime="@_lastRefreshTime"
									 TooltipText="@($"Время последнего обновления: {_lastRefreshTime.ToLocalTime()}")" />
				<MudIconButton OnClick="RefreshAsync"
							   Icon="@Icons.Material.Filled.Refresh"
							   Color="Color.Primary" />
			</div>
		</ToolBarContent>
		<RowTemplate>
			<MudTd>
				<div class="d-flex align-center">
					<div class="tree-indent"
						 style="margin-left: @($"{context.Level * 24}px")">
						@if (context.Level > 0)
						{
							<div class="tree-line-container"
								 style="left: -@($"{context.Level * 24}px")">
								@for (int i = 0; i < context.Level; i++)
								{
									bool showLine = ShouldShowVerticalLine(context, i + 1);
									<div class="@($"tree-line {(showLine ? "tree-line-visible" : "")}")"></div>
								}
								<div class="tree-line tree-line-horizontal"></div>
							</div>
						}
						@if (context.HasChildren)
						{
							<MudIconButton Icon="@(context.IsExpanded ? Icons.Material.Filled.KeyboardArrowDown : Icons.Material.Filled.KeyboardArrowRight)"
										   Size="Size.Small"
										   OnClick="@(() => ToggleExpand(context))" />
						}
						else
						{
							<div class="tree-leaf-indent"></div>
						}
						<MudIcon Icon="@context.Item.Icon"
								 Class="mr-2" />
						<MudText>@context.Item.Name</MudText>
					</div>
				</div>
			</MudTd>
			<MudTd>
				@if (context.Item.ElementType == ElementType.Organization || context.Item.ElementType == ElementType.Folder)
				{
					@context.Item.CameraCount
				}
				else
				{
					<MudText>-</MudText>
				}
			</MudTd>
			<MudTd>
				@if (context.Item.ElementType != ElementType.Link)
				{
					@context.Item.PublicAccessCount
				}
				else
				{
					<MudText>-</MudText>
				}
			</MudTd>
			<MudTd>
				<MudStack Row="true"
						  AlignItems="AlignItems.Center"
						  Justify="Justify.FlexEnd">
					@switch (context.Item.ElementType)
					{
						case ElementType.Camera:
							<AuthorizeView Policy="@AppPermissions.Main.CameraPublicAccess.Create.GetEnumPermissionString()"
										   Context="createContext"
										   Resource="new PolicyRequirementResource(context.Item.OrganizationId, context.Item.Id)">
								<MudIconButton Icon="@Icons.Material.Filled.Add"
											   Variant="Variant.Outlined"
											   Color="Color.Primary"
											   Size="Size.Small"
											   OnClick="@(() => CreateUrl(context.Item.OrganizationId, context.Item.Id))" />
							</AuthorizeView>
							break;
						case ElementType.Link:
							<MudIconButton Icon="@Icons.Material.Filled.PanoramaFishEye"
										   Variant="Variant.Outlined"
										   Color="Color.Primary"
										   Size="Size.Small"
										   OnClick="@(() => SelectUrl(context.Item.OrganizationId, context.Parent!.Item.Id, context.Item.Id))" />
							<AuthorizeView Policy="@AppPermissions.Main.CameraPublicAccess.Update.GetEnumPermissionString()"
										   Context="updateContext"
										   Resource="new PolicyRequirementResource(context.Item.OrganizationId, context.Item.Id)">
								<MudIconButton Icon="@Icons.Material.Filled.Edit"
											   Variant="Variant.Outlined"
											   Color="Color.Primary"
											   Size="Size.Small"
											   OnClick="@(() => EditUrl(context.Item.OrganizationId, context.Parent!.Item.Id, context.Item.Id))" />
								<MudIconButton Icon="@Icons.Material.Filled.Update"
											   Variant="Variant.Outlined"
											   Color="Color.Warning"
											   Size="Size.Small"
											   OnClick="@(() => ReissueUrl(context.Item.OrganizationId,context.Parent!.Item.Id, context.Item.Id))" />
							</AuthorizeView>
							<AuthorizeView Policy="@AppPermissions.Main.CameraPublicAccess.Delete.GetEnumPermissionString()"
										   Context="deleteContext"
										   Resource="new PolicyRequirementResource(context.Item.OrganizationId, context.Item.Id)">
								<MudDivider Vertical="true"
											style="min-height: 18px;" />
								<MudIconButton Icon="@Icons.Material.Filled.Delete"
											   Variant="Variant.Outlined"
											   Color="Color.Error"
											   Size="Size.Small"
											   OnClick="@(() => DeleteUrl(context.Item.OrganizationId, context.Parent!.Item.Id, context.Item.Id))" />
							</AuthorizeView>
							break;
					}
				</MudStack>
			</MudTd>
		</RowTemplate>
		<LoadingContent>
			<MudSkeleton />
		</LoadingContent>
		<NoRecordsContent>
			<MudStack Class="mud-width-full"
					  AlignItems="AlignItems.Center"
					  Justify="Justify.Center">
				<MudIcon Icon="@Icons.Material.Filled.PersonOff"
						 Style="font-size: 8rem;" />
				<MudText Typo="Typo.body1">Ничего не найдено</MudText>
				<MudText Typo="Typo.subtitle1">Попробуйте снова позднее</MudText>
				<MudButton OnClick="RefreshAsync"
						   Variant="Variant.Filled"
						   Color="Color.Primary">Обновить</MudButton>
			</MudStack>
		</NoRecordsContent>
		<PagerContent>
			<MudTablePager InfoFormat="{first_item}-{last_item} из {all_items}"
						   RowsPerPageString="Строк на страницу:" />
		</PagerContent>
	</MudTable>
</div>
