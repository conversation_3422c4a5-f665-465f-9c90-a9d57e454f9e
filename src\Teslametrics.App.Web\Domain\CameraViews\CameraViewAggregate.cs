using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Domain.CameraViews.Events;
using Teslametrics.App.Web.Shared;

namespace Teslametrics.App.Web.Domain.CameraViews;

public partial class CameraViewAggregate : IEntity
{
    private readonly List<CameraViewCell> _cells;

    public Guid Id { get; private set; }

    public Guid OrganizationId { get; private set;}

    public string Name { get; private set;}

    public short ColumnCount { get; private set;}

    public short RowCount { get; private set; }

    public GridType GridType { get; private set; }

    public IReadOnlyCollection<CameraViewCell> Cells => _cells;

    public static (CameraViewAggregate CameraView, List<object> Events) Create(Guid id,
                                                                               Guid organizationId,
                                                                               string name,
                                                                               short columnCount,
                                                                               short rowCount,
                                                                               GridType gridType,
                                                                               List<(Guid CameraId, short CellIndex)> cells)
    {
        return (new CameraViewAggregate(id, organizationId, name, columnCount, rowCount, gridType, cells), [new CameraViewCreatedEvent(id, organizationId)]);
    }

    private CameraViewAggregate()
    {
        _cells = [];
        Name = string.Empty;
    }

    private CameraViewAggregate(Guid id, Guid organizationId, string name, short columnCount, short rowCount, GridType gridType, List<(Guid CameraId, short CellIndex)> cells)
    {
        Id = id;
        OrganizationId = organizationId;
        Name = name;
        ColumnCount = columnCount;
        RowCount = rowCount;
        GridType = gridType;
        _cells = cells.Select(c => new CameraViewCell(c.CameraId, c.CellIndex)).ToList();
    }

    public List<object> Update(string name, short columnCount, short rowCount, GridType gridType, List<(Guid CameraId, short CellIndex)> cells)
    {
        var isUpdated = false;

        if (Name != name)
        {
            Name = name;
            isUpdated = true;
        }

        if (ColumnCount != columnCount)
        {
            ColumnCount = columnCount;
            isUpdated = true;
        }

        if (RowCount != rowCount)
        {
            RowCount = rowCount;
            isUpdated = true;
        }

        if (GridType != gridType)
        {
            GridType = gridType;
            isUpdated = true;
        }

        var currentCells = _cells.Select(c => (c.CameraId, c.CellIndex)).ToHashSet();
        var newCells = cells.Select(c => (c.CameraId, c.CellIndex)).ToHashSet();

        if (!currentCells.SetEquals(newCells))
        {
            _cells.Clear();
            _cells.AddRange(cells.Select(c => new CameraViewCell(c.CameraId, c.CellIndex)));

            isUpdated = true;
        }

        return isUpdated ? [new CameraViewUpdatedEvent(Id, OrganizationId)] : [];
    }
}