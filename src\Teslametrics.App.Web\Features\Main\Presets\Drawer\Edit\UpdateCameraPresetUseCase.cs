using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Domain.CameraPresets;
using Teslametrics.App.Web.Services.Outbox;
using Teslametrics.App.Web.Services.TransactionManager;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Features.Main.Presets.Drawer.Edit;

public static class UpdateCameraPresetUseCase
{
    public record Command(Guid Id, string Name, Command.StreamConfig ArchiveStreamConfig, Command.StreamConfig ViewStreamConfig, Command.StreamConfig PublicStreamConfig) : BaseRequest<Response>
    {
        public record StreamConfig(Resolution Resolution, VideoCodec VideoCodec, FrameRate FrameRate, SceneDynamic SceneDynamic, AudioCodec AudioCodec);
    }

    public record Response : BaseResponse
    {
        public Guid Id { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(Guid id)
        {
            Id = id;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Id = Guid.Empty;
            Result = result;
        }
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        CameraPresetNotFound,
        CameraPresetNameAlreadyExists
    }

    public class Validator : AbstractValidator<Command>
    {
        public Validator()
        {
            RuleFor(x => x.Id).NotEmpty();
            RuleFor(x => x.Name).Length(3, 60);
        }
    }

    public class Handler : IRequestHandler<Command, Response>
    {
        private readonly IValidator<Command> _validator;
        private readonly IPresetRepository _presetRepository;
        private readonly ITransactionManager _transactionManager;
        private readonly IPublisher _publisher;
        private readonly IOutbox _outbox;

        public Handler(IValidator<Command> validator,
                       IPresetRepository presetRepository,
                       ITransactionManager transactionManager,
                       IPublisher publisher,
                       IOutbox outbox)
        {
            _validator = validator;
            _presetRepository = presetRepository;
            _transactionManager = transactionManager;
            _publisher = publisher;
            _outbox = outbox;
        }

        public async Task<Response> Handle(Command request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            using var transaction = await _transactionManager.CreateTransactionAsync();

            var preset = await _presetRepository.FindAsync(request.Id, cancellationToken);
            if (preset is null)
            {
                return new Response(Result.CameraPresetNotFound);
            }

            if (preset.Name != request.Name)
            {
                if (await _presetRepository.IsCameraPresetNameExistsAsync(request.Name, cancellationToken))
                {
                    return new Response(Result.CameraPresetNameAlreadyExists);
                }
            }

            var events = preset.Update(request.Name, ToStreamConfigValueObject(request.ArchiveStreamConfig), ToStreamConfigValueObject(request.ViewStreamConfig), ToStreamConfigValueObject(request.PublicStreamConfig));

            await _presetRepository.SaveChangesAsync(cancellationToken);

            foreach (var @event in events)
            {
                await _publisher.Publish(@event, cancellationToken);
            }

            await _outbox.AddRangeAsync(events);

            await transaction.CommitAsync();

            return new Response(preset.Id);
        }

        private static StreamConfigValueObject ToStreamConfigValueObject(Command.StreamConfig streamConfig)
        {
            return new StreamConfigValueObject(
                streamConfig.Resolution,
                streamConfig.VideoCodec,
                streamConfig.FrameRate,
                streamConfig.SceneDynamic,
                streamConfig.AudioCodec);
        }
    }
}
