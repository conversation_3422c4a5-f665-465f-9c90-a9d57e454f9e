﻿@using Teslametrics.App.Web.Features.Main.Devices.RoomContent.Fridge
@inherits InteractiveBaseComponent
<MudExpansionPanel Class="list_item"
                   Dense="false"
                   Gutters="false"
                   HeaderClass="px-4 py-3">
    <TitleContent>
        @if (IsLoading && (_response is null || !_response.IsSuccess))
        {
            <MudSkeleton Width="60%" />
        }
        @if (_response is not null && _response.IsSuccess)
        {
            <div class="d-flex gap-2 align-center">
                <MudIcon Icon="@(_response.Fridge.HasIncidents? TeslaIcons.State.Warning : TeslaIcons.State.Success)"
                         Color="@(_response.Fridge.HasIncidents? Color.Error: Color.Default)" />
                <MudText>@_response.Fridge.Name</MudText>
            </div>
        }
    </TitleContent>
    <ChildContent>
        @if (IsLoading && (_response is null || !_response.IsSuccess))
        {
            <MudSkeleton Width="60%" />
            <MudSkeleton Width="60%" />
            <MudSkeleton Width="60%" />
            <MudSkeleton Width="60%" />
        }
        @if (_response is not null && _response.IsSuccess)
        {
            <MudGrid Class="pa-4"
                     Spacing="2">
                @foreach (var card in _doubledCards)
                {
                    <MudItem xs="12">
                        <MudPaper Elevation="0"
                                  Outlined="true"
                                  Class="pa-4">
                            <MudGrid>
                                <MudItem xs="6">
                                    @if (card.Temperature is not null)
                                    {
                                        <SensorDataComponent TopicName="@card.Temperature.Name"
                                                             Name="@card.Temperature.DisplayName"
                                                             ValueProcessor="@((object value) => value is double doubleValue ? Math.Round(doubleValue, 2) + "°C" : value.ToString())"
                                                             Error="@(card.Temperature.Incident is not null)"
                                                             Icon="@TeslaIcons.Sensors.Temperature" />
                                    }
                                </MudItem>
                                <MudItem xs="6">
                                    @if (card.Humidity is not null)
                                    {
                                        <SensorDataComponent TopicName="@card.Humidity.Name"
                                                             Name="@card.Humidity.DisplayName"
                                                             Error="@(card.Humidity.Incident is not null)"
                                                             ValueProcessor="@((object value) => value.ToString() + "%")"
                                                             Icon="@TeslaIcons.Sensors.Humidity" />
                                    }
                                </MudItem>
                            </MudGrid>
                        </MudPaper>
                    </MudItem>
                }
                @foreach (var item in _door ?? [])
                {
                    <MudItem xs="12"
                             md="6">
                        <MudPaper Elevation="0"
                                  Outlined="true"
                                  Class="pa-4">
                            <SensorDataComponent TopicName="@item.Name"
                                                 Name="@item.DisplayName"
                                                 Error="@(item.Incident is not null)"
                                                 ValueProcessor="@((object value) => value is bool boolValue ? (boolValue ? "Открыта" : "Закрыта") : value.ToString())"
                                                 Icon="@TeslaIcons.Sensors.Door" />
                        </MudPaper>
                    </MudItem>
                }
                @foreach (var item in _power ?? [])
                {
                    <MudItem xs="12"
                             md="6">
                        <MudPaper Elevation="0"
                                  Outlined="true"
                                  Class="pa-4">
                            <SensorDataComponent TopicName="@item.Name"
                                                 Name="@item.DisplayName"
                                                 Error="@(item.Incident is not null)"
                                                 ValueProcessor="@((object value) => value is bool boolValue ? (boolValue ? "Подключен" : "Не подключен") : value.ToString())"
                                                 Icon="@TeslaIcons.Sensors.Power" />
                        </MudPaper>
                    </MudItem>
                }
                @foreach (var item in _leak ?? [])
                {
                    <MudItem xs="12">
                        <MudPaper Elevation="0"
                                  Outlined="true"
                                  Class="pa-4">
                            <SensorDataComponent TopicName="@item.Name"
                                                 Name="@item.DisplayName"
                                                 Error="@(item.Incident is not null)"
                                                 ValueProcessor="@((object value) => value is bool boolValue ? (boolValue ? "Есть протечка" : "Нет протечек") : value.ToString())"
                                                 Icon="@TeslaIcons.Sensors.Leak" />
                        </MudPaper>
                    </MudItem>
                }
            </MudGrid>

            if (_sensorsWithIncidents.Any())
            {
                <MudList T="string">
                    @foreach (var item in _sensorsWithIncidents)
                    {
                        <MudListItem Text="@item.DisplayName"
                                     SecondaryText="Подробнее"
                                     @key="item" />
                    }
                </MudList>
            }
        }
    </ChildContent>
</MudExpansionPanel>