using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using MudBlazor;

namespace Teslametrics.App.Web.Features.Main;

public partial class MainLayout : LayoutComponentBase, IAsyncDisposable, IBrowserViewportObserver
{
	[Inject] public IBrowserViewportService BrowserViewportService { get; set; } = null!;
	[Inject] public IStringLocalizer<MainLayout> Localizer { get; set; } = null!;
	[Inject] public MudTheme CustomTheme { get; set; } = null!;

	#region Drawer
	private bool _drawerOpen = true;
	private DrawerVariant MenuVariant = DrawerVariant.Mini;
	#endregion

	#region Theme mode
	private bool _isDarkMode;
	private MudThemeProvider? _mudThemeProvider;
	#endregion

	public async ValueTask DisposeAsync() => await BrowserViewportService.UnsubscribeAsync(this);

	private void OnToggle()
	{
		_drawerOpen = !_drawerOpen;
	}

	#region Drawer
	public readonly IEnumerable<Breakpoint> Markers = new Breakpoint[] { Breakpoint.Xs, Breakpoint.Sm, Breakpoint.Md, Breakpoint.SmAndDown, Breakpoint.MdAndDown };
	public Guid Id { get; } = Guid.NewGuid();
	public async Task NotifyBrowserViewportChangeAsync(BrowserViewportEventArgs browserViewportEventArgs)
	{
		if (Markers.Contains(browserViewportEventArgs.Breakpoint) && MenuVariant == DrawerVariant.Mini)
		{
			MenuVariant = DrawerVariant.Responsive;

			await InvokeAsync(StateHasChanged);
		}

		if (!Markers.Contains(browserViewportEventArgs.Breakpoint) && MenuVariant == DrawerVariant.Responsive)
		{
			MenuVariant = DrawerVariant.Mini;

			await InvokeAsync(StateHasChanged);
		}
	}
	#endregion

	protected override async Task OnAfterRenderAsync(bool firstRender)
	{
		if (firstRender)
		{
			#region Drawer
			await BrowserViewportService.SubscribeAsync(this, fireImmediately: true);
			#endregion

			#region Theme mode
			if (_mudThemeProvider is not null)
			{
				_isDarkMode = await _mudThemeProvider.GetSystemPreference();
				await _mudThemeProvider.WatchSystemPreference(OnSystemPreferenceChanged);
			}
			#endregion
			StateHasChanged();
		}
	}

	#region Theme mode
	private async Task OnSystemPreferenceChanged(bool newValue)
	{
		_isDarkMode = newValue;
		await InvokeAsync(StateHasChanged);
	}
	#endregion
}
