using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using MudBlazor;
using Teslametrics.App.Web.Services.UserDevice;

namespace Teslametrics.App.Web.Features.Main;

public partial class MainLayout : LayoutComponentBase
{
    [Inject]
    private IUserDeviceService _userDeviceService { get; set; } = null!;

    [Inject] public IStringLocalizer<MainLayout> Localizer { get; set; } = null!;
    [Inject] public MudTheme CustomTheme { get; set; } = null!;

    #region Drawer
    private bool _drawerOpen = false;
    #endregion

    #region Theme mode
    private bool _isDarkMode;
    private MudThemeProvider? _mudThemeProvider;
    #endregion

    protected override void OnInitialized()
    {
        _drawerOpen = !_userDeviceService.IsMobile;

        base.OnInitialized();
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            #region Theme mode
            if (_mudThemeProvider is not null)
            {
                var darkMode = await _mudThemeProvider.GetSystemPreference();
                if (darkMode != _isDarkMode)
                {
                    _isDarkMode = darkMode;
                    StateHasChanged();
                }
                await _mudThemeProvider.WatchSystemPreference(OnSystemPreferenceChanged);
            }
            #endregion
        }
    }

    #region Theme mode
    private async Task OnSystemPreferenceChanged(bool newValue)
    {
        _isDarkMode = newValue;
        await InvokeAsync(StateHasChanged);
    }
    #endregion
}
