using FluentValidation;
using Microsoft.AspNetCore.Components;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Components.Drawer;
using Teslametrics.App.Web.Components.Form;
using Teslametrics.App.Web.Eto.Users;
using Teslametrics.App.Web.Extensions;
using Severity = MudBlazor.Severity;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Users.Drawer.Create;

public partial class UserCreateComponent
{
	private class RoleModel(Guid id, string name)
	{
		public Guid Id { get; set; } = id;
		public string Name { get; set; } = name;
	}
	private class CreateUserModel
	{
		public string Username { get; set; } = string.Empty;

		public string Password { get; set; } = string.Empty;

		public string PasswordConfirm { get; set; } = string.Empty;

		public HashSet<RoleModel> Roles { get; set; } = [];
	}

	private class CreateUserValidator : BaseFluentValidator<CreateUserModel>
	{
		public CreateUserValidator()
		{
			RuleFor(model => model.Username)
				.Length(3, 60)
				.WithMessage("Логин должен быть длиной от 3 до 60 символов");

			RuleFor(model => model.Password)
				.NotEmpty().WithMessage("Поле должно быть заполнено");

			When(model => !string.IsNullOrWhiteSpace(model.Password) && !string.IsNullOrWhiteSpace(model.PasswordConfirm), () =>
			{
				RuleFor(model => model.Password).Equal(model => model.PasswordConfirm).WithMessage("Пароли не совпадают");
				RuleFor(model => model.PasswordConfirm).Equal(model => model.Password).WithMessage("Пароли не совпадают");
			});

			RuleFor(model => model.PasswordConfirm)
				.NotEmpty().WithMessage("Поле должно быть заполнено");

			RuleFor(model => model.Roles)
				.NotEmpty()
				.WithMessage("Добавьте ххотя бы 1 роль");
		}
	}

	[CascadingParameter(Name = DrawerConsts.InstanceName)]
	private DrawerComponent Drawer { get; set; } = null!;

	private bool _isValid;
	private CreateUserModel _model = new();
	private RoleModel? _search;

	private CreateUserValidator _validator = new();

	private PasswordFieldComponent? pwdFieldRef;
	private PasswordFieldComponent? pwdConfirmFieldRef;

	#region Parameters
	[Parameter]
	public Guid OrganizationId { get; set; }
	#endregion

	private async Task<IEnumerable<RoleModel>> FetchRolesAsync(string value, CancellationToken token)
	{
		List<RoleModel> roles = [];
		try
		{
			var result = await ScopeFactory.MediatorSend(new GetRoleListUseCase.Query(OrganizationId, value), token);
			switch (result.Result)
			{
				case GetRoleListUseCase.Result.Success:
					roles.AddRange(result.Items.Select(item => new RoleModel(item.Id, item.Name)));
					break;
				case GetRoleListUseCase.Result.ValidationError:
					Snackbar.Add("Не удалось получить список ролей. Повторите попытку", Severity.Error);
					break;
				case GetRoleListUseCase.Result.Unknown:
				default:
					Snackbar.Add("Не удалось получить список ролей. Повторите попытку", Severity.Error);
					break;
			}
			return roles;
		}
		catch (Exception ex)
		{
			Snackbar.Add("Не удалось получить список ролей. Повторите попытку", Severity.Error);
			Logger.LogError(ex, ex.Message);
		}
		return roles;
	}

	#region [Actions]
	private Task CancelAsync() => Drawer.HideAsync();
	private async Task SubmitAsync()
	{
		if (IsLoading) return;
		try
		{
			await SetLoadingAsync();
			var result = await ScopeFactory.MediatorSend(new CreateUserUseCase.Command(_model.Username, _model.Password, OrganizationId, RoleIds: _model.Roles.Select(x => x.Id).ToList()));
			if (result.IsSuccess)
			{
				Snackbar.Add("Пользователь успешно создан", Severity.Success);
				EventSystem.Publish(new UserSelectEto(result.Id));
				return;
			}

			switch (result.Result)
			{
				case CreateUserUseCase.Result.ValidationError:
					Snackbar.Add("Ошибка валидации данных", Severity.Error);
					break;
				case CreateUserUseCase.Result.RoleNotFound:
					Snackbar.Add("Одна из выбранных ролей недоступна", Severity.Error);
					break;
				case CreateUserUseCase.Result.UsernameAlreadyExists:
					Snackbar.Add("Пользователь уже существует", Severity.Error);
					break;
				case CreateUserUseCase.Result.Unknown:
				default:
					Snackbar.Add("Не удалось создать пользователя из-за непредвиденной ошибки:" + result.Result.ToString(), Severity.Error);
					break;
			}
		}
		catch (Exception ex)
		{
			Snackbar.Add("Не удалось создать пользователя, повторите попытку", Severity.Error);
			Logger.LogError(ex, ex.Message);
		}
		finally
		{
			await SetLoadingAsync(false);
		}
	}
	#endregion

	private async Task OnPwdChange(string pwd)
	{
		_model.Password = pwd;
		if (_model.PasswordConfirm is not null)
		{
			await pwdFieldRef!.Validate();
			await pwdConfirmFieldRef!.Validate();
		}
	}

	private async Task OnPwdConfirmChange(string pwd)
	{
		_model.PasswordConfirm = pwd;
		if (_model.Password is not null)
		{
			await pwdFieldRef!.Validate();
			await pwdConfirmFieldRef!.Validate();
		}
	}

	private void OnRoleSelected(RoleModel role)
	{
		_search = null;
		if (!_model?.Roles.Any(x => x.Id == role.Id) ?? false)
			_model?.Roles.Add(role);
	}
}
