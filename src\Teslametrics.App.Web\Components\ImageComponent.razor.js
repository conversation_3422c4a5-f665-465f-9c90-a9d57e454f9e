export async function loadImageWithRetry(
  dotNetRef,
  imgElement,
  url,
  initialDelay = 1000,
  version = 0
) {
  let delay = initialDelay;

  dotNetRef.invokeMethodAsync("OnImageLoading", version);

  while (true) {
    try {
      await loadOnce(url); // проверяем во временном Image()
      imgElement.src = url; // подменяем только после удачной проверки
      await dotNetRef.invokeMethodAsync("OnImageLoaded", url, version);
      return;
    } catch {
      await sleep(delay);
      delay = Math.min(delay * 2, 30000);
    }
  }
}

function loadOnce(url) {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve();
    img.onerror = img.onabort = () => reject();
    img.src = addCacheBuster(url);
  });
}

function sleep(ms) {
  return new Promise((r) => setTimeout(r, ms));
}

function addCacheBuster(url) {
  const sep = url.includes("?") ? "&" : "?";
  return `${url}${sep}cb=${Date.now()}`;
}
