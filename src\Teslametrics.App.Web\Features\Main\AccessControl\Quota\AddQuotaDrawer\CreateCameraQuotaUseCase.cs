using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.Core.Domain.AccessControl.Organizations;
using Teslametrics.Core.Services.Outbox;
using Teslametrics.Core.Services.TransactionManager;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Quota.AddQuotaDrawer;

public static class CreateCameraQuotaUseCase
{
    public record Command(Guid OrganizationId, List<Quota> Quotas) : BaseRequest<Response>;

    public record Quota(Guid? CameraPresetId, string Name, int Limit, int RetentionPeriodDays, int StorageLimitMb);

    public record Response : BaseResponse
    {
        public Guid Id { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(Guid id)
        {
            Id = id;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Id = Guid.Empty;
            Result = result;
        }
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        OrganizationNotFound
    }

    public class Validator : AbstractValidator<Command>
    {
        public Validator()
        {
            RuleFor(c => c.OrganizationId).NotEmpty();
            RuleFor(c => c.Quotas).NotEmpty()
                .ForEach(quota => quota
                    .ChildRules(quota =>
                    {
                        quota.RuleFor(q => q.Name).NotEmpty();
                        quota.RuleFor(q => q.Limit).Must(limit => limit == -1 || limit >= 1);
                        quota.RuleFor(q => q.RetentionPeriodDays).InclusiveBetween(1, 365);
                        quota.RuleFor(q => q.StorageLimitMb).InclusiveBetween(1, 100_000_000);
                    }));
        }
    }

    public class Handler : IRequestHandler<Command, Response>
    {
        private readonly IValidator<Command> _validator;
        private readonly IOrganizationRepository _organizationRepository;
        private readonly ITransactionManager _transactionManager;
        private readonly IPublisher _publisher;
        private readonly IOutbox _outbox;

        public Handler(IValidator<Command> validator,
                       IOrganizationRepository organizationRepository,
                       ITransactionManager transactionManager,
                       IPublisher publisher,
                       IOutbox outbox)
        {
            _validator = validator;
            _organizationRepository = organizationRepository;
            _transactionManager = transactionManager;
            _publisher = publisher;
            _outbox = outbox;

        }

        public async Task<Response> Handle(Command request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            using var transaction = await _transactionManager.CreateTransactionAsync();

            var organization = await _organizationRepository.FindAsync(request.OrganizationId, cancellationToken);
            if (organization is null)
            {
                return new Response(Result.OrganizationNotFound);
            }

            var events = organization.AddCameraQuotas(request.Quotas.Select(q => (GuidGenerator.New(), q.CameraPresetId, q.Name, q.Limit, q.RetentionPeriodDays, q.StorageLimitMb)));

            await _organizationRepository.SaveChangesAsync(cancellationToken);

            foreach (var @event in events)
            {
                await _publisher.Publish(@event, cancellationToken);
            }

            await _outbox.AddRangeAsync(events);

            await transaction.CommitAsync();

            return new Response(organization.Id);
        }
    }
}
