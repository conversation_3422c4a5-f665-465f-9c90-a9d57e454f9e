using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.DependencyInjection.Extensions;

namespace Teslametrics.App.Web.Services.Authorization;

public static class AuthorizationModule
{
	public static void Install(IServiceCollection services)
	{
		services.TryAddSingleton<IAuthorizationHandler, PolicyAuthorizationHandler>();
		services.AddSingleton<IAuthorizationPolicyProvider, TeslametricsAuthorizationPolicyProvider>();
		services.AddAuthorizationCore();
	}
}
