﻿@using Teslametrics.App.Web.Features.Main.AccessControl.Quota.Drawer.Edit.PresetField
@using Teslametrics.Shared
@inherits InteractiveBaseComponent
<DrawerHeader>
    <MudStack Spacing="0">
        <MudText Typo="Typo.h3">Просмотр квоты</MudText>
        @if (IsLoading)
        {
            <MudSkeleton Width="60%"
                         Height="calc(var(--mud-typography-subtitle1-lineheight) * var(--mud-typography-subtitle1-size))" />
        }
        @if (!IsLoading && _response is not null && _response.IsSuccess)
        {
            <MudText Typo="Typo.subtitle1">@_response.Name</MudText>
        }
        @if (!IsLoading && (_response is null || !_response.IsSuccess))
        {
            <MudText Typo="Typo.subtitle1">Не удалось получить настройки квоты</MudText>
        }
    </MudStack>
    <MudSpacer />
    @if (!_subscribing && (_subscriptionResponse is null || !_subscriptionResponse.IsSuccess))
    {
        <MudTooltip Arrow="true"
                    Placement="Placement.Start"
                    Text="Ошибка подписки на события">
            <MudIconButton OnClick="SubscribeAsync"
                           Icon="@Icons.Material.Filled.ErrorOutline"
                           Color="Color.Error" />
        </MudTooltip>
    }
    @if (!IsLoading && _response is not null && _response.IsSuccess)
    {
        <MudMenu Icon="@Icons.Material.Filled.MoreVert"
                 AriaLabel="Действия с выбранной квотой"
                 Color="Color.Primary"
                 Variant="Variant.Outlined">
            <MudMenuItem OnClick="Cancel"
                         Icon="@Icons.Material.Outlined.PanoramaFishEye">К режиму просмотра</MudMenuItem>
            <AuthorizeView Policy="@(Fqdn<AppPermissions>.GetNames(AppPermissions.Main.CameraQuotas.Delete).Last())"
                           Resource="new PolicyRequirementResource(OrganizationId, QuotaId)"
                           Context="innerContext">
                <MudDivider Class="my-4" />
                <MudMenuItem OnClick="Delete"
                             Icon="@Icons.Material.Outlined.Delete"
                             IconColor="Color.Warning">Удалить</MudMenuItem>
            </AuthorizeView>
        </MudMenu>
    }
</DrawerHeader>
@if (IsLoading)
{
    <MudProgressLinear Color="Color.Primary"
                       Indeterminate="true" />
}
else
{
    <div style="height: 4px;" />
}

<MudStack Spacing="8"
          Class="px-4 pt-4">
    @if (_response is not null && _response.IsSuccess && _model is not null)
    {
        <MudForm Model="_model"
                 Validation="_validator.ValidateValue"
                 @bind-IsValid="_isValid"
                 OverrideFieldValidation="true"
                 UserAttributes="@(new Dictionary<string, object>() { { "autocomplete", "off" }, { "aria-autocomplete", "none" }, { "role", "presentation" } })"
                 Spacing="8">
            <MudTextField @bind-Value="_model.Name"
                          Variant="Variant.Text"
                          Label="Наименование" />

            <PresetFieldComponent @bind-Selected="_model.Preset"
                                  For="@(() => _model.Preset)" />

            <MudCheckBox T="bool"
                         Value="_model.TotalQuota == -1"
                         ValueChanged="() => _model.TotalQuota = _model.TotalQuota != -1 ? -1 : 1"
                         Label="Количество камер неограничено"
                         LabelPlacement="@Placement.End"
                         Class="ml-n4" />
            @if (_model.TotalQuota != -1)
            {
                <MudNumericField T="int"
                                 @bind-Value="_model.TotalQuota"
                                 For="() => _model.TotalQuota"
                                 Min="_response.UsedQuota"
                                 Immediate="true"
                                 RequiredError="Задайте значение" />
            }
            <MudNumericField T="int"
                             @bind-Value="_model.StorageLimitMb"
                             For="@(() => _model.StorageLimitMb)"
                             Variant="Variant.Text"
                             Min="1"
                             Label="Расчётный объём хранимых записей, мб"
                             HelperText="Объём записей должен варьироваться от 1 мегабайта" />

            <MudNumericField T="int"
                             @bind-Value="_model.RetentionPeriodDays"
                             For="@(() => _model.RetentionPeriodDays)"
                             Label="Глубина хранения записей, дней"
                             Clearable="true"
                             Variant="Variant.Text"
                             Min="1"
                             Max="365"
                             Immediate="true"
                             HelperText="От 1 до 365 дней"
                             RequiredError="Данное поле обязательно"
                             Pattern="^([+,0-9.]+)"
                             Required="true" />
        </MudForm>
    }
    <FormLoadingComponent IsLoading="IsLoading && _response is null" />
    <NoItemsFoundComponent HasItems="!IsLoading && _response is not null && _response.IsSuccess" />
</MudStack>

<DrawerActions>
    <MudSpacer />
    <MudButton OnClick="Cancel">Отменить</MudButton>
    <AuthorizeView Policy="@AppPermissions.Main.CameraQuotas.Update.GetEnumPermissionString()"
                   Context="innerContext"
                   Resource="new PolicyRequirementResource(OrganizationId, QuotaId)">
        <MudButton OnClick="SubmitAsync"
                   Color="Color.Secondary"
                   Variant="Variant.Outlined">Сохранить</MudButton>
    </AuthorizeView>
</DrawerActions>