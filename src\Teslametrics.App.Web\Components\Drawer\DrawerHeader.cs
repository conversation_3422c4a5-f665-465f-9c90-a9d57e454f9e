using Microsoft.AspNetCore.Components;

namespace Teslametrics.App.Web.Components.Drawer;

public class DrawerHeader : ComponentBase, IDisposable
{
	[Parameter]
	public RenderFragment? ChildContent { get; set; }

	[Parameter]
	public string? Class { get; set; }

	[CascadingParameter(Name = DrawerConsts.InstanceName)]
	public DrawerComponent Drawer { get; set; } = null!;

	public event Action? OnChange;

	protected override void OnInitialized()
	{
		base.OnInitialized();

		Drawer.SetHeader(this);
		Update();
	}

	protected override void OnParametersSet()
	{
		base.OnParametersSet();

		OnChange?.Invoke();
	}

	public void Update()
	{
		OnChange?.Invoke();
		StateHasChanged();
	}


	public void Dispose()
	{
		Drawer?.SetHeader(null);
	}
}
