﻿@inherits InteractiveBaseComponent
<MudAutocomplete T="GetDeviceListUseCase.Response.Item"
                 SearchFunc="@SearchAsync"
                 ToStringFunc="@(e => e == null ? null : e.Name)"
                 Value="@_selected"
                 ValueChanged="@ValueChanged"
                 Label="Оборудование"
                 Clearable="true"
                 Margin="Margin.Dense"
                 ResetValueOnEmptyText="true"
                 Variant="Variant.Outlined"
                 Disabled="@_disabled" />

@code {
    private bool _disabled => BuildingId is null || CityId is null || FloorId is null || RoomId is null;
    private GetDeviceListUseCase.Response.Item? _selected;

    [Parameter]
    public Guid? CityId { get; set; }

    [Parameter]
    public Guid? BuildingId { get; set; }

    [Parameter]
    public Guid? FloorId { get; set; }

    [Parameter]
    public Guid? RoomId { get; set; }

    [Parameter]
    public Guid? DeviceId { get; set; }

    [Parameter]
    public EventCallback<Guid?> DeviceIdChanged { get; set; }

    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();
        if (_selected?.Id != DeviceId)
        {
            _selected = null;
        }
    }

    private async Task ValueChanged(GetDeviceListUseCase.Response.Item? item)
    {
        _selected = item;
        await DeviceIdChanged.InvokeAsync(item?.Id);
    }

    private async Task<IEnumerable<GetDeviceListUseCase.Response.Item>> SearchAsync(string value, CancellationToken token)
    {
        if (!CityId.HasValue || !BuildingId.HasValue || !FloorId.HasValue || !RoomId.HasValue) return [];

        GetDeviceListUseCase.Response? response = null;
        try
        {
            response = await ScopeFactory.MediatorSend(new GetDeviceListUseCase.Query(CityId!.Value, BuildingId!.Value, FloorId!.Value, RoomId!.Value, value), token);
        }
        catch (TaskCanceledException)
        {
            // Search was canceled, just return empty result
            return Enumerable.Empty<GetDeviceListUseCase.Response.Item>();
        }
        catch (Exception exc)
        {
            Logger.LogError(exc, "Error searching cities");
            Snackbar.Add("Не удалось получить список этажей из-за непредвиденной ошибки.", MudBlazor.Severity.Error);
            return Enumerable.Empty<GetDeviceListUseCase.Response.Item>();
        }

        if (response.Result == GetDeviceListUseCase.Result.Success)
            return response.Items;

        if (response.Result == GetDeviceListUseCase.Result.ValidationError)
            Snackbar.Add("Ошибка валидации при получении списка городов", MudBlazor.Severity.Error);
        else
            Snackbar.Add("Не удалось получить список городов из-за непредвиденной ошибки.", MudBlazor.Severity.Error);

        return Enumerable.Empty<GetDeviceListUseCase.Response.Item>();
    }
}