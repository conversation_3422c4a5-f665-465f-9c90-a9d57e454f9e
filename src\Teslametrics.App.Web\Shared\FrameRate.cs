using System.ComponentModel.DataAnnotations;

namespace Teslametrics.App.Web.Shared;

public enum FrameRate
{
    [Display(Name = "1/16 FPS")]
    Fps1_16 = 0,

    [Display(Name = "1/8 FPS")]
    Fps1_8 = 1,

    [Display(Name = "1/4 FPS")]
    Fps1_4 = 2,

    [Display(Name = "1/2 FPS")]
    Fps1_2 = 3,

    [Display(Name = "1 FPS")]
    Fps1 = 4,

    [Display(Name = "2 FPS")]
    Fps2 = 5,

    [Display(Name = "4 FPS")]
    Fps4 = 6,

    [Display(Name = "6 FPS")]
    Fps6 = 7,

    [Display(Name = "8 FPS")]
    Fps8 = 8,

    [Display(Name = "10 FPS")]
    Fps10 = 9,

    [Display(Name = "12 FPS")]
    Fps12 = 10,

    [Display(Name = "15 FPS")]
    Fps15 = 11,

    [Display(Name = "16 FPS")]
    Fps16 = 12,

    [Display(Name = "18 FPS")]
    Fps18 = 13,

    [Display(Name = "20 FPS")]
    Fps20 = 14,

    [Display(Name = "22 FPS")]
    Fps22 = 15,

    [Display(Name = "25 FPS")]
    Fps25 = 16,

    [Display(Name = "30 FPS")]
    Fps30 = 17,

    [Display(Name = "60 FPS")]
    Fps60 = 18
}