using Orleans.Streams;
using Teslametrics.MediaServer.Orleans.Camera.Events;
using Teslametrics.Shared;
using static Teslametrics.MediaServer.Orleans.Camera.ICameraGrain;

namespace Teslametrics.MediaServer.Orleans.Camera;

public class CameraGrain : Grain, ICameraGrain
{
    private readonly ILogger<CameraGrain> _logger;
    private RtspStateMachine _rtspStateMachine;
    private readonly OnvifStateMachine _onvifStateMachine;
    private readonly CancellationTokenSource _cts;
    private CameraStatus _cameraStatus = CameraStatus.Stopped;
    private IAsyncStream<CameraStatusChangedEvent>? _eventLiveStream;
    private Task? _updateStatusTask;
    private IGrainTimer? _rtspStateMachineTimer;
    private IGrainTimer? _onvifStateMachineTimer;
    private static readonly TimeSpan _updateInterval = TimeSpan.FromSeconds(1);

    public CameraGrain(ILoggerFactory loggerFactory,
                       IServiceScopeFactory serviceScopeFactory)
    {
        _logger = loggerFactory.CreateLogger<CameraGrain>();

        _rtspStateMachine = new RtspStateMachine(loggerFactory.CreateLogger<RtspStateMachine>(), this.GetPrimaryKey(), serviceScopeFactory);
        _rtspStateMachine.Disconnected += () =>
        {
            _rtspStateMachineTimer!.Dispose();
            _rtspStateMachineTimer = null;
        };

        _onvifStateMachine = new OnvifStateMachine(loggerFactory.CreateLogger<OnvifStateMachine>(), this.GetPrimaryKey(), serviceScopeFactory);
        _onvifStateMachine.Disconnected += () =>
        {
            _onvifStateMachineTimer!.Dispose();
            _onvifStateMachineTimer = null;
        };

        _cts = new CancellationTokenSource();
    }

    public override async Task OnActivateAsync(CancellationToken cancellationToken)
    {
        var provider = this.GetStreamProvider(StreamNames.CameraEventLiveStream);
        _eventLiveStream = provider.GetStream<CameraStatusChangedEvent>(StreamId.Create(StreamNamespaces.CameraStreams, Guid.Empty));

        _updateStatusTask = Task.Run(() => UpdateStatusAsync(_cts.Token));
        await base.OnActivateAsync(cancellationToken);
    }

    public override async Task OnDeactivateAsync(DeactivationReason reason, CancellationToken cancellationToken)
    {
        if (await _rtspStateMachine.GetStatusAsync() is not RtspStateMachine.Status.Stopped)
        {
            _rtspStateMachine.Disconnect();
        }

        if (_onvifStateMachine.GetStatus() is not (OnvifStateMachine.Status.Disabled or OnvifStateMachine.Status.Stopped))
        {
            await _onvifStateMachine.DisconnectAsync();
        }

        _cts.Cancel();
        await _updateStatusTask!;

        // Освобождаем CancellationTokenSource
        _cts.Dispose();

        await base.OnDeactivateAsync(reason, cancellationToken);
    }

    public Task<CameraStatus> GetStatusAsync() =>
        Task.FromResult(_cameraStatus);

    public async Task ConnectRtspAsync(ConnectRtspRequest request)
    {
        if (await _rtspStateMachine.GetStatusAsync() is RtspStateMachine.Status.Stopped)
        {
            _rtspStateMachineTimer = this.RegisterGrainTimer(_rtspStateMachine.ProcessAsync, new GrainTimerCreationOptions(TimeSpan.Zero, TimeSpan.FromMilliseconds(100)) { KeepAlive = true });
            _rtspStateMachine.Connect(request.ArchiveUri, request.ViewUri, request.PublicUri);
        }
    }

    public async Task ConnectOnvifAsync(ConnectOnvifRequest request)
    {
        if (_onvifStateMachine.GetStatus() is OnvifStateMachine.Status.Disabled or OnvifStateMachine.Status.Stopped)
        {
            _onvifStateMachineTimer = this.RegisterGrainTimer(_onvifStateMachine.ProcessAsync, new GrainTimerCreationOptions(TimeSpan.Zero, TimeSpan.FromMilliseconds(100)) { KeepAlive = true });
            await _onvifStateMachine.ConnectAsync(request.Host, request.Port, request.Username, request.Password);
        }
    }

    public async Task DisconnectAsync()
    {
        if (await _rtspStateMachine.GetStatusAsync() is not RtspStateMachine.Status.Stopped)
        {
            _rtspStateMachine.Disconnect();
        }

        if (_onvifStateMachine.GetStatus() is not (OnvifStateMachine.Status.Disabled or OnvifStateMachine.Status.Stopped))
        {
            await _onvifStateMachine.DisconnectAsync();
        }
    }

    public Task<GetCameraStreamIdResponse> GetCameraStreamIdAsync(GetCameraStreamIdRequest request) =>
         Task.FromResult(new GetCameraStreamIdResponse(_rtspStateMachine.GetCameraStreamGrain(request.StreamType)?.GetPrimaryKey()));

    private async Task UpdateStatusAsync(CancellationToken cancellationToken)
    {
        var currentTime = DateTime.UtcNow;

        while (!cancellationToken.IsCancellationRequested)
        {
            var state = (await _rtspStateMachine.GetStatusAsync(), _onvifStateMachine.GetStatus());

            var status = state switch
            {
                (RtspStateMachine.Status.Problem, _) => CameraStatus.Problem,
                (_, OnvifStateMachine.Status.Problem) => CameraStatus.Problem,
                (RtspStateMachine.Status.Stopped, OnvifStateMachine.Status.Disabled) => CameraStatus.Stopped,
                (RtspStateMachine.Status.Starting, OnvifStateMachine.Status.Disabled) => CameraStatus.Starting,
                (RtspStateMachine.Status.Running, OnvifStateMachine.Status.Disabled) => CameraStatus.Running,
                (RtspStateMachine.Status.Stopped, OnvifStateMachine.Status.Stopped) => CameraStatus.Stopped,
                (RtspStateMachine.Status.Starting, _) => CameraStatus.Starting,
                (_, OnvifStateMachine.Status.Starting) => CameraStatus.Starting,
                (RtspStateMachine.Status.Running, OnvifStateMachine.Status.Running) => CameraStatus.Running,
                _ => CameraStatus.Problem
            };

            if (_cameraStatus != status)
            {
                _cameraStatus = status;

                await _eventLiveStream!.OnNextAsync(new CameraStatusChangedEvent(this.GetPrimaryKey(), _cameraStatus));
            }

            var deltaTime = DateTime.UtcNow - currentTime;

            var remainingTime = _updateInterval - deltaTime;

            if (remainingTime > TimeSpan.Zero)
            {
                await Task.Delay(remainingTime);
            }

            currentTime = DateTime.UtcNow;
        }
    }
}