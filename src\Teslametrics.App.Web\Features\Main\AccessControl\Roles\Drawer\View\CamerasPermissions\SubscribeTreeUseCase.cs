using System.Reactive.Linq;
using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.Core.Domain.Cameras.Events;
using Teslametrics.Core.Domain.Folders.Events;
using Teslametrics.Core.Services.DomainEventBus;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Roles.Drawer.View.CamerasPermissions;

public static class SubscribeTreeUseCase
{
    public record Request(IObserver<object> Observer, Guid RoleId) : BaseRequest<Response>; // Если удаляют/обновляют директорию/камеру к которой есть доступ у пользователя

    public record Response : BaseResponse
    {
        public IDisposable? Subscription { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(IDisposable subscription)
        {
            Subscription = subscription;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Subscription = null;
            Result = result;
        }
    }

    public record UpdatedEvent;

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError
    }

    public class Validator : AbstractValidator<Request>
    {
        public Validator()
        {
            RuleFor(r => r.Observer).NotEmpty();
            RuleFor(r => r.RoleId).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Request, Response>
    {
        private readonly IValidator<Request> _validator;
        private readonly IDomainEventBus _domainEventBus;

        public Handler(IValidator<Request> validator,
                       IDomainEventBus domainEventBus)
        {
            _validator = validator;
            _domainEventBus = domainEventBus;
        }

        public async Task<Response> Handle(Request request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            var eventStream = await _domainEventBus.GetEventStreamAsync();

            var subscription = eventStream
                .Where(e => e switch
                {
                    FolderUpdatedEvent @event => @event.OrganizationId == request.RoleId,
                    FolderParentChangedEvent @event => @event.OrganizationId == request.RoleId,
                    FolderDeletedEvent @event => @event.OrganizationId == request.RoleId,
                    CameraUpdatedEvent @event => @event.OrganizationId == request.RoleId,
                    CameraFolderChangedEvent @event => @event.OrganizationId == request.RoleId,
                    CameraDeletedEvent @event => @event.OrganizationId == request.RoleId,
                    _ => false
                })
                .Select(e => new UpdatedEvent())
                .Subscribe(request.Observer);

            return new Response(subscription);
        }
    }
}