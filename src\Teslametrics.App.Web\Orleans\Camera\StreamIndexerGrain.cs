using Dapper;
using Orleans.Streams;
using Orleans.Streams.Core;
using System.Data;
using Teslametrics.App.Web.Services.FileStorage;
using Teslametrics.App.Web.Services.Persistence;

namespace Teslametrics.App.Web.Orleans.Camera;

[ImplicitStreamSubscription(StreamTopicNames.MinioEvents)]
public class StreamIndexerGrain : Grain, IStreamIndexerGrain, IStreamSubscriptionObserver, IAsyncObserver<S3EventNotification>
{
    private readonly IServiceScopeFactory _serviceScopeFactory;
    private readonly IFileStorage _fileStorage;
    private readonly ILogger<StreamIndexerGrain> _logger;

    public StreamIndexerGrain(IServiceScopeFactory serviceScopeFactory,
                              IFileStorage fileStorage,
                              ILogger<StreamIndexerGrain> logger)
    {
        _serviceScopeFactory = serviceScopeFactory;
        _fileStorage = fileStorage;
        _logger = logger;
    }

    public override async Task OnActivateAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("StreamIndexerGrain activated");

        IStreamProvider streamProvider = this.GetStreamProvider(StreamNames.PersistentStream);
        StreamId streamId =
            StreamId.Create(StreamTopicNames.MinioEvents, StreamTopicNames.MinioEvents);
        IAsyncStream<S3EventNotification> stream =
            streamProvider.GetStream<S3EventNotification>(streamId);

        StreamSubscriptionHandle<S3EventNotification> subscription =
            await stream.SubscribeAsync(this);

        await base.OnActivateAsync(cancellationToken);
    }

    public Task OnCompletedAsync()
    {
        return Task.CompletedTask;
    }

    public Task OnErrorAsync(Exception ex)
    {
        return Task.CompletedTask;
    }

    public async Task OnNextAsync(S3EventNotification item, StreamSequenceToken? token = null)
    {
        try
        {
            var splittedKey = item.Key.Split('/');

            var cameraId = splittedKey[0];
            var tableName = $"{Db.StreamSegments.Table}_{cameraId}";
            var segmentName = splittedKey[1];

            switch (item.EventName)
            {
                case "s3:ObjectCreated:Put":
                    await IndexStreamSegmentAsync(cameraId, tableName, segmentName);
                    break;
                case "s3:ObjectRemoved:Delete":
                    await RemoveStreamSegmentIndexAsync(tableName, segmentName);
                    break;
                default:
                    return;
            }
        }
        catch (Exception exc)
        {
            _logger.LogError(exc, "Error processing stream event: {Message}", exc.Message);
        }
    }

    private async Task IndexStreamSegmentAsync(string cameraId, string tableName, string segmentName)
    {
        try
        {
            var tags = await _fileStorage.GetFileTagsAsync(cameraId, segmentName);

            using var scope = _serviceScopeFactory.CreateScope();
            using var dbConnection = scope.ServiceProvider.GetRequiredService<IDbConnection>();

            if (dbConnection.State != ConnectionState.Open)
            {
                dbConnection.Open();
            }

            var startTime = DateTimeOffset.FromUnixTimeMilliseconds(long.Parse(tags["StartTime"]));
            var endTime = DateTimeOffset.FromUnixTimeMilliseconds(long.Parse(tags["EndTime"]));

            var sql = $"INSERT INTO {tableName} ({Db.StreamSegments.Columns.FileName}, {Db.StreamSegments.Columns.StartTime}, {Db.StreamSegments.Columns.EndTime}) " +
                    $"VALUES (:FileName, :StartTime, :EndTime) ON CONFLICT ({Db.StreamSegments.Columns.StartTime}) DO NOTHING";

            await dbConnection.ExecuteAsync(sql, new { FileName = segmentName, startTime, endTime });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error indexing stream segment: {CameraId}, {SegmentName}",
                cameraId, segmentName);
        }
    }

    private async Task RemoveStreamSegmentIndexAsync(string tableName, string segmentName)
    {
        try
        {
            using var scope = _serviceScopeFactory.CreateScope();
            using var dbConnection = scope.ServiceProvider.GetRequiredService<IDbConnection>();

            if (dbConnection.State != ConnectionState.Open)
            {
                dbConnection.Open();
            }

            var sql = $"DELETE FROM {tableName} WHERE {Db.StreamSegments.Columns.FileName} = :FileName";

            await dbConnection.ExecuteAsync(sql, new { FileName = segmentName });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing segment index: {TableName}, {SegmentName}",
                tableName, segmentName);
        }
    }

    public async Task OnSubscribed(IStreamSubscriptionHandleFactory handleFactory)
    {
        var handle = handleFactory.Create<S3EventNotification>();
        await handle.ResumeAsync(this);
    }
}

[Alias("Teslametrics.App.Web.Orleans.IStreamIndexerGrain")]
public interface IStreamIndexerGrain : IGrainWithStringKey
{
}