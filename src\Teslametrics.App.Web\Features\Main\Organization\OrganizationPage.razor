﻿@using Teslametrics.App.Web.Features.Main.Organization.Organizations;

@rendermode InteractiveServer
@attribute [Authorize]
@attribute [Route(RouteConstants.Organizations)]

@inherits BaseComponent

<div class="overflow-auto mud-height-full pt-4">
	<MudContainer MaxWidth="MaxWidth.False"
				  Class="mud-height-full">
		<MudPaper Class="py-3">
			<OrganizationsListComponent />
		</MudPaper>
	</MudContainer>
</div>