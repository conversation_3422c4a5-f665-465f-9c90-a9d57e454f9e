﻿::deep .list {
	overflow: auto;
}

::deep .list .list_item {
	border-radius: 8px;
	border: none;
	background: rgba(248, 252, 255, 1);
}

.mud_theme_light div ::deep .list .list_item {
	background: rgba(248, 252, 255, 1);
}

.mud_theme_dark div ::deep .list .list_item {
	background: #33333a;
}

::deep .list .list_item:not(:first-child) {
	margin-top: 8px;
}

::deep .list .list_item .content {
	display: flex;
	flex-direction: row;
	align-content: center;
	align-items: center;
	gap: 10px;
	overflow: auto;
	white-space: nowrap;
}

::deep .list .list_item .content {
	color: rgba(184, 194, 204, 1) !important;
}

::deep .list .list_item .content .room_name {
	color: rgba(92, 97, 102, 1);
}