using Microsoft.EntityFrameworkCore;

namespace Teslametrics.App.Web.Abstractions;

public abstract class BaseRepository<TEntity> : IRepository<TEntity>
    where TEntity : class, IEntity
{
    protected readonly DbContext DbContext;

    public BaseRepository(DbContext dbContext)
    {
        DbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
    }

    public Task<TEntity?> FindAsync(Guid id,
                                    CancellationToken cancellationToken = default) =>
        DbContext.Set<TEntity>()
            .AsTracking()
            .OrderBy(entity => entity.Id)
            .FirstOrDefaultAsync(entity => entity.Id == id, cancellationToken);

    public async Task AddAsync(TEntity entity,
                               CancellationToken cancellationToken = default) =>
        await DbContext.Set<TEntity>()
            .AddAsync(entity, cancellationToken);

    public async Task DeleteAsync(Guid id,
                                  CancellationToken cancellationToken = default)
    {
        var entity = await DbContext.Set<TEntity>()
            .AsTracking()
            .OrderBy(entity => entity.Id)
            .FirstOrDefaultAsync(e => e.Id == id, cancellationToken);

        if (entity is null)
        {
            return;
        }

        DbContext.Remove(entity);
    }

    public Task SaveChangesAsync(CancellationToken cancellationToken = default) =>
        DbContext.SaveChangesAsync(cancellationToken);
}