// Store chart instances to update them later
let temperatureChart = null;
let humidityChart = null;

/**
 * Initialize the temperature line chart
 * @param {Object} data - The chart data
 */
export function initTemperatureChart(data) {
    if (temperatureChart) {
        temperatureChart.destroy();
    }

    const options = {
        chart: {
            type: 'line',
            height: 300,
            toolbar: {
                show: false
            }
        },
        colors: ['#FF5722', '#2196F3', '#4CAF50'],
        stroke: {
            width: [3, 2, 2],
            curve: 'smooth',
            dashArray: [0, 0, 5]
        },
        xaxis: {
            categories: data.categories
        },
        yaxis: {
            min: -100,
            max: 100,
            tickAmount: 5
        },
        grid: {
            xaxis: {
                lines: {
                    show: true
                }
            },
            yaxis: {
                lines: {
                    show: true
                }
            }
        },
        legend: {
            position: 'top',
            horizontalAlign: 'right'
        },
        series: data.series,
        markers: {
            size: 4,
            strokeWidth: 0,
            hover: {
                size: 6
            }
        }
    };

    temperatureChart = new ApexCharts(document.querySelector("#temperature-chart"), options);
    temperatureChart.render();
}

/**
 * Initialize the humidity line chart
 * @param {Object} data - The chart data
 */
export function initHumidityChart(data) {
    if (humidityChart) {
        humidityChart.destroy();
    }

    const options = {
        chart: {
            type: 'line',
            height: 300,
            toolbar: {
                show: false
            }
        },
        colors: ['#FF5722', '#2196F3', '#4CAF50'],
        stroke: {
            width: [3, 2, 2],
            curve: 'smooth',
            dashArray: [0, 0, 5]
        },
        xaxis: {
            categories: data.categories
        },
        yaxis: {
            min: -100,
            max: 100,
            tickAmount: 5
        },
        grid: {
            xaxis: {
                lines: {
                    show: true
                }
            },
            yaxis: {
                lines: {
                    show: true
                }
            }
        },
        legend: {
            position: 'top',
            horizontalAlign: 'right'
        },
        series: data.series,
        markers: {
            size: 4,
            strokeWidth: 0,
            hover: {
                size: 6
            }
        }
    };

    humidityChart = new ApexCharts(document.querySelector("#humidity-chart"), options);
    humidityChart.render();
}

