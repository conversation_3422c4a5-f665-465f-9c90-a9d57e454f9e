using Dapper;
using FluentValidation;
using MediatR;
using System.Data;
using System.Security.Claims;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.Core.Services.Persistence;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Services.UserSession;

public static class GetUserClaimsFunction
{
    public record Command(Guid UserId) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public List<(string, string)> Permissions { get; private set; }
        public Result Result { get; private set; }
        public bool IsSuccess => Result == Result.Success;

        public Response(List<(string, string)> permissions)
        {
            Permissions = permissions;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Permissions = [];
            Result = result;
        }
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        UserLockedout,
        UserNotFound
    }
    public class Validator : AbstractValidator<Command>
    {
        public Validator()
        {
            RuleFor(c => c.UserId).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Command, Response>
    {
        private readonly IValidator<Command> _validator;
        private readonly IDbConnection _dbConnection;

        public Handler(IValidator<Command> validator,
                       IDbConnection dbConnection)
        {
            _validator = validator;
            _dbConnection = dbConnection;
        }

        public async Task<Response> Handle(Command request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            var permissions = new List<(string, string)>
            {
                (ClaimTypes.NameIdentifier, request.UserId.ToString())
            };

            var appPermissions = AppPermissions.GetAll();

            if (request.UserId == SystemConsts.RootUserId)
            {
                permissions.AddRange(appPermissions.Select(p => ("Permission", $"*/{p}/*")));
            }
            else
            {
                var template = SqlQueryBuilder.Create()
                    .Select(Db.Users.Props.Id)
                    .Select(Db.Users.Props.LockoutEnabled)
                    .Select(Db.Organizations.Props.Id, "OrganizationId")
                    .LeftJoin(Db.Organizations.Table, Db.Organizations.Props.OwnerId, Db.Users.Props.Id, SqlOperator.Equals)
                    .Where(Db.Users.Props.Id, ":Id", SqlOperator.Equals, new { request.UserId })
                    .Build(QueryType.Standard, Db.Users.Table, RowSelection.AllRows);


                var queryResult = await _dbConnection.QueryAsync<UserModel, OrganizationModel, UserModel>(template.RawSql, (user, organization) =>
                {
                    if (organization is not null)
                    {
                        user.OwnedOrganizations.Add(organization);
                    }

                    return user;
                },
                template.Parameters, splitOn: "OrganizationId");

                if (!queryResult.Any())
                {
                    return new Response(Result.UserNotFound);
                }

                var groupResult = queryResult.GroupBy(u => u.Id).Select(u =>
                {
                    var groupedUser = u.First();
                    groupedUser.OwnedOrganizations = u.SelectMany(u => u.OwnedOrganizations).ToList();

                    return groupedUser;
                });

                var user = groupResult.Single();

                if (user.LockoutEnabled)
                {
                    return new Response(Result.UserLockedout);
                }

                // TODO возможно список не полный, смотри сюда LogInDomainService
                string[] exceptPermission =
                [
                    Fqdn<AppPermissions>.GetName(AppPermissions.Main.AccessControl.Organizations.Create),
                    Fqdn<AppPermissions>.GetName(AppPermissions.Main.AccessControl.Organizations.Delete)
                ];

                foreach (var organizationId in user.OwnedOrganizations)
                {
                    permissions.AddRange(appPermissions.Where(p => !exceptPermission.Contains(p)).Select(p => ("Permission", $"{organizationId}/{p}/*")));
                }

                var permissionTemplate = SqlQueryBuilder.Create()
                    .Select(Db.Roles.Props.OrganizationId)
                    .Select(Db.RolePermissions.Props.Permission)
                    .Select(Db.RolePermissions.Props.ResourceId)
                    .InnerJoin(Db.Roles.Table, Db.Roles.Props.Id, Db.RolePermissions.Props.RoleId, SqlOperator.Equals)
                    .InnerJoin(Db.UserRoles.Table, Db.UserRoles.Props.RoleId, Db.RolePermissions.Props.RoleId, SqlOperator.Equals)
                    .Where(Db.UserRoles.Props.UserId, ":UserId", SqlOperator.Equals, new { request.UserId })
                    .Build(QueryType.Standard, Db.RolePermissions.Table, RowSelection.AllRows);

                var userPermissions = await _dbConnection.QueryAsync<PermissionModel>(permissionTemplate.RawSql, permissionTemplate.Parameters);

                permissions.AddRange(userPermissions.Select(p => ("Permission", $"{(p.OrganizationId == SystemConsts.RootOrganizationId ? "*" : p.OrganizationId)}/{p.Permission}/{(p.ResourceId == SystemConsts.ResourceWildcardId ? "*" : p.ResourceId)}")));
            }

            return new Response(permissions);
        }
    }

    public record UserModel(Guid Id, bool LockoutEnabled)
    {
        public List<OrganizationModel> OwnedOrganizations { get; set; } = [];
    }

    public record OrganizationModel(Guid OrganizationId);

    public record PermissionModel(Guid OrganizationId, string Permission, Guid ResourceId);
}