using FluentValidation;
using Microsoft.AspNetCore.Components;
using System.Reactive;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Components.Drawer;
using Teslametrics.App.Web.Events.Organization;
using Teslametrics.App.Web.Extensions;
using Severity = MudBlazor.Severity;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Organizations.Drawer.Edit;

public partial class OrganizationEditComponent
{
	private class UserModel(Guid id, string name)
	{
		public Guid Id { get; set; } = id;
		public string Name { get; set; } = name;
	}
	private class OrganizationModel(Guid id, string name, UserModel? owner)
	{
		public Guid Id { get; set; } = id;
		public string Name { get; set; } = name;
		public UserModel? Owner { get; set; } = owner;
	}

	private class CreateUserValidator : BaseFluentValidator<OrganizationModel>
	{
		public CreateUserValidator()
		{
			RuleFor(model => model.Name)
				.Length(3, 60)
				.WithMessage("Наименование должно быть длиной от 3 до 60 символов");
			RuleFor(c => c.Owner)
				.NotEmpty().WithMessage("Поле должно быть заполнено");
		}
	}


	[CascadingParameter(Name = DrawerConsts.InstanceName)]
	private DrawerComponent Drawer { get; set; } = null!;

	private DateTime _lastRefreshTime = DateTime.Now;

	private Guid _organizationId;
	private CreateUserValidator _validator = new();
	private bool _isValid;

	private Guid? _ownerId;
	private OrganizationModel? _model;
	private GetOrganizationUseCase.Response? _response;

	private bool _subscribing;
	private SubscribeOrganizationUseCase.Response? _subscriptionResult;


	#region Parameters
	[Parameter]
	[EditorRequired]
	public Guid OrganizationId { get; set; }
	#endregion

	protected override async Task OnParametersSetAsync()
	{
		if (OrganizationId != _organizationId)
		{
			_organizationId = OrganizationId;
			_model = null;
			await FetchAsync();
		}

		await base.OnParametersSetAsync();
	}

	protected async Task FetchAsync()
	{
		if (IsLoading) return;

		try
		{
			await SetLoadingAsync();
			_response = await ScopeFactory.MediatorSend(new GetOrganizationUseCase.Query(OrganizationId));
			_ownerId = _response.OwnerId;
			switch (_response.Result)
			{
				case GetOrganizationUseCase.Result.Success:
					_lastRefreshTime = DateTime.Now;
					_model = new(_response.Id, _response.Name, new UserModel(_response.OwnerId, _response.Owner));
					await SubscribeAsync();
					break;
				case GetOrganizationUseCase.Result.ValidationError:
					Snackbar.Add("Ошибка валидации данных", Severity.Error);
					break;
				case GetOrganizationUseCase.Result.OrganizationNotFound:
					Snackbar.Add("Организация не найдена", Severity.Error);
					break;
				case GetOrganizationUseCase.Result.Unknown:
				default:
					throw new Exception($"Unexpected result in {nameof(GetOrganizationUseCase)}: {_response.Result}");
			}
		}
		catch (Exception ex)
		{
			Snackbar.Add("Не удалось получить выбранную организацию. Повторите попытку", Severity.Error);
			Logger.LogError(ex, ex.Message);
		}
		finally
		{
			await SetLoadingAsync(false);
		}
	}

	private async Task SubscribeAsync()
	{
		try
		{
			if (_response is null || !_response.IsSuccess) return;
			await SetSubscribingAsync(true);
			Unsubscribe();
			_subscriptionResult = await ScopeFactory.MediatorSend(new SubscribeOrganizationUseCase.Request(Observer.Create<object>(OnAppEventHandler, OnError), _response.Id));
			switch (_subscriptionResult.Result)
			{
				case SubscribeOrganizationUseCase.Result.Success:
					CompositeDisposable.Add(_subscriptionResult.Subscription!);
					break;

				case SubscribeOrganizationUseCase.Result.ValidationError:
					Snackbar.Add("Ошибка валидации при подписке на события", Severity.Error);
					break;

				case SubscribeOrganizationUseCase.Result.Unknown:
				default:
					throw new Exception($"Unexpected result in {nameof(SubscribeOrganizationUseCase)}: {_subscriptionResult.Result}");
			}
		}
		catch (Exception exc)
		{
			Snackbar.Add("Не удалось получить подписку на события роли. Повторите попытку", Severity.Error);
			Logger.LogError(exc, exc.Message);
		}
		finally
		{
			await SetSubscribingAsync(false);
		}
	}

	private void Unsubscribe()
	{
		if (_subscriptionResult is not null && _subscriptionResult.Subscription is not null)
		{
			CompositeDisposable.Remove(_subscriptionResult.Subscription);
			_subscriptionResult.Subscription.Dispose();
		}
	}
	protected Task SetSubscribingAsync(bool isLoading = true) => UpdateViewAsync(() =>
	{
		_subscribing = isLoading;
	});

	#region [Actions]
	private Task CancelAsync()
	{
		Unsubscribe();
		_model = null;
		return Drawer.HideAsync();
	}
	private async Task SubmitAsync()
	{
		if (IsLoading || _model is null) return;
		try
		{
			await SetLoadingAsync();
			if (_ownerId != _model.Owner!.Id)
			{
				var ownerUpdateResponse = await ScopeFactory.MediatorSend(new ChangeOrganizationOwnerUseCase.Command(_model.Id, _model.Owner.Id));
				switch (ownerUpdateResponse.Result)
				{
					case ChangeOrganizationOwnerUseCase.Result.Success:
						Snackbar.Add("Владелец успешно изменён", Severity.Success);
						break;
					case ChangeOrganizationOwnerUseCase.Result.OwnerNotFound:
						Snackbar.Add("Невозможно изменить владельца - владелец не найден", Severity.Success);
						return;
					case ChangeOrganizationOwnerUseCase.Result.ValidationError:
						Snackbar.Add("Невозможно изменить владельца - ошибка валидации", Severity.Success);
						return;
					case ChangeOrganizationOwnerUseCase.Result.OrganizationNotFound:
						Snackbar.Add("Невозможно изменить владельца - организация не найдена", Severity.Success);
						return;
					case ChangeOrganizationOwnerUseCase.Result.Unknown:
					default:
						Snackbar.Add("Невозможно изменить владельца - произошла неизвестная ошибка: " + ownerUpdateResponse.Result.ToString(), Severity.Success);
						return;
				}
			}

			var result = await ScopeFactory.MediatorSend(new UpdateOrganizationUseCase.Command(_model.Id, _model.Name));
			switch (result.Result)
			{
				case UpdateOrganizationUseCase.Result.Success:
					Snackbar.Add("Организация успешно обновлена", Severity.Success);
					EventSystem.Publish(new OrganizationSelectEto(result.Id));
					break;
				case UpdateOrganizationUseCase.Result.ValidationError:
					Snackbar.Add("Ошибка валидации данных", Severity.Error);
					break;
				case UpdateOrganizationUseCase.Result.OwnerNotFound:
					Snackbar.Add("Не удаётся получить владельца организации", Severity.Error);
					break;
				case UpdateOrganizationUseCase.Result.OrganizationNotFound:
					Snackbar.Add("Не удаётся получить обновляемую организацию", Severity.Error);
					break;
				case UpdateOrganizationUseCase.Result.OrganizationNameAlreadyExists:
					Snackbar.Add("Организация с данным названием уже существует", Severity.Error);
					break;
				case UpdateOrganizationUseCase.Result.Unknown:
				default:
					Snackbar.Add("Не удалось обновить организацию из-за ошибки: " + result.Result.ToString() + " , повторите попытку", Severity.Error);
					break;
			}
		}
		catch (Exception ex)
		{
			Snackbar.Add("Не удалось обновить организацию из-за непредвиденной ошибки, повторите попытку", Severity.Error);
			Logger.LogError(ex, ex.Message);
		}
		finally
		{
			await SetLoadingAsync(false);
		}
	}

	private async Task<IEnumerable<UserModel>> SearchOwnerAsync(string value, CancellationToken token)
	{
		List<UserModel> items = [];
		try
		{
			var result = await ScopeFactory.MediatorSend(new GetUserListUseCase.Query(value), token);
			switch (result.Result)
			{
				case GetUserListUseCase.Result.Success:
					items.AddRange(result.Items.Select(item => new UserModel(item.Id, item.Name)));
					break;
				case GetUserListUseCase.Result.ValidationError:
					Snackbar.Add("Не удалось получить список пользователей. Повторите попытку", Severity.Error);
					break;
				case GetUserListUseCase.Result.Unknown:
				default:
					Snackbar.Add("Не удалось получить список пользователей. Повторите попытку", Severity.Error);
					break;
			}
			return items;
		}
		catch (Exception ex)
		{
			Snackbar.Add("Не удалось получить список пользователей. Повторите попытку", Severity.Error);
			Logger.LogError(ex, ex.Message);
		}
		return items;
	}

	private Task RefreshAsync() => FetchAsync();
	private void View() => EventSystem.Publish(new OrganizationSelectEto(OrganizationId));
	private void Delete() => EventSystem.Publish(new OrganizationDeleteEto(OrganizationId));
	#endregion

	#region [Event Handlers]
	private async void OnAppEventHandler(object appEvent)
	{
		if (_model is null)
		{
			Unsubscribe();
			return;
		}

		switch (appEvent)
		{
			case SubscribeOrganizationUseCase.UpdatedEvent updatedEto:
				await FetchAsync();
				await UpdateViewAsync();
				break;

			case SubscribeOrganizationUseCase.DeletedEvent deletedEto:
				Unsubscribe();
				_model = null;
				Snackbar.Add("Редактируемая вами организация была удалена", Severity.Error);
				await Drawer.HideAsync();
				break;

			default:
				break;
		}
	}

	private void OnError(Exception exc)
	{
		Logger.LogError(exc, exc.Message);
		Snackbar.Add("Ошибка валидации при подписке на события", Severity.Error);
	}
	#endregion [Event Handlers]
}
