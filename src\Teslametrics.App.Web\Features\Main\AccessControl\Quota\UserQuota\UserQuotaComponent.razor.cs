namespace Teslametrics.App.Web.Features.Main.AccessControl.Quota.UserQuota;

public partial class UserQuotaComponent
{
    private MudBlazor.Color GetQuotaColor(int remainingQuota, int totalQuota)
    {
        if (remainingQuota <= 0)
            return MudBlazor.Color.Error;

        if ((remainingQuota < 5 && totalQuota < 50) ||
            (remainingQuota <= totalQuota * 0.1 && totalQuota >= 50))
            return MudBlazor.Color.Warning;

        return MudBlazor.Color.Default;
    }
}
