div ::deep .list_item {
    align-items: flex-start;
    border-radius: 8px;
}

.mud_theme_light div ::deep .list_item {
    background: rgba(248, 252, 255, 1);
    border: 1px solid rgba(248, 252, 255, 1);
}

.mud_theme_dark div::deep .list_item {
    border: 1px solid var(--mud-palette-divider-light);
}

div ::deep .building_count {
    color: #B8C2CC;
}

.mud_theme_light div ::deep .list_item.selected {
    border: 1px solid #DFE8F1;
}

div ::deep .city_checkbox .mud-typography {
    font-size: 24px;
}

div ::deep .list_item .building_address {
    font-size: 14px;
}

div ::deep .list_item .mud-button-root {
    padding: 16p;
}

.mud_theme_light div ::deep .city_checkbox .mud-typography {
    color: #17181A !important;
}


.mud_theme_light div ::deep .list_item .building_address {
    color: #17181A !important;
}

div ::deep .list_item .incident_count {
    display: flex;
    align-items: center;
    gap: 8px;
}

div ::deep .list_item .incident_count::before {
    border-radius: 50%;
    content: " ";
    display: block;
    width: 9px;
    height: 9px;
}

.mud_theme_light div ::deep .list_item .incident_count::before {
    background: rgba(184, 194, 204, 1);
}

.mud_theme_dark div ::deep .list_item .incident_count::before {
    background: rgb(83 88 93);
}

::deep .to_device_icon {}