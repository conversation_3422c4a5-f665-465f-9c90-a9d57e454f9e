@inherits InteractiveBaseComponent
<div class="d-flex flex-column @Class">
	<MudText Typo="Typo.h6">События</MudText>
	<MudTabs Elevation="2"
			 Rounded="true"
			 PanelClass="overflow-auto"
			 ApplyEffectsToContainer="true"
			 AlwaysShowScrollButtons="true">
		<MudTabPanel Text="Видимые">
			@if (TimeLine is null)
			{
				<MudStack>
					<MudText Typo="Typo.subtitle1">Нет временной линии</MudText>
					<MudText>начните просмотр чтобы увидеть события</MudText>
				</MudStack>

			}
			else
			{
				@if (_archiveEvents is not null && _archiveEvents.IsSuccess)
				{
					<MudList T="GetEventsUseCase.Response.Event">
						@foreach (var @event in _archiveEvents.Events.Where(e => !(TimeLine.Value.End < e.StartTime.UtcDateTime || (e.EndTime.HasValue ? e.EndTime!.Value.UtcDateTime : DateTime.UtcNow) < TimeLine.Value.Start)))
						{
							<MudListItem @key="@(@event)"
										 Text="@(@event.Name)"
										 SecondaryText="@($"{@event.StartTime.ToLocalTime()} - {(@event.EndTime?.ToLocalTime())}")"
										 OnClick="@(() => OnEventSelected(@event))">
							</MudListItem>
						}
					</MudList>
				}
			}
		</MudTabPanel>
		<MudTabPanel Text="За день">
			@if (_archiveEvents is not null && _archiveEvents.IsSuccess)
			{
				<MudList T="GetEventsUseCase.Response.Event">
					@foreach (var @event in _archiveEvents.Events)
					{
						<MudListItem @key="@(@event)"
									 Text="@(@event.Name)"
									 SecondaryText="@($"{@event.StartTime.ToLocalTime()} - {(@event.EndTime?.ToLocalTime())}")"
									 OnClick="@(() => OnEventSelected(@event))">
						</MudListItem>
					}
				</MudList>
			}
		</MudTabPanel>
	</MudTabs>
</div>