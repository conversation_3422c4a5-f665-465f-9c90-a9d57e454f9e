using System.Reactive;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using MudBlazor;
using Teslametrics.App.Web.Components.DragAndDrop;
using Teslametrics.App.Web.Events.Cameras;
using Teslametrics.App.Web.Extensions;
using Teslametrics.MediaServer.Orleans.Camera;
using Teslametrics.App.Web.Services.Authorization;
using Teslametrics.Shared;
using static Teslametrics.App.Web.Features.Main.Cameras.TreeView.SubcribeTreeViewCameraStatusUseCase;

namespace Teslametrics.App.Web.Features.Main.Cameras.TreeView;

public partial class FolderViewComponent
{
	#region [Type Declarations]
	public enum ItemType
	{
		Organization,
		Folder,
		Camera
	}

	// TODO:: создать уникальные презентеры для каждого типа
	public class TreeItemPresenter : TreeItemData<Guid>
	{
		public int StoppedCameraCount => Children?.Aggregate(0, (sum, x) =>
		{
			var item = (TreeItemPresenter)x;
			return sum + (item.Type == ItemType.Camera &&
				(item.Status == CameraStatus.Stopped)
				? 1
				: item.Type != ItemType.Camera
					? item.StoppedCameraCount
					: 0);
		}) ?? 0;

		public int ProblemCameraCount => Children?.Aggregate(0, (sum, x) =>
		{
			var item = (TreeItemPresenter)x;
			return sum + (item.Type == ItemType.Camera &&
				(item.Status == CameraStatus.Problem)
				? 1
				: item.Type != ItemType.Camera
					? item.ProblemCameraCount
					: 0);
		}) ?? 0;

		public int StartingCameraCount => Children?.Aggregate(0, (sum, x) =>
		{
			var item = (TreeItemPresenter)x;
			return sum + (item.Type == ItemType.Camera &&
				(item.Status == CameraStatus.Starting)
				? 1
				: item.Type != ItemType.Camera
					? item.StartingCameraCount
					: 0);
		}) ?? 0;

		public int ConnectedCameraCount => Children?.Aggregate(0, (sum, x) =>
		{
			var item = (TreeItemPresenter)x;
			return sum + (item.Type == ItemType.Camera &&
				(item.Status == CameraStatus.Running)
				? 1
				: item.Type != ItemType.Camera
					? item.ConnectedCameraCount
					: 0);
		}) ?? 0;

		public int CameraCount { get; set; }
		public override bool Expandable => Type != ItemType.Camera;
		public bool ReadOnly => Type == ItemType.Camera;
		public bool IsFolder => Type == ItemType.Folder;
		public bool IsCamera => Type == ItemType.Camera;
		public bool IsOrganization => Type == ItemType.Organization;
		public Guid OrganizationId { get; set; }
		public Guid Id => Value;
		public Guid ParentId { get; init; }
		public ItemType Type { get; init; }
		public CameraStatus Status { get; set; }

		public TreeItemPresenter(Guid id, Guid parentId, string title, ItemType type) : base(id)
		{
			Text = title;
			ParentId = parentId;
			Type = type;
			Icon = type switch
			{
				ItemType.Folder => Icons.Material.Outlined.Folder,
				ItemType.Camera => Icons.Material.Outlined.Camera,
				ItemType.Organization => Icons.Material.Outlined.Business,
				_ => Icons.Material.Outlined.QuestionMark
			};
		}
	}
	#endregion [Type Declarations]

	private bool _disposedValue;

	private int _offset = 0;
	private int _limit = 25;
	private string _orderBy = "Name";
	private int _totalPages => _response is null ? 0 : _response.TotalCount / _limit;
	private int _currentPage => _offset / _limit + 1;
	private OrderDirection _orderDirection = OrderDirection.Ascending;

	private MudTreeView<Guid>? _treeViewRef;
	private bool _subscribing;
	private bool _subscribingCameraStatus;
	private Guid _selected;
	private DateTime _lastRefreshTime = DateTime.Now;
	private string _searchString = string.Empty;

	private SubcribeTreeViewCameraStatusUseCase.Response? _subscriptionCameraStatusResponse;
	private SubscribeTreeViewUseCase.Response? _subscriptionResult;

	GetOrganizationListUseCase.Response? _response = null;
	private List<TreeItemPresenter> Items = [];

	#region [Parameters]
	[Parameter]
	public Guid? OrganizationId { get; set; }
	[Parameter]
	public EventCallback<Guid?> OrganizationIdChanged { get; set; }
	#endregion [Parameters]

	protected override void OnInitialized()
	{
		base.OnInitialized();
		CompositeDisposable.Add(EventSystem.Subscribe<CameraGroupCreateEto>(GroupCreateHandler));
	}

	protected override async Task OnInitializedAsync()
	{
		await base.OnInitializedAsync();
		await FetchAsync();
		await CameraListStatusSibscribeAsync();

		AuthenticationStateProvider.AuthenticationStateChanged += OnAuthenticationStateChanged;
	}

	protected override void Dispose(bool disposing)
	{
		if (!_disposedValue)
		{
			if (disposing)
			{
				AuthenticationStateProvider.AuthenticationStateChanged -= OnAuthenticationStateChanged;
			}

			_disposedValue = true;
		}

		base.Dispose(disposing);
	}

	private async Task FetchAsync()
	{
		if (IsLoading) return;

		try
		{
			await SetLoadingAsync(true);
			var userId = await GetCurrentUserIdAsync() ?? throw new UnauthorizedAccessException();
			_response = await ScopeFactory.MediatorSend(new GetOrganizationListUseCase.Query(userId, _offset, _limit, _orderBy, _orderDirection));
		}
		catch (Exception ex)
		{
			Snackbar.Add("Не удалось получить древо камер из-за ошибки сервера. Повторите попытку", Severity.Error);
			Logger.LogError(ex, ex.Message);
		}

		await SetLoadingAsync(false);
		if (_response == null) return;

		switch (_response.Result)
		{
			case GetOrganizationListUseCase.Result.Success:
				_lastRefreshTime = DateTime.Now;
				var buffer = ConvertResponseToTreeItemPresenters(_response);
				if (Items.Count != 0)
				{
					TransferState(Items, buffer);
					Items.Clear();
				}
				Items.AddRange(buffer);
				var element = FindElementById(Items, _selected) as TreeItemPresenter;
				if (element is not null && element.IsFolder)
				{
					await SelectFolderAsync(element);
				}
				if (element is not null && element.IsOrganization)
				{
					await SelectOrganizationAsync(element);
				}
				buffer.Clear();
				buffer = null;
				await SubscribeAsync();
				break;
			case GetOrganizationListUseCase.Result.ValidationError:
				Snackbar.Add("Не удалось получить древо камер из-за ошибки валидации. Повторите попытку", Severity.Error);
				break;
			case GetOrganizationListUseCase.Result.Unknown:
				Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(FolderViewComponent), nameof(GetOrganizationListUseCase));
				Snackbar.Add($"Не удалось получить древо камер из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
				break;
			default:
				Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(FolderViewComponent), nameof(GetOrganizationListUseCase), _response.Result);
				Snackbar.Add($"Не удалось получить древо камер из-за ошибки: {_response.Result} Обратитесь к администратору.", MudBlazor.Severity.Error);
				break;
		}
	}
	private async Task SubscribeAsync()
	{
		try
		{
			Unsubscribe();

			await SetSubscribingAsync(true);
			var orgIds = Items.Select(x => x.Value).ToList();
			if (orgIds.Count == 0)
			{
				Snackbar.Add("Нет доступных для просмотра организаций. Невозможно подписаться на обновления.", Severity.Error);
				return;
			}
			_subscriptionResult = await ScopeFactory.MediatorSend(new SubscribeTreeViewUseCase.Request(Observer.Create<object>(OnAppEventHandler, OnError), orgIds));
			await SetSubscribingAsync(false);
			switch (_subscriptionResult.Result)
			{
				case SubscribeTreeViewUseCase.Result.Success:
					CompositeDisposable.Add(_subscriptionResult.Subscription!);
					break;
				case SubscribeTreeViewUseCase.Result.ValidationError:
					Snackbar.Add("Ошибка валидации при подписке на события", Severity.Error);
					break;
				case SubscribeTreeViewUseCase.Result.Unknown:
				default:
					throw new Exception($"Unexpected result in {nameof(SubscribeTreeViewUseCase)}: {_subscriptionResult.Result}");
			}
		}
		catch (Exception ex)
		{
			await SetSubscribingAsync(false);
			Snackbar.Add("Не удалось получить подписку на события дерева из-за непредвиденной ошибки. Повторите попытку", Severity.Error);
			Logger.LogError(ex, ex.Message);
		}
	}

	private async Task CameraListStatusSibscribeAsync()
	{
		try
		{
			UnsubscribeCameraListStatus();
			var expandedOrganizations = Items.Where(i => i.Expanded).Select(i => i.Value).ToList();
			if (expandedOrganizations.Count == 0)
			{
				return;
			}
			await SetCameraListStatusSubscribingAsync(true);
			_subscriptionCameraStatusResponse = await ScopeFactory.MediatorSend(new SubcribeTreeViewCameraStatusUseCase.Request(Observer.Create<CameraStatusUpdatedEvent>(OnCameraStatusEventHandler, OnError), expandedOrganizations));
		}
		catch (Exception ex)
		{
			_subscriptionCameraStatusResponse = null;
			Snackbar.Add("Не удалось подписаться на события статуса камер из-за непредвиденной ошибки. Повторите попытку", Severity.Error);
			Logger.LogError(ex, ex.Message);
		}

		await SetCameraListStatusSubscribingAsync(false);
		if (_subscriptionCameraStatusResponse is null) return;
		switch (_subscriptionCameraStatusResponse.Result)
		{
			case SubcribeTreeViewCameraStatusUseCase.Result.Success:
				CompositeDisposable.Add(_subscriptionCameraStatusResponse.Subscription!);
				break;
			case SubcribeTreeViewCameraStatusUseCase.Result.ValidationError:
				Snackbar.Add("Ошибка валидации при подписке на события", Severity.Error);
				break;
			case SubcribeTreeViewCameraStatusUseCase.Result.Unknown:
				Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(FolderViewComponent), nameof(SubcribeTreeViewCameraStatusUseCase));
				Snackbar.Add($"Не удалось подписаться на события статуса камер из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
				break;
			default:
				Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(FolderViewComponent), nameof(SubcribeTreeViewCameraStatusUseCase), _subscriptionCameraStatusResponse.Result);
				Snackbar.Add($"Не удалось подписаться на события статуса камер из-за ошибки: {_subscriptionCameraStatusResponse.Result}", MudBlazor.Severity.Error);
				break;
		}
	}

	private void Unsubscribe()
	{
		if (_subscriptionResult?.Subscription is not null)
		{
			CompositeDisposable.Remove(_subscriptionResult.Subscription);
			_subscriptionResult.Subscription.Dispose();
		}
	}
	private Task SetCameraListStatusSubscribingAsync(bool isLoading = true) => UpdateViewAsync(() =>
	{
		_subscribingCameraStatus = isLoading;
	});

	private void UnsubscribeCameraListStatus()
	{
		if (_subscriptionCameraStatusResponse?.Subscription is not null)
		{
			CompositeDisposable.Remove(_subscriptionCameraStatusResponse.Subscription);
			_subscriptionCameraStatusResponse.Subscription.Dispose();
		}
	}
	private Task SetSubscribingAsync(bool isLoading = true) => UpdateViewAsync(() =>
	{
		_subscribing = isLoading;
	});

	#region [Actions]
	private Task RefreshAsync() => FetchAsync();
	/// <summary>
	/// Collapses all tree items in the folder view
	/// </summary>
	private Task CollapseAllAsync() => UpdateViewAsync(() =>
	{
		// Set all items to collapsed state
		foreach (var item in Items)
		{
			item.Expanded = false;
			if (item.Children != null)
			{
				SetExpandedStateRecursive(item.Children, false);
			}
		}
	});

	/// <summary>
	/// Expands all tree items in the folder view
	/// </summary>
	private Task ExpandAllAsync() => UpdateViewAsync(() =>
	{
		// Set all items to expanded state
		foreach (var item in Items)
		{
			item.Expanded = true;
			if (item.Children != null)
			{
				SetExpandedStateRecursive(item.Children, true);
			}
		}

		// After expanding all, we should refresh camera status subscriptions
		_ = CameraListStatusSibscribeAsync();
	});

	/// <summary>
	/// Recursively sets the expanded state for all children in the tree
	/// </summary>
	private static void SetExpandedStateRecursive(List<TreeItemData<Guid>> items, bool expanded)
	{
		foreach (var item in items)
		{
			item.Expanded = expanded;
			if (item.Children != null)
			{
				SetExpandedStateRecursive(item.Children, expanded);
			}
		}
	}
	private Task SelectFolderAsync(TreeItemData<Guid> item)
	{
		return InvokeAsync(async () =>
		{
			item.Selected = true;
			_selected = item.Value;
			TreeItemPresenter presenter = (TreeItemPresenter)item;

			OrganizationId = presenter.OrganizationId;
			if (OrganizationIdChanged.HasDelegate)
				await OrganizationIdChanged.InvokeAsync(OrganizationId);

			EventSystem.Publish(new CameraGroupSelectEto(presenter.OrganizationId, item.Value));
		});
	}
	private Task SelectOrganizationAsync(TreeItemData<Guid> item)
	{
		return InvokeAsync(async () =>
		{
			item.Selected = true;
			_selected = item.Value;

			OrganizationId = item.Value;
			if (OrganizationIdChanged.HasDelegate)
				await OrganizationIdChanged.InvokeAsync(OrganizationId);

			EventSystem.Publish(new CameraGroupSelectEto(item.Value, null));
		});
	}

	private Task AlphabetSortToggleAsync()
	{
		_orderDirection = _orderDirection == OrderDirection.Descending ? OrderDirection.Ascending : OrderDirection.Descending;

		return FetchAsync();
	}

	private void SelectCamera(TreeItemData<Guid> item)
	{
		var presenter = (TreeItemPresenter)item;
		EventSystem.Publish(new CameraSelectEto(presenter.OrganizationId, presenter.Value));
	}
	#endregion [Actions]

	#region [Event Hanndlers]
	private async Task<bool> CanDropAsync(TreeItemData<Guid> dragging, TreeItemData<Guid> target)
	{
		var targetPresenter = (TreeItemPresenter)target;
		var draggingPresenter = (TreeItemPresenter)dragging;
		if (targetPresenter.IsCamera)
			return false;

		if (draggingPresenter.OrganizationId != targetPresenter.OrganizationId)
			return false;

		var state = await AuthenticationStateProvider.GetAuthenticationStateAsync();
		var canUpdate = await AuthorizationService.AuthorizeAsync(state.User, resource: new PolicyRequirementResource(OrganizationId, targetPresenter.Value), policyName: AppPermissions.Main.Folders.Update.GetEnumPermissionString());
		if (canUpdate.Succeeded)
		{
			return true;
		}

		return false;
	}

	private async Task<bool> CanDragAsync(TreeItemData<Guid> item)
	{
		var presenter = (TreeItemPresenter)item;
		var candragTask = presenter switch
		{
			{ IsCamera: true } => CanDragCamera(presenter),
			_ => Task.FromResult(false)
		};

		return await candragTask;
	}

	private async Task<bool> CanDragCamera(TreeItemPresenter camera)
	{
		var state = await AuthenticationStateProvider.GetAuthenticationStateAsync();
		var result = await AuthorizationService.AuthorizeAsync(state.User, resource: new PolicyRequirementResource(OrganizationId, camera.Value), policyName: AppPermissions.Main.Cameras.Move.GetEnumPermissionString());
		return result.Succeeded;
	}

	// TODO:: Вот тут добавление в род. группу элемента
	private async Task OnDrop(DragAndDropContainer<TreeItemData<Guid>>.OnDropHandler args)
	{
		try
		{

			if (((TreeItemPresenter)args.DraggedItem).ParentId == args.Target.Value)
				return;

			// TODO:: Добавить пермещение папки
			var response = await ScopeFactory.MediatorSend(new ChangeCameraFolderUseCase.Command(args.DraggedItem.Value, /*eto.ParentGroupId ?? Guid.Empty,*/ args.Target.Value));
			switch (response.Result)
			{
				case ChangeCameraFolderUseCase.Result.Success:
					Snackbar.Add("Камера успешно перенесена", Severity.Success);
					break;
				case ChangeCameraFolderUseCase.Result.FolderNotFound:
					Snackbar.Add("Не смогли перенести в директорию, так как данной директории более нет на сервере", Severity.Error);
					break;
				case ChangeCameraFolderUseCase.Result.CameraNotFound:
					Snackbar.Add("Перемещаемая камера не найдена", Severity.Error);
					break;
				case ChangeCameraFolderUseCase.Result.ValidationError:
					Snackbar.Add("не удалось переместить камеру из-за ошибки валидации", Severity.Error);
					break;
				case ChangeCameraFolderUseCase.Result.Unknown:
				default:
					throw new Exception($"Unexpected result in {nameof(CreateFolderUseCase)}: {response.Result}");
			}
		}
		catch (Exception ex)
		{
			Snackbar.Add("не удалось переместить камеру из-за непредвиденной ошибки.", Severity.Error);
			Logger.LogError(ex, ex.Message);
		}
	}

	private async void OnAppEventHandler(object appEvent)
	{
		await RefreshAsync();
		await UpdateViewAsync();
	}

	private async void OnCameraStatusEventHandler(CameraStatusUpdatedEvent appEvent)
	{
		try
		{
			// Update status for each camera in the event
			foreach (var (cameraId, newStatus) in appEvent.Statuses)
			{
				// Find the camera in our tree items
				var camera = Items.SelectMany(org =>
					org.Children?.SelectMany(folder =>
						folder.Children?.Where(item => item is TreeItemPresenter presenter && presenter.Type == ItemType.Camera && presenter.Id == cameraId) ?? []
					) ?? Enumerable.Empty<TreeItemPresenter>()
				).FirstOrDefault();

				if (camera != null && camera is TreeItemPresenter presenter)
				{
					presenter.Status = newStatus;
				}
			}

			await InvokeAsync(StateHasChanged);
		}
		catch (Exception ex)
		{
			Logger.LogError(ex, "Error updating camera statuses");
			Snackbar.Add("Ошибка при обновлении статусов камер", Severity.Error);
		}
	}

	private void OnError(Exception exc)
	{
		Snackbar.Add("Ошибка при подписке на события", Severity.Error);
		Logger.LogError(exc, exc.Message);
	}

	private async Task GroupCreateHandler(CameraGroupCreateEto eto)
	{
		try
		{
			var response = await ScopeFactory.MediatorSend(new CreateFolderUseCase.Command(eto.OrganizationId, "Новая директория"));
			switch (response.Result)
			{
				case CreateFolderUseCase.Result.Success:
					_selected = response.Id;
					foreach (var item in Items)
					{
						if (item.Value == eto.OrganizationId)
						{
							item.Expanded = true;
							break;
						}
					}
					Snackbar.Add("Директория успешно добавлена", Severity.Success);
					break;
				case CreateFolderUseCase.Result.ValidationError:
					Snackbar.Add("не удалось создать Директорию из-за ошибки валдиации", Severity.Error);
					break;
				case CreateFolderUseCase.Result.FolderNameAlreadyExists:
					Snackbar.Add("Директория \"Новая директория\" уже существует", Severity.Error);
					break;
				case CreateFolderUseCase.Result.Unknown:
				default:
					throw new Exception($"Unexpected result in {nameof(CreateFolderUseCase)}: {response.Result}");
			}
		}
		catch (Exception ex)
		{
			Snackbar.Add("не удалось создать директорию из-за непредвиденной ошибки.", Severity.Error);
			Logger.LogError(ex, ex.Message);
		}
	}

	private async void OnAuthenticationStateChanged(Task<AuthenticationState> authState)
	{
		await FetchAsync();
	}

	private Task PageChanged(int page)
	{
		_offset = (page - 1) * _limit;
		return FetchAsync();
	}

	private Task OnOrganizationExpandChanged((bool, Guid) args)
	{
		var organization = Items.FirstOrDefault(i => i.Value == args.Item2);
		if (organization != null)
		{
			organization.Expanded = args.Item1;
		}
		return CameraListStatusSibscribeAsync();
	}
	#endregion [Event Handlers]



	#region [Casts]
	private static List<TreeItemPresenter> ConvertResponseToTreeItemPresenters(GetOrganizationListUseCase.Response response) // Нужно, т.к. treeview от mudblazor`а привередливый и жрет только TreeItemData<TYPE>
	{
		var treeItems = new List<TreeItemPresenter>();

		// Обрабатываем организации (верхний уровень)
		foreach (var organization in response.Items)
		{
			TreeItemPresenter organizationPresenter = new(
				organization.Id,
				Guid.Empty, // У организаций нет родителя
				organization.Name,
				ItemType.Organization
			)
			{
				Expanded = true,
				CameraCount = organization.CameraCount,
				OrganizationId = organization.Id
			};

			treeItems.Add(organizationPresenter);

			// Обрабатываем папки, вложенные в организацию
			foreach (var folder in organization.Folders)
			{
				ConvertFolderToTreeItems(folder, organization.Id, organizationPresenter);
			}
		}

		return treeItems;
	}

	private static void ConvertFolderToTreeItems(GetOrganizationListUseCase.Response.Folder folder, Guid parentId, TreeItemPresenter presenter)
	{
		// Добавляем папку
		TreeItemPresenter folderPresenter = new(
			folder.Id,
			parentId,
			folder.Name,
			ItemType.Folder
		)
		{
			Children = [],
			Expanded = true,
			OrganizationId = presenter.Id,
			CameraCount = folder.CameraCount
		};

		presenter.Children ??= [];
		presenter.Children.Add(folderPresenter);

		// Обрабатываем камеры, вложенные в папку
		foreach (var camera in folder.Cameras)
		{
			TreeItemPresenter cameraPresenter = new(
				camera.Id,
				folder.Id,
				camera.Name,
				ItemType.Camera
			)
			{
				Expanded = true,
				OrganizationId = presenter.Id,
				Children = [],
				Status = camera.Status,
			};
			folderPresenter.Children.Add(cameraPresenter);
		}
	}

	// Опять-таки проблема широкого списка против глубокого. Если элементов немного, то лучше оставить рекурсивный поиск. Если поменяется - добавить словарь.
	private static void TransferState<T>(List<T> sourceTree, List<T> targetTree) where T : TreeItemData<Guid>
	{
		foreach (var sourceElement in sourceTree)
		{
			// if (!sourceElement.Expanded)
			// 	continue;

			// Находим соответствующий элемент во втором дереве
			var targetElement = FindElementById(targetTree, sourceElement.Value);

			if (targetElement != null)
			{
				// Переносим значение Expanded
				targetElement.Expanded = sourceElement.Expanded;
			}

			// Рекурсивно обрабатываем вложенные элементы
			if (sourceElement.Children?.Count > 0 && targetElement?.Children?.Count > 0)
			{
				TransferState(sourceElement.Children, targetElement.Children);
			}
		}
	}

	private static TreeItemData<Guid>? FindElementById(IEnumerable<TreeItemData<Guid>> tree, Guid id)
	{
		foreach (var element in tree)
		{
			if (element.Value == id)
				return element;

			if (element.Children?.Count > 0)
			{
				var found = FindElementById(element.Children, id);
				if (found != null)
					return found;
			}
		}

		return null;
	}
	#endregion [Casts]
}
