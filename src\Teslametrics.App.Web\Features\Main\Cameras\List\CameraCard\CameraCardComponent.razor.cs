using Microsoft.AspNetCore.Components;
using MudBlazor;
using System.Reactive;
using Teslametrics.App.Web.Events.Cameras;
using Teslametrics.App.Web.Extensions;
using Teslametrics.MediaServer.Orleans.Camera;

namespace Teslametrics.App.Web.Features.Main.Cameras.List.CameraCard;

public partial class CameraCardComponent : IAsyncDisposable
{
    private CameraStatus _cameraStatus;

    private GetCameraUseCase.Response? _response;
    private SubscribeCameraUseCase.Response? _subscriptionResult;
    private SubscribeCameraStatusUseCase.Response? _statusSubscriptionResult;

    [Inject]
    public NavigationManager NavigationManager { get; set; } = null!;

    [Parameter]
    [EditorRequired]
    public Guid CameraId { get; set; }

    [Parameter]
    [EditorRequired]
    public Guid OrganizationId { get; set; }

    #region [Public Methods]
    public async ValueTask DisposeAsync()
    {
        if (_statusSubscriptionResult?.Subscription is not null)
        {
            await _statusSubscriptionResult.Subscription.DisposeAsync();
        }

        GC.SuppressFinalize(this);
    }
    #endregion

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();

        IEnumerable<Task> tasks = [
            SubscribeAsync(),
            SubscribeStatusAsync(),
            FetchAsync(),
        ];
        await Task.WhenAll(tasks);
    }

    private async Task FetchAsync()
    {
        try
        {
            await SetLoadingAsync(true);
            _response = await ScopeFactory.MediatorSend(new GetCameraUseCase.Query(CameraId));
        }
        catch (Exception exc)
        {
            _response = null;
            Logger.LogError(exc, exc.Message);
            Snackbar.Add("Не удалось получить обновление данных камеры из-за непредвиденной ошибки.", Severity.Error);
        }
        await SetLoadingAsync(false);

        if (_response is null) return;

        switch (_response.Result)
        {
            case GetCameraUseCase.Result.Success:
                _cameraStatus = _response.CameraStatus;
                break;
            case GetCameraUseCase.Result.ValidationError:
                Snackbar.Add($"Не удалось получить обновление данных камеры {_response.Name}. Ошибка запроса", Severity.Error);
                Logger.LogWarning("ValidationError result in {UseCase}: {Result}", nameof(GetCameraUseCase), _response.Result);
                break;
            case GetCameraUseCase.Result.CameraNotFound:
                Snackbar.Add($"Не удалось получить обновление данных камеры {_response.Name}. Камера не найдена.", Severity.Error);
                break;
            case GetCameraUseCase.Result.Unknown:
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(CameraCardComponent), nameof(GetCameraUseCase));
                Snackbar.Add($"Не удалось получить обновление данных камеры {_response.Name} из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
                break;
            default:
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(CameraCardComponent), nameof(GetCameraUseCase), _response.Result);
                Snackbar.Add("Не удалось получить обновление данных камеры из-за ошибки.", Severity.Error);
                break;
        }
    }
    private async Task SubscribeAsync()
    {
        try
        {
            Unsubscribe();

            _subscriptionResult = await ScopeFactory.MediatorSend(new SubscribeCameraUseCase.Request(Observer.Create<object>(OnAppEventHandler, OnError), CameraId));

        }
        catch (Exception ex)
        {
            _subscriptionResult = null;
            Snackbar.Add("Не удалось получить подписку на события камеры из-за непредвиденной ошибки. Повторите попытку", Severity.Error);
            Logger.LogError(ex, ex.Message);
        }

        switch (_subscriptionResult?.Result)
        {
            case SubscribeCameraUseCase.Result.Success:
                CompositeDisposable.Add(_subscriptionResult.Subscription!);
                break;
            case SubscribeCameraUseCase.Result.ValidationError:
                Snackbar.Add("Ошибка валидации при подписке на события", Severity.Error);
                break;
            case SubscribeCameraUseCase.Result.Unknown:
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(CameraCardComponent), nameof(SubscribeCameraUseCase));
                Snackbar.Add($"Не удалось получить подписку на события камеры из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
                break;
            default:
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(CameraCardComponent), nameof(SubscribeCameraUseCase), _subscriptionResult?.Result);
                Snackbar.Add($"Не удалось получить подписку на события камеры из-за ошибки: {_subscriptionResult?.Result}", MudBlazor.Severity.Error);
                break;
        }
    }

    private async Task SubscribeStatusAsync()
    {
        try
        {
            _statusSubscriptionResult = await ScopeFactory.MediatorSend(new SubscribeCameraStatusUseCase.Request(Observer.Create<SubscribeCameraStatusUseCase.StatusChangedEvent>(StatusUpdated, OnError), CameraId));
        }
        catch (Exception ex)
        {
            _statusSubscriptionResult = null;
            Snackbar.Add("Не удалось подписаться на события статуса камеры из-за ошибки сообщения с сервером. Повторите попытку позже.", MudBlazor.Severity.Error);
            Logger.LogError(ex, ex.Message);
        }

        if (_statusSubscriptionResult is null) return;

        switch (_statusSubscriptionResult?.Result)
        {
            case SubscribeCameraStatusUseCase.Result.Success:
                break;
            case SubscribeCameraStatusUseCase.Result.ValidationError:
                Snackbar.Add("Ошибка валидации при подписке на события статуса камеры", MudBlazor.Severity.Error);
                break;
            case SubscribeCameraStatusUseCase.Result.Unknown:
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(CameraCardComponent), nameof(SubscribeCameraStatusUseCase));
                Snackbar.Add($"Не удалось получить подписку на события статуса камеры из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
                break;
            default:
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(CameraCardComponent), nameof(SubscribeCameraStatusUseCase), _statusSubscriptionResult?.Result);
                Snackbar.Add($"Не удалось получить подписку на события статуса камеры из-за ошибки: {_statusSubscriptionResult?.Result}", MudBlazor.Severity.Error);
                break;
        }
    }

    private void Unsubscribe()
    {
        if (_subscriptionResult?.Subscription is not null)
        {
            CompositeDisposable.Remove(_subscriptionResult.Subscription);
            _subscriptionResult.Subscription.Dispose();
        }
    }

    #region [Actions]
    private void ShowArchive() => NavigationManager.NavigateTo($"/cameras/archive/{OrganizationId}/{CameraId}");
    private void ShowPlayer()
    {
        if (_response is null) return;

        if (_response.CameraStatus == CameraStatus.Running)
            EventSystem.Publish(new ShowCameraStreamEto(CameraId));
    }
    private void Select() => EventSystem.Publish(new CameraSelectEto(OrganizationId, CameraId));
    private void Delete() => EventSystem.Publish(new CameraDeleteEto(CameraId));
    private void Edit() => EventSystem.Publish(new CameraEditEto(OrganizationId, CameraId));

    private async Task ConnectAsync()
    {
        ConnectCameraUseCase.Response? response;
        try
        {
            response = await ScopeFactory.MediatorSend(new ConnectCameraUseCase.Command(CameraId));
        }
        catch (Exception ex)
        {
            response = null;
            Snackbar.Add($"Не удалось подключить камеру из-за ошибки сообщения с сервером. Повторите попытку позже.", MudBlazor.Severity.Error);
            Logger.LogError(ex, ex.Message);
        }

        if (response is null) return;

        switch (response.Result)
        {
            case ConnectCameraUseCase.Result.Success:
                Snackbar.Add("Отправлен запрос на подключение камеры", Severity.Success);
                break;
            case ConnectCameraUseCase.Result.CameraNotFound:
                Snackbar.Add("Не удалось подключить камеру так как она не числится в системе", Severity.Error);
                break;
            case ConnectCameraUseCase.Result.ValidationError:
                Snackbar.Add("Не удалось подключить камеру из-за ошибки валидации", Severity.Error);
                break;
            case ConnectCameraUseCase.Result.CameraIsBlocked:
                Snackbar.Add("Камера заблокирована", Severity.Error);
                break;
            case ConnectCameraUseCase.Result.Unknown:
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(CameraCardComponent), nameof(ConnectCameraUseCase));
                Snackbar.Add($"Не удалось подключить камеру из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
                break;
            default:
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(CameraCardComponent), nameof(ConnectCameraUseCase), response.Result);
                Snackbar.Add($"Не удалось подключить камеру из-за ошибки: {response.Result}", MudBlazor.Severity.Error);
                break;
        }

    }

    private async Task DisconnectAsync()
    {
        DisconnectCameraUseCase.Response? response = null;
        try
        {
            response = await ScopeFactory.MediatorSend(new DisconnectCameraUseCase.Command(CameraId));
        }
        catch (Exception ex)
        {
            response = null;
            Snackbar.Add($"Не удалось отключить камеру из-за ошибки сообщения с сервером. Повторите попытку позже.", MudBlazor.Severity.Error);
            Logger.LogError(ex, ex.Message);
        }

        if (response is null) return;
        switch (response.Result)
        {
            case DisconnectCameraUseCase.Result.Success:
                Snackbar.Add("Отправлен запрос на отключение камеры", Severity.Success);
                break;
            case DisconnectCameraUseCase.Result.CameraNotFound:
                Snackbar.Add("Не удалось отключить камеру так как она не числится в системе", Severity.Error);
                break;
            case DisconnectCameraUseCase.Result.ValidationError:
                Snackbar.Add("Не удалось отключить камеру из-за ошибки валидации", Severity.Error);
                break;
            case DisconnectCameraUseCase.Result.Unknown:
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(CameraCardComponent), nameof(DisconnectCameraUseCase));
                Snackbar.Add($"Не удалось отключить камеру из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
                break;
            default:
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(CameraCardComponent), nameof(DisconnectCameraUseCase), response.Result);
                Snackbar.Add($"Не удалось отключить камеру из-за ошибки: {response.Result}", MudBlazor.Severity.Error);
                break;
        }
    }

    private async Task ClearArchiveAsync()
    {
        try
        {
            await SetLoadingAsync(true);
            var resolution = await ScopeFactory.MediatorSend(new ClearArchiveUseCase.Command(CameraId));
            switch (resolution.Result)
            {
                case ClearArchiveUseCase.Result.Success:
                    Snackbar.Add("Архив камеры очищён", Severity.Success);
                    break;
                case ClearArchiveUseCase.Result.ValidationError:
                    Snackbar.Add("Ошибка валидации при очистке архива камеры", Severity.Error);
                    break;
                case ClearArchiveUseCase.Result.CameraNotFound:
                    Snackbar.Add("Камера не найдена", Severity.Error);
                    break;
                case ClearArchiveUseCase.Result.Unknown:
                default:
                    throw new Exception($"Unexpected result in {nameof(ClearArchiveUseCase)}: {resolution.Result}");
            }
        }
        catch (Exception exc)
        {
            Logger.LogError(exc, exc.Message);
            Snackbar.Add("Не удалось очистить архив камеры из-за непредвиденной ошибки. Повторите попытку", Severity.Error);
        }
        finally
        {
            await SetLoadingAsync(false);
        }
    }
    #endregion

    private async void OnAppEventHandler(object appEvent)
    {
        await FetchAsync();
        await UpdateViewAsync();
    }

    private async void StatusUpdated(SubscribeCameraStatusUseCase.StatusChangedEvent appEvent)
    {
        _cameraStatus = appEvent.Status;
        await UpdateViewAsync();
    }

    private void OnError(Exception exc)
    {
        Snackbar.Add("Ошибка при подписке на события", Severity.Error);
        Logger.LogError(exc, exc.Message);
    }
}
