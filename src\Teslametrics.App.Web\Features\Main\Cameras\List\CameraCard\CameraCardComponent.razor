@using Teslametrics.App.Web.Extensions
@using Teslametrics.MediaServer.Orleans.Camera
@using Teslametrics.App.Web.Features.Main.Cameras.List.CameraCard.Preview
@using Teslametrics.Shared
@inherits InteractiveBaseComponent
<MudCard Class="mud-height-full mud-width-full ">
    <MudCardHeader>
        <div class="chips">
            @if (_response is null)
            {
                <MudSkeleton Width="100px" />
            }
            else
            {
                @if (_response.IsBlocked)
                {
                    <MudChip T="string"
                             Variant="Variant.Outlined"
                             Color="Color.Error">Заблокирована</MudChip>
                }
                else
                {
                    switch (_cameraStatus)
                    {
                        case CameraStatus.Running:
                            <MudChip T="string"
                                     Variant="Variant.Outlined"
                                     Color="Color.Info">Подключено</MudChip>
                            break;
                        case CameraStatus.Problem:
                            <MudChip T="string"
                                     Variant="Variant.Outlined"
                                     Color="Color.Error">Ошибка</MudChip>
                            break;
                        case CameraStatus.Stopped:
                            <MudChip T="string"
                                     Variant="Variant.Outlined"
                                     Color="Color.Success">Отключено</MudChip>
                            break;
                        case CameraStatus.Connecting:
                            <MudChip T="string"
                                     Variant="Variant.Outlined"
                                     Color="Color.Info">Подключается</MudChip>
                            break;
                        default:
                            break;
                    }
                }
            }
        </div>
        <MudSpacer />
        @if (_response is null)
        {
            <MudSkeleton Width="30px"
                         Height="30px" />
        }
        else
        {
            <MudMenu Icon="@Icons.Material.Filled.MoreVert"
                     AriaLabel="Действия с камерой"
                     Variant="Variant.Outlined"
                     Color="Color.Primary">
                @if (_cameraStatus == CameraStatus.Running && !_response.IsBlocked)
                {
                    <MudMenuItem OnClick="ShowPlayer"
                                 Icon="@Icons.Material.Outlined.PanoramaFishEye">Просмотр</MudMenuItem>
                }
                <MudMenuItem OnClick="ShowArchive"
                             Icon="@Icons.Material.Filled.FolderZip">Архив</MudMenuItem>
                <MudMenuItem OnClick="Select"
                             Icon="@Icons.Material.Outlined.PanoramaFishEye">Показать настройки
                </MudMenuItem>
                @if (_cameraStatus == CameraStatus.Running || _cameraStatus == CameraStatus.Connecting || _cameraStatus == CameraStatus.Problem)
                {
                    <AuthorizeView Policy="@AppPermissions.Main.Cameras.Disconnect.GetEnumPermissionString()"
                                   Resource="new PolicyRequirementResource(OrganizationId, CameraId)"
                                   Context="innerContext">
                        <MudMenuItem OnClick="DisconnectAsync"
                                     Icon="@Icons.Material.Filled.VisibilityOff">Отключить</MudMenuItem>
                    </AuthorizeView>
                }
                @if (_cameraStatus == CameraStatus.Stopped)
                {
                    <AuthorizeView Policy="@AppPermissions.Main.Cameras.Connect.GetEnumPermissionString()"
                                   Resource="new PolicyRequirementResource(OrganizationId, CameraId)"
                                   Context="innerContext">
                        <MudMenuItem OnClick="ConnectAsync"
                                     Icon="@Icons.Material.Filled.Visibility">Подключить</MudMenuItem>
                    </AuthorizeView>
                }
                <AuthorizeView Policy="@AppPermissions.Main.Cameras.Update.GetEnumPermissionString()"
                               Resource="new PolicyRequirementResource(OrganizationId, CameraId)"
                               Context="innerContext">
                    <MudMenuItem OnClick="Edit"
                                 Icon="@Icons.Material.Outlined.Edit">Редактировать настройки
                    </MudMenuItem>
                </AuthorizeView>
                <AuthorizeView Policy="@AppPermissions.Main.Cameras.Delete.GetEnumPermissionString()"
                               Resource="new PolicyRequirementResource(OrganizationId, CameraId)"
                               Context="innerContext">
                    <MudDivider Class="my-3" />

                    <MudMenuItem OnClick="ClearArchiveAsync"
                                 Icon="@Icons.Material.Outlined.Delete"
                                 IconColor="Color.Warning">
                        Очистить архив</MudMenuItem>

                    <MudMenuItem OnClick="Delete"
                                 Icon="@Icons.Material.Outlined.Delete"
                                 IconColor="Color.Warning">
                        Удалить</MudMenuItem>
                </AuthorizeView>
            </MudMenu>
        }
    </MudCardHeader>
    <div class="d-flex mud-width-full flex-column justify-center align-center mud-height-full image_container"
         @onclick="ShowPlayer">
        @if (_response is null)
        {
            <MudSkeleton SkeletonType="SkeletonType.Rectangle"
                         Width="100%"
                         Height="100%" />
        }
        else
        {
            @switch (_cameraStatus)
            {
                case CameraStatus.Running:
                    <CameraPreview CameraId="@_response.Id"
                                   CameraStreamId="@_response.CameraStreamId" />
                    break;
                case CameraStatus.Stopped:
                    <MudIcon Icon="@Icons.Material.Filled.Block"
                             Color="Color.Warning" />
                    <div>Камера отключена</div>
                    break;
                case CameraStatus.Connecting:
                    <MudProgressCircular Color="Color.Info"
                                         Style="height:70px;width:70px;"
                                         Indeterminate="true" />
                    <div>Камера подключается</div>
                    break;
                case CameraStatus.Problem:
                    <MudIcon Icon="@Icons.Material.Filled.Block"
                             Color="Color.Error" />
                    <div>Ошибка</div>
                    break;
                default:
                    break;
            }
        }
    </div>
    <MudCardContent>
        @if (_response is null)
        {
            <MudSkeleton />
        }
        else
        {
            <MudText Typo="Typo.h5"
                     class="camera_name">@_response.Name</MudText>
        }
    </MudCardContent>
</MudCard>