@using Teslametrics.App.Web.Extensions
@using Teslametrics.App.Web.Orleans.Camera
@using Teslametrics.App.Web.Features.Main.Cameras.List.CameraCard.Preview
@inherits InteractiveBaseComponent
<MudCard Class="mud-height-full mud-width-full ">
	<MudCardHeader>
		<div class="chips">
			@if (_response is null)
			{
				<MudSkeleton Width="100px" />
			}
			else
			{
				@if (_response.IsBlocked)
				{
					<MudChip T="string"
							 Variant="Variant.Outlined"
							 Color="Color.Error">Заблокирована</MudChip>
				}
				else
				{
					@if (_response.CameraStatus == CameraStatus.Starting)
					{
						<MudChip T="string"
								 Variant="Variant.Outlined"
								 Color="Color.Info">Подключается</MudChip>
					}
					@if (_response.CameraStatus == CameraStatus.Problem)
					{
						<MudChip T="string"
								 Variant="Variant.Outlined"
								 Color="Color.Error">Ошибка</MudChip>
					}
					@if (_response.CameraStatus == CameraStatus.Running)
					{
						<MudChip T="string"
								 Variant="Variant.Outlined"
								 Color="Color.Success">Подключено</MudChip>
					}
					@if (_response.CameraStatus == CameraStatus.Stopped)
					{
						<MudChip T="string"
								 Variant="Variant.Outlined"
								 Color="Color.Info">Отключено</MudChip>
					}
				}
			}
		</div>
		<MudSpacer />
		@if (_response is null)
		{
			<MudSkeleton Width="30px"
						 Height="30px" />
		}
		else
		{
			<MudMenu Icon="@Icons.Material.Filled.MoreVert"
					 AriaLabel="Действия с камерой"
					 Variant="Variant.Outlined"
					 Color="Color.Primary">
				@if (_response.CameraStatus == CameraStatus.Running && !_response.IsBlocked)
				{
					<MudMenuItem OnClick="ShowPlayer"
								 Icon="@Icons.Material.Outlined.PanoramaFishEye">Просмотр</MudMenuItem>
				}
				<MudMenuItem OnClick="ShowArchive"
							 Icon="@Icons.Material.Filled.FolderZip">Архив</MudMenuItem>
				<MudMenuItem OnClick="Select"
							 Icon="@Icons.Material.Outlined.PanoramaFishEye">Показать настройки
				</MudMenuItem>
				@if (_response.CameraStatus == CameraStatus.Running || _response.CameraStatus == CameraStatus.Starting || _response.CameraStatus ==
							CameraStatus.Problem)
				{
					<AuthorizeView Policy="@AppPermissions.Main.Cameras.Disconnect.GetEnumPermissionString()"
								   Resource="new PolicyRequirementResource(OrganizationId, CameraId)"
								   Context="innerContext">
						<MudMenuItem OnClick="DisconnectAsync"
									 Icon="@Icons.Material.Filled.VisibilityOff">Отключить</MudMenuItem>
					</AuthorizeView>
				}
				@if (_response.CameraStatus == CameraStatus.Stopped)
				{
					<AuthorizeView Policy="@AppPermissions.Main.Cameras.Connect.GetEnumPermissionString()"
								   Resource="new PolicyRequirementResource(OrganizationId, CameraId)"
								   Context="innerContext">
						<MudMenuItem OnClick="ConnectAsync"
									 Icon="@Icons.Material.Filled.Visibility">Подключить</MudMenuItem>
					</AuthorizeView>
				}
				<AuthorizeView Policy="@AppPermissions.Main.Cameras.Update.GetEnumPermissionString()"
							   Resource="new PolicyRequirementResource(OrganizationId, CameraId)"
							   Context="innerContext">
					<MudMenuItem OnClick="Edit"
								 Icon="@Icons.Material.Outlined.Edit">Редактировать настройки
					</MudMenuItem>
				</AuthorizeView>
				<AuthorizeView Policy="@AppPermissions.Main.Cameras.Delete.GetEnumPermissionString()"
							   Resource="new PolicyRequirementResource(OrganizationId, CameraId)"
							   Context="innerContext">
					<MudDivider Class="my-3" />

					<MudMenuItem OnClick="ClearArchiveAsync"
								 Icon="@Icons.Material.Outlined.Delete"
								 IconColor="Color.Warning">
						Очистить архив</MudMenuItem>

					<MudMenuItem OnClick="Delete"
								 Icon="@Icons.Material.Outlined.Delete"
								 IconColor="Color.Warning">
						Удалить</MudMenuItem>
				</AuthorizeView>
			</MudMenu>
		}
	</MudCardHeader>
	<div class="d-flex mud-width-full flex-column justify-center align-center mud-height-full image_container"
		 @onclick="ShowPlayer">
		@if (_response is null)
		{
			<MudSkeleton SkeletonType="SkeletonType.Rectangle"
						 Width="100%"
						 Height="100%" />
		}
		else
		{
			@switch (_response.CameraStatus)
			{
				case CameraStatus.Running:
					<CameraPreview CameraId="@_response.Id"
								   CameraStreamId="@_response.CameraStreamId" />
					break;
				case CameraStatus.Stopped:
					<MudIcon Icon="@Icons.Material.Filled.Block"
							 Color="Color.Warning" />
					<div>Камера отключена</div>
					break;
				case CameraStatus.Starting:
					<MudProgressCircular Color="Color.Info"
										 Style="height:70px;width:70px;"
										 Indeterminate="true" />
					<div>Камера подключается</div>
					break;
				case CameraStatus.Problem:
					<MudIcon Icon="@Icons.Material.Filled.Block"
							 Color="Color.Error" />
					<div>Ошибка</div>
					break;
				default:
					break;
			}
		}
	</div>
	<MudCardContent>
		@if (_response is null)
		{
			<MudSkeleton />
		}
		else
		{
			<MudText Typo="Typo.h5"
					 class="camera_name">@_response.Name</MudText>
		}
	</MudCardContent>
</MudCard>