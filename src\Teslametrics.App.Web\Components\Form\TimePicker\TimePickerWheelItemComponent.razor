﻿<div class="@Class"
	 style="@Style"
	 @onclick="OnCLickHandler"
	 @onmouseover="OnMouseOverHandler"
	 @onclick:stopPropagation="true" />

@code
{
	[Parameter]
	public string? Style { get; set; }

	[Parameter]
	public string? Class { get; set; }

	[Parameter]
	public EventCallback<int> OnMouseOver { get; set; }

	[Parameter]
	public EventCallback<int> OnClick { get; set; }

	[Parameter]
	public int Value { get; set; }

	private Task OnCLickHandler()
	{
		if (OnClick.HasDelegate)
			return OnClick.InvokeAsync(Value);

		return Task.CompletedTask;
	}

	private Task OnMouseOverHandler()
	{
		if (OnMouseOver.HasDelegate)
			return OnMouseOver.InvokeAsync(Value);

		return Task.CompletedTask;
	}
}