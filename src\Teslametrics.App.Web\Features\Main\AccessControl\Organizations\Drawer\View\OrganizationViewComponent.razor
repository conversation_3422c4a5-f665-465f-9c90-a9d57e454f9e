@inherits InteractiveBaseComponent
<DrawerHeader>
	<MudStack Spacing="0">
		<MudText Typo="Typo.h1">Просмотр организации</MudText>
		@if (IsLoading)
		{
			<MudSkeleton Width="60%"
						 Height="calc(var(--mud-typography-subtitle1-lineheight) * var(--mud-typography-subtitle1-size))" />
		}
		@if (!IsLoading && _response is not null && _response.IsSuccess)
		{
			<MudText Typo="Typo.subtitle1">@_response.Name</MudText>
		}
		@if (!IsLoading && (_response is null || !_response.IsSuccess))
		{
			<MudText Typo="Typo.subtitle1">Не удалось получить параметры организации</MudText>
		}
	</MudStack>
	<MudSpacer />
	@if (!_subscribing && (_subscriptionResult is null || !_subscriptionResult.IsSuccess))
	{
		<MudTooltip Arrow="true"
					Placement="Placement.Start"
					Text="Ошибка подписки на события">
			<MudIconButton OnClick="SubscribeAsync"
						   Icon="@Icons.Material.Filled.ErrorOutline"
						   Color="Color.Error" />
		</MudTooltip>
	}
	<MudTooltip Text="@($"Время последнего обновления: {_lastRefreshTime.ToLocalTime()}")"
				Arrow="true"
				Placement="Placement.Start">
		<MudIconButton OnClick="RefreshAsync"
					   Icon="@Icons.Material.Filled.Refresh"
					   Color="Color.Primary" />
	</MudTooltip>
	@if (!IsLoading && _response is not null && _response.IsSuccess)
	{
		<AuthorizeView Policy="@_contextMenuAuthPolicyString"
					   Resource="new PolicyRequirementResource(OrganizationId, OrganizationId)"
					   Context="menuContext">
			<MudMenu Icon="@Icons.Material.Filled.MoreVert"
					 AriaLabel="Действия с выбранной организацией"
					 Color="Color.Primary"
					 Variant="Variant.Outlined">
				<AuthorizeView Policy="@(Fqdn<AppPermissions>.GetNames(AppPermissions.Main.AccessControl.Organizations.Update).Last())"
							   Resource="new PolicyRequirementResource(OrganizationId, OrganizationId)"
							   Context="innerContext">
					<MudMenuItem OnClick="Edit"
								 Icon="@Icons.Material.Outlined.PanoramaFishEye">Редактировать</MudMenuItem>
				</AuthorizeView>
				<AuthorizeView Policy="@(Fqdn<AppPermissions>.GetNames(AppPermissions.Main.AccessControl.Organizations.Delete).Last())"
							   Resource="new PolicyRequirementResource(OrganizationId, OrganizationId)"
							   Context="innerContext">
					<MudDivider Class="my-4" />
					<MudMenuItem OnClick="Delete"
								 Icon="@Icons.Material.Outlined.Delete"
								 IconColor="Color.Warning">Удалить</MudMenuItem>
				</AuthorizeView>
			</MudMenu>
		</AuthorizeView>
	}
</DrawerHeader>
<MudForm Model="_model"
		 Class="flex-1"
		 OverrideFieldValidation="true"
		 UserAttributes="@(new Dictionary<string, object>() {{"autocomplete", "off"}, {"aria-autocomplete", "none" }})"
		 Spacing="8">
	@if (!IsLoading && _model is not null)
	{
		<FormSectionComponent Title="Описание организации">
			<MudTextField Value="_model.Name"
						  Label="Наименование"
						  ReadOnly="true" />

			<MudAutocomplete T="UserModel"
							 Value="_model.Owner"
							 Label="Владелец организации"
							 ToStringFunc="@(e=> e==null?null : e.Name)"
							 ReadOnly="true">
				<NoItemsTemplate>
					<MudText Align="Align.Center"
							 Class="px-4 py-1">
						Не найдено владельцев
					</MudText>
				</NoItemsTemplate>
			</MudAutocomplete>
		</FormSectionComponent>
	}
	<FormLoadingComponent IsLoading="IsLoading && (_response is null || !_response.IsSuccess)" />
	<NoItemsFoundComponent HasItems="_response is not null && _response.IsSuccess"
						   LastRefreshTime="_lastRefreshTime"
						   RefreshAsync="RefreshAsync" />
</MudForm>
<DrawerActions>
	<MudSpacer />
	<MudButton OnClick="CancelAsync"
			   Variant="Variant.Outlined"
			   StartIcon="@Icons.Material.Outlined.Close">Закрыть</MudButton>
	@if (IsLoading)
	{
		<MudSkeleton Width="150px"
					 Height="36.5px" />
	}
	else
	{
		<AuthorizeView Policy="@AppPermissions.Main.AccessControl.Organizations.Update.GetEnumPermissionString()"
					   Resource="new PolicyRequirementResource(OrganizationId, OrganizationId)"
					   Context="innerContext">
			<MudButton OnClick="Edit"
					   Color="Color.Secondary"
					   Variant="Variant.Outlined">Редактировать</MudButton>
		</AuthorizeView>
	}
</DrawerActions>