@using Teslametrics.App.Web.Orleans.Camera

@page "/Cameras/View/{CameraId:guid}"
@inherits InteractiveBaseComponent
@layout CameraIFrameLayout
@if (_status == CameraStatus.Running)
{
	<Teslametrics.App.Web.Features.Main.Cameras.IFrame.Player Id="@_playerId"
															  CameraId="@CameraId!.Value"
															  PathToFile="@_pathToFile" />
}
else
{
	<MudCard>
		<div class="no_source d-flex flex-column pa-8 align-center gap-4">
			<MudIcon Icon="@Icons.Material.Filled.VideocamOff"
					 Style="font-size: 4rem;"
					 Color="Color.Warning" />
			<MudText Typo="Typo.subtitle1">Камера недоступна, выключена или несуществует</MudText>
		</div>
	</MudCard>
}