using FluentValidation;
using MediatR;
using System.Data;
using System.Reactive.Linq;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Domain.AccessControl.Users.Events;
using Teslametrics.App.Web.Exceptions;
using Teslametrics.App.Web.Services.DomainEventBus;

namespace Teslametrics.App.Web.Services.UserSession;

public static class SubscribeSessionFunction
{
    public record Request(IObserver<object> Observer) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public IDisposable? Subscription { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(IDisposable subscription)
        {
            Subscription = subscription;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Subscription = null;
            Result = result;
        }
    }

    public record UpdatedEvent(Guid UserId);

    public record LockedEvent(Guid UserId);

    public record DeletedEvent(Guid UserId);

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        UserNotFound
    }

    public class Validator : AbstractValidator<Request>
    {
        public Validator()
        {
            RuleFor(r => r.Observer).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Request, Response>
    {
        private readonly IValidator<Request> _validator;
        private readonly IDomainEventBus _domainEventBus;

        public Handler(IValidator<Request> validator,
                       IDomainEventBus domainEventBus)
        {
            _validator = validator;
            _domainEventBus = domainEventBus;
        }

        public async Task<Response> Handle(Request request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            var eventStream = await _domainEventBus.GetEventStreamAsync();

            var subscription = eventStream
                .Where(e => e switch
                {
                    UserUpdatedEvent @event => true,
                    UserRoleUpdatedEvent @event => true,
                    UserLockedEvent @event => true,
                    UserDeletedEvent @event => true,
                    _ => false
                })
                .Select<object, object>(e => e switch
                {
                    UserUpdatedEvent @event => new UpdatedEvent(@event.UserId),
                    UserRoleUpdatedEvent @event => new UpdatedEvent(@event.Id),
                    UserLockedEvent @event => new LockedEvent(@event.Id),
                    UserDeletedEvent @event => new DeletedEvent(@event.UserId),
                    _ => throw new AppException("Invalid event type")
                })
                .Subscribe(request.Observer);

            return new Response(subscription);
        }
    }
}