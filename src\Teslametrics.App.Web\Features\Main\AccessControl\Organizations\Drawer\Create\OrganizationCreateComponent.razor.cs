using FluentValidation;
using Microsoft.AspNetCore.Components;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Components.Drawer;
using Teslametrics.App.Web.Events.Organization;
using Teslametrics.App.Web.Extensions;
using Severity = MudBlazor.Severity;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Organizations.Drawer.Create;

public partial class OrganizationCreateComponent
{
	private class UserModel(Guid id, string name)
	{
		public Guid Id { get; set; } = id;
		public string Name { get; set; } = name;
	}
	private class CreateOrganizationModel
	{
		public string Name { get; set; } = string.Empty;

		public UserModel? Owner { get; set; }
	}

	private class CreateUserValidator : BaseFluentValidator<CreateOrganizationModel>
	{
		public CreateUserValidator()
		{
			RuleFor(model => model.Name)
				.Length(3, 60)
				.WithMessage("Наименование должно быть длиной от 3 до 60 символов");
			RuleFor(c => c.Owner)
				.NotNull().WithMessage("Поле должно быть заполнено")
				.NotEmpty().WithMessage("Поле должно быть заполнено");
		}
	}

	[CascadingParameter(Name = DrawerConsts.InstanceName)]
	private DrawerComponent Drawer { get; set; } = null!;

	private CreateUserValidator _validator = new();
	private bool _isValid;

	private CreateOrganizationModel _model = new();

	#region [Actions]
	private Task CancelAsync()
	{
		return Drawer.HideAsync();
	}

	private async Task SubmitAsync()
	{
		if (IsLoading) return;
		try
		{
			await SetLoadingAsync();
			var result = await ScopeFactory.MediatorSend(new CreateOrganizationUseCase.Command(_model.Name, _model.Owner!.Id));
			switch (result.Result)
			{
				case CreateOrganizationUseCase.Result.Success:
					Snackbar.Add("организация успешно создана", Severity.Success);
					EventSystem.Publish(new OrganizationSelectEto(result.Id));
					break;
				case CreateOrganizationUseCase.Result.ValidationError:
					Snackbar.Add("Ошибка валидации данных", Severity.Error);
					break;
				case CreateOrganizationUseCase.Result.OwnerNotFound:
					Snackbar.Add("Не удаётся получить владельца организации", Severity.Error);
					break;
				case CreateOrganizationUseCase.Result.OrganizationNameAlreadyExists:
					Snackbar.Add("Организация с данным названием уже существует", Severity.Error);
					break;
				case CreateOrganizationUseCase.Result.Unknown:
				default:
					Snackbar.Add("Не удалось создать организацию из-за непредвиденной ошибки:" + result.Result.ToString(), Severity.Error);
					break;
			}
		}
		catch (Exception ex)
		{
			Snackbar.Add("Не удалось создать организацию, повторите попытку", Severity.Error);
			Logger.LogError(ex, ex.Message);
		}
		finally
		{
			await SetLoadingAsync(false);
		}
	}

	private async Task<IEnumerable<UserModel>> SearchOwnerAsync(string value, CancellationToken token)
	{
		List<UserModel> items = [];
		try
		{
			var result = await ScopeFactory.MediatorSend(new GetUserListUseCase.Query(value), token);
			switch (result.Result)
			{
				case GetUserListUseCase.Result.Success:
					items.AddRange(result.Items.Select(item => new UserModel(item.Id, item.Name)));
					break;
				case GetUserListUseCase.Result.ValidationError:
					Snackbar.Add("Не удалось получить список пользователей. Повторите попытку", Severity.Error);
					break;
				case GetUserListUseCase.Result.Unknown:
				default:
					Snackbar.Add("Не удалось получить список пользователей. Повторите попытку", Severity.Error);
					break;
			}
			return items;
		}
		catch (Exception ex)
		{
			Snackbar.Add("Не удалось получить список пользователей. Повторите попытку", Severity.Error);
			Logger.LogError(ex, ex.Message);
		}
		return items;
	}
	#endregion
}
