using System.Reactive;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Teslametrics.App.Web.Components;
using Teslametrics.App.Web.Data.Sql;
using Teslametrics.App.Web.Events.CameraView;
using Teslametrics.App.Web.Extensions;
using Teslametrics.App.Web.Shared;

namespace Teslametrics.App.Web.Features.Main.CameraViews.TreeView;

public partial class TreeViewComponent
{
	#region [Type Definitions]
	private enum ItemType
	{
		Organization,
		View
	}

	private class TreeItemPresenter : MudBlazor.TreeItemData<Guid>
	{
		public int CameraCount { get; set; }
		public int ViewCount { get; set; }
		public override bool Expandable => Type != ItemType.View;
		public bool IsView => Type == ItemType.View;
		public bool IsOrganization => Type == ItemType.Organization;
		public Guid OrganizationId { get; set; }
		public Guid Id => Value;
		public Guid ParentId { get; init; }
		public ItemType Type { get; init; }

		public TreeItemPresenter(Guid id, Guid parentId, string title, ItemType type, GridType? gridType = null) : base(id)
		{
			Text = title;
			ParentId = parentId;
			Type = type;
			Icon = type switch
			{
				ItemType.Organization => MudBlazor.Icons.Material.Outlined.Business,
				ItemType.View => gridType switch
				{
					GridType.GridCustom => TeslaIcons.Grid.Custom,
					GridType.Grid1Plus5 => TeslaIcons.Grid.Grid1Plus5,
					GridType.Grid1Plus7 => TeslaIcons.Grid.Grid1Plus7,
					GridType.Grid1Plus12 => TeslaIcons.Grid.Grid1Plus12,
					GridType.Grid2Plus8 => TeslaIcons.Grid.Grid2Plus8,
					GridType.Grid3Plus4 => TeslaIcons.Grid.Grid3Plus4,
					_ => MudBlazor.Icons.Material.Outlined.ViewModule
				},
				_ => MudBlazor.Icons.Material.Outlined.QuestionMark
			};
		}
	}
	#endregion

	#region [Fields]
	private bool _disposedValue;

	private string _orderBy = "Name";
	private OrderDirection _orderDirection = OrderDirection.Ascending;

	private bool _subscribing;
	private DateTime _lastRefreshTime = DateTime.Now;
	private SubscribeTreeUseCase.Response? _subscriptionResult;
	private GetTreeUseCase.Response? _response;
	private List<TreeItemPresenter> _items = [];
	#endregion

	[Inject]
	private NavigationManager NavigationManager { get; set; } = null!;

	#region [Parameters]
	[Parameter]
	public EventCallback<(Guid? OrganizationId, Guid? ViewId)> OnParametersChanged { get; set; }

	[Parameter]
	[EditorRequired]
	public Guid? OrganizationId { get; set; }

	[Parameter]
	[EditorRequired]
	public Guid? ViewId { get; set; }
	#endregion

	protected override async Task OnInitializedAsync()
	{
		await base.OnInitializedAsync();
		await FetchAsync();

		AuthenticationStateProvider.AuthenticationStateChanged += OnAuthenticationStateChanged;
	}

	protected override void Dispose(bool disposing)
	{
		if (!_disposedValue)
		{
			if (disposing)
			{
				AuthenticationStateProvider.AuthenticationStateChanged -= OnAuthenticationStateChanged;
			}

			_disposedValue = true;
		}

		base.Dispose(disposing);
	}

	private async Task FetchAsync()
	{
		if (IsLoading) return;

		await SetLoadingAsync(true);
		try
		{
			var userId = await GetCurrentUserIdAsync() ?? throw new UnauthorizedAccessException();
			_response = await ScopeFactory.MediatorSend(new GetTreeUseCase.Query(userId, _orderBy, _orderDirection));
		}
		catch (Exception ex)
		{
			_response = null;
			Snackbar.Add("Не удалось получить список видов камер из-за ошибки сервера. Повторите попытку и обратитесь к администратору", MudBlazor.Severity.Error);
			Logger.LogError(ex, ex.Message);
		}

		await SetLoadingAsync(false);
		if (_response is null) return;

		switch (_response.Result)
		{
			case GetTreeUseCase.Result.Success:
				_lastRefreshTime = DateTime.Now;
				var buffer = ConvertResponseToTreeItemPresenters(_response);
				TransferState(_items, buffer);
				_items.Clear();
				_items.AddRange(buffer);
				buffer.Clear();
				buffer = null;
				await SubscribeAsync();
				break;
			case GetTreeUseCase.Result.ValidationError:
				Snackbar.Add("Не удалось получить список видов камер. Повторите попытку", MudBlazor.Severity.Error);
				break;
			case GetTreeUseCase.Result.Unknown:
				Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(TreeViewComponent), nameof(GetTreeUseCase));
				Snackbar.Add($"Не удалось получить список видов камер из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
				break;
			default:
				Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(TreeViewComponent), nameof(GetTreeUseCase), _response.Result);
				Snackbar.Add($"Не удалось получить список видов камер из-за ошибки: {_response.Result}", MudBlazor.Severity.Error);
				break;
		}
	}

	private List<TreeItemPresenter> ConvertResponseToTreeItemPresenters(GetTreeUseCase.Response response)
	{
		var result = new List<TreeItemPresenter>();

		if (response?.Items == null) return result;

		// Добавляем организации и их виды
		foreach (var organization in response.Items)
		{
			// Создаем узел организации
			var orgPresenter = new TreeItemPresenter(
				id: organization.Id,
				parentId: Guid.Empty, // Организации находятся на верхнем уровне
				title: organization.Name,
				type: ItemType.Organization)
			{
				Expanded = true,
				CameraCount = organization.CameraCount,
				ViewCount = organization.ViewCount,
				Children = []
			};
			result.Add(orgPresenter);

			// Добавляем виды для этой организации
			foreach (var view in organization.Views)
			{
				var viewPresenter = new TreeItemPresenter(
					id: view.Id,
					parentId: organization.Id, // Привязываем к родительской организации
					title: view.Name,
					type: ItemType.View,
					gridType: view.GridType)
				{
					CameraCount = view.CameraCount,
					OrganizationId = organization.Id
				};
				orgPresenter.Children?.Add(viewPresenter);
			}
		}

		return result;
	}

	private void TransferState(List<TreeItemPresenter> oldItems, List<TreeItemPresenter> newItems)
	{
		// Создаем словарь для быстрого поиска старых элементов по Id
		var oldItemsMap = oldItems.ToDictionary(item => item.Id, item => item);

		// Для каждого нового элемента проверяем, был ли соответствующий старый элемент развернут
		foreach (var newItem in newItems)
		{
			if (oldItemsMap.TryGetValue(newItem.Id, out var oldItem))
			{
				newItem.Expanded = oldItem.Expanded;
			}
		}
	}

	private async Task SubscribeAsync()
	{
		Unsubscribe();

		await SetSubscribingAsync(true);
		var orgIds = _items.Select(x => x.Value).ToList();
		if (orgIds.Count == 0)
		{
			Snackbar.Add("Нет доступных для просмотра организаций. Невозможно подписаться на обновления.", MudBlazor.Severity.Error);
			return;
		}
		try
		{
			_subscriptionResult = await ScopeFactory.MediatorSend(new SubscribeTreeUseCase.Request(Observer.Create<object>(OnAppEventHandler, OnError), orgIds));
		}
		catch (Exception ex)
		{
			_subscriptionResult = null;
			Snackbar.Add("Не удалось получить подписку на события дерева из-за непредвиденной ошибки. Повторите попытку", MudBlazor.Severity.Error);
			Logger.LogError(ex, ex.Message);
		}

		await SetSubscribingAsync(false);
		if (_subscriptionResult is null) return;

		switch (_subscriptionResult.Result)
		{
			case SubscribeTreeUseCase.Result.Success:
				CompositeDisposable.Add(_subscriptionResult.Subscription!);
				break;
			case SubscribeTreeUseCase.Result.ValidationError:
				Snackbar.Add("Ошибка валидации при подписке на события", MudBlazor.Severity.Error);
				break;
			case SubscribeTreeUseCase.Result.Unknown:
				Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(TreeViewComponent), nameof(SubscribeTreeUseCase));
				Snackbar.Add($"Не удалось получить подписку на события дерева из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
				break;
			default:
				Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(TreeViewComponent), nameof(SubscribeTreeUseCase), _subscriptionResult.Result);
				Snackbar.Add($"Не удалось получить подписку на события дерева из-за ошибки: {_subscriptionResult.Result}", MudBlazor.Severity.Error);
				break;
		}
	}

	private void Unsubscribe()
	{
		if (_subscriptionResult?.Subscription is not null)
		{
			CompositeDisposable.Remove(_subscriptionResult.Subscription);
			_subscriptionResult.Subscription.Dispose();
		}
	}

	private Task SetSubscribingAsync(bool isLoading = true) => UpdateViewAsync(() =>
	{
		_subscribing = isLoading;
	});

	#region [Actions]
	private async Task SelectAsync(TreeItemPresenter presenter)
	{
		OrganizationId = presenter.OrganizationId;
		ViewId = presenter.Type == ItemType.View ? presenter.Id : null;

		if (OnParametersChanged.HasDelegate)
			await OnParametersChanged.InvokeAsync((OrganizationId, ViewId));
	}

	private void CreateView(Guid organizationId) => EventSystem.Publish(new CameraViewCreateEto(organizationId));

	private void EditView(TreeItemPresenter presenter) => EventSystem.Publish(new CameraViewEditEto(presenter.OrganizationId, presenter.Id));

	private void ShowView(TreeItemPresenter presenter) => EventSystem.Publish(new CameraViewSelectEto(presenter.OrganizationId, presenter.Id));
	private void DeleteView(TreeItemPresenter presenter) => EventSystem.Publish(new CameraViewDeleteEto(presenter.OrganizationId, presenter.Id));

	private void NavigateToDetails(TreeItemPresenter presenter) => NavigationManager.NavigateTo($"/camera_views_detail?OrganizationId={presenter.OrganizationId}&ViewId={presenter.Id}", false);

	private Task RefreshAsync() => FetchAsync();
	#endregion

	#region [Event Handlers]
	private async void OnAppEventHandler(object appEvent)
	{
		await RefreshAsync();
		await UpdateViewAsync();
	}

	private void OnError(Exception exc)
	{
		Snackbar.Add("Ошибка при подписке на события", MudBlazor.Severity.Error);
		Logger.LogError(exc, exc.Message);
	}

	private async void OnAuthenticationStateChanged(Task<AuthenticationState> authState)
	{
		await FetchAsync();
	}
	#endregion
}
