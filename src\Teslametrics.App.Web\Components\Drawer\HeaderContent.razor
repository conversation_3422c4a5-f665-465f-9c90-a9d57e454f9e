@using System.Text
@using Teslametrics.App.Web.Extensions
@if (Header is not null && Header.ChildContent is not null)
{
	<MudDrawerHeader Class="@_headerClassBuilder.ToString()">
		<div>
			<MudIconButton OnClick="Drawer.HideAsync"
						   Icon="@Icons.Material.Filled.ArrowBack"
						   Color="Color.Primary"
						   Class="close_button" />
		</div>
		<div class="d-flex align-center flex-wrap mud-width-full">
			@Header.ChildContent
		</div>
	</MudDrawerHeader>
}
else
{
	<div />
}
@code {
	private DrawerHeader? _header;
	private StringBuilder _headerClassBuilder => new StringBuilder("drawer_header mud-elevation-1")
	.Append(Header?.Class)
	.AppendIf(" flex-row pr-2 pl-0", Drawer.Options.Anchor == Anchor.Right)
	.AppendIf(" flex-row-reverse pl-4 pr-2", Drawer.Options.Anchor == Anchor.Left);

	[Parameter]
	public DrawerHeader? Header { get; set; }


	[CascadingParameter(Name = DrawerConsts.InstanceName)]
	public DrawerComponent Drawer { get; set; } = null!;

	protected override void OnParametersSet()
	{
		if (_header != Header)
		{
			if (_header is not null)
			{
				_header.OnChange -= OnHeaderChange;
			}
			_header = Header;
			if (_header is not null)
			{
				_header.OnChange += OnHeaderChange;
			}
		}
		base.OnParametersSet();
	}

	private void OnHeaderChange() => StateHasChanged();
}