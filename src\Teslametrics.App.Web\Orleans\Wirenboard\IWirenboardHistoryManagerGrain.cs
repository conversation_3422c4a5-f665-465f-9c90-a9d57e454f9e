using Teslametrics.App.Web.Shared;

namespace Teslametrics.App.Web.Orleans.Wirenboard;

/// <summary>
/// Интерфейс для управления историей данных с Wirenboard
/// </summary>
[<PERSON><PERSON>("Teslametrics.App.Web.Orleans.IWirenboardHistoryManagerGrain")]
public interface IWirenboardHistoryManagerGrain : IGrainWithGuidKey
{
    /// <summary>
    /// Настраивает сбор истории для списка топиков
    /// При первом вызове происходит запуск сервиса
    /// </summary>
    /// <param name="request">Запрос с информацией о топиках</param>
    [<PERSON><PERSON>("ConfigureHistoryCollectionAsync")]
    Task ConfigureHistoryCollectionAsync(ConfigureHistoryCollectionRequest request);

    [GenerateSerializer]
    [<PERSON><PERSON>("Teslametrics.App.Web.Orleans.IWirenboardHistoryManagerGrain.ConfigureHistoryCollectionRequest")]
    public record ConfigureHistoryCollectionRequest(List<HistoryTopicInfo> TopicInfos);
}

/// <summary>
/// Информация о топике для сбора истории
/// </summary>
[GenerateSerializer]
[Alias("Teslametrics.App.Web.Orleans.HistoryTopicInfo")]
public record HistoryTopicInfo(string Topic, SensorValueType ValueType);