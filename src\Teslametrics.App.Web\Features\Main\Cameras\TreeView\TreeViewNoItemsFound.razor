@if (!HasItems)
{
	<MudStack AlignItems="AlignItems.Center" Justify="Justify.Center" Class="mud-height-full">
		<MudIcon Icon="@Icons.Material.Filled.FolderOff" Style="font-size: 8rem;" />
		<MudText Typo="Typo.body1">Ничего не найдено</MudText>
		<MudText Typo="Typo.subtitle1">У пользователя нет доступных организаций</MudText>
		<MudText Typo="Typo.subtitle1">Невозможно подписаться на обновления</MudText>
		<MudText Typo="Typo.subtitle1">Обновите список после получения доступа к просмотру организации</MudText>
		<MudText Typo="Typo.subtitle2">Время последнего обновления: @LastRefreshTime.ToLocalTime().ToString("dd/MM/yyyy HH:mm")</MudText>
		<MudButton OnClick="Refresh" Variant="Variant.Outlined" Color="Color.Primary" Class="mt-4">Обновить</MudButton>
	</MudStack>
}
@code {
	[Parameter]
	[EditorRequired]
	public bool HasItems { get; set; }

	[Parameter]
	[EditorRequired]
	public DateTimeOffset LastRefreshTime { get; set; }

	[Parameter]
	[EditorRequired]
	public EventCallback Refresh { get; set; }
}