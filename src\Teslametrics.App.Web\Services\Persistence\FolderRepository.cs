﻿using Microsoft.EntityFrameworkCore;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Domain.Folders;

namespace Teslametrics.App.Web.Services.Persistence;

public class FolderRepository : BaseRepository<FolderAggregate>, IFolderRepository
{
    public FolderRepository(CommandAppDbContext dbContext)
        : base (dbContext)
    {
    }

    public Task<FolderAggregate?> FindByNameAsync(string folderName, Guid organizationId, CancellationToken cancellationToken = default) =>
        DbContext.Set<FolderAggregate>()
            .AsTracking()
            .FirstOrDefaultAsync(entity => entity.Name == folderName && entity.OrganizationId == organizationId, cancellationToken);

    public async Task<bool> IsFolderExistsAsync(Guid folderId,
                                                CancellationToken cancellationToken = default) =>
        await DbContext.Set<FolderAggregate>()
            .AnyAsync(entity => entity.Id == folderId, cancellationToken);

    public async Task<bool> IsFolderNameExistsAsync(string name,
                                                    Guid organizationId,
                                                    CancellationToken cancellationToken = default) =>
        await DbContext.Set<FolderAggregate>()
            .AnyAsync(entity => entity.Name == name && entity.OrganizationId == organizationId, cancellationToken);
}