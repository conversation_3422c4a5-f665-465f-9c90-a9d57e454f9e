@typeparam TEnum where TEnum : struct, Enum
@inherits BaseComponent<Teslametrics.App.Web.Locales.Features.Main.AccessControl.Roles.Drawer.Edit.WildcardPermissionComponents>
<MudGrid Spacing="1">
	<MudItem xs="12" md="2" Class="mt-4">
		<MudText Typo="Typo.subtitle2" Align="Align.End"><b>@Title</b></MudText>
	</MudItem>
	<MudItem xs="12" md="10">
		@foreach (var permission in _values)
		{
			InheritStatus status = GetInheritStatus(permission);
			if (status.Inherited)
			{
				<MudTooltip Arrow="true" Placement="Placement.Start" @key="permission" Class="pa-1" Style="background:var(--mud-palette-gray-dark);box-shadow: var(--mud-elevation-2);">
					<ChildContent>
						<MudCheckBox Value="true"
									 Label="@Localizer[GetValueString(permission)]"
									 Disabled="true"
									 Color="Color.Primary" />
					</ChildContent>
					<TooltipContent>
						<MudList T="string" ReadOnly="true">
							<MudListSubheader Class="align-left" Style="color: var(--mud-palette-dark-text)">
								Данное право было наследовано от другого и не может быть снято напрямую
							</MudListSubheader>
							@foreach (var item in status.InheritedFrom)
							{
								<MudListItem Icon="@Icons.Material.Filled.Check" @key="item">
									@Localizer[item]
								</MudListItem>
							}
						</MudList>
					</TooltipContent>
				</MudTooltip>
			}
			else
			{
				<MudCheckBox T="bool"
							 Value="@_wildcardPermissions.Any(x => x.Permission.Value == GetValueString(permission))"
							 ValueChanged="(isChecked) => OnChangedHandler(isChecked, permission)"
							 Label="@Localizer[GetValueString(permission)]"
							 Color="Color.Primary"
							 @key="permission" />
			}
		}
	</MudItem>
</MudGrid>