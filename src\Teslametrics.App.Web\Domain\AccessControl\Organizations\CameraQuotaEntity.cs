namespace Teslametrics.App.Web.Domain.AccessControl.Organizations;

public class CameraQuotaEntity
{
    public Guid Id { get; private set;}

    public Guid OrganizationId { get; private set; }

    public Guid? PresetId { get; private set; }

    public string Name { get; private set;}

    public int Limit {get; private set; }

    public int RetentionPeriodDays { get; private set; }

    public int StorageLimitMb { get; private set; }

    public static CameraQuotaEntity Create(Guid id, Guid organizationId, Guid? presetId, string name, int limit, int retentionPeriodDays, int storageLimitMb)
        => new(id, organizationId, presetId, name, limit, retentionPeriodDays, storageLimitMb);

    public CameraQuotaEntity(Guid id, Guid organizationId, Guid? presetId, string name, int limit, int retentionPeriodDays, int storageLimitMb)
    {
        Id = id;
        OrganizationId = organizationId;
        PresetId = presetId;
        Name = name;
        Limit = limit;
        RetentionPeriodDays = retentionPeriodDays;
        StorageLimitMb = storageLimitMb;
    }

    public bool Update(string name, int limit, int retentionPeriodDays, int storageLimitMb)
    {
        var isUpdated = false;

        if (Name != name)
        {
            Name = name;
            isUpdated = true;
        }

        if (Limit != limit)
        {
            Limit = limit;
            isUpdated = true;
        }

        if (RetentionPeriodDays != retentionPeriodDays)
        {
            RetentionPeriodDays = retentionPeriodDays;
            isUpdated = true;
        }

        if (StorageLimitMb != storageLimitMb)
        {
            StorageLimitMb = storageLimitMb;
            isUpdated = true;
        }

        return isUpdated;
    }
}