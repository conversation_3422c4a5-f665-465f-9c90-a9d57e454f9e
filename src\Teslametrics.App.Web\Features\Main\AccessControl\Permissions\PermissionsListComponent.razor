﻿@using Microsoft.AspNetCore.Components.Authorization
@inherits BaseComponent<Teslametrics.App.Web.Locales.Features.Main.AccessControl.Permisssions.List.PermissionsListComponent>
<div class="d_contents">
	<MudDataGrid T="string"
				 Items="@Items"
				 MultiSelection="true"
				 Filterable="false"
				 SortMode="SortMode.None"
				 QuickFilter="@_quickFilter"
				 Outlined="false"
				 Elevation="0"
				 Loading="_isLoading"
				 Virtualize="true"
				 FixedHeader="true"
				 Height="100%"
				 CurrentPage="CurrentPage"
				 RowsPerPage="Limit"
				 Striped="true"
				 Hover="true"
				 RowsPerPageChanged="RowsPerPageChanged">
		<ToolBarContent>
			<MudText Typo="Typo.h6">Права доступа</MudText>
			<MudSpacer />
			<MudStack Row="true"
					  AlignItems="AlignItems.Center"
					  Justify="Justify.Center">
				<MudTextField @bind-Value="_searchString"
							  Placeholder="Поиск"
							  Label="Поиск"
							  Adornment="Adornment.Start"
							  AdornmentIcon="@Icons.Material.Filled.Search"
							  IconSize="Size.Medium"
							  Immediate="true"
							  Class="mb-0" />
			</MudStack>
		</ToolBarContent>
		<Columns>
			<PropertyColumn Property="@(x => Localizer[x])"
							Title="Наименование" />
			<PropertyColumn Property="@(x => Localizer["Description:" + x])"
							Title="Описание" />
		</Columns>
		<NoRecordsContent>
			<MudStack Class="mud-width-full"
					  AlignItems="AlignItems.Center"
					  Justify="Justify.Center">
				<NoPermissionsFoundComponent />
			</MudStack>
		</NoRecordsContent>
		<PagerContent>
			<MudDataGridPager T="string"
							  InfoFormat="{first_item}-{last_item} из {all_items}"
							  RowsPerPageString="Строк на страницу:" />
		</PagerContent>
	</MudDataGrid>
</div>