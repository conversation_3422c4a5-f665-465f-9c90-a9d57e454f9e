﻿// <auto-generated />
using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using Teslametrics.App.Web.Services.Persistence;

#nullable disable

namespace Teslametrics.DbMigrator.Migrations
{
    [DbContext(typeof(CommandAppDbContext))]
    [Migration("20250324101043_OnvifSettingsAdded")]
    partial class OnvifSettingsAdded
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.11")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("Teslametrics.App.Web.Domain.AccessControl.Organizations.CameraQuotaEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<int>("Limit")
                        .HasColumnType("integer")
                        .HasColumnName("limit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("name");

                    b.Property<Guid>("OrganizationId")
                        .HasColumnType("uuid")
                        .HasColumnName("organization_id");

                    b.Property<Guid?>("PresetId")
                        .HasColumnType("uuid")
                        .HasColumnName("preset_id");

                    b.Property<int>("RetentionPeriodDays")
                        .HasColumnType("integer")
                        .HasColumnName("retention_period_days");

                    b.Property<int>("StorageLimitMb")
                        .HasColumnType("integer")
                        .HasColumnName("storage_limit_mb");

                    b.HasKey("Id")
                        .HasName("pk_camera_quotas");

                    b.HasIndex("OrganizationId")
                        .HasDatabaseName("ix_camera_quotas_organization_id");

                    b.ToTable("camera_quotas", (string)null);
                });

            modelBuilder.Entity("Teslametrics.App.Web.Domain.AccessControl.Organizations.OrganizationAggregate", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("name");

                    b.Property<Guid>("OwnerId")
                        .HasColumnType("uuid")
                        .HasColumnName("owner_id");

                    b.HasKey("Id")
                        .HasName("pk_organizations");

                    b.ToTable("organizations", (string)null);
                });

            modelBuilder.Entity("Teslametrics.App.Web.Domain.AccessControl.Organizations.OrganizationRoleEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<bool>("IsAdmin")
                        .HasColumnType("boolean")
                        .HasColumnName("is_admin");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("name");

                    b.Property<Guid?>("organization_id")
                        .HasColumnType("uuid")
                        .HasColumnName("organization_id");

                    b.HasKey("Id")
                        .HasName("pk_roles");

                    b.HasIndex("organization_id")
                        .HasDatabaseName("ix_roles_organization_id");

                    b.ToTable("roles", (string)null);
                });

            modelBuilder.Entity("Teslametrics.App.Web.Domain.AccessControl.Organizations.RolePermissionEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid>("OrganizationId")
                        .HasColumnType("uuid")
                        .HasColumnName("organization_id");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("uuid")
                        .HasColumnName("role_id");

                    b.ComplexProperty<Dictionary<string, object>>("ResourcePermission", "Teslametrics.App.Web.Domain.AccessControl.Organizations.RolePermissionEntity.ResourcePermission#ResourcePermission", b1 =>
                        {
                            b1.ComplexProperty<Dictionary<string, object>>("Permission", "Teslametrics.App.Web.Domain.AccessControl.Organizations.RolePermissionEntity.ResourcePermission#ResourcePermission.Permission#Permission", b2 =>
                                {
                                    b2.Property<string>("Value")
                                        .IsRequired()
                                        .HasColumnType("text")
                                        .HasColumnName("permission");
                                });

                            b1.ComplexProperty<Dictionary<string, object>>("ResourceId", "Teslametrics.App.Web.Domain.AccessControl.Organizations.RolePermissionEntity.ResourcePermission#ResourcePermission.ResourceId#ResourceId", b2 =>
                                {
                                    b2.Property<Guid>("Value")
                                        .HasColumnType("uuid")
                                        .HasColumnName("resource_id");
                                });
                        });

                    b.HasKey("Id")
                        .HasName("pk_role_permissions");

                    b.HasIndex("RoleId")
                        .HasDatabaseName("ix_role_permissions_role_id");

                    b.ToTable("role_permissions", (string)null);
                });

            modelBuilder.Entity("Teslametrics.App.Web.Domain.AccessControl.Users.UserAggregate", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<bool>("ForcePasswordChange")
                        .HasColumnType("boolean")
                        .HasColumnName("force_password_change");

                    b.Property<DateTimeOffset?>("LastLogInTime")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("last_log_in_time");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("boolean")
                        .HasColumnName("lockout_enabled");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("name");

                    b.Property<string>("Password")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("password");

                    b.HasKey("Id")
                        .HasName("pk_users");

                    b.ToTable("users", (string)null);
                });

            modelBuilder.Entity("Teslametrics.App.Web.Domain.AccessControl.Users.UserOrganizationEntity", b =>
                {
                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid")
                        .HasColumnName("user_id");

                    b.Property<Guid>("OrganizationId")
                        .HasColumnType("uuid")
                        .HasColumnName("organization_id");

                    b.HasKey("UserId", "OrganizationId")
                        .HasName("pk_user_organizations");

                    b.ToTable("user_organizations", (string)null);
                });

            modelBuilder.Entity("Teslametrics.App.Web.Domain.AccessControl.Users.UserRoleEntity", b =>
                {
                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid")
                        .HasColumnName("user_id");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("uuid")
                        .HasColumnName("role_id");

                    b.HasKey("UserId", "RoleId")
                        .HasName("pk_user_roles");

                    b.ToTable("user_roles", (string)null);
                });

            modelBuilder.Entity("Teslametrics.App.Web.Domain.CameraPresets.PresetAggregate", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("ArchiveStreamConfig")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("archive_stream_config");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("name");

                    b.Property<string>("PublicStreamConfig")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("public_stream_config");

                    b.Property<string>("ViewStreamConfig")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("view_stream_config");

                    b.HasKey("Id")
                        .HasName("pk_presets");

                    b.ToTable("presets", (string)null);
                });

            modelBuilder.Entity("Teslametrics.App.Web.Domain.CameraViews.CameraViewAggregate", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Cells")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("cells");

                    b.Property<short>("ColumnCount")
                        .HasColumnType("smallint")
                        .HasColumnName("column_count");

                    b.Property<int>("GridType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0)
                        .HasColumnName("grid_type");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("name");

                    b.Property<Guid>("OrganizationId")
                        .HasColumnType("uuid")
                        .HasColumnName("organization_id");

                    b.Property<short>("RowCount")
                        .HasColumnType("smallint")
                        .HasColumnName("row_count");

                    b.HasKey("Id")
                        .HasName("pk_camera_views");

                    b.ToTable("camera_views", (string)null);
                });

            modelBuilder.Entity("Teslametrics.App.Web.Domain.Cameras.CameraAggregate", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("ArchiveUri")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("archive_uri");

                    b.Property<bool>("AutoStart")
                        .HasColumnType("boolean")
                        .HasColumnName("auto_start");

                    b.Property<Guid>("FolderId")
                        .HasColumnType("uuid")
                        .HasColumnName("folder_id");

                    b.Property<bool>("IsBlocked")
                        .HasColumnType("boolean")
                        .HasColumnName("is_blocked");

                    b.Property<double?>("Latitude")
                        .HasColumnType("double precision")
                        .HasColumnName("latitude");

                    b.Property<double?>("Longitude")
                        .HasColumnType("double precision")
                        .HasColumnName("longitude");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("name");

                    b.Property<bool>("OnvifEnabled")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("onvif_enabled");

                    b.Property<string>("OnvifSettings")
                        .HasColumnType("text")
                        .HasColumnName("onvif_settings");

                    b.Property<Guid>("OrganizationId")
                        .HasColumnType("uuid")
                        .HasColumnName("organization_id");

                    b.Property<string>("PublicUri")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("public_uri");

                    b.Property<Guid?>("QuotaId")
                        .HasColumnType("uuid")
                        .HasColumnName("quota_id");

                    b.Property<TimeSpan>("TimeZone")
                        .HasColumnType("interval")
                        .HasColumnName("time_zone");

                    b.Property<string>("ViewUri")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("view_uri");

                    b.HasKey("Id")
                        .HasName("pk_cameras");

                    b.ToTable("cameras", (string)null);
                });

            modelBuilder.Entity("Teslametrics.App.Web.Domain.Folders.FolderAggregate", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("name");

                    b.Property<Guid>("OrganizationId")
                        .HasColumnType("uuid")
                        .HasColumnName("organization_id");

                    b.Property<Guid?>("ParentId")
                        .HasColumnType("uuid")
                        .HasColumnName("parent_id");

                    b.HasKey("Id")
                        .HasName("pk_folders");

                    b.ToTable("folders", (string)null);
                });

            modelBuilder.Entity("Teslametrics.App.Web.Domain.PublicLinks.PublicLinkAggregate", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid>("CameraId")
                        .HasColumnType("uuid")
                        .HasColumnName("camera_id");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("name");

                    b.HasKey("Id")
                        .HasName("pk_public_links");

                    b.ToTable("public_links", (string)null);
                });

            modelBuilder.Entity("Teslametrics.App.Web.Services.Outbox.OutboxMessage", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("content");

                    b.Property<string>("EventType")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("event_type");

                    b.HasKey("Id")
                        .HasName("pk_outbox_messages");

                    b.ToTable("outbox_messages", (string)null);
                });

            modelBuilder.Entity("Teslametrics.App.Web.Domain.AccessControl.Organizations.CameraQuotaEntity", b =>
                {
                    b.HasOne("Teslametrics.App.Web.Domain.AccessControl.Organizations.OrganizationAggregate", null)
                        .WithMany("CameraQuotas")
                        .HasForeignKey("OrganizationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_camera_quotas_organizations_organization_id");
                });

            modelBuilder.Entity("Teslametrics.App.Web.Domain.AccessControl.Organizations.OrganizationRoleEntity", b =>
                {
                    b.HasOne("Teslametrics.App.Web.Domain.AccessControl.Organizations.OrganizationAggregate", null)
                        .WithMany("Roles")
                        .HasForeignKey("organization_id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .HasConstraintName("fk_roles_organizations_organization_id");
                });

            modelBuilder.Entity("Teslametrics.App.Web.Domain.AccessControl.Organizations.RolePermissionEntity", b =>
                {
                    b.HasOne("Teslametrics.App.Web.Domain.AccessControl.Organizations.OrganizationRoleEntity", null)
                        .WithMany("ResourcePermissions")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_role_permissions_roles_role_id");
                });

            modelBuilder.Entity("Teslametrics.App.Web.Domain.AccessControl.Users.UserOrganizationEntity", b =>
                {
                    b.HasOne("Teslametrics.App.Web.Domain.AccessControl.Users.UserAggregate", null)
                        .WithMany("Organizations")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_user_organizations_users_user_id");
                });

            modelBuilder.Entity("Teslametrics.App.Web.Domain.AccessControl.Users.UserRoleEntity", b =>
                {
                    b.HasOne("Teslametrics.App.Web.Domain.AccessControl.Users.UserAggregate", null)
                        .WithMany("Roles")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_user_roles_users_user_id");
                });

            modelBuilder.Entity("Teslametrics.App.Web.Domain.AccessControl.Organizations.OrganizationAggregate", b =>
                {
                    b.Navigation("CameraQuotas");

                    b.Navigation("Roles");
                });

            modelBuilder.Entity("Teslametrics.App.Web.Domain.AccessControl.Organizations.OrganizationRoleEntity", b =>
                {
                    b.Navigation("ResourcePermissions");
                });

            modelBuilder.Entity("Teslametrics.App.Web.Domain.AccessControl.Users.UserAggregate", b =>
                {
                    b.Navigation("Organizations");

                    b.Navigation("Roles");
                });
#pragma warning restore 612, 618
        }
    }
}
