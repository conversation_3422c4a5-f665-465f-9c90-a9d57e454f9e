let playerCameras_ = {};

/**
 * Проверяет поддержку HLS в браузере
 * @returns {boolean} true если HLS поддерживается нативно
 */
function checkHLSSupport() {
	const video = document.createElement('video');
	return video.canPlayType('application/vnd.apple.mpegurl') !== "";
}

/**
 * Создает конфигурацию плеера
 * @param {string} src - URL потока
 * @returns {Object} Конфигурация плеера
 */
function createPlayerConfig(src) {
	return {
		controls: false,
		autoplay: true,
		preload: 'auto',
		liveui: true,
		muted: true,
		fluid: true,
		html5: {
			vhs: {
				overrideNative: !videojs.browser.IS_SAFARI,
				enableLowInitialPlaylist: true,
				useBandwidthFromLocalStorage: true,
				allowSeeksWithinUnsafeLiveWindow: true,
				handlePartialData: true,
				experimentalBufferBasedABR: true,
				debug: true
			},
			nativeAudioTracks: false,
			nativeVideoTracks: false
		},
		sources: [{
			src,
			type: 'application/x-mpegURL',
			withCredentials: false
		}]
	};
}

/**
 * Stops and disposes of the video player
 * @param {string} playerId - The ID of the player to stop
 */
export async function stopPlayer(playerId) {
	const player = videojs.getPlayer(playerId);
	if (player) {
		player.dispose();
	}
}

/**
 * Updates the player with a new video source
 * @param {string} playerId - The ID of the player to update
 */
export async function updatePlayer(playerId) {
	const src = `/streams/${playerCameras_[playerId]}/view/stream.m3u8`;
	const player = videojs.getPlayer(playerId);
	if (!player) return;

	try {
		player.reset();
		player.src({
			src,
			type: 'application/x-mpegURL',
			withCredentials: false
		});
		player.load();

		player.ready(function () {
			this.play().catch(error => {
				console.error('Error playing video:', error);
			});
		});
	} catch (error) {
		console.error('Error updating player:', error);
	}
}

/**
 * Configures player settings and event handlers
 * @param {object} player - The video.js player instance
 */
function onPlayerReady(player) {
	// Disable fullscreen
	player.requestFullscreen = () => { };

	// Prevent pause
	player.on('pause', () => player.play());

	// Prevent pause on video click
	player.tech(true).on('click', (event) => {
		event.preventDefault();
		player.play();
	});
}

/**
 * Initializes or reinitializes the video player
 * @param {object} objRef - Reference to the Blazor component
 * @param {string} playerId - The ID for the player instance
 */
export async function initializePlayer(objRef, playerId, cameraId) {
	try {
		playerCameras_[playerId] = cameraId;
		const src = `/streams/${cameraId}/view/stream.m3u8`;
		const oldPlayer = videojs.getPlayer(playerId);

		if (oldPlayer) {
			return reinitializeExistingPlayer(oldPlayer, src);
		}

		await initializeNewPlayer(objRef, playerId, src);
	} catch (error) {
		console.error('Error initializing player:', error);
		objRef.invokeMethodAsync('ShowError', "Ошибка инициализации плеера");
	}
}

/**
 * Reinitializes an existing player with new source
 * @param {object} player - The existing video.js player instance
 * @param {string} src - The video source URL
 */
function reinitializeExistingPlayer(player, src) {
	try {
		player.reset();
		player.src({
			src,
			type: 'application/x-mpegURL',
			withCredentials: false
		});
		player.load();

		player.ready(function () {
			this.play().catch(error => {
				console.error('Error playing video:', error);
			});
		});
	} catch (error) {
		console.error('Error reinitializing player:', error);
		throw error;
	}
}

/**
 * Creates and configures a new player instance
 * @param {object} objRef - Reference to the Blazor component
 * @param {string} playerId - The ID for the player instance
 * @param {string} src - The video source URL
 */
async function initializeNewPlayer(objRef, playerId, src) {
	try {
		const playerConfig = createPlayerConfig(src);
		const player = window.videojs(playerId, playerConfig);

		// Проверяем поддержку HLS
		const hasNativeHLS = checkHLSSupport();
		console.log('Native HLS support:', hasNativeHLS);

		// Добавляем обработчик для проверки поддержки формата
		player.on('sourceset', () => {
			console.log('Source set, checking format support');
			if (!hasNativeHLS && !window.Hls) {
				console.warn('HLS is not supported in this browser and HLS.js is not loaded');
				objRef.invokeMethodAsync('ShowError', "Формат видео не поддерживается в вашем браузере");
				return;
			}
		});

		// Добавляем обработчик для отслеживания состояния буфера
		player.on('waiting', () => {
			const buffered = player.buffered();
			if (buffered.length > 0) {
				console.log('Buffer status:', {
					start: buffered.start(0),
					end: buffered.end(0),
					current: player.currentTime()
				});
			}
		});

		player.ready(() => onPlayerReady(player));

		setupErrorHandling(player, objRef, src);

		// Disable play toggle button
		player.controlBar.playToggle = null;

	} catch (error) {
		console.error('Error creating new player:', error);
		objRef.invokeMethodAsync('ShowError', "Ошибка создания плеера");
		throw error;
	}
}

/**
 * Sets up error handling and retry logic
 * @param {object} player - The video.js player instance
 * @param {object} objRef - Reference to the Blazor component
 * @param {string} src - The video source URL
 */
function setupErrorHandling(player, objRef, src) {
	const baseRetryDelay = 2000;
	let retryCount = 0;

	player.on('error', async function () {
		const error = player.error();
		console.error('Video.js Error:', error);

		// Добавляем дополнительную информацию об ошибке
		const errorDetails = {
			code: error.code,
			message: error.message,
			type: error.type,
			currentTime: player.currentTime(),
			networkState: player.networkState(),
			readyState: player.readyState(),
			buffered: player.buffered().length > 0 ? {
				start: player.buffered().start(0),
				end: player.buffered().end(0)
			} : null
		};
		console.log('Error details:', errorDetails);

		retryCount++;
		const retryMessage = `Повторная попытка подключения... (${retryCount})`;

		player.errorDisplay.open();
		player.errorDisplay.contentEl().innerHTML = retryMessage;
		console.log(retryMessage);

		await new Promise(resolve => setTimeout(resolve, baseRetryDelay));

		try {
			player.reset();
			player.src({
				src,
				type: 'application/x-mpegURL',
				withCredentials: false
			});
			player.load();

			player.ready(() => onPlayerReady(player));
			player.on('loadeddata', () => {
				player.play().catch(error => console.error('Playback failed:', error));
			});
		} catch (retryError) {
			console.error('Retry attempt failed:', retryError);
		}
	});
}