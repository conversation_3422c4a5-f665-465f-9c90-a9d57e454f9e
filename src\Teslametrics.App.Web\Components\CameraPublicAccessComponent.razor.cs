using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using MudBlazor;

namespace Teslametrics.App.Web.Components;

public partial class CameraPublicAccessComponent
{
	private bool _isClipboardAvaliable = false;
	private IJSObjectReference? _jSObjectReference;
	private string _src = string.Empty;
	private Guid _id = Guid.NewGuid();
	private string _iframe = string.Empty;

	[Inject]
	public IJSRuntime JS { get; set; } = null!;

	[Inject]
	public ISnackbar Snackbar { get; set; } = null!;

	[Parameter]
	public Guid AccessId { get; set; }

	[JSInvokable]
	public void ShowError(string message)
	{
		Snackbar.Add(message, MudBlazor.Severity.Error);
	}

	protected override async Task OnAfterRenderAsync(bool firstRender)
	{
		await base.OnAfterRenderAsync(firstRender);
		if (firstRender)
		{
			try
			{
				_jSObjectReference = await JS.InvokeAsync<IJSObjectReference>("import", "./Components/CameraPublicAccessComponent.razor.js");
				if (_jSObjectReference is not null)
				{
					_isClipboardAvaliable = await _jSObjectReference.InvokeAsync<bool>("isClipboardAvailable");
				}
			}
			catch (JSDisconnectedException) // https://learn.microsoft.com/en-us/aspnet/core/blazor/javascript-interoperability/?view=aspnetcore-9.0
			{
			}
			catch (Exception exc)
			{
				_isClipboardAvaliable = false;
				Logger.LogError(exc, "Unexpected exception in {Component}.{Method}", nameof(CameraPublicAccessComponent), nameof(OnAfterRenderAsync));
			}

			try
			{
				if (string.IsNullOrEmpty(_src) || string.IsNullOrEmpty(_iframe)) return;
				await JS.InvokeVoidAsync("eval", $"(() => hljs.highlightElement(document.getElementById('{_id}')))()");
			}
			catch (JSDisconnectedException) // https://learn.microsoft.com/en-us/aspnet/core/blazor/javascript-interoperability/?view=aspnetcore-9.0
			{
			}
			catch (Exception exc)
			{
				Logger.LogError(exc, "Unexpected exception in {Component}.{Method}", nameof(CameraPublicAccessComponent), nameof(OnAfterRenderAsync));
				Snackbar.Add("Не удалось создать стили кода для сайта.", MudBlazor.Severity.Error);
			}
		}
	}

	protected override async Task OnInitializedAsync()
	{
		try
		{
			var host = await JS.InvokeAsync<string>("eval", "(() => window.location.hostname)()"); // В теории eval плохо, но, т.к. serverside blazor, то можно
			var protocol = await JS.InvokeAsync<string>("eval", "(() => window.location.protocol)()");
			_iframe = $"<iframe src=\"{protocol}//{host}/Cameras/View/{AccessId}\" width=\"100%\" height=\"100%\" frameborder=\"0\" allowfullscreen></iframe>";
			_src = $"{protocol}//{host}/Cameras/View/{AccessId}";
		}
		catch (JSDisconnectedException) // https://learn.microsoft.com/en-us/aspnet/core/blazor/javascript-interoperability/?view=aspnetcore-9.0
		{
		}
		catch (Exception exc)
		{
			Logger.LogError(exc, "Unexpected exception in {Component}.{Method}", nameof(CameraPublicAccessComponent), nameof(OnInitializedAsync));
			Snackbar.Add("Не удалось получить ссылку на плеер.", MudBlazor.Severity.Error);
		}

		await base.OnInitializedAsync();
	}

	private async Task CopyLinkToClipboardAsync()
	{
		try
		{
			await JS.InvokeVoidAsync("navigator.clipboard.writeText", _src);
			Snackbar.Add("Ссылка на плеер скопирована!", MudBlazor.Severity.Success);
		}
		catch (JSDisconnectedException) // https://learn.microsoft.com/en-us/aspnet/core/blazor/javascript-interoperability/?view=aspnetcore-9.0
		{
		}
		catch (Exception exc)
		{
			Logger.LogError(exc, "Unexpected exception in {Component}.{Method}", nameof(CameraPublicAccessComponent), nameof(CopyLinkToClipboardAsync));
			Snackbar.Add("Не удалось скопировать ссылку на плеер", MudBlazor.Severity.Error);
		}
	}

	private async Task CopyIFrameToClipboardAsync()
	{
		try
		{
			//var host = await JS.InvokeAsync<string>("eval", "(() => window.location.hostname)()"); // В теории eval плохо, но, т.к. serverside blazor, то можно
			await JS.InvokeVoidAsync("navigator.clipboard.writeText", _iframe);
			Snackbar.Add("Код для сайта скопирован!", MudBlazor.Severity.Success);
		}
		catch (JSDisconnectedException) // https://learn.microsoft.com/en-us/aspnet/core/blazor/javascript-interoperability/?view=aspnetcore-9.0
		{
		}
		catch (Exception exc)
		{
			Logger.LogError(exc, "Unexpected exception in {Component}.{Method}", nameof(CameraPublicAccessComponent), nameof(CopyIFrameToClipboardAsync));
			Snackbar.Add("Не удалось скопировать код плеера для сайта", MudBlazor.Severity.Error);
		}
	}

}
