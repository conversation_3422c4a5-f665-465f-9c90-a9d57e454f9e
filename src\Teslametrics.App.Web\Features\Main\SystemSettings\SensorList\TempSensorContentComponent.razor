@if (_model is not null)
{
	<div class="d-flex flex-row align-center gap-3">
		<MudNumericField @bind-Value="@_model.MinTemp" Label="Минимальная температура" Variant="Variant.Outlined" />
		<MudNumericField @bind-Value="@_model.MaxTemp" Label="Максимальная температура" Variant="Variant.Outlined" />
	</div>
}

@code {
	private TemperatureModel? _model => SensorModel as TemperatureModel;

	[Parameter]
	public ISensorModel? SensorModel { get; set; }
}