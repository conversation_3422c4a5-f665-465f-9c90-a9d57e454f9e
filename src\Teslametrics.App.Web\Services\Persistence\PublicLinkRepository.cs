using Microsoft.EntityFrameworkCore;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Domain.PublicLinks;

namespace Teslametrics.App.Web.Services.Persistence;

public class PublicLinkRepository : BaseRepository<PublicLinkAggregate>, IPublicLinkRepository
{
    public PublicLinkRepository(CommandAppDbContext dbContext)
        : base(dbContext)
    {
    }

    public async Task DeleteByCameraIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var entities = await DbContext.Set<PublicLinkAggregate>()
            .Where(entity => entity.CameraId == id)
            .AsTracking()
            .ToListAsync(cancellationToken);

        foreach (var entity in entities)
        {
            DbContext.Remove(entity);
        }
    }

    public Task<bool> IsNameExistsAsync(string name, CancellationToken cancellationToken = default) =>
        DbContext.Set<PublicLinkAggregate>()
            .AsNoTracking()
            .AnyAsync(entity => entity.Name == name, cancellationToken);
}