﻿<FormSectionComponent Title="Подключение к ONVIF">
	<MudTextField T="string"
				  @bind-Value="Onvif.Username"
				  Label="Логин" />
	<PasswordFieldComponent @bind-Value="Onvif.Password"
							Label="Пароль" />

	<MudStack Row="true">
		<MudTextField @bind-Value="Onvif.Host"
					  Label="Адрес ONVIF" />
		<div style="max-width: 100px">
			<MudNumericField T="int"
							 @bind-Value="Onvif.Port"
							 Label="Порт" />
		</div>
	</MudStack>
	@* <MudButton>Подключить</MudButton> *@
</FormSectionComponent>

@* <FormSectionComponent Title="Характеристики ONVIF"> *@
@* 	<MudCheckBox T="bool" *@
@* 				 Label="PTZ" /> *@
@* 	<MudCheckBox T="bool" *@
@* 				 Label="Получать события" /> *@
@* </FormSectionComponent> *@

@* <FormSectionComponent Title="Информация об устройстве"> *@
@* </FormSectionComponent> *@

@* <FormSectionComponent Title="Настройка выходных потоков"> *@
@* 	<MudStack> *@
@* 		<MudText Typo="Typo.subtitle2">Main stream</MudText> *@
@* 		<MudSelect T="string" *@
@* 				   Label="Профиль H.264"> *@
@* 			<MudSelectItem T="string">Main</MudSelectItem> *@
@* 		</MudSelect> *@
@* 		<SelectEnumComponent TEnum="Resolution" *@
@* 							 Label="Разрешение" *@
@* 							 Required="true" *@
@* 							 RequiredError="Данное поле обязательно" /> *@

@* 		<MudNumericField T="int" *@
@* 						 Label="Битрейт, кбит/с" /> *@

@* 		<SelectEnumComponent TEnum="FrameRate" *@
@* 							 Label="Частота кадров" *@
@* 							 Required="true" *@
@* 							 RequiredError="Данное поле обязательно" /> *@

@* 		<MudNumericField T="int" *@
@* 						 Label="Качество" /> *@
@* 	</MudStack> *@
@* </FormSectionComponent> *@

@* <FormSectionComponent Title="Сеть"> *@
@* 	<MudCheckBox T="bool" *@
@* 				 Label="DHCP" /> *@
@* </FormSectionComponent> *@

@* <FormSectionComponent Title="Настройки изображения"> *@
@* </FormSectionComponent> *@

@code
{
	public class OnvifSettings
	{
		public string Host { get; set; } = string.Empty;
		public int Port { get; set; }
		public string Username { get; set; } = string.Empty;
		public string Password { get; set; } = string.Empty;

		public OnvifSettings(string host, int port, string username, string password)
		{
			Host = host;
			Port = port;
			Username = username;
			Password = password;
		}

		public OnvifSettings()
		{
		}
	}

	[Parameter]
	[EditorRequired]
	public OnvifSettings Onvif { get; set; } = null!;
}