using FluentValidation;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using System.Reactive;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Components.Drawer;
using Teslametrics.App.Web.Eto.Users;
using Teslametrics.App.Web.Events.Users;
using Teslametrics.App.Web.Extensions;
using Severity = MudBlazor.Severity;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Users.Drawer.Edit;

public partial class UserEditComponent
{
	private class RoleModel(Guid id, string name)
	{
		public Guid Id { get; set; } = id;
		public string Name { get; set; } = name;
	}
	private class UpdateUserModel(Guid id, HashSet<RoleModel> roles)
	{
		public Guid Id { get; init; } = id;
		public HashSet<RoleModel> Roles { get; set; } = roles;
	}
	private class UpdateValidator : BaseFluentValidator<UpdateUserModel>
	{
		public UpdateValidator()
		{
			RuleFor(model => model.Roles)
				.NotEmpty()
				.WithMessage("Добавьте хотя бы 1 роль");
		}
	}

	[CascadingParameter(Name = DrawerConsts.InstanceName)]
	private DrawerComponent Drawer { get; set; } = null!;

	private Guid? _currentUserId;
	private bool _sameUser => _model?.Id is null ? false : _currentUserId == _model?.Id;// Пользователь просматривает сам себя

	private bool _subscribing;
	private Guid _userId;
	private bool _isValid;
	private RoleModel? _search;
	private UpdateUserModel? _model;
	private UpdateValidator _validator = new();
	private List<RoleModel> _roles = new();

	private GetUserUseCase.Response? _user;
	private SubscribeUserUseCase.Response? _subscriptionResult;

	#region Parameters
	[Parameter]
	[EditorRequired]
	public Guid UserId { get; set; }

	[Parameter]
	[EditorRequired]
	public Guid OrganizationId { get; set; }
	#endregion

	protected override void OnInitialized()
	{
		AuthenticationStateProvider.AuthenticationStateChanged += OnAuthStateChanged;
		base.OnInitialized();
	}

	protected override async Task OnInitializedAsync()
	{
		try
		{
			var state = await AuthenticationStateProvider.GetAuthenticationStateAsync();
			if (state.User.Identity?.IsAuthenticated ?? false)
			{
				_currentUserId = state.User.GetUserId()!.Value;
			}
		}
		catch (Exception exc)
		{
			Logger.LogError(exc, exc.Message);
		}
	}

	protected override async Task OnParametersSetAsync()
	{
		if (UserId != _userId)
		{
			_userId = UserId;
			_user = null;
			await FetchAsync();
		}

		await base.OnParametersSetAsync();
	}

	protected async Task FetchAsync()
	{
		if (IsLoading) return;

		try
		{
			await SetLoadingAsync();
			_user = await ScopeFactory.MediatorSend(new GetUserUseCase.Query(UserId, OrganizationId));
			switch (_user.Result)
			{
				case GetUserUseCase.Result.Success:
					_model = new(_user.Id, _user.Roles.Select(item => new RoleModel(item.Id, item.Name)).ToHashSet());
					await SubscribeAsync();
					break;
				case GetUserUseCase.Result.ValidationError:
					Snackbar.Add("Ошибка валидации данных", Severity.Error);
					break;
				case GetUserUseCase.Result.UserNotFound:
					Snackbar.Add("Пользователь не найден, вероятно, он был удалён", Severity.Error);
					break;
				case GetUserUseCase.Result.Unknown:
				default:
					throw new Exception($"Unexpected result in {nameof(GetUserUseCase)}: {_user.Result}");
			}
		}
		catch (Exception ex)
		{
			Snackbar.Add("Не удалось получить выбранную пользователя. Повторите попытку", Severity.Error);
			Logger.LogError(ex, ex.Message);
		}
		finally
		{
			await SetLoadingAsync(false);
		}
	}


	private async Task<IEnumerable<RoleModel>> FetchRolesAsync(string value, CancellationToken token)
	{
		List<RoleModel> roles = [];
		if (IsLoading) return roles;
		try
		{
			var result = await ScopeFactory.MediatorSend(new GetRoleListUseCase.Query(OrganizationId, value), token);
			switch (result.Result)
			{
				case GetRoleListUseCase.Result.Success:
					roles.AddRange(result.Items.Select(item => new RoleModel(item.Id, item.Name)));
					break;
				case GetRoleListUseCase.Result.ValidationError:
					Snackbar.Add("Не удалось получить список ролей. Повторите попытку", Severity.Error);
					break;
				case GetRoleListUseCase.Result.Unknown:
				default:
					Snackbar.Add("Не удалось получить список ролей. Повторите попытку", Severity.Error);
					break;
			}
			return roles;
		}
		catch (Exception ex)
		{
			Snackbar.Add("Не удалось получить список ролей. Повторите попытку", Severity.Error);
			Logger.LogError(ex, ex.Message);
		}
		return roles;
	}


	private async Task SubscribeAsync()
	{
		try
		{
			if (_user is null || !_user.IsSuccess) return;

			await SetSubscribingAsync(true);
			Unsubscribe();
			_subscriptionResult = await ScopeFactory.MediatorSend(new SubscribeUserUseCase.Request(Observer.Create<object>(OnAppEventHandler, OnError), _user.Id));
			await SetSubscribingAsync(false);
			switch (_subscriptionResult.Result)
			{
				case SubscribeUserUseCase.Result.Success:
					CompositeDisposable.Add(_subscriptionResult.Subscription!);
					break;

				case SubscribeUserUseCase.Result.ValidationError:
					Snackbar.Add("Ошибка валидации при подписке на события", Severity.Error);
					break;

				case SubscribeUserUseCase.Result.Unknown:
				default:
					throw new Exception($"Unexpected result in {nameof(SubscribeUserUseCase)}: {_subscriptionResult.Result}");
			}
		}
		catch (Exception exc)
		{
			Snackbar.Add("Не удалось получить подписку на события пользователя. Повторите попытку", Severity.Error);
			Logger.LogError(exc, exc.Message);
		}
	}

	private void Unsubscribe()
	{
		if (_subscriptionResult is not null && _subscriptionResult.Subscription is not null)
		{
			CompositeDisposable.Remove(_subscriptionResult.Subscription);
			_subscriptionResult.Subscription.Dispose();
		}
	}
	protected Task SetSubscribingAsync(bool isLoading = true) => UpdateViewAsync(() =>
	{
		_subscribing = isLoading;
	});

	#region [Actions]
	private Task RefreshAsync() => FetchAsync();

	private async Task SubmitAsync()
	{
		if (IsLoading || _model is null) return;
		try
		{
			await SetLoadingAsync();
			var result = await ScopeFactory.MediatorSend(new UpdateUserUseCase.Command(_model.Id, OrganizationId, _model.Roles.Select(x => x.Id).ToList()));
			switch (result.Result)
			{
				case UpdateUserUseCase.Result.Success:
					Snackbar.Add("Пользователь успешно обновлён", Severity.Success);
					EventSystem.Publish(new UserSelectEto(result.Id));
					break;
				case UpdateUserUseCase.Result.ValidationError:
					Snackbar.Add("Ошибка валидации данных", Severity.Error);
					break;
				case UpdateUserUseCase.Result.RoleNotFound:
					Snackbar.Add("Одна из выбранных ролей недоступна", Severity.Error);
					break;
				case UpdateUserUseCase.Result.CannotUpdateSystemUser:
					Snackbar.Add("Системного пользователя обновить нельзя!", Severity.Error);
					break;
				case UpdateUserUseCase.Result.Unknown:
				default:
					throw new Exception($"Unexpected result in {nameof(UpdateUserUseCase)}: {result.Result}");
			}
		}
		catch (Exception ex)
		{
			Snackbar.Add("Не удалось обновить пользователя из-за непредвиденной ошибки, повторите попытку", Severity.Error);
			Logger.LogError(ex, ex.Message);
		}
		finally
		{
			await SetLoadingAsync(false);
		}
	}
	private Task CancelAsync() => Drawer.HideAsync();
	private void Delete() => EventSystem.Publish(new UserDeleteEto(OrganizationId, _model!.Id));
	private void Select() => EventSystem.Publish(new UserSelectEto(_model!.Id));
	private void LoginpasswordChange() => EventSystem.Publish(new ForcePasswordChangeEto(_model!.Id));
	private void ChangeUserPassword() => EventSystem.Publish(new ChangeUserPasswordEto(UserId));
	private async Task UnLockAsync()
	{
		if (IsLoading || _user is null) return;

		try
		{
			await SetLoadingAsync();
			var result = await ScopeFactory.MediatorSend(new UnlockUserUseCase.Command(_user.Id));
			if (result.IsSuccess)
			{
				Snackbar.Add("Пользователь разблокирован", Severity.Success);
				return;
			}

			switch (result.Result)
			{
				case UnlockUserUseCase.Result.CannotUnlockSystemUser:
					Snackbar.Add("Системного пользователя невозможно разблокировать", Severity.Error);
					break;
				case UnlockUserUseCase.Result.UserNotFound:
					Snackbar.Add("Выбранный пользователь недоступен", Severity.Error);
					break;
				case UnlockUserUseCase.Result.ValidationError:
					Snackbar.Add("Произошла ошибка валидации пользователя", Severity.Error);
					break;
				case UnlockUserUseCase.Result.Unknown:
				default:
					Snackbar.Add("Произошла неизвестная ошибка при попытке разблокировать пользователя. Код: " + result.Result.ToString(), Severity.Error);
					break;
			}
		}
		catch (Exception ex)
		{
			Snackbar.Add("Произошла ошибка при смене пароля", Severity.Error);
			Logger.LogError(ex, ex.Message);
		}
		finally
		{
			await SetLoadingAsync(false);
		}
	}
	private async Task LockAsync()
	{
		if (IsLoading || _user is null) return;

		try
		{
			await SetLoadingAsync();
			var result = await ScopeFactory.MediatorSend(new LockUserUseCase.Command(_user.Id));
			if (result.IsSuccess)
			{
				Snackbar.Add("Пользователь заблокирован", Severity.Success);
				return;
			}

			switch (result.Result)
			{
				case LockUserUseCase.Result.CannotLockSystemUser:
					Snackbar.Add("Системного пользователя невозможно заблокировать", Severity.Error);
					break;
				case LockUserUseCase.Result.UserNotFound:
					Snackbar.Add("Выбранный пользователь недоступен", Severity.Error);
					break;
				case LockUserUseCase.Result.ValidationError:
					Snackbar.Add("Произошла ошибка валидации пользователя", Severity.Error);
					break;
				case LockUserUseCase.Result.Unknown:
				default:
					Snackbar.Add("Произошла неизвестная ошибка при попытке заблокировать пользователя. Код: " + result.Result.ToString(), Severity.Error);
					break;
			}
		}
		catch (Exception ex)
		{
			Snackbar.Add("Произошла ошибка при смене пароля", Severity.Error);
			Logger.LogError(ex, ex.Message);
		}
		finally
		{
			await SetLoadingAsync(false);
		}
	}
	#endregion [Actions]

	#region [Event Handlers]
	private async void OnAppEventHandler(object appEvent)
	{
		if (_model is null) return;

		switch (appEvent)
		{
			case SubscribeUserUseCase.UpdatedEvent updatedEto:
				await FetchAsync();
				await UpdateViewAsync();
				break;

			case SubscribeUserUseCase.DeletedEvent deletedEto:
				Unsubscribe();
				_model = null;
				Snackbar.Add("Редактируемый вами пользователь был удалён", Severity.Warning);
				await CancelAsync();
				break;

			default:
				break;
		}
	}

	private void OnError(Exception exc)
	{
		Logger.LogError(exc, exc.Message);
		Snackbar.Add("Ошибка валидации при подписке на события", Severity.Error);
	}

	private async void OnAuthStateChanged(Task<AuthenticationState> authenticationState)
	{
		await InvokeAsync(async () =>
		{
			if (_model is null) return;

			var authState = await authenticationState;
			if (authState == null) return;

			if (authState.User.Identity?.IsAuthenticated ?? false)
			{
				_currentUserId = authState.User.GetUserId()!.Value;
			}

			_currentUserId = null;

			StateHasChanged();
		});
	}

	private void OnRoleSelected(RoleModel role)
	{
		_search = null;
		if (!_model?.Roles.Any(x => x.Id == role.Id) ?? false)
			_model?.Roles.Add(role);
	}
	#endregion [Event Handlers]
}
