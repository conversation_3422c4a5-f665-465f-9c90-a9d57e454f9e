@using Teslametrics.App.Web.Domain.AccessControl
@using Teslametrics.App.Web.Extensions
@inherits InteractiveBaseComponent<Teslametrics.App.Web.Locales.Features.Main.AccessControl.Roles.Drawer.Create.CameraViews.CameraViewsConcretePermissionsComponent>
<div class="d_contents">
	<MudList T="Guid"
			 ReadOnly="true"
			 Class="ma-n4">
		@if (IsLoading)
		{
			<MudProgressLinear Color="Color.Primary"
							   Indeterminate="true" />
		}
		else
		{
			<div style="height: 4px;"></div>
		}
		@if (IsLoading && (_response is null || !_response.IsSuccess))
		{
			<MudStack Spacing="2">
				@for (var i = 0; i < Random.Shared.Next(3, 7); i++)
				{
					<MudPaper Class="pa-4">
						<MudStack Row="true"
								  Spacing="2">
							<MudSkeleton SkeletonType="SkeletonType.Circle"
										 Height="24px"
										 Width="24px" />
							<MudSkeleton Width="60%"
										 Height="24px" />
							<MudSpacer />
							<MudSkeleton Width="80px"
										 Height="24px" />
						</MudStack>
					</MudPaper>
				}
			</MudStack>
		}
		@if (!IsLoading && (_response is null || !_response.IsSuccess))
		{
			<MudStack AlignItems="AlignItems.Center"
					  Justify="Justify.Center"
					  Class="mud-height-full">
				<MudIcon Icon="@Icons.Material.Filled.GroupOff"
						 Style="font-size: 8rem;" />
				<MudText Typo="Typo.body1">@L["NothingFound"]</MudText>
				<MudText Typo="Typo.subtitle1">@L["TryAgainLater"]</MudText>
			</MudStack>
		}
		@if (!IsLoading && _response is not null && _response.IsSuccess)
		{
			@if (_response.Items.Count == 0)
			{
				<MudStack AlignItems="AlignItems.Center"
						  Justify="Justify.Center"
						  Class="mud-height-full">
					<MudIcon Icon="@Icons.Material.Filled.GroupOff"
							 Style="font-size: 8rem;" />
					<MudText Typo="Typo.body1">@L["NothingFound"]</MudText>
					<MudText Typo="Typo.subtitle1">@L["TryAgainLater"]</MudText>
				</MudStack>
			}

			@foreach (var view in _response.Items)
			{
				<MudListItem Text="Inbox"
							 Icon="@Icons.Material.Filled.ViewModule"
							 @key="view.Id"
							 Value="view.Id">
					<ChildContent>
						<MudStack Spacing="2"
								  AlignItems="AlignItems.Center"
								  Row="true">
							<MudText Typo="Typo.body1">@view.Name</MudText>
							<MudSpacer />
							<div class="checkboxes">
								@foreach (var permission in _viewValues)
								{
									<MudCheckBox T="bool"
												 Class="input_checkbox"
												 Value="ContainsPermission(permission, view.Id)"
												 ValueChanged="(isChecked) => OnChangedHandler(isChecked, permission, view)"
												 Label="@Localizer[permission.GetEnumPermissionString()]"
												 Color="Color.Primary"
												 @key="permission" />
								}
							</div>
						</MudStack>
					</ChildContent>
				</MudListItem>
			}
		}
	</MudList>
</div>