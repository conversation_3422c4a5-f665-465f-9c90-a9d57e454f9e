using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using MudBlazor;
using System.Reactive;
using Teslametrics.App.Web.Eto.Roles;
using Teslametrics.App.Web.Eto.Users;
using Teslametrics.App.Web.Exceptions;
using Teslametrics.App.Web.Extensions;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Roles.List;

public partial class RoleListComponent
{
	private bool _disposedValue;
	private Guid _organizationId;
	private DateTime _lastRefreshTime = DateTime.Now;
	private string _searchString = string.Empty;

	protected internal class RoleListItemDto
	{
		public Guid Id { get; set; }
		public string Name { get; set; } = string.Empty;
	}

	protected List<RoleListItemDto> Items = [];

	#region Parameters
	[Parameter]
	public int Offset { get; set; } = 0;
	[Parameter]
	public EventCallback<int> OffsetChanged { get; set; }

	[Parameter]
	public int Limit { get; set; } = 25;
	[Parameter]
	public EventCallback<int> LimitChanged { get; set; }

	[Parameter]
	[EditorRequired]
	public Guid OrganizationId { get; set; }
	#endregion

	protected int CurrentPage => Offset / Limit;

	protected override async Task OnParametersSetAsync()
	{
		await base.OnParametersSetAsync();

		if (OrganizationId != _organizationId)
		{
			_organizationId = OrganizationId;
			await FetchAsync();
		}
	}
	protected override async Task OnInitializedAsync()
	{
		await base.OnInitializedAsync();

		await SubscribeAsync();
		await FetchAsync();

		AuthenticationStateProvider.AuthenticationStateChanged += OnAuthenticationStateChanged;
	}

	protected override void Dispose(bool disposing)
	{
		if (!_disposedValue)
		{
			if (disposing)
			{
				AuthenticationStateProvider.AuthenticationStateChanged -= OnAuthenticationStateChanged;
			}

			_disposedValue = true;
		}

		// Вызов базового метода Dispose
		base.Dispose(disposing);
	}

	private async Task FetchAsync()
	{
		if (IsLoading) return;
		await SetLoadingAsync(true);
		GetRoleListUseCase.Response? response = null;

		try
		{

			Items.Clear();
			var userId = await GetCurrentUserIdAsync() ?? throw new NotAuthorizedException();
			response = await ScopeFactory.MediatorSend(new GetRoleListUseCase.Query(userId, OrganizationId, Offset, Limit, _searchString));
		}
		catch (Exception ex)
		{
			response = null;
			Logger.LogError(ex, ex.Message);
			Snackbar.Add("Не удалось получить список ролей из-за ошибки на сервере. Обратитесь к администратору", Severity.Error);
		}
		await SetLoadingAsync(false);

		if (response is null) return;
		switch (response.Result)
		{
			case GetRoleListUseCase.Result.Success:
				Items.AddRange(response.Items.Select(item => new RoleListItemDto() { Id = item.Id, Name = item.Name }));
				_lastRefreshTime = DateTime.Now;
				break;
			case GetRoleListUseCase.Result.ValidationError:
				Snackbar.Add("Ошибка валидации при получении списка ролей", Severity.Error);
				break;
			case GetRoleListUseCase.Result.Unknown:
				Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(RoleListComponent), nameof(GetRoleListUseCase));
				Snackbar.Add($"Не удалось получить список ролей из-за непредвиденного ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
				break;
			default:
				Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(RoleListComponent), nameof(GetRoleListUseCase), response.Result);
				Snackbar.Add($"Не удалось получить список ролей из-за ошибки: {response.Result}. Обратитесь к администратору.", MudBlazor.Severity.Error);
				break;
		}
	}

	private Func<RoleListItemDto, bool> _quickFilter => x =>
	{
		if (string.IsNullOrWhiteSpace(_searchString))
			return true;

		if (x.Name.Contains(_searchString, StringComparison.OrdinalIgnoreCase))
			return true;

		return false;
	};

	private Task RowsPerPageChanged(int limit)
	{
		Limit = limit;
		if (LimitChanged.HasDelegate)
		{
			return LimitChanged.InvokeAsync(limit);
		}
		return Task.CompletedTask;
	}

	#region [Actions]
	private void Select(DataGridRowClickEventArgs<RoleListItemDto> args) => EventSystem.Publish(new RoleSelectEto(OrganizationId, args.Item.Id));
	private void Select(RoleListItemDto item) => EventSystem.Publish(new RoleSelectEto(OrganizationId, item.Id));
	private void Edit(RoleListItemDto item) => EventSystem.Publish(new RoleEditEto(OrganizationId, item.Id));
	private void Delete(RoleListItemDto item) => EventSystem.Publish(new RoleDeleteEto(OrganizationId, item.Id));
	private void CreateRole() => EventSystem.Publish(new RoleCreateEto(OrganizationId));
	private void CreateUser() => EventSystem.Publish<UserCreateEto>();
	private Task RefreshAsync() => FetchAsync();
	#endregion [Actions]

	private async Task SubscribeAsync()
	{
		var result = await ScopeFactory.MediatorSend(new SubscribeRoleListUseCase.Request(Observer.Create<SubscribeRoleListUseCase.UpdatedEvent>(OnAppEventHandler, OnError)));
		if (result.IsSuccess)
		{
			CompositeDisposable.Add(result.Subscription!);
			return;
		}

		switch (result.Result)
		{
			case SubscribeRoleListUseCase.Result.ValidationError:
				Snackbar.Add("Ошибка валидации при подписке на события", Severity.Error);
				break;

			case SubscribeRoleListUseCase.Result.Unknown:
			default:
				Snackbar.Add("Не удалось получить подписку на обновления из-за непредвиденной ошибки:" + result.Result.ToString(), Severity.Error);
				break;
		}
	}

	#region [Event Handlers]
	protected async void OnAppEventHandler(SubscribeRoleListUseCase.UpdatedEvent appEvent)
	{
		await RefreshAsync();
		await UpdateViewAsync();
	}

	protected void OnError(Exception exc)
	{
		Logger.LogError(exc, exc.Message);
		Snackbar.Add("Ошибка валидации при подписке на события", Severity.Error);
	}

	private async void OnAuthenticationStateChanged(Task<AuthenticationState> authState)
	{
		await FetchAsync();
	}
	#endregion [Event Handlers]
}
