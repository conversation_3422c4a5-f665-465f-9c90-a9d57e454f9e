using System.Collections.Frozen;
using Teslametrics.App.Web.Shared;

namespace Teslametrics.App.Web.Services;

public static class BitrateCalculator
{
    private const double AdjustmentFactor = 1.05;

    private static readonly FrozenDictionary<Resolution, (int width, int height)> _resolutionSizes = new Dictionary<Resolution, (int, int)>
    {
        { Resolution.R320x240, (320, 240) },
        { Resolution.R640x480, (640, 480) },
        { Resolution.R800x600, (800, 600) },
        { Resolution.R1024x768, (1024, 768) },
        { Resolution.R1280x720, (1280, 720) },
        { Resolution.R1280x1024, (1280, 1024) },
        { Resolution.R1920x1080, (1920, 1080) },
        { Resolution.R2048x1536, (2048, 1536) },
        { Resolution.R2592x1944, (2592, 1944) },
        { Resolution.R2688x1520, (2688, 1520) },
        { Resolution.R3840x2160, (3840, 2160) }
    }.ToFrozenDictionary();

    private static readonly FrozenDictionary<VideoCodec, double> _compressionRatios = new Dictionary<VideoCodec, double>
    {
        { VideoCodec.H264Baseline, 0.1 },         // Базовый профиль, меньше сжатие
        { VideoCodec.H264Main, 0.08 },            // Основной профиль, среднее сжатие
        { VideoCodec.H264High, 0.07 },            // Высокий профиль, лучшее сжатие
        { VideoCodec.H264PlusBaseline, 0.065 },   // H.264+ Baseline ~35% лучше H.264 Baseline
        { VideoCodec.H264PlusMain, 0.052 },       // H.264+ Main ~35% лучше H.264 Main
        { VideoCodec.H264PlusHigh, 0.045 }       // H.264+ High ~35% лучше H.264 High
    }.ToFrozenDictionary();

    private static readonly FrozenDictionary<FrameRate, double> _frameRates = new Dictionary<FrameRate, double>
    {
        { FrameRate.Fps1_16, 1.0/16 },
        { FrameRate.Fps1_8, 1.0/8 },
        { FrameRate.Fps1_4, 1.0/4 },
        { FrameRate.Fps1_2, 1.0/2 },
        { FrameRate.Fps1, 1 },
        { FrameRate.Fps2, 2 },
        { FrameRate.Fps4, 4 },
        { FrameRate.Fps6, 6 },
        { FrameRate.Fps8, 8 },
        { FrameRate.Fps10, 10 },
        { FrameRate.Fps12, 12 },
        { FrameRate.Fps15, 15 },
        { FrameRate.Fps16, 16 },
        { FrameRate.Fps18, 18 },
        { FrameRate.Fps20, 20 },
        { FrameRate.Fps22, 22 },
        { FrameRate.Fps25, 25 },
        { FrameRate.Fps30, 30 },
        { FrameRate.Fps60, 60 }
    }.ToFrozenDictionary();

    private static readonly FrozenDictionary<SceneDynamic, double> _sceneDynamicRatios = new Dictionary<SceneDynamic, double>
    {
        { SceneDynamic.Low, 0.44 },
        { SceneDynamic.Medium, 0.7 },
        { SceneDynamic.High, 0.9 }
    }.ToFrozenDictionary();

    private static readonly FrozenDictionary<AudioCodec, double> _audioBitrates = new Dictionary<AudioCodec, double>
    {
        { AudioCodec.None, 0 },       // Без аудио
        { AudioCodec.G722_1, 32 },    // 24/32 Kbps
        { AudioCodec.G711ulaw, 64 },  // 64 Kbps
        { AudioCodec.G711alaw, 64 },  // 64 Kbps
        { AudioCodec.MP2L2, 128 },    // 128 Kbps
        { AudioCodec.G726, 32 },      // 16/24/32/40 Kbps
        { AudioCodec.AAC, 96 },       // Variable, typically 96 Kbps for good quality
        { AudioCodec.PCM, 128 }       // Linear PCM
    }.ToFrozenDictionary();

    /// <summary>
    /// Рассчитывает примерный битрейт в Кбит/с, включая аудио
    /// </summary>
    public static double CalculateBitrate(Resolution resolution, VideoCodec videoCodec, FrameRate frameRate, SceneDynamic sceneDynamic, AudioCodec audioCodec)
    {
        var (width, height) = _resolutionSizes[resolution];
        var fps = _frameRates[frameRate];
        var compressionRatio = _compressionRatios[videoCodec];
        var sceneDynamicRatio = _sceneDynamicRatios[sceneDynamic];
        var audioBitrate = _audioBitrates[audioCodec];

        var bbp = compressionRatio * sceneDynamicRatio;

        // Расчет битрейта видео:
        // (Ширина × Высота × Кадров_в_секунду × Бит_на_пиксель × Коэффициент_корректировки) / 1024 для перевода в Кбит/с
        var videoBitrate = width * height * fps * bbp * AdjustmentFactor / 1000;

        // Общий битрейт = видео + аудио
        var totalBitrate = videoBitrate + audioBitrate;

        return Math.Round(totalBitrate, 2);
    }

    /// <summary>
    /// Вычисляет размер хранилища в мегабайтах (МБ), необходимый для заданного битрейта и количества дней.
    /// </summary>
    public static double CalculateStorageSizeForDays(double bitrateInKbps, int days)
    {
        if (days < 1)
        {
            throw new ArgumentOutOfRangeException(nameof(days), "Number of days cannot be less than 1.");
        }

        if (bitrateInKbps <= 0)
        {
            throw new ArgumentOutOfRangeException(nameof(bitrateInKbps), "Bitrate must be greater than 0.");
        }

        const double secondsPerDay = 86400; // 24 hours * 60 minutes * 60 seconds
        const double bytesPerKilobit = 125; // 1 kilobit = 1,000 bits = 125 bytes
        const double megabytesPerByte = 1.0 / 1_048_576; // 1 MB = 1,048,576 bytes

        double storageSize = bitrateInKbps * bytesPerKilobit * secondsPerDay * days * megabytesPerByte;
        return Math.Round(storageSize, 2);
    }
}