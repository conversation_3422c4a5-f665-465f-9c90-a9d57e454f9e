using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using MudBlazor;
using System.Reactive;
using Teslametrics.App.Web.Events.Cameras;
using Teslametrics.App.Web.Extensions;
using Teslametrics.MediaServer.Orleans.Camera;

namespace Teslametrics.App.Web.Features.Main.Cameras.CameraDrawer.View;

public partial class CameraViewComponent
{
    private CameraStatus _cameraStatus;

    private bool _subscribing;
    private GetCameraUseCase.Response? _model;
    private SubscribeCameraUseCase.Response? _subscriptionResult;
    private SubscribeCameraStatusUseCase.Response? _statusSubscriptionResult;

    [Inject]
    public IJSRuntime JS { get; set; } = null!;

    #region Parameters
    [Parameter]
    [EditorRequired]
    public Guid OrganizationId { get; set; }

    [Parameter]
    [EditorRequired]
    public Guid CameraId { get; set; }

    [CascadingParameter]
    public CameraDrawer CameraDrawer { get; set; } = null!;
    #endregion

    public void StatusUpdated(SubscribeCameraStatusUseCase.StatusChangedEvent @event)
    {
        InvokeAsync(() =>
        {
            _cameraStatus = @event.Status;
            StateHasChanged();
        });
    }


    protected override async Task OnInitializedAsync()
    {
        await SubscribeStatusAsync();

        await base.OnInitializedAsync();
    }

    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();

        if (_model is null || _model.Id != CameraId)
        {
            _model = null;
            await FetchAsync();
            await SubscribeAsync();
        }
    }

    protected async Task FetchAsync()
    {
        await SetLoadingAsync(true);
        try
        {
            _model = await ScopeFactory.MediatorSend(new GetCameraUseCase.Query(CameraId));
        }
        catch (Exception ex)
        {
            _model = null;
            Snackbar.Add($"Не удалось получить камеру из-за ошибки сообщения с сервером. Повторите попытку позже.", MudBlazor.Severity.Error);
            Logger.LogError(ex, ex.Message);
        }

        await SetLoadingAsync(false);
        if (_model is null) return;

        switch (_model.Result)
        {
            case GetCameraUseCase.Result.Success:
                _cameraStatus = _model.CameraStatus;
                break;
            case GetCameraUseCase.Result.ValidationError:
                Snackbar.Add("Ошибка валидации данных", Severity.Error);
                break;
            case GetCameraUseCase.Result.Unknown:
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(CameraViewComponent), nameof(GetCameraUseCase));
                Snackbar.Add($"Не удалось получить камеру из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
                break;
            default:
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(CameraViewComponent), nameof(GetCameraUseCase), _model.Result);
                Snackbar.Add($"Не удалось получить камеру из-за ошибки: {_model.Result}", MudBlazor.Severity.Error);
                break;
        }
    }
    private async Task SubscribeAsync()
    {
        Unsubscribe();
        await SetSubscribingAsync(true);

        try
        {
            _subscriptionResult = await ScopeFactory.MediatorSend(new SubscribeCameraUseCase.Request(Observer.Create<object>(OnAppEventHandler, OnError), CameraId));
        }
        catch (Exception ex)
        {
            _subscriptionResult = null;
            Snackbar.Add($"Не удалось получить подписку на события камеры из-за ошибки сообщения с сервером. Повторите попытку позже.", MudBlazor.Severity.Error);
            Logger.LogError(ex, ex.Message);
        }

        await SetSubscribingAsync(false);
        if (_subscriptionResult is null) return;

        switch (_subscriptionResult.Result)
        {
            case SubscribeCameraUseCase.Result.Success:
                CompositeDisposable.Add(_subscriptionResult.Subscription!);
                break;
            case SubscribeCameraUseCase.Result.ValidationError:
                Snackbar.Add("Ошибка валидации при подписке на события", Severity.Error);
                break;
            case SubscribeCameraUseCase.Result.CameraNotFound:
                Snackbar.Add("Ошибка подписки на события камеры", Severity.Error);
                break;
            case SubscribeCameraUseCase.Result.Unknown:
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(CameraViewComponent), nameof(SubscribeCameraUseCase));
                Snackbar.Add($"Не удалось получить подписку на события камеры из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
                break;

            default:
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(CameraViewComponent), nameof(SubscribeCameraUseCase), _subscriptionResult.Result);
                Snackbar.Add($"Не удалось получить подписку на события камеры из-за ошибки: {_subscriptionResult.Result}", MudBlazor.Severity.Error);
                break;
        }
    }
    private void Unsubscribe()
    {
        if (_subscriptionResult?.Subscription is not null)
        {
            CompositeDisposable.Remove(_subscriptionResult.Subscription);
            _subscriptionResult.Subscription.Dispose();
        }
    }

    protected Task SetSubscribingAsync(bool isLoading = true) => UpdateViewAsync(() =>
    {
        _subscribing = isLoading;
    });


    private async Task DisconnectAsync()
    {
        DisconnectCameraUseCase.Response? response = null;
        try
        {
            response = await ScopeFactory.MediatorSend(new DisconnectCameraUseCase.Command(CameraId));
        }
        catch (Exception ex)
        {
            response = null;
            Snackbar.Add($"Не удалось отключить камеру из-за ошибки сообщения с сервером. Повторите попытку позже.", MudBlazor.Severity.Error);
            Logger.LogError(ex, ex.Message);
        }

        if (response is null) return;
        switch (response.Result)
        {
            case DisconnectCameraUseCase.Result.Success:
                Snackbar.Add("Отправлен запрос на отключение камеры", Severity.Success);
                break;
            case DisconnectCameraUseCase.Result.CameraNotFound:
                Snackbar.Add("Не удалось отключить камеру так как она не числится в системе", Severity.Error);
                break;
            case DisconnectCameraUseCase.Result.ValidationError:
                Snackbar.Add("Не удалось отключить камеру из-за ошибки валидации", Severity.Error);
                break;
            case DisconnectCameraUseCase.Result.Unknown:
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(CameraViewComponent), nameof(DisconnectCameraUseCase));
                Snackbar.Add($"Не удалось отключить камеру из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
                break;
            default:
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(CameraViewComponent), nameof(DisconnectCameraUseCase), response.Result);
                Snackbar.Add($"Не удалось отключить камеру из-за ошибки: {response.Result}", MudBlazor.Severity.Error);
                break;
        }
    }

    private async Task SubscribeStatusAsync()
    {
        try
        {
            _statusSubscriptionResult = await ScopeFactory.MediatorSend(new SubscribeCameraStatusUseCase.Request(Observer.Create<SubscribeCameraStatusUseCase.StatusChangedEvent>(StatusUpdated, OnError), CameraId));
        }
        catch (Exception ex)
        {
            _statusSubscriptionResult = null;
            Snackbar.Add($"Не удалось подписаться на события статуса камеры из-за ошибки сообщения с сервером. Повторите попытку позже.", MudBlazor.Severity.Error);
            Logger.LogError(ex, "Failed to subscribe to camera preview events");
        }

        switch (_statusSubscriptionResult?.Result)
        {
            case SubscribeCameraStatusUseCase.Result.Success:
                break;
            case SubscribeCameraStatusUseCase.Result.ValidationError:
                Snackbar.Add("Ошибка валидации при подписке на события статуса камеры", MudBlazor.Severity.Error);
                break;
            case SubscribeCameraStatusUseCase.Result.Unknown:
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(CameraViewComponent), nameof(SubscribeCameraStatusUseCase));
                Snackbar.Add($"Не удалось получить подписку на события статуса камеры из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
                break;
            default:
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(CameraViewComponent), nameof(SubscribeCameraStatusUseCase), _subscriptionResult?.Result);
                Snackbar.Add($"Не удалось получить подписку на события статуса камеры из-за ошибки", MudBlazor.Severity.Error);
                break;
        }
    }

    #region [Actions]
    private void Edit() => EventSystem.Publish(new CameraEditEto(OrganizationId, CameraId));
    private void Cancel() => CameraDrawer.Close();
    private Task RefreshAsync() => FetchAsync();
    private async Task ConnectAsync()
    {
        if (_model is null) return;

        ConnectCameraUseCase.Response? response;
        try
        {
            response = await ScopeFactory.MediatorSend(new ConnectCameraUseCase.Command(CameraId));
        }
        catch (Exception ex)
        {
            response = null;
            Snackbar.Add($"Не удалось подключить камеру из-за ошибки сообщения с сервером. Повторите попытку позже.", MudBlazor.Severity.Error);
            Logger.LogError(ex, ex.Message);
        }

        if (response is null) return;

        switch (response.Result)
        {
            case ConnectCameraUseCase.Result.Success:
                Snackbar.Add("Отправлен запрос на подключение камеры", Severity.Success);
                break;
            case ConnectCameraUseCase.Result.CameraNotFound:
                Snackbar.Add("Не удалось подключить камеру так как она не числится в системе", Severity.Error);
                break;
            case ConnectCameraUseCase.Result.ValidationError:
                Snackbar.Add("Не удалось подключить камеру из-за ошибки валидации", Severity.Error);
                break;
            case ConnectCameraUseCase.Result.CameraIsBlocked:
                Snackbar.Add("Камера заблокирована", Severity.Error);
                await FetchAsync();
                break;
            case ConnectCameraUseCase.Result.Unknown:
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(CameraViewComponent), nameof(ConnectCameraUseCase));
                Snackbar.Add($"Не удалось подключить камеру из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
                break;
            default:
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(CameraViewComponent), nameof(ConnectCameraUseCase), response.Result);
                Snackbar.Add($"Не удалось подключить камеру из-за ошибки: {response.Result}", MudBlazor.Severity.Error);
                break;
        }

    }
    #endregion

    #region [Event Handlers]
    private async void OnAppEventHandler(object appEvent)
    {
        switch (appEvent)
        {
            case SubscribeCameraUseCase.UpdatedEvent updatedEto:
                await FetchAsync();
                await UpdateViewAsync();
                break;

            case SubscribeCameraUseCase.DeletedEvent deletedEto:
                Snackbar.Add("Просматриваемая вами камеры была удалена", Severity.Warning);
                CameraDrawer.Close();
                break;

            default:
                Snackbar.Add("Было получено непредвиденное событие.", Severity.Warning);
                await FetchAsync();
                await UpdateViewAsync();
                Logger.LogWarning("Unexpected event in {UseCase}: {Event}", nameof(SubscribeCameraUseCase), nameof(appEvent));
                break;
        }
    }

    private void OnError(Exception exc)
    {
        Snackbar.Add("Ошибка при подписке на события", Severity.Error);
        Logger.LogError(exc, exc.Message);
    }
    #endregion [Event Handlers]
}
