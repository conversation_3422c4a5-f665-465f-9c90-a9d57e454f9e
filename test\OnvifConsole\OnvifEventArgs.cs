
/// <summary>
/// Класс для хранения данных события движения
/// </summary>
public class OnvifEventArgs : EventArgs
{
    /// <summary>
    /// Время события с устройства
    /// </summary>
    public DateTimeOffset DeviceTime { get; set; }

    /// <summary>
    /// Состояние движения (true = обнаружено, false = прекращено)
    /// </summary>
    public bool IsMotion { get; set; }

    /// <summary>
    /// Конструктор
    /// </summary>
    /// <param name="deviceTime">Время события с устройства</param>
    /// <param name="isMotion">Состояние движения</param>
    public OnvifEventArgs(DateTimeOffset deviceTime, bool isMotion)
    {
        DeviceTime = deviceTime;
        IsMotion = isMotion;
    }
}
