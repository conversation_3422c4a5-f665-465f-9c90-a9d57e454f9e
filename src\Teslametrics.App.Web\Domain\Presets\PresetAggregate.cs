using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Domain.CameraPresets.Events;
using Teslametrics.App.Web.Shared;

namespace Teslametrics.App.Web.Domain.CameraPresets;

public readonly record struct StreamConfigValueObject
{
    public Resolution Resolution { get; init; }

    public VideoCodec VideoCodec { get; init;}

    public FrameRate FrameRate { get; init; }

    public SceneDynamic SceneDynamic { get; init; }

    public AudioCodec AudioCodec { get; init; }

    public StreamConfigValueObject(Resolution resolution, VideoCodec videoCodec, FrameRate frameRate, SceneDynamic sceneDynamic, AudioCodec audioCodec)
    {
        Resolution = resolution;
        VideoCodec = videoCodec;
        FrameRate = frameRate;
        SceneDynamic = sceneDynamic;
        AudioCodec = audioCodec;
    }
}

public class PresetAggregate : IEntity
{
    public Guid Id { get; private set; }

    public string Name { get; private set; }

    public StreamConfigValueObject ArchiveStreamConfig { get; private set; }

    public StreamConfigValueObject ViewStreamConfig { get; private set; }

    public StreamConfigValueObject PublicStreamConfig { get; private set; }

    public static (PresetAggregate preset, List<object> events) Create(Guid id, string name, StreamConfigValueObject archiveStreamConfig, StreamConfigValueObject viewStreamConfig, StreamConfigValueObject publicStreamConfig) =>
        (new(id, name, archiveStreamConfig, viewStreamConfig, publicStreamConfig), [new PresetCreatedEvent(id)]);

    private PresetAggregate()
    {
        Name = string.Empty;
    }

    public PresetAggregate(Guid id, string name, StreamConfigValueObject archiveStreamConfig, StreamConfigValueObject viewStreamConfig, StreamConfigValueObject publicStreamConfig)
    {
        Id = id;
        Name = name;
        ArchiveStreamConfig = archiveStreamConfig;
        ViewStreamConfig = viewStreamConfig;
        PublicStreamConfig = publicStreamConfig;
    }

    public List<object> Update(string name, StreamConfigValueObject archiveStreamConfig, StreamConfigValueObject viewStreamConfig, StreamConfigValueObject publicStreamConfig)
    {
        bool isUpdated = false;

        if (Name != name)
        {
            Name = name;
            isUpdated = true;
        }

        if (ArchiveStreamConfig != archiveStreamConfig)
        {
            ArchiveStreamConfig = archiveStreamConfig;
            isUpdated = true;
        }

        if (ViewStreamConfig != viewStreamConfig)
        {
            ViewStreamConfig = viewStreamConfig;
            isUpdated = true;
        }

        if (PublicStreamConfig != publicStreamConfig)
        {
            PublicStreamConfig = publicStreamConfig;
            isUpdated = true;
        }

        return isUpdated ? [new PresetUpdatedEvent(Id)] : [];
    }
}
