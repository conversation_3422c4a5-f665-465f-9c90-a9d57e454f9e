using Teslametrics.App.Web.Components.Drawer;
using Teslametrics.App.Web.Events;
using Teslametrics.App.Web.Events.Quota;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Quota.Drawer;

public partial class QuotaDrawer
{
	private DrawerMode _mode = DrawerMode.Hidden;
	private Guid? _resourceId;
	public Guid _organizationId;

	public bool IsOpened => _mode != DrawerMode.Hidden;

	public enum DrawerMode
	{
		Hidden,
		Create,
		Edit,
		View,
	}

	protected override void OnInitialized()
	{
		CompositeDisposable.Add(EventSystem.Subscribe<CameraQuotaCreateEto>(EvetHandler));
		CompositeDisposable.Add(EventSystem.Subscribe<CameraQuotaSelectEto>(EvetHandler));
		CompositeDisposable.Add(EventSystem.Subscribe<CameraQuotaEditEto>(EvetHandler));

		base.OnInitialized();
	}
	public Task ShowCreateAsync(Guid organizationId) => InvokeAsync(() =>
	{
		_organizationId = organizationId;
		_resourceId = null;
		_mode = DrawerMode.Create;
		StateHasChanged();
	});

	public Task ShowEditAsync(Guid organizationId, Guid id) => InvokeAsync(() =>
	{
		_organizationId = organizationId;
		_resourceId = id;
		_mode = DrawerMode.Edit;
		StateHasChanged();
	});

	public Task ShowViewAsync(Guid organizationId, Guid id) => InvokeAsync(() =>
	{
		_organizationId = organizationId;
		_resourceId = id;
		_mode = DrawerMode.View;
		StateHasChanged();
	});

	public Task CloseAsync() => InvokeAsync(() =>
	{
		try
		{
			_mode = DrawerMode.Hidden;
			_resourceId = null;
			StateHasChanged();
		}
		catch (Exception ex)
		{
			Console.WriteLine($"Error in CloseAsync: {ex.Message}");
			throw;
		}
	});

	#region [Event Handlers]
	private async void EvetHandler(BaseEto eto)
	{
		switch (eto)
		{
			case CameraQuotaEditEto editEto:
				await ShowEditAsync(editEto.OrganizationId, editEto.QuotaId);
				break;

			case CameraQuotaCreateEto createEto:
				await ShowCreateAsync(createEto.OrganizationId);
				break;

			case CameraQuotaSelectEto selectEto:
				await ShowViewAsync(selectEto.OrganizationId, selectEto.QuotaId);
				break;

			default:
				await CloseAsync();
				return;
		}
	}

	private Task OnOpenChanged(bool opened) => InvokeAsync(() =>
	{
		_mode = DrawerMode.Hidden;
		_resourceId = null;
		StateHasChanged();
	});
	#endregion [Event Handlers]
}
