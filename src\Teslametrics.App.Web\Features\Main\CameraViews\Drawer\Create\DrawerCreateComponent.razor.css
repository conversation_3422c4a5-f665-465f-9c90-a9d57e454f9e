::deep .grid
{
	user-select: none;
	display: grid;
	gap: 8px;
	width: 100%;
	height: 100%;
}

::deep .GridCustom
{
	grid-template-rows: repeat(var(--rows), 1fr);
	grid-template-columns: repeat(var(--cols), 1fr);
}

::deep .Grid1Plus5
{
	grid-template-areas:
		"main main small1"
		"main main small2"
		"small3 small4 small5";
	grid-template-rows: repeat(3, 1fr);
	grid-template-columns: repeat(3, 1fr);
}

::deep .Grid1Plus5> :nth-child(1),
::deep .Grid1Plus7> :nth-child(1),
::deep .Grid1Plus12> :nth-child(1)
{
	grid-area: main;
}

::deep .Grid1Plus7
{
	grid-template-areas:
		"main main main small1"
		"main main main small2"
		"main main main small3"
		"small4 small5 small6 small7";
	grid-template-rows: repeat(4, 1fr);
	grid-template-columns: repeat(4, 1fr);
}

::deep .Grid1Plus12
{
	grid-template-areas:
		"main main small1 small2"
		"main main small3 small4"
		"small5 small6 small7 small8"
		"small9 small10 small11 small12";
	grid-template-rows: repeat(4, 1fr);
	grid-template-columns: repeat(4, 1fr);
}

::deep .Grid2Plus8
{
	grid-template-areas:
		"main1 main1 main2 main2"
		"main1 main1 main2 main2"
		"small1 small2 small3 small4"
		"small5 small6 small7 small8";
	grid-template-rows: repeat(4, 1fr);
	grid-template-columns: repeat(4, 1fr);
}

::deep .Grid2Plus8> :nth-child(1)
{
	grid-area: main1;
}

::deep .Grid2Plus8> :nth-child(2)
{
	grid-area: main2;
}

::deep .Grid3Plus4
{
	grid-template-areas:
		"main1 main1 main2 main2"
		"main1 main1 main2 main2"
		"main3 main3 small1 small2"
		"main3 main3 small3 small4";
	grid-template-rows: repeat(4, 1fr);
	grid-template-columns: repeat(4, 1fr);
}

::deep .Grid3Plus4> :nth-child(1)
{
	grid-area: main1;
}

::deep .Grid3Plus4> :nth-child(2)
{
	grid-area: main2;
}

::deep .Grid3Plus4> :nth-child(3)
{
	grid-area: main3;
}