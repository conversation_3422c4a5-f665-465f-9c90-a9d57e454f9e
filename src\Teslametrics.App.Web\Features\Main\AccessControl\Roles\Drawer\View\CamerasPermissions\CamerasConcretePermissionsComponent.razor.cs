using Dapper;
using Microsoft.AspNetCore.Components;
using MudBlazor;
using System.Reactive;
using Teslametrics.App.Web.Domain.AccessControl;
using Teslametrics.App.Web.Extensions;
using Teslametrics.Shared;


namespace Teslametrics.App.Web.Features.Main.AccessControl.Roles.Drawer.View.CamerasPermissions;

public partial class CamerasConcretePermissionsComponent
{
	#region [Type Declarations]
	private enum ItemType
	{
		Folder,
		Camera
	}

	private class TreeItemPresenter : TreeItemData<Guid>
	{
		public int CameraCount { get; set; }
		public override bool Expandable => Type != ItemType.Camera;
		public bool ReadOnly => Type == ItemType.Camera;
		public bool IsFolder => Type == ItemType.Folder;
		public bool IsCamera => Type == ItemType.Camera;
		public Guid ParentId { get; init; }
		public ItemType Type { get; init; }

		public TreeItemPresenter(Guid id, Guid parentId, string title, ItemType type) : base(id)
		{
			Text = title;
			ParentId = parentId;
			Type = type;
			Icon = type switch
			{
				ItemType.Folder => Icons.Material.Outlined.Folder,
				ItemType.Camera => Icons.Material.Outlined.Camera,
				_ => Icons.Material.Outlined.QuestionMark
			};
		}
	}
	#endregion [Type Declarations]

	private record InheritStatus(bool Inherited, IEnumerable<string> InheritedFrom);
	private bool _subscribing;
	private DateTime _lastRefreshTime = DateTime.Now;
	private string _searchString = string.Empty;
	private IEnumerable<AppPermissions.Main.Cameras> _cameraValues = [];
	private IEnumerable<AppPermissions.Main.Folders> _folderValues = [];

	private IEnumerable<ResourcePermission> _wildcardPermissions => Selected.Where(x => x.ResourceId.IsWildcard);

	private SubscribeTreeUseCase.Response? _subscriptionResult;
	private List<TreeItemPresenter> Items = [];

	[Parameter]
	public Guid RoleId { get; set; }

	[Parameter]
	public Guid OrganizationId { get; set; }

	[Parameter]
	[EditorRequired]
	public IEnumerable<ResourcePermission> Selected { get; set; } = [];

	[Parameter]
	public bool ShowAdminTaggedValues { get; set; }

	protected override async Task OnInitializedAsync()
	{
		await base.OnInitializedAsync();

		await FetchAsync();
		await SubscribeAsync();
	}

	protected override void OnParametersSet()
	{
		base.OnParametersSet();
		List<AppPermissions.Main.Cameras> cameraEnums = [];
		List<AppPermissions.Main.Folders> folderEnumValues = [];
		if (ShowAdminTaggedValues)
		{
			cameraEnums = ((AppPermissions.Main.Cameras[])Enum.GetValues(typeof(AppPermissions.Main.Cameras)))
				.Where(item => item != AppPermissions.Main.Cameras.Invalid && !_wildcardPermissions.Any(x => x.Permission.Value == item.GetEnumPermissionString()))
				.AsList();

			folderEnumValues = ((AppPermissions.Main.Folders[])Enum.GetValues(typeof(AppPermissions.Main.Folders)))
				.AsList();

			folderEnumValues = folderEnumValues.Where(item => item != AppPermissions.Main.Folders.Invalid && !_wildcardPermissions.Any(x => x.Permission.Value == item.GetEnumPermissionString()))
				.AsList();
		}
		else
		{
			cameraEnums = ((AppPermissions.Main.Cameras[])Enum.GetValues(typeof(AppPermissions.Main.Cameras)))
				.Where(item => item != AppPermissions.Main.Cameras.Invalid && !item.IsAdminTagged() && !_wildcardPermissions.Any(x => x.Permission.Value == item.GetEnumPermissionString()))
				.AsList();

			folderEnumValues = ((AppPermissions.Main.Folders[])Enum.GetValues(typeof(AppPermissions.Main.Folders)))
				.Where(item => item != AppPermissions.Main.Folders.Invalid && !item.IsAdminTagged() && !_wildcardPermissions.Any(x => x.Permission.Value == item.GetEnumPermissionString()))
				.AsList();
		}

		//cameraEnums.Remove(AppPermissions.Main.Cameras.Create);
		cameraEnums.Remove(AppPermissions.Main.Cameras.Invalid);
		folderEnumValues.Remove(AppPermissions.Main.Folders.Invalid);
		folderEnumValues.Remove(AppPermissions.Main.Folders.Create);

		_cameraValues = cameraEnums;
		_folderValues = folderEnumValues;
	}

	private async Task FetchAsync()
	{
		if (IsLoading) return;

		try
		{
			//if (Items.Count() == 0) await SetLoadingAsync(true);
			var response = await ScopeFactory.MediatorSend(new GetTreeUseCase.Query(RoleId));
			switch (response.Result)
			{
				case GetTreeUseCase.Result.Success:
					_lastRefreshTime = DateTime.Now;
					var buffer = ConvertResponseToTreeItemPresenters(response);
					TransferExpandedState(Items, buffer);
					Items.Clear();
					Items.AddRange(buffer);
					buffer.Clear();
					buffer = null;
					//await SubscribeAsync();
					break;
				case GetTreeUseCase.Result.ValidationError:
					Snackbar.Add("Не удалось получить список организаций. Повторите попытку", Severity.Error);
					break;
				case GetTreeUseCase.Result.Unknown:
				default:
					Snackbar.Add("Не удалось получить список организаций. Повторите попытку", Severity.Error);
					break;
			}
		}
		catch (Exception ex)
		{
			Snackbar.Add("Не удалось получить список организаций. Повторите попытку", Severity.Error);
			Logger.LogError(ex, ex.Message);
		}
		finally
		{
			await SetLoadingAsync(false);
		}
	}
	private async Task SubscribeAsync()
	{
		try
		{
			Unsubscribe();

			await SetSubscribingAsync(true);
			var orgIds = Items.Select(x => x.Value).ToList();
			_subscriptionResult = await ScopeFactory.MediatorSend(new SubscribeTreeUseCase.Request(Observer.Create<object>(OnAppEventHandler, OnError), OrganizationId));
			await SetSubscribingAsync(false);
			switch (_subscriptionResult.Result)
			{
				case SubscribeTreeUseCase.Result.Success:
					CompositeDisposable.Add(_subscriptionResult.Subscription!);
					break;
				case SubscribeTreeUseCase.Result.ValidationError:
					Snackbar.Add("Ошибка валидации при подписке на события", Severity.Error);
					break;
				case SubscribeTreeUseCase.Result.Unknown:
				default:
					throw new Exception($"Unexpected result in {nameof(SubscribeTreeUseCase)}: {_subscriptionResult.Result}");
			}
		}
		catch (Exception ex)
		{
			await SetSubscribingAsync(false);
			Snackbar.Add("Не удалось получить подписку на события дерева из-за непредвиденной ошибки. Повторите попытку", Severity.Error);
			Logger.LogError(ex, ex.Message);
		}
	}
	private void Unsubscribe()
	{
		if (_subscriptionResult?.Subscription is not null)
		{
			CompositeDisposable.Remove(_subscriptionResult.Subscription);
			_subscriptionResult.Subscription.Dispose();
		}
	}
	private Task SetSubscribingAsync(bool isLoading = true) => UpdateViewAsync(() =>
	{
		_subscribing = isLoading;
	});

	#region [Actions]
	private Task RefreshAsync() => FetchAsync();
	#endregion [Actions]

	#region [Event Hanndlers]
	private async void OnAppEventHandler(object appEvent)
	{
		await RefreshAsync();
		await UpdateViewAsync();
	}

	private void OnError(Exception exc)
	{
		Snackbar.Add("Ошибка при подписке на события", Severity.Error);
		Logger.LogError(exc, exc.Message);
	}
	#endregion [Event Handlers]

	#region [Casts]
	private static List<TreeItemPresenter> ConvertResponseToTreeItemPresenters(GetTreeUseCase.Response response) // Нужно, т.к. treeview от mudblazor`а привередливый и жрет только TreeItemData<TYPE>
	{
		var treeItems = new List<TreeItemPresenter>();

		foreach (var folder in response.Items)
		{
			ConvertFolderToTreeItems(folder, folder.Id, treeItems);
		}

		return treeItems;
	}

	private static void ConvertFolderToTreeItems(GetTreeUseCase.Response.Folder folder, Guid parentId, List<TreeItemPresenter> items)
	{
		// Добавляем папку
		var folderPresenter = new TreeItemPresenter(
			folder.Id,
			parentId,
			folder.Name,
			ItemType.Folder
		);

		folderPresenter.Children ??= [];

		items.Add(folderPresenter);

		// Обрабатываем камеры, вложенные в папку
		foreach (var camera in folder.Cameras)
		{
			var cameraPresenter = new TreeItemPresenter(
				camera.Id,
				folder.Id,
				camera.Name,
				ItemType.Camera
			);
			folderPresenter.Children.Add(cameraPresenter);
		}

		// Рекурсивно обрабатываем вложенные папки
		/*foreach (var subFolder in folder.Folders)
        {
            ConvertFolderToTreeItems(subFolder, folder.Id, treeItems);
        }*/
	}

	// Опять-таки проблема широкого списка против глубокого. Если элементов немного, то лучше оставить рекурсивный поиск. Если поменяется - добавить словарь.
	private static void TransferExpandedState<T>(List<T> sourceTree, List<T> targetTree) where T : TreeItemData<Guid>
	{
		foreach (var sourceElement in sourceTree)
		{
			if (!sourceElement.Expanded)
				continue;

			// Находим соответствующий элемент во втором дереве
			var targetElement = FindElementById(targetTree, sourceElement.Value);

			if (targetElement != null)
			{
				// Переносим значение Expanded
				targetElement.Expanded = sourceElement.Expanded;
			}

			// Рекурсивно обрабатываем вложенные элементы
			if (sourceElement.Children?.Count > 0 && targetElement?.Children?.Count > 0)
			{
				TransferExpandedState(sourceElement.Children, targetElement.Children);
			}
		}
	}

	private static TreeItemData<Guid>? FindElementById(IEnumerable<TreeItemData<Guid>> tree, Guid id)
	{
		foreach (var element in tree)
		{
			if (element.Value == id)
				return element;

			if (element.Children?.Count > 0)
			{
				var found = FindElementById(element.Children, id);
				if (found != null)
					return found;
			}
		}

		return null;
	}
	#endregion [Casts]

	private bool ContainsPermission(Enum value, Guid resourceId)
	{
		return Selected.Any(x => x.ResourceId.Value == resourceId && x.Permission.Value == value.GetEnumPermissionString());
	}
}