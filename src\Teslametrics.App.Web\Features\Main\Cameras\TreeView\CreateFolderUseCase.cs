using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Domain.AccessControl.Organizations;
using Teslametrics.App.Web.Domain.Folders;
using Teslametrics.App.Web.Services.Outbox;
using Teslametrics.App.Web.Services.TransactionManager;

namespace Teslametrics.App.Web.Features.Main.Cameras.TreeView;

public static class CreateFolderUseCase
{
    public record Command(Guid OrganizationId, /*Guid ParentId, */ string Name) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public Guid Id { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(Guid id)
        {
            Id = id;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Id = Guid.Empty;
            Result = result;
        }
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        OrganizationNotFound,
        FolderNameAlreadyExists
    }

    public class Validator : AbstractValidator<Command>
    {
        public Validator()
        {
            //RuleFor(c => c.ParentId).NotEmpty();
            RuleFor(c => c.Name).Length(3, 120);
        }
    }

    public class Handler : IRequestHandler<Command, Response>
    {
        private readonly IValidator<Command> _validator;
        private readonly IFolderRepository _folderRepository;
        private readonly IOrganizationRepository _organizationRepository;
        private readonly ITransactionManager _transactionManager;
        private readonly IPublisher _publisher;
        private readonly IOutbox _outbox;

        public Handler(IValidator<Command> validator,
                       IFolderRepository folderRepository,
                       IOrganizationRepository organizationRepository,
                       ITransactionManager transactionManager,
                       IPublisher publisher,
                       IOutbox outbox)
        {
            _validator = validator;
            _folderRepository = folderRepository;
            _organizationRepository = organizationRepository;
            _transactionManager = transactionManager;
            _publisher = publisher;
            _outbox = outbox;
        }

        public async Task<Response> Handle(Command request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            using var transaction = await _transactionManager.CreateTransactionAsync();

            if (!await _organizationRepository.IsOrganizationExistsAsync(request.OrganizationId, cancellationToken))
            {
                return new Response(Result.OrganizationNotFound);
            }

            if (await _folderRepository.IsFolderNameExistsAsync(request.Name, request.OrganizationId, cancellationToken))
            {
                return new Response(Result.FolderNameAlreadyExists);
            }

            var (folder, events) = FolderAggregate.Create(Guid.NewGuid(), request.OrganizationId, null, request.Name);

            await _folderRepository.AddAsync(folder, cancellationToken);
            await _folderRepository.SaveChangesAsync(cancellationToken);

            foreach (var @event in events)
            {
                await _publisher.Publish(@event, cancellationToken);
            }

            await _outbox.AddRangeAsync(events);

            await transaction.CommitAsync();

            return new Response(folder.Id);
        }
    }
}