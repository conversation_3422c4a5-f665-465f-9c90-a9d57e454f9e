<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<Nullable>enable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
		<UserSecretsId>aspnet-Teslametrics.App.Web-d286f7ce-cecf-4f9e-b370-aa095b0c7be2</UserSecretsId>
		<InvariantTimezone>true</InvariantTimezone>
	</PropertyGroup>
	<PropertyGroup>
		<StaticWebAssetBasePath>_content/$(PackageId)</StaticWebAssetBasePath>
	</PropertyGroup>

	<ItemGroup>
		<EmbeddedResource Include="wwwroot\**\*" />
		<None Include="entrypoint.sh" CopyToOutputDirectory="PreserveNewest" />
	</ItemGroup>

    <ItemGroup>
        <!-- В публикацию попадут ТОЛЬКО *.js, нужные для MSEPlayer`а -->
        <Content Include="Components\MSEPlayer\Core\**\*.js">
            <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
        </Content>
        <Content Include="Components\MSEPlayer\Plugins\**\*.js">
            <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
        </Content>
    </ItemGroup>

	<ItemGroup>
		<Folder Include="Components\Drawer\" />
		<Folder Include="Domain\Incidents\" />
		<Folder Include="Services\Cookies\" />
		<Folder Include="Services\Onvif\" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="CircularBuffer" Version="1.4.0" />
		<PackageReference Include="Dapper" Version="2.1.66" />
		<PackageReference Include="Dapper.SqlBuilder" Version="2.1.66" />
		<PackageReference Include="EFCore.NamingConventions" Version="9.0.0" />
		<PackageReference Include="MediatR" Version="12.5.0" />
		<PackageReference Include="Microsoft.Orleans.Client" Version="9.1.2" />
		<PackageReference Include="Microsoft.Orleans.Reminders" Version="9.1.2" />
		<PackageReference Include="Minio" Version="6.0.5" />
		<PackageReference Include="MudBlazor" Version="8.8.0" />
		<PackageReference Include="CodeBeam.MudBlazor.Extensions" Version="8.2.3" />
		<PackageReference Include="BlazorDownloadFileFast" Version="1.0.0.1" />
		<PackageReference Include="FluentValidation.DependencyInjectionExtensions" Version="12.0.0" />
		<PackageReference Include="Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore" Version="9.0.6" />
		<PackageReference Include="Microsoft.AspNetCore.SignalR.Protocols.MessagePack" Version="9.0.6" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.6">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.4" />
		<PackageReference Include="OpenTelemetry.Exporter.Console" Version="1.12.0" />
		<PackageReference Include="OpenTelemetry.Exporter.OpenTelemetryProtocol" Version="1.12.0" />
		<PackageReference Include="OpenTelemetry.Exporter.Prometheus.AspNetCore" Version="1.12.0-beta.1" />
		<PackageReference Include="OpenTelemetry.Extensions.Hosting" Version="1.12.0" />
		<PackageReference Include="OpenTelemetry.Instrumentation.AspNetCore" Version="1.12.0" />
		<PackageReference Include="OpenTelemetry.Instrumentation.Http" Version="1.12.0" />
		<PackageReference Include="Otp.NET" Version="1.4.0" />
		<PackageReference Include="Polly" Version="8.6.1" />
		<PackageReference Include="QRCoder" Version="1.6.0" />
		<PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
		<PackageReference Include="Serilog.Exceptions" Version="8.4.0" />
		<PackageReference Include="Serilog.Sinks.Graylog" Version="3.1.1" />
		<PackageReference Include="System.Reactive" Version="6.0.1" />
		<PackageReference Include="System.ServiceModel.Duplex" Version="6.0.0" />
		<PackageReference Include="System.ServiceModel.Http" Version="8.1.2" />
		<PackageReference Include="System.ServiceModel.NetTcp" Version="8.1.2" />
		<PackageReference Include="System.ServiceModel.Security" Version="6.0.0" />
		<PackageReference Include="Z.Blazor.Diagrams" Version="3.0.3" />
		<PackageReference Include="MQTTnet" Version="5.0.1.1416" />
	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\FFMpegNET\FFMpegNET.csproj" />
	  <ProjectReference Include="..\Orleans.LiveStreams\Orleans.LiveStreams.csproj" />
	  <ProjectReference Include="..\Orleans.Streams.Kafka\Orleans.Streams.Kafka.csproj" />
	  <ProjectReference Include="..\Teslametrics.MediaServer.Contracts\Teslametrics.MediaServer.Contracts.csproj" />
	  <ProjectReference Include="..\Teslametrics.Shared\Teslametrics.Shared.csproj" />
	</ItemGroup>

	<ItemGroup>
		<Compile Update="Locales\Features\Main\AccessControl\Permisssions\List\PermissionsListComponent.Designer.cs">
			<DesignTime>True</DesignTime>
			<AutoGen>True</AutoGen>
			<DependentUpon>PermissionsListComponent.resx</DependentUpon>
		</Compile>
		<Compile Update="Locales\Features\Main\AccessControl\Roles\Drawer\Create\CamerasPermissions\CamerasConcretePermissionsComponent.Designer.cs">
		  <DesignTime>True</DesignTime>
		  <AutoGen>True</AutoGen>
		  <DependentUpon>CamerasConcretePermissionsComponent.resx</DependentUpon>
		</Compile>
		<Compile Update="Locales\Features\Main\AccessControl\Roles\Drawer\Create\CameraViews\CameraViewsConcretePermissionsComponent.Designer.cs">
		  <DesignTime>True</DesignTime>
		  <AutoGen>True</AutoGen>
		  <DependentUpon>CameraViewsConcretePermissionsComponent.resx</DependentUpon>
		</Compile>
		<Compile Update="Locales\Features\Main\AccessControl\Roles\Drawer\Create\RoleCreateComponent.Designer.cs">
		  <DesignTime>True</DesignTime>
		  <AutoGen>True</AutoGen>
		  <DependentUpon>RoleCreateComponent.resx</DependentUpon>
		</Compile>
		<Compile Update="Locales\Features\Main\AccessControl\Roles\Drawer\View\WildcardPermissionComponents.Designer.cs">
			<DependentUpon>WildcardPermissionComponents.resx</DependentUpon>
			<DesignTime>True</DesignTime>
			<AutoGen>True</AutoGen>
		</Compile>
		<Compile Update="Locales\Features\Main\AccessControl\Roles\Drawer\Edit\WildcardPermissionComponents.Designer.cs">
			<DependentUpon>WildcardPermissionComponents.resx</DependentUpon>
			<DesignTime>True</DesignTime>
			<AutoGen>True</AutoGen>
		</Compile>
		<Compile Update="Locales\Features\Main\AccessControl\Roles\Drawer\Create\WildcardPermissionComponents.Designer.cs">
			<DesignTime>True</DesignTime>
			<AutoGen>True</AutoGen>
			<DependentUpon>WildcardPermissionComponents.resx</DependentUpon>
		</Compile>
	</ItemGroup>

	<ItemGroup>
		<EmbeddedResource Update="Locales\Features\Main\AccessControl\Permisssions\List\PermissionsListComponent.en-US.resx">
			<Generator>PublicResXFileCodeGenerator</Generator>
		</EmbeddedResource>
		<EmbeddedResource Update="Locales\Features\Main\AccessControl\Permisssions\List\PermissionsListComponent.resx">
			<Generator>PublicResXFileCodeGenerator</Generator>
			<LastGenOutput>PermissionsListComponent.Designer.cs</LastGenOutput>
		</EmbeddedResource>
		<EmbeddedResource Update="Locales\Features\Main\AccessControl\Permisssions\List\PermissionsListComponent.ru-Ru.resx">
			<Generator>PublicResXFileCodeGenerator</Generator>
		</EmbeddedResource>
		<EmbeddedResource Update="Locales\Features\Main\AccessControl\Roles\Drawer\Create\CamerasPermissions\CamerasConcretePermissionsComponent.resx">
		  <Generator>PublicResXFileCodeGenerator</Generator>
		  <LastGenOutput>CamerasConcretePermissionsComponent.Designer.cs</LastGenOutput>
		</EmbeddedResource>
		<EmbeddedResource Update="Locales\Features\Main\AccessControl\Roles\Drawer\Create\CameraViews\CameraViewsConcretePermissionsComponent.resx">
		  <Generator>PublicResXFileCodeGenerator</Generator>
		  <LastGenOutput>CameraViewsConcretePermissionsComponent.Designer.cs</LastGenOutput>
		</EmbeddedResource>
		<EmbeddedResource Update="Locales\Features\Main\AccessControl\Roles\Drawer\Create\RoleCreateComponent.resx">
		  <Generator>PublicResXFileCodeGenerator</Generator>
		  <LastGenOutput>RoleCreateComponent.Designer.cs</LastGenOutput>
		</EmbeddedResource>
		<EmbeddedResource Update="Locales\Features\Main\AccessControl\Roles\Drawer\Create\RoleCreateComponent.en-US.resx">
		  <Generator>PublicResXFileCodeGenerator</Generator>
		</EmbeddedResource>
		<EmbeddedResource Update="Locales\Features\Main\AccessControl\Roles\Drawer\Create\RoleCreateComponent.ru-RU.resx">
		  <Generator>PublicResXFileCodeGenerator</Generator>
		</EmbeddedResource>
		<EmbeddedResource Update="Locales\Features\Main\AccessControl\Roles\Drawer\View\WildcardPermissionComponents.en-US.resx">
			<Generator>PublicResXFileCodeGenerator</Generator>
		</EmbeddedResource>
		<EmbeddedResource Update="Locales\Features\Main\AccessControl\Roles\Drawer\View\WildcardPermissionComponents.resx">
			<LastGenOutput>WildcardPermissionComponents.Designer.cs</LastGenOutput>
			<Generator>PublicResXFileCodeGenerator</Generator>
		</EmbeddedResource>
		<EmbeddedResource Update="Locales\Features\Main\AccessControl\Roles\Drawer\View\WildcardPermissionComponents.ru-RU.resx">
			<Generator>PublicResXFileCodeGenerator</Generator>
		</EmbeddedResource>
		<EmbeddedResource Update="Locales\Features\Main\AccessControl\Roles\Drawer\Edit\WildcardPermissionComponents.en-US.resx">
			<Generator>PublicResXFileCodeGenerator</Generator>
		</EmbeddedResource>
		<EmbeddedResource Update="Locales\Features\Main\AccessControl\Roles\Drawer\Edit\WildcardPermissionComponents.resx">
			<LastGenOutput>WildcardPermissionComponents.Designer.cs</LastGenOutput>
			<Generator>PublicResXFileCodeGenerator</Generator>
		</EmbeddedResource>
		<EmbeddedResource Update="Locales\Features\Main\AccessControl\Roles\Drawer\Edit\WildcardPermissionComponents.ru-RU.resx">
			<Generator>PublicResXFileCodeGenerator</Generator>
		</EmbeddedResource>
		<EmbeddedResource Update="Locales\Features\Main\AccessControl\Roles\Drawer\Create\WildcardPermissionComponents.en-US.resx">
			<Generator>PublicResXFileCodeGenerator</Generator>
		</EmbeddedResource>
		<EmbeddedResource Update="Locales\Features\Main\AccessControl\Roles\Drawer\Create\WildcardPermissionComponents.resx">
			<Generator>PublicResXFileCodeGenerator</Generator>
			<LastGenOutput>WildcardPermissionComponents.Designer.cs</LastGenOutput>
		</EmbeddedResource>
		<EmbeddedResource Update="Locales\Features\Main\AccessControl\Roles\Drawer\Create\WildcardPermissionComponents.ru-RU.resx">
			<Generator>PublicResXFileCodeGenerator</Generator>
		</EmbeddedResource>
		<EmbeddedResource Update="wwwroot\**\*">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</EmbeddedResource>
		<EmbeddedResource Update="Locales\Features\Main\AccessControl\Roles\Drawer\View\CameraViews\CameraViewsConcretePermissionsComponent.resx">
			<Generator>PublicResXFileCodeGenerator</Generator>
		</EmbeddedResource>
		<EmbeddedResource Update="Locales\Features\Main\AccessControl\Roles\Drawer\View\CameraViews\CameraViewsConcretePermissionsComponent.en-US.resx">
			<Generator>PublicResXFileCodeGenerator</Generator>
		</EmbeddedResource>
		<EmbeddedResource Update="Locales\Features\Main\AccessControl\Roles\Drawer\View\CameraViews\CameraViewsConcretePermissionsComponent.ru-RU.resx">
			<Generator>PublicResXFileCodeGenerator</Generator>
		</EmbeddedResource>
	</ItemGroup>

	<ItemGroup>
	  <Content Update="Features\Main\AccessControl\Roles\DeleteDialog\LoadingComponent.razor">
	    <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
	  </Content>
	  <Content Update="Features\Main\AccessControl\Roles\DeleteDialog\NotFoundComponent.razor">
	    <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
	  </Content>
	</ItemGroup>

</Project>
