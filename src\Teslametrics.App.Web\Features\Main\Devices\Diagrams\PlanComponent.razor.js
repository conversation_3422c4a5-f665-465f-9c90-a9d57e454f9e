// Store panzoom instances
const panzoomInstances = new Map();

/**
 * Initialize panzoom for the plan container
 * @param {string} containerId - ID of the container element
 * @param {string} contentId - ID of the content element to be panned and zoomed
 */
export function initializePanZoom(containerId, contentId) {
    // Check if panzoom is already initialized for this container
    if (panzoomInstances.has(containerId)) {
        return;
    }

    // Load panzoom script
    const script = document.createElement('script');
    script.src = 'https://cdn.jsdelivr.net/npm/panzoom@9.4.0/dist/panzoom.min.js';
    script.async = true;

    script.onload = () => {
        const container = document.getElementById(containerId);
        const content = document.getElementById(contentId);

        if (!container || !content) {
            console.error('Container or content element not found');
            return;
        }

        // Initialize panzoom
        const instance = window.panzoom(content, {
            maxZoom: 4,
            minZoom: 0.1,
            zoomDoubleClickSpeed: 1,
            bounds: false, // Отключаем ограничение границ для свободного перемещения
            // Prevent panzoom from capturing events on elements with specific classes
        });

        // Store the instance
        panzoomInstances.set(containerId, instance);

        // Add double-click to reset handler
        container.addEventListener('dblclick', (e) => {
            // Only reset if the target is the container or content
            if (e.target === container || e.target === content) {
                resetZoom(containerId);
            }
        });

        // Центрируем план при первой загрузке
        centerContent(containerId);

        // Добавляем обработчик изменения размера окна
        window.addEventListener('resize', () => {
            // Обновляем размеры при изменении размера окна
            instance.resume();
            centerContent(containerId);
        });
    };

    script.onerror = (error) => {
        console.error('Failed to load panzoom:', error);
    };

    document.head.appendChild(script);
}

/**
 * Center content within container
 * @param {string} containerId - ID of the container element
 */
function centerContent(containerId) {
    const instance = panzoomInstances.get(containerId);
    const container = document.getElementById(containerId);
    const content = document.getElementById(containerId.replace('container', 'content'));

    if (!instance || !container || !content) return;

    // Дадим время для рендеринга содержимого
    setTimeout(() => {
        const containerRect = container.getBoundingClientRect();
        const contentRect = content.getBoundingClientRect();

        // Если контент меньше контейнера, центрируем его
        if (contentRect.width < containerRect.width || contentRect.height < containerRect.height) {
            const x = (containerRect.width - contentRect.width) / 2;
            const y = (containerRect.height - contentRect.height) / 2;
            instance.moveTo(x, y);
        }
    }, 100);
}

/**
 * Reset zoom and pan to default
 * @param {string} containerId - ID of the container element
 */
export function resetZoom(containerId) {
    const instance = panzoomInstances.get(containerId);
    const container = document.getElementById(containerId);
    const content = container.querySelector('.plan-content');

    if (!instance || !container || !content) return;

    const containerRect = container.getBoundingClientRect();
    const transform = instance.getTransform();

    // Рассчитаем центрирование
    const contentWidth = content.offsetWidth;
    const contentHeight = content.offsetHeight;

    const targetScale = 1;

    const targetX = (containerRect.width - contentWidth * targetScale) / 2;
    const targetY = (containerRect.height - contentHeight * targetScale) / 2;

    const start = {
        scale: transform.scale,
        x: transform.x,
        y: transform.y,
    };

    const end = {
        scale: targetScale,
        x: targetX,
        y: targetY,
    };

    smoothZoomAndMove(instance, start, end, 400);  // 400ms как в zoomToElement
    instance.resume();
}

function smoothZoomAndMove(instance, start, end, duration = 400) {
    const startTime = performance.now();

    function animate(time) {
        const elapsed = time - startTime;
        const progress = Math.min(elapsed / duration, 1);
        const currentScale = start.scale + (end.scale - start.scale) * progress;
        const currentX = start.x + (end.x - start.x) * progress;
        const currentY = start.y + (end.y - start.y) * progress;

        instance.zoomAbs(0, 0, currentScale);
        instance.moveTo(currentX, currentY);

        if (progress < 1) {
            requestAnimationFrame(animate);
        }
    }

    requestAnimationFrame(animate);
}

export function zoomToElement(containerId, elementId) {
    const instance = panzoomInstances.get(containerId);
    const container = document.getElementById(containerId);
    const content = container.querySelector('.plan-content');
    const element = document.querySelector(`[data-id="${elementId}"]`);

    if (!instance || !container || !content || !element) {
        console.error('Instance, container, content, or element not found');
        return;
    }

    const containerRect = container.getBoundingClientRect();
    const transform = instance.getTransform();

    const elementWidth = element.offsetWidth;
    const elementHeight = element.offsetHeight;
    const elementLeft = element.offsetLeft;
    const elementTop = element.offsetTop;

    const padding = 16;

    // Рассчитаем масштаб, чтобы элемент с padding влез в контейнер
    const zoomX = (containerRect.width - padding * 2) / elementWidth;
    const zoomY = (containerRect.height - padding * 2) / elementHeight;
    const targetScale = Math.min(zoomX, zoomY, 4);  // maxZoom = 4

    // Центр элемента внутри content
    const elementCenterX = elementLeft + elementWidth / 2;
    const elementCenterY = elementTop + elementHeight / 2;

    // Куда сдвинуть, чтобы элемент оказался по центру контейнера
    const scaledElementCenterX = elementCenterX * targetScale;
    const scaledElementCenterY = elementCenterY * targetScale;

    const newX = (containerRect.width / 2) - scaledElementCenterX;
    const newY = (containerRect.height / 2) - scaledElementCenterY;

    const start = {
        scale: transform.scale,
        x: transform.x,
        y: transform.y,
    };

    const end = {
        scale: targetScale,
        x: newX,
        y: newY,
    };

    // Остановим анимации и плавно перейдём
    // instance.pause();

    smoothZoomAndMove(instance, start, end, 400);
    instance.pause();
}

/**
 * Unlock panning and zooming
 * @param {string} containerId - ID of the container element
 */
export function unlockPanZoom(containerId) {
    const instance = panzoomInstances.get(containerId);

    if (!instance) {
        console.error('Instance not found');
        return;
    }

    instance.resume();
}

/**
 * Dispose panzoom instance
 * @param {string} containerId - ID of the container element
 */
export function disposePanZoom(containerId) {
    const instance = panzoomInstances.get(containerId);

    if (instance) {
        // Remove event listeners
        const container = document.getElementById(containerId);
        if (container) {
            // Удаляем инструкции
            const instructions = container.querySelector('.plan-instructions');
            if (instructions) {
                container.removeChild(instructions);
            }
        }

        // Удаляем обработчик изменения размера окна
        window.removeEventListener('resize', () => { });

        // Dispose panzoom instance
        instance.dispose();

        // Remove from map
        panzoomInstances.delete(containerId);
    }
}
