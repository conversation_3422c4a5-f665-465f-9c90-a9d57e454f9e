<?xml version="1.0" encoding="utf-8"?>
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="System.Reactive.Linq" Version="6.0.1" />
    <PackageReference Include="System.ServiceModel.Duplex" Version="6.0.0" />
    <PackageReference Include="System.ServiceModel.Http" Version="8.1.2" />
    <PackageReference Include="System.ServiceModel.NetTcp" Version="8.1.2" />
    <PackageReference Include="System.ServiceModel.Security" Version="6.0.0" />
    <PackageReference Include="System.ServiceModel.Federation" Version="8.1.2" />
  </ItemGroup>
</Project>