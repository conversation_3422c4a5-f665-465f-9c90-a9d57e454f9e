@using Teslametrics.Shared
@inherits InteractiveBaseComponent
<MudDialog ActionsClass="mx-2" ContentClass="pt-8 pb-12" Visible="_isVisible" VisibleChanged="VisibilityChanged" Options="_dialogOptions">
	<TitleContent>
		<MudStack Row=true>
			<MudIcon Icon="@Icons.Material.Filled.Key" Class="mt-1" />
			<MudStack Spacing="0">
				<MudText Typo="Typo.h6">Смена пароля пользователю</MudText>
				<MudText Typo="Typo.subtitle2">@_user?.Username</MudText>
			</MudStack>
		</MudStack>
	</TitleContent>
	<DialogContent>
		<MudStack AlignItems="AlignItems.Center" Justify="Justify.Center" Spacing="0" Class="mud-height-full">
			<MudIcon Icon="@Icons.Material.Outlined.Key" Color="Color.Warning" Style="font-size: 8rem;" Class="mb-2" />
			<MudText Typo="Typo.subtitle1" Color="Color.Warning">Сменить пароль пользователю <b>@_user?.Username</b></MudText>
			<MudText Typo="Typo.body2">@(_user?.LastLoginTime is null ? "Пользователь не входил в систему" : "Последний вход: " + _user.LastLoginTime.Value.ToLocalTime())</MudText>
			<MudGrid Class="mt-6" Justify="Justify.Center">
				<MudItem xs="12" md="8" Class="pa-0">
					<MudForm @ref="_formRef" Model="_model" Validation="Validator.ValidateValue" @bind-IsValid="_isValid" Class="flex-1" OverrideFieldValidation="true">
						<MudStack Spacing="6">
							<PasswordFieldComponent Value="@_model.Password"
													ValueChanged="OnPwdChange"
													For="@(() => _model.Password)"
													Validation="Validator.ValidateValue"
													Label="Пароль"
													Required="true"
													RequiredError="Поле должно быть заполнено"
													Immediate="true"
													FullWidth="true"
													@ref="pwdFieldRef" />

							<PasswordFieldComponent Value="@_model.PasswordConfirm"
													ValueChanged="OnPwdConfirmChange"
													For="@(() => _model.PasswordConfirm)"
													Validation="Validator.ValidateValue"
													Label="Подтверждение пароля"
													RequiredError="Поле должно быть заполнено"
													Required="true"
													Immediate="true"
													FullWidth="true"
													@ref="pwdConfirmFieldRef" />
						</MudStack>
					</MudForm>
				</MudItem>
			</MudGrid>
		</MudStack>
	</DialogContent>
	<DialogActions>
		<MudButton OnClick="Cancel">Отменить</MudButton>
		@if(_user is not null)
		{
			<AuthorizeView Policy="@AppPermissions.Main.AccessControl.Users.ForceChangePassword.GetEnumPermissionString()" Resource="@(new PolicyRequirementResource(organizationId: null, resourceId: _user.Id))" Context="innerContext">
				<MudButton OnClick="SubmitAsync" Disabled="@(!_isValid)" Color="Color.Warning">Подвердить</MudButton>
			</AuthorizeView>
		}
	</DialogActions>
</MudDialog>