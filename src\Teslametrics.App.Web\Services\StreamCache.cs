using System.Collections.Concurrent;
using CircularBuffer;
using Teslametrics.App.Web.Orleans.Camera;

namespace Teslametrics.App.Web.Services;

public class StreamCache
{
    private int _nextIndex;
    private readonly ConcurrentDictionary<(Guid, StreamType), int> _index;

    private readonly ConcurrentDictionary<int, VideoCache> _streams;

    private readonly ConcurrentDictionary<int, byte[]> _previewImages;

    // Для отслеживания используемых индексов
    private readonly ConcurrentDictionary<int, bool> _activeStreams;

    private readonly object _lock;
    private bool _disposed;

    public StreamCache()
    {
        _lock = new object();
        _index = new ConcurrentDictionary<(Guid, StreamType), int>();
        _streams = new ConcurrentDictionary<int, VideoCache>();
        _previewImages = new ConcurrentDictionary<int, byte[]>();
        _activeStreams = new ConcurrentDictionary<int, bool>();
    }

    public int AddStream(Guid cameraId, StreamType streamType)
    {
        ObjectDisposedException.ThrowIf(_disposed, nameof(StreamCache));

        lock (_lock)
        {
            var index = _nextIndex++;
            _streams.TryAdd(index, new VideoCache(6));
            _activeStreams.TryAdd(index, true);

            if (streamType.HasFlag(StreamType.Archive))
            {
                _index[(cameraId, StreamType.Archive)] = index;
            }

            if (streamType.HasFlag(StreamType.View))
            {
                _index[(cameraId, StreamType.View)] = index;
            }

            if (streamType.HasFlag(StreamType.Public))
            {
                _index[(cameraId, StreamType.Public)] = index;
            }

            return index;
        }
    }

    public void AddSegment(int streamIndex, byte[] data, DateTimeOffset startTime, DateTimeOffset endTime)
    {
        var buffer = _streams[streamIndex];
        buffer.Push(new StreamSegment(buffer.Serial, data, startTime, endTime));
    }

    public void UpdatePreviewImage(int streamIndex, byte[] data)
    {
        _previewImages[streamIndex] = data;
    }

    public byte[] GetSegment(Guid cameraId, StreamType streamType, int index)
    {
        if (_index.TryGetValue((cameraId, streamType), out var streamIndex))
        {
            if (_streams.TryGetValue(streamIndex, out var buffer))
            {
                return buffer.CircularBuffer.First(s => s.Serial == index).Data;
            }
        }

        throw new Exception();
    }

    public byte[] GetPreviewImage(Guid cameraId, StreamType streamType)
    {
        if (_index.TryGetValue((cameraId, streamType), out var streamIndex))
        {
            if (_previewImages.TryGetValue(streamIndex, out var data))
            {
                return data;
            }
        }

        throw new Exception();
    }

    public List<(int Index, DateTimeOffset StartTime, DateTimeOffset EndTime)> GetTwoLastIndices(Guid cameraId, StreamType streamType)
    {
        var result = new List<(int Index, DateTimeOffset StartTime, DateTimeOffset EndTime)>();

        if (_index.TryGetValue((cameraId, streamType), out var streamIndex))
        {
            if (_streams.TryGetValue(streamIndex, out var buffer))
            {
                if (buffer.CircularBuffer.Size > 1)
                {
                    for (int i = buffer.CircularBuffer.Size - 2; i < buffer.CircularBuffer.Size; i++)
                    {
                        result.Add((buffer.CircularBuffer[i].Serial, buffer.CircularBuffer[i].StartTime, buffer.CircularBuffer[i].EndTime));
                    }
                }

                return result;
            }
        }

        throw new Exception();
    }

    public void Clear(Guid cameraId, StreamType streamType)
    {
        ObjectDisposedException.ThrowIf(_disposed, nameof(StreamCache));

        if (streamType.HasFlag(StreamType.Archive) && _index.Remove((cameraId, StreamType.Archive), out var index))
        {
            _streams.Remove(index, out var videoCache);
            _previewImages.Remove(index, out _);
            _activeStreams.Remove(index, out _);
            videoCache?.Dispose();
        }

        if (streamType.HasFlag(StreamType.View) && _index.Remove((cameraId, StreamType.View), out index))
        {
            _streams.Remove(index, out var videoCache);
            _previewImages.Remove(index, out _);
            _activeStreams.Remove(index, out _);
            videoCache?.Dispose();
        }

        if (streamType.HasFlag(StreamType.Public) && _index.Remove((cameraId, StreamType.Public), out index))
        {
            _streams.Remove(index, out var videoCache);
            _previewImages.Remove(index, out _);
            _activeStreams.Remove(index, out _);
            videoCache?.Dispose();
        }
    }

    public void Dispose()
    {
        if (_disposed) return;

        _disposed = true;

        // Очищаем все кэши
        foreach (var kvp in _streams)
        {
            kvp.Value?.Dispose();
        }

        _streams.Clear();
        _previewImages.Clear();
        _index.Clear();
        _activeStreams.Clear();
    }

    private record StreamSegment(int Serial, byte[] Data, DateTimeOffset StartTime, DateTimeOffset EndTime);

    private class VideoCache : IDisposable
    {
        public int Serial { get; private set; }

        public CircularBuffer<StreamSegment> CircularBuffer { get; private set; }

        private bool _disposed;

        public VideoCache(int capacity)
        {
            CircularBuffer = new CircularBuffer<StreamSegment>(capacity);
        }

        public void Push(StreamSegment segment)
        {
            ObjectDisposedException.ThrowIf(_disposed, nameof(VideoCache));
            Serial++;
            CircularBuffer.PushBack(segment);
        }

        public void Dispose()
        {
            if (_disposed) return;
            _disposed = true;

            // Очищаем буфер
            CircularBuffer?.Clear();
        }
    }
}