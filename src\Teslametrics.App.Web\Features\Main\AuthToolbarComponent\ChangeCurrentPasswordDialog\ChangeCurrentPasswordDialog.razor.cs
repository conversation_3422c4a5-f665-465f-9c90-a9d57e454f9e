using FluentValidation;
using MediatR;
using Microsoft.AspNetCore.Components.Authorization;
using MudBlazor;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Components.Form;
using Teslametrics.App.Web.Events.Users;
using Teslametrics.App.Web.Extensions;
using Severity = MudBlazor.Severity;

namespace Teslametrics.App.Web.Features.Main.AuthToolbarComponent.ChangeCurrentPasswordDialog;

public partial class ChangeCurrentPasswordDialog
{
	private bool _isValid;
	private class CurrentUserPasswordChangeDto
	{
		public string Password { get; set; } = string.Empty;
		public string NewPassword { get; set; } = string.Empty;
		public string NewPasswordConfirmation { get; set; } = string.Empty;
	}
	private class PWDChangeValidator : BaseFluentValidator<CurrentUserPasswordChangeDto>
	{
		public PWDChangeValidator()
		{
			RuleFor(model => model.Password)
				.NotEmpty().WithMessage("Поле должно быть заполнено")
				.MinimumLength(3).WithMessage("Пароль должен содержать ботльше трёх символов");

			When(model => !string.IsNullOrWhiteSpace(model.Password) && !string.IsNullOrWhiteSpace(model.NewPassword), () =>
			{
				RuleFor(model => model.Password).NotEqual(model => model.NewPassword).WithMessage("Новый пароль не может совпадать со старым");
				RuleFor(model => model.NewPassword).NotEqual(model => model.Password).WithMessage("Новый пароль не может совпадать со старым");
			});

			When(model => !string.IsNullOrWhiteSpace(model.NewPassword) && !string.IsNullOrWhiteSpace(model.NewPasswordConfirmation), () =>
			{
				RuleFor(model => model.NewPassword).Equal(model => model.NewPasswordConfirmation).WithMessage("Пароли не совпадают");
				RuleFor(model => model.NewPasswordConfirmation).Equal(model => model.NewPassword).WithMessage("Пароли не совпадают");
			});
		}
	}

	private DialogOptions _dialogOptions = new() { CloseOnEscapeKey = true, FullWidth = true, MaxWidth = MaxWidth.Small, CloseButton = true };
	private bool _isVisible;
	private MudForm? _formRef;
	private PasswordFieldComponent? pwdFieldRef;
	private PasswordFieldComponent? pwdConfirmFieldRef;
	private PWDChangeValidator Validator = new PWDChangeValidator();
	private CurrentUserPasswordChangeDto _model = new();

	protected override void OnInitialized()
	{
		CompositeDisposable.Add(EventSystem.Subscribe<ChangeCurrentUserPassword>(OnChangePwdHandler));
		base.OnInitialized();
	}


	#region [Actions]
	private async Task SubmitAsync()
	{
		if (IsLoading) return;

		try
		{
			await SetLoadingAsync();
			var authenticationState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
			if (!authenticationState.User.Identity?.IsAuthenticated ?? true)
			{
				Snackbar.Add("Пользователь не аутентифицирован");
				await CancelAsync();
				return;
			}

			Guid userId = authenticationState.User.GetUserId()!.Value;
			var result = await ScopeFactory.CreateScope().ServiceProvider.GetRequiredService<IMediator>().Send(new ChangeCurrentUserPasswordUseCase.Command(userId, _model.Password, _model.NewPassword));
			if (result.IsSuccess)
			{
				Snackbar.Add("Пароль успешно изменён", Severity.Success);
				await CancelAsync();
				return;
			}

			switch (result.Result)
			{
				case ChangeCurrentUserPasswordUseCase.Result.ValidationError:
					Snackbar.Add("Ошибка валидации данных", Severity.Error);
					break;
				case ChangeCurrentUserPasswordUseCase.Result.NewPasswordEqualsOld:
					await pwdFieldRef.SetErrorAsync("Новый пароль не может совпадать со старым");
					break;
				case ChangeCurrentUserPasswordUseCase.Result.UserNotFound:
					Snackbar.Add("Пользователь не найден", Severity.Error);
					break;
				case ChangeCurrentUserPasswordUseCase.Result.Unknown:
				default:
					Snackbar.Add("Не удалось изменить пароль из-за непредвиденной ошибки:" + result.Result.ToString(), Severity.Error);
					break;
			}
		}
		catch (Exception ex)
		{
			Snackbar.Add("Произошла ошибка при смене пароля", Severity.Error);
			Logger.LogError(ex, ex.Message);
		}
		finally
		{
			await SetLoadingAsync(false);
		}
	}
	private Task CancelAsync() => UpdateViewAsync(() =>
	{
		_isVisible = false;
		StateHasChanged();
	});
	#endregion

	#region [Event Handlers]
	private void VisibilityChanged(bool isVisible)
	{
		_isVisible = isVisible;
	}

	private async Task OnPwdChange(string pwd)
	{
		_model.NewPassword = pwd;
		if (!string.IsNullOrWhiteSpace(_model.NewPasswordConfirmation))
		{
			pwdConfirmFieldRef!.ResetValidation();
		}
		if (_model.NewPasswordConfirmation == _model.Password)
		{
			await _formRef!.Validate();
		}
	}

	private async Task OnPwdConfirmChange(string pwd)
	{
		_model.NewPasswordConfirmation = pwd;
		if (!string.IsNullOrWhiteSpace(_model.NewPassword))
		{
			pwdFieldRef!.ResetValidation();
		}
		if (_model.NewPasswordConfirmation == _model.NewPassword)
		{
			await _formRef!.Validate();
		}
	}

	private Task OnChangePwdHandler() => UpdateViewAsync(() =>
		{
			_isVisible = true;
			_model.Password = string.Empty;
			_model.NewPassword = string.Empty;
			_model.NewPasswordConfirmation = string.Empty;
			StateHasChanged();
		});
	#endregion
}
