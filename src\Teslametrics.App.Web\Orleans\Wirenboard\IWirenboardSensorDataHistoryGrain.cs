namespace Teslametrics.App.Web.Orleans.Wirenboard;

[<PERSON><PERSON>("Teslametrics.App.Web.Orleans.IWirenboardSensorDataHistoryGrain")]
public interface IWirenboardSensorDataHistoryGrain : IGrainWithGuidKey
{
    /// <summary>
    /// Запускает сбор истории для указанного топика
    /// </summary>
    /// <param name="request">Запрос на запуск сбора истории</param>
    [<PERSON><PERSON>("StartAsync")]
    Task StartAsync(StartRequest request);

    /// <summary>
    /// Останавливает сбор истории
    /// </summary>
    [<PERSON><PERSON>("StopAsync")]
    Task StopAsync();

    /// <summary>
    /// Метод для поддержания активности грейна, предотвращающий его деактивацию
    /// </summary>
    /// <returns>Задача, представляющая асинхронную операцию</returns>
    [Alias("PingAsync")]
    Task PingAsync();

    [GenerateSerializer]
    [Alias("Teslametrics.App.Web.Orleans.StartRequest")]
    public record StartRequest(string BrokerAddress, int Port, HistoryTopicInfo TopicInfo);
}