using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Services;
using Teslametrics.App.Web.Shared;

namespace Teslametrics.App.Web.Features.Main.Presets.Drawer.Create;

public static class GetCalculatedBitrateUseCase
{
	public record Query(Resolution Resolution, VideoCodec VideoCodec, FrameRate FrameRate, SceneDynamic SceneDynamic, AudioCodec AudioCodec) : BaseRequest<Response>;

	public record Response : BaseResponse
	{
		public double Bitrate { get; init; }

		public Result Result { get; init; }

		public bool IsSuccess => Result == Result.Success;

		public Response(double bitrate)
		{
			Bitrate = bitrate;
			Result = Result.Success;
		}

		public Response(Result result)
		{
			if (result == Result.Success)
			{
				throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
			}

			Result = result;

			Bitrate = 0;
		}
	}

	public enum Result
	{
		Unknown = 0,
		Success,
		ValidationError
	}

	public class Validator : AbstractValidator<Query>
	{
		public Validator()
		{
		}
	}

	public class Handler : IRequestHandler<Query, Response>
	{
		private readonly IValidator<Query> _validator;

		public Handler(IValidator<Query> validator)
		{
			_validator = validator;
		}

		public Task<Response> Handle(Query request, CancellationToken cancellationToken)
		{
			if (!_validator.Validate(request).IsValid)
			{
				return Task.FromResult(new Response(Result.ValidationError));
			}

			var bitrate = BitrateCalculator.CalculateBitrate(request.Resolution, request.VideoCodec, request.FrameRate, request.SceneDynamic, request.AudioCodec);

			return Task.FromResult(new Response(bitrate));
		}
	}
}