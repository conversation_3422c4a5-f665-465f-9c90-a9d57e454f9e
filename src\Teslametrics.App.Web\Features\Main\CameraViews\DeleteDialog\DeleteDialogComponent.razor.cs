using System.Reactive;
using Microsoft.AspNetCore.Components.Authorization;
using Teslametrics.App.Web.Events.CameraView;
using Teslametrics.App.Web.Extensions;
using Teslametrics.App.Web.Services.Authorization;
using Teslametrics.App.Web.Shared;

namespace Teslametrics.App.Web.Features.Main.CameraViews.DeleteDialog;

public partial class DeleteDialogComponent
{
    private bool _disposedValue;

    private bool _subscribing;
    private MudBlazor.DialogOptions _dialogOptions = new() { CloseOnEscapeKey = true, FullWidth = true, MaxWidth = MudBlazor.MaxWidth.Medium, NoHeader = true };
    private bool _isVisible;
    private Guid _organizationId;
    private Guid _viewId;

    private SubscribeViewUseCase.Response? _subscriptionResult;
    private GetViewUseCase.Response? _viewResponse;
    protected override void Dispose(bool disposing)
    {
        if (!_disposedValue)
        {
            if (disposing)
            {
                AuthenticationStateProvider.AuthenticationStateChanged -= OnAuthenticationStateChanged;
            }

            _disposedValue = true;
        }

        base.Dispose(disposing);
    }

    protected override void OnInitialized()
    {
        base.OnInitialized();

        CompositeDisposable.Add(EventSystem.Subscribe<CameraViewDeleteEto>(OnDeleteHandler));
    }

    private async Task FetchAsync()
    {
        try
        {
            await SetLoadingAsync(true);
            _viewResponse = await ScopeFactory.MediatorSend(new GetViewUseCase.Query(_viewId));
            await SetLoadingAsync(false);
            switch (_viewResponse.Result)
            {
                case GetViewUseCase.Result.Success:
                    await SubscribeAsync();
                    break;
                case GetViewUseCase.Result.ViewNotFound:
                    Snackbar.Add("Не удалось получить вид. Возможно вид уже удалён.", MudBlazor.Severity.Warning);
                    Cancel();
                    break;
                case GetViewUseCase.Result.ValidationError:
                    Snackbar.Add("Не удалось получить вид из-за ошибки валидации.", MudBlazor.Severity.Error);
                    Cancel();
                    break;
                case GetViewUseCase.Result.Unknown:
                default:
                    throw new Exception($"Unexpected result in {nameof(GetViewUseCase)}: {_viewResponse.Result}");
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, ex.Message);
            Snackbar.Add("Не удалось получить удаляемую камеру.", MudBlazor.Severity.Error);
        }
        finally
        {
            await SetLoadingAsync(false);
        }
    }
    private async Task SubscribeAsync()
    {
        try
        {
            Unsubscribe();

            await SetSubscribingAsync(true);
            _subscriptionResult = await ScopeFactory.MediatorSend(new SubscribeViewUseCase.Request(Observer.Create<object>(OnAppEventHandler, OnError), _viewId));
            await SetSubscribingAsync(false);
            switch (_subscriptionResult.Result)
            {
                case SubscribeViewUseCase.Result.Success:
                    CompositeDisposable.Add(_subscriptionResult.Subscription!);
                    StateHasChanged();
                    break;
                case SubscribeViewUseCase.Result.ValidationError:
                    Snackbar.Add("Ошибка валидации при подписке на события", MudBlazor.Severity.Error);
                    break;
                case SubscribeViewUseCase.Result.Unknown:
                default:
                    throw new Exception($"Unexpected result in {nameof(SubscribeViewUseCase)}: {_subscriptionResult.Result}");
            }
        }
        catch (Exception ex)
        {
            await SetSubscribingAsync(false);
            Snackbar.Add("Не удалось получить подписку на события вида из-за непредвиденной ошибки. Повторите попытку", MudBlazor.Severity.Error);
            Logger.LogError(ex, ex.Message);
        }
    }

    private void Unsubscribe()
    {
        if (_subscriptionResult?.Subscription is not null)
        {
            CompositeDisposable.Remove(_subscriptionResult.Subscription);
            _subscriptionResult.Subscription.Dispose();
        }
    }

    protected Task SetSubscribingAsync(bool isLoading = true) => UpdateViewAsync(() =>
    {
        _subscribing = isLoading;
    });

    #region [Actions]
    private void Cancel()
    {
        _isVisible = false;
        AuthenticationStateProvider.AuthenticationStateChanged -= OnAuthenticationStateChanged;
        Unsubscribe();
    }
    private Task RefreshAsync() => FetchAsync();
    private async Task SubmitAsync()
    {
        try
        {
            if (_viewResponse is null) return;
            Unsubscribe();
            var response = await ScopeFactory.MediatorSend(new DeleteViewUseCase.Command(_viewResponse.Id));
            switch (response.Result)
            {
                case DeleteViewUseCase.Result.Success:
                    Snackbar.Add("Вид успешно удалён", MudBlazor.Severity.Success);
                    Cancel();
                    break;
                case DeleteViewUseCase.Result.ValidationError:
                    await SubscribeAsync();
                    Snackbar.Add("Не удалось удалить вид из-за ошибки валидации", MudBlazor.Severity.Error);
                    break;
                case DeleteViewUseCase.Result.Unknown:
                default:
                    await SubscribeAsync();
                    throw new Exception($"Unexpected result in {nameof(DeleteViewUseCase)}: {response.Result}");
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, ex.Message);
            Snackbar.Add("Не удалось удалить вид.", MudBlazor.Severity.Error);
        }
    }
    #endregion


    #region [Event Handlers]
    private async void OnDeleteHandler(CameraViewDeleteEto eto)
    {
        var userAuthState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        if (userAuthState is null) return;

        var policyRequirementResource = new PolicyRequirementResource(eto.OrganizationId, eto.ViewId);
        var authorizationResult = await AuthorizationService.AuthorizeAsync(
            userAuthState.User,  // Current user from AuthenticationStateProvider
            policyRequirementResource, // The resource being authorized
            AppPermissions.Main.CameraViews.Delete.GetEnumPermissionString() // The policy name
        );

        if (authorizationResult.Succeeded)
        {
            _organizationId = eto.OrganizationId;
            _viewId = eto.ViewId;
            _isVisible = true;

            await FetchAsync();
            await SubscribeAsync();

            AuthenticationStateProvider.AuthenticationStateChanged += OnAuthenticationStateChanged;

            StateHasChanged();
        }
        else
        {
            Snackbar.Add("Недостаточно прав для удаления вида", MudBlazor.Severity.Warning);
            Cancel();
        }
    }

    private async void OnAppEventHandler(object appEvent)
    {
        switch (appEvent)
        {
            case SubscribeViewUseCase.UpdatedEvent updatedEto:
                await FetchAsync();
                await UpdateViewAsync();
                break;

            case SubscribeViewUseCase.DeletedEvent deletedEto:
                Snackbar.Add("Вид был удалён", MudBlazor.Severity.Warning);
                Cancel();
                break;

            default:
                Snackbar.Add("Было получено непредвиденное событие.", MudBlazor.Severity.Warning);
                await FetchAsync();
                await UpdateViewAsync();
                Logger.LogWarning("Unexpected event in {UseCase}: {Event}", nameof(SubscribeViewUseCase), nameof(appEvent));
                break;
        }
    }

    private void OnError(Exception exc)
    {
        Snackbar.Add("Ошибка при подписке на события", MudBlazor.Severity.Error);
        Logger.LogError(exc, exc.Message);
    }

    private async void OnAuthenticationStateChanged(Task<AuthenticationState> authenticationState)
    {
        var userAuthState = await authenticationState;
        if (userAuthState.User is null || userAuthState.User.Identity is null || !userAuthState.User.Identity.IsAuthenticated)
        {
            Cancel();
            return;
        }

        var policyRequirementResource = new PolicyRequirementResource(_organizationId, _viewId);
        var authorizationResult = await AuthorizationService.AuthorizeAsync(
            userAuthState.User,  // Current user from AuthenticationStateProvider
            policyRequirementResource, // The resource being authorized
            AppPermissions.Main.CameraPublicAccess.Update.GetEnumPermissionString() // The policy name
        );

        if (!authorizationResult.Succeeded)
        {
            Snackbar.Add("Недостаточно прав для перевыпуска ссылки на публичный доступ к камере", MudBlazor.Severity.Warning);
            Cancel();
        }
    }
    #endregion [Event Handlers]
}
