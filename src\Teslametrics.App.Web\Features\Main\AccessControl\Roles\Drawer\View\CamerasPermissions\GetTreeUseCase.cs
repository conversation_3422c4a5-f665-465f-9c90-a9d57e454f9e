using System.Data;
using Dapper;
using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Data.Sql;
using Teslametrics.App.Web.Services.Persistence;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Roles.Drawer.View.CamerasPermissions;

public static class GetTreeUseCase
{
    public record Query(Guid RoleId) : BaseRequest<Response>; // Запрашиваю все директории и камеры, к которым имеет доступ пользователь

    public record Response : BaseResponse
    {
        public List<Folder> Items { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(List<Folder> items)
        {
            Items = items;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Result = result;

            Items = [];
        }

        public record Folder(Guid Id, string Name, List<Camera> Cameras);

        public record Camera(Guid Id, string Name);
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError
    }

    public class Validator : AbstractValidator<Query>
    {
        public Validator()
        {
            RuleFor(q => q.RoleId).NotEmpty();
        }
    }
    public class Handler : IRequestHandler<Query, Response>
    {
        private readonly IValidator<Query> _validator;
        private readonly IDbConnection _dbConnection;

        public Handler(IValidator<Query> validator,
                       IDbConnection dbConnection)
        {
            _validator = validator;
            _dbConnection = dbConnection;
        }

        public async Task<Response> Handle(Query request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            var foldersTemplate = SqlQueryBuilder.Create()
                .Select(Db.Folders.Props.Id)
                .Select(Db.Folders.Props.Name)
                .InnerJoin(Db.RolePermissions.Table, Db.RolePermissions.Props.ResourceId, Db.Folders.Props.Id, SqlOperator.Equals)
                .Where(Db.RolePermissions.Props.RoleId, ":RoleId", SqlOperator.Equals, new { request.RoleId })
                .Build(QueryType.Standard, Db.Folders.Table, RowSelection.UniqueRows);

            var folderModels = await _dbConnection.QueryAsync<FolderModel>(foldersTemplate.RawSql, foldersTemplate.Parameters);

            var camerasTemplate = SqlQueryBuilder.Create()
                .Select(Db.Cameras.Props.Id)
                .Select(Db.Cameras.Props.FolderId)
                .Select(Db.Cameras.Props.Name)
                .InnerJoin(Db.RolePermissions.Table, Db.RolePermissions.Props.ResourceId, Db.Cameras.Props.Id, SqlOperator.Equals)
                .Where(Db.RolePermissions.Props.RoleId, ":RoleId", SqlOperator.Equals, new { request.RoleId })
                .Build(QueryType.Standard, Db.Cameras.Table, RowSelection.UniqueRows);

            var cameraModels = await _dbConnection.QueryAsync<CameraModel>(camerasTemplate.RawSql, camerasTemplate.Parameters);

            var folders = folderModels.Select(f =>
            {
                var cameras = cameraModels.Where(c => c.FolderId == f.Id)
                    .Select(c => new Response.Camera(c.Id, c.Name )).ToList();

                return new Response.Folder(f.Id, f.Name, cameras);
            }).ToList();

            return new Response(folders);
        }
    }

    public record FolderModel(Guid Id, string Name);

    public record CameraModel(Guid Id, Guid FolderId, string Name);
}
