using System.Data;
using System.Text.Json;
using Dapper;
using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Services;
using Teslametrics.Core.Services.Persistence;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Features.Main.Cameras.CameraDrawer.Create.PresetField;

public static class GetPresetStreamConfigUseCase
{
    public record Query(Guid QuotaId) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public StreamConfig? ArchiveStreamConfig { get; init; }
        public StreamConfig? ViewStreamConfig { get; init; }
        public StreamConfig? PublicStreamConfig { get; init; }
        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(StreamConfig? archiveStreamConfig, StreamConfig? viewStreamConfig, StreamConfig? publicStreamConfig)
        {
            Result = Result.Success;

            ArchiveStreamConfig = archiveStreamConfig;
            ViewStreamConfig = viewStreamConfig;
            PublicStreamConfig = publicStreamConfig;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Result = result;
        }

        public record StreamConfig(Resolution Resolution, VideoCodec VideoCodec, FrameRate FrameRate, SceneDynamic SceneDynamic, AudioCodec AudioCodec, double bitrate);
    }
    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError
    }

    public class Validator : AbstractValidator<Query>
    {
        public Validator()
        {
            RuleFor(q => q.QuotaId).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Query, Response>
    {
        private readonly IValidator<Query> _validator;
        private readonly IDbConnection _dbConnection;

        public Handler(IValidator<Query> validator,
                       IDbConnection dbConnection)
        {
            _validator = validator;
            _dbConnection = dbConnection;
        }

        public async Task<Response> Handle(Query request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            var template = SqlQueryBuilder.Create()
                .Select(Db.Presets.Props.ArchiveStreamConfig)
                .Select(Db.Presets.Props.ViewStreamConfig)
                .Select(Db.Presets.Props.PublicStreamConfig)
                .LeftJoin(Db.Presets.Table, Db.Presets.Props.Id, Db.CameraQuotas.Props.PresetId, SqlOperator.Equals)
                .Where(Db.CameraQuotas.Props.Id, ":QuotaId", SqlOperator.Equals, new { request.QuotaId })
                .Build(QueryType.Standard, Db.CameraQuotas.Table, RowSelection.AllRows);

            var preset = await _dbConnection.QuerySingleAsync<CameraPresetModel>(template.RawSql, template.Parameters);

            return new Response(ToStreamConfigResponse(preset.ArchiveStreamConfig),
                                ToStreamConfigResponse(preset.ViewStreamConfig),
                                ToStreamConfigResponse(preset.PublicStreamConfig));
        }

        private static Response.StreamConfig? ToStreamConfigResponse(string streamConfig)
        {
            if (string.IsNullOrEmpty(streamConfig))
            {
                return null;
            }

            var streamConfigModel = JsonSerializer.Deserialize<Response.StreamConfig>(streamConfig);

            var bitrate = BitrateCalculator.CalculateBitrate(streamConfigModel!.Resolution, streamConfigModel.VideoCodec, streamConfigModel.FrameRate, streamConfigModel.SceneDynamic, streamConfigModel.AudioCodec);
            return new Response.StreamConfig(streamConfigModel.Resolution, streamConfigModel.VideoCodec, streamConfigModel.FrameRate, streamConfigModel.SceneDynamic, streamConfigModel.AudioCodec, bitrate);
        }
    }

    public record CameraPresetModel(string ArchiveStreamConfig, string ViewStreamConfig, string PublicStreamConfig);
}
