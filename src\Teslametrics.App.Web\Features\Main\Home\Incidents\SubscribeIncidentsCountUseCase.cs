using FluentValidation;
using MediatR;
using System.Reactive.Linq;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Domain.Cameras.Events;
using Teslametrics.App.Web.Domain.Incidents.Events;
using Teslametrics.App.Web.Exceptions;
using Teslametrics.App.Web.Features.Main.SystemSettings;
using Teslametrics.App.Web.Orleans.Camera.Events;
using Teslametrics.App.Web.Services.DomainEventBus;

namespace Teslametrics.App.Web.Features.Main.Home.Incidents;

public static class SubscribeIncidentsCountUseCase
{
    public record Request(IObserver<UpdatedEvent> Observer, IEnumerable<Guid> BuildingIds, IEnumerable<Guid> CameraIds) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public IDisposable? Subscription { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(IDisposable subscription)
        {
            Subscription = subscription;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Subscription = null;
            Result = result;
        }
    }

    // События, на которые можно подписаться
    public record UpdatedEvent;

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError
    }

    public class Validator : AbstractValidator<Request>
    {
        public Validator()
        {
            RuleFor(r => r.Observer).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Request, Response>
    {
        private readonly IValidator<Request> _validator;
        private readonly IDomainEventBus _domainEventBus;

        public Handler(IValidator<Request> validator,
                       IDomainEventBus domainEventBus)
        {
            _validator = validator;
            _domainEventBus = domainEventBus;
        }

        public async Task<Response> Handle(Request request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            var eventStream = await _domainEventBus.GetEventStreamAsync();

            // Подписываемся на события, связанные с инцидентами
            var subscription = eventStream
                .Where(e => e switch
                {
                    CameraRunningEvent @event => request.CameraIds.Contains(@event.Id), // Камера работает
                    CameraStoppedEvent @event => request.CameraIds.Contains(@event.Id), // Камера остановлена
                    CameraProblemEvent @event => request.CameraIds.Contains(@event.Id), // Проблемы с камерой
                    CameraDeletedEvent @event => request.CameraIds.Contains(@event.Id),
                    IncidentCreatedEvent @event => !request.BuildingIds.Any() || request.BuildingIds.Contains(@event.BuildingId),
                    IncidentResolvedEvent @event => !request.BuildingIds.Any() || request.BuildingIds.Contains(@event.BuildingId),
                    PlanUpdatedEvent => true,
                    _ => false
                })
                .Select(e => e switch
                {
                    // События камер
                    CameraStoppedEvent => new UpdatedEvent(),
                    CameraProblemEvent => new UpdatedEvent(),
                    CameraStartingEvent => new UpdatedEvent(),
                    CameraDeletedEvent => new UpdatedEvent(),
                    IncidentCreatedEvent => new UpdatedEvent(),
                    IncidentResolvedEvent => new UpdatedEvent(),
                    PlanUpdatedEvent => new UpdatedEvent(),
                    _ => throw new AppException("Invalid event type")
                })
                .Subscribe(request.Observer);

            return new Response(subscription);
        }
    }
}