using Microsoft.AspNetCore.Components;
using MudBlazor;
using System.Reactive;
using Teslametrics.Shared;
using Teslametrics.App.Web.Events.Cameras;
using Teslametrics.App.Web.Exceptions;
using Teslametrics.App.Web.Extensions;
using Teslametrics.Core.Services.Persistence;

namespace Teslametrics.App.Web.Features.Main.Cameras.List;

public partial class CamsListComponent
{
    private Guid _organizationId = Guid.Empty;
    private Guid? _folderId;
    private bool _subscribing;
    private DateTime _lastRefreshTime;
    protected class Group
    {
        public Guid Id { get; set; }

        public string Name { get; set; } = string.Empty;
    }
    protected Group? SelectedGroup;

    private string _orderBy => Db.Cameras.Props.Name;
    private OrderDirection _orderDirection = OrderDirection.Ascending;

    private SubscribeCameraListUseCase.Response? _subscriptionResult;
    private GetCameraListUseCase.Response? _listResponse;

    [Parameter]
    public Guid OrganizationId { get; set; }

    [Parameter]
    public Guid? FolderId { get; set; }
    [Parameter]
    public EventCallback<Guid?> FolderIdChanged { get; set; }

    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();

        if (OrganizationId != _organizationId || FolderId != _folderId)
        {
            _organizationId = OrganizationId;
            _folderId = FolderId;

            await SubscribeAsync();
            await FetchAsync(FolderId);
        }
    }

    private async Task FetchAsync(Guid? id)
    {
        await SetLoadingAsync(true);
        _lastRefreshTime = DateTime.Now;
        try
        {
            var userId = await GetCurrentUserIdAsync() ?? throw new NotAuthorizedException();
            _listResponse = await ScopeFactory.MediatorSend(new GetCameraListUseCase.Query(userId, OrganizationId, FolderId, _orderBy, _orderDirection));
        }
        catch (Exception exc)
        {
            Logger.LogError(exc, exc.Message);
            Snackbar.Add("Не удалось получить список камер из-за непредвиденной ошибки.", Severity.Error);
        }

        await SetLoadingAsync(false);
        if (_listResponse is null) return;

        switch (_listResponse.Result)
        {
            case GetCameraListUseCase.Result.Success:
                break;
            case GetCameraListUseCase.Result.Unknown:
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(CamsListComponent), nameof(GetCameraListUseCase));
                Snackbar.Add($"Не удалось получить список камер из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
                break;
            default:
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(CamsListComponent), nameof(GetCameraListUseCase), _listResponse.Result);
                Snackbar.Add($"Не удалось получить список камер из-за ошибки: {_listResponse.Result}", MudBlazor.Severity.Error);
                break;
        }
    }
    private async Task SubscribeAsync()
    {
        try
        {
            Unsubscribe();

            await SetSubscribingAsync(true);
            _subscriptionResult = await ScopeFactory.MediatorSend(new SubscribeCameraListUseCase.Request(Observer.Create<object>(OnAppEventHandler, OnError)));
            await SetSubscribingAsync(false);
            switch (_subscriptionResult.Result)
            {
                case SubscribeCameraListUseCase.Result.Success:
                    CompositeDisposable.Add(_subscriptionResult.Subscription!);
                    break;
                case SubscribeCameraListUseCase.Result.ValidationError:
                    Snackbar.Add("Ошибка валидации при подписке на события", Severity.Error);
                    break;
                case SubscribeCameraListUseCase.Result.Unknown:
                default:
                    throw new Exception($"Unexpected result in {nameof(SubscribeCameraListUseCase)}: {_subscriptionResult.Result}");
            }
        }
        catch (Exception ex)
        {
            await SetSubscribingAsync(false);
            Snackbar.Add("Не удалось получить подписку на события камеры из-за непредвиденной ошибки. Повторите попытку", Severity.Error);
            Logger.LogError(ex, ex.Message);
        }
    }
    private void Unsubscribe()
    {
        if (_subscriptionResult?.Subscription is not null)
        {
            CompositeDisposable.Remove(_subscriptionResult.Subscription);
            _subscriptionResult.Subscription.Dispose();
        }
    }

    protected Task SetSubscribingAsync(bool isLoading = true) => UpdateViewAsync(() =>
    {
        _subscribing = isLoading;
    });

    #region [Actions]
    private void CreateFolder()
    {
        if (FolderId is not null) return; // TODO: remove in future

        EventSystem.Publish(new CameraGroupCreateEto(OrganizationId)); //, Group
    }

    private void CreateCamera()
    {
        if (FolderId is null) return; // TODO: remove in future

        EventSystem.Publish(new CameraCreateEto(OrganizationId, FolderId));
    }

    private Task AlphabetSortToggleAsync()
    {
        _orderDirection = _orderDirection == OrderDirection.Ascending ? OrderDirection.Descending : OrderDirection.Ascending;

        return FetchAsync(FolderId);
    }

    private Task RefreshAsync() => FetchAsync(FolderId);
    #endregion

    #region [Event Handlers]
    private void Import() => EventSystem.Publish(new CameraListImportEto(OrganizationId));
    private void Export() => EventSystem.Publish(new CameraListExportEto(OrganizationId));

    private async void OnAppEventHandler(object appEvent)
    {
        await FetchAsync(FolderId);
        await UpdateViewAsync();
    }

    private void OnError(Exception exc)
    {
        Snackbar.Add("Ошибка при подписке на события", Severity.Error);
        Logger.LogError(exc, exc.Message);
    }
    #endregion [Event Handlers]
}
