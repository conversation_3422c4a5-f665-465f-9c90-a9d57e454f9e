using System.Data;
using Dapper;
using Teslametrics.App.Web.Services.Onvif;
using Teslametrics.App.Web.Services.Persistence;
using System.Reactive.Linq;
using System.Collections.Concurrent;

namespace Teslametrics.App.Web.Orleans.Camera;

public class  OnvifStateMachine
{
    private readonly ILogger<OnvifStateMachine> _logger;
    private readonly Guid _cameraId;
    private readonly string _tableName;
    private readonly IServiceScopeFactory _serviceScopeFactory;
    private string _host = string.Empty;
    private int _port = 0;
    private string _username = string.Empty;
    private string _password = string.Empty;
    private OnvifEventClient? _eventClient;
    private State _state = State.Disabled;
    private readonly ConcurrentQueue<Signal> _externalQueue;
    private Task? _processTask;
    private readonly CancellationTokenSource _cts;
    private IDisposable? _motionEventsSubscription;

    public OnvifStateMachine(ILogger<OnvifStateMachine> logger,
                             Guid cameraId,
                             IServiceScopeFactory serviceScopeFactory)
    {
        _logger = logger;
        _cameraId = cameraId;
        _tableName = $"{Db.MotionEvents.Table}_{_cameraId.ToString("N")}";
        _serviceScopeFactory = serviceScopeFactory;
        _cts = new CancellationTokenSource();

        _externalQueue = new ConcurrentQueue<Signal>();
    }

    public Status GetStatus()
    {
        return _state switch
        {
            State.Disabled => Status.Disabled,
            State.Disconnected => Status.Stopped,
            State.Connecting => Status.Starting,
            State.Connected => Status.Running,
            _ => Status.Problem
        };
    }

    public Task OnActivateAsync(CancellationToken cancellationToken)
    {
        _processTask = Task.Run(() => ProcessAsync(_cts.Token));

        return Task.CompletedTask;
    }

    public async Task OnDeactivateAsync(CancellationToken cancellationToken)
    {
        try
        {
            await StopAsync();

            _cts.Cancel();
            await _processTask!;
        }
        catch (OperationCanceledException)
        {
            _logger.LogWarning("ONVIF camera {CameraId} deactivation process was canceled", _cameraId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to deactivate ONVIF camera {CameraId}", _cameraId);
        }
    }

    public async Task ConnectAsync(string host, int port, string username, string password)
    {
        try
        {
            // Create table for motion events if it doesn't exist
            bool tableCreated = await CreateTableIfNotExistsAsync();

            // Close any open motion events from previous sessions only if table existed before
            if (!tableCreated)
            {
                await CloseOpenMotionEventAsync();
                _logger.LogInformation("Closed open motion events from previous sessions for ONVIF camera {CameraId}", _cameraId);
            }
            else
            {
                _logger.LogInformation("Created motion events table for ONVIF camera {CameraId}", _cameraId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error initializing motion events table during activation for ONVIF camera {CameraId}", _cameraId);
        }

        _host = host;
        _port = port;
        _username = username;
        _password = password;

        _externalQueue.Enqueue(Signal.Connect);
    }

    public Task DisconnectAsync()
    {
        _externalQueue.Enqueue(Signal.Disconnect);
        return Task.CompletedTask;
    }

    private async Task ProcessAsync(CancellationToken cancellationToken)
    {
        while (!cancellationToken.IsCancellationRequested)
        {
            if (!_externalQueue.TryDequeue(out var signal))
            {
                await Task.Delay(100);
                continue;
            }

            switch (signal)
            {
                case Signal.Connect:
                    {
                        switch (_state)
                        {
                            case State.Disabled:
                            case State.Disconnected:
                                {
                                    ChangeState(State.Connecting);
                                    await StartAsync(_host, _port, _username, _password);
                                    break;
                                }
                        }

                        break;
                    }
                case Signal.Reconnect:
                    {
                        switch (_state)
                        {
                            case State.Connected:
                            case State.Connecting:
                            case State.Reconnecting:
                                {
                                    ChangeState(State.Reconnecting);

                                    await _eventClient!.UnsubscribeAsync();

                                    // Unsubscribe from events
                                    _motionEventsSubscription?.Dispose();

                                    // Dispose resources
                                    _eventClient.Dispose();
                                    _eventClient = null;

                                    await StartAsync(_host, _port, _username, _password);
                                    break;
                                }
                        }
                        break;
                    }
                case Signal.Connected:
                    {
                        switch (_state)
                        {
                            case State.Connecting:
                                {
                                    ChangeState(State.Connected);
                                    break;
                                }
                        }

                        break;
                    }
                case Signal.Disconnect:
                    {
                        switch (_state)
                        {
                            case State.Connected:
                            case State.Connecting:
                            case State.Reconnecting:
                                {
                                    await StopAsync();
                                    ChangeState(State.Disconnected);
                                    break;
                                }
                        }

                        break;
                    }
            }
        }
    }

    private async Task StartAsync(string host, int port, string username, string password)
    {
        try
        {
            _eventClient = new OnvifEventClient(host, port, username, password);

            // Subscribe to motion events
            await SubscribeToMotionEventsAsync();

            await _eventClient.CreateSubscriptionAsync();

            await _eventClient.StartEventPollingAsync();

            _externalQueue.Enqueue(Signal.Connected);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting ONVIF camera {CameraId}", _cameraId);
            _externalQueue.Enqueue(Signal.Reconnect);
        }
    }

    private async Task StopAsync()
    {
        // Stop and dispose ONVIF event client
        if (_eventClient is not null)
        {
            try
            {
                // Stop event polling
                await _eventClient.UnsubscribeAsync();

                // Unsubscribe from events
                _motionEventsSubscription?.Dispose();

                // Dispose resources
                _eventClient.Dispose();
                _eventClient = null;

                _logger.LogInformation("ONVIF event client stopped and disposed for camera {CameraId}", _cameraId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error stopping ONVIF event client for camera {CameraId}", _cameraId);
            }
        }
    }

    private async Task SaveMotionEventAsync(OnvifMotionEvent motionEvent)
    {
        if (motionEvent.IsMotion)
        {
            // Start of motion - create a new record
            await AddMotionEventAsync(motionEvent.DeviceTime);
            _logger.LogInformation("New motion event started at {Time:yyyy-MM-dd HH:mm:ss.fff} for ONVIF camera {CameraId}", motionEvent.DeviceTime, _cameraId);
        }
        else
        {
            // End of motion - close the open event
            var rowsAffected = await CloseOpenMotionEventAsync(motionEvent.DeviceTime);

            if (rowsAffected > 0)
            {
                _logger.LogInformation("Motion event ended at {Time:yyyy-MM-dd HH:mm:ss.fff} for ONVIF camera {CameraId}", motionEvent.DeviceTime, _cameraId);
            }
            else
            {
                _logger.LogWarning("No open motion events found to close at {Time:yyyy-MM-dd HH:mm:ss.fff} for ONVIF camera {CameraId}", motionEvent.DeviceTime, _cameraId);
            }
        }
    }

    private async Task AddMotionEventAsync(DateTimeOffset startTime)
    {
        using var scope = _serviceScopeFactory.CreateScope();
        using var dbConnection = scope.ServiceProvider.GetRequiredService<IDbConnection>();

        if (dbConnection.State != ConnectionState.Open)
        {
            dbConnection.Open();
        }

        // Create a new record with EndTime = NULL
        await dbConnection.ExecuteAsync(
            $"INSERT INTO {_tableName} ({Db.MotionEvents.Columns.StartTime}, {Db.MotionEvents.Columns.EndTime}) " +
            $"VALUES (@StartTime, NULL)",
            new { StartTime = startTime.UtcDateTime });
    }

    private async Task<int> CloseOpenMotionEventAsync(DateTimeOffset? endTime = null)
    {
        using var scope = _serviceScopeFactory.CreateScope();
        using var dbConnection = scope.ServiceProvider.GetRequiredService<IDbConnection>();

        if (dbConnection.State != ConnectionState.Open)
        {
            dbConnection.Open();
        }

        // If time is not specified, use the current server time
        var actualEndTime = endTime ?? DateTimeOffset.Now;

        // Execute the query with the time parameter
        return await dbConnection.ExecuteAsync(
            $"UPDATE {_tableName} SET {Db.MotionEvents.Columns.EndTime} = @EndTime " +
            $"WHERE {Db.MotionEvents.Columns.EndTime} IS NULL",
            new { EndTime = actualEndTime.UtcDateTime });
    }

    private async Task<bool> CreateTableIfNotExistsAsync()
    {
        using var scope = _serviceScopeFactory.CreateScope();
        using var dbConnection = scope.ServiceProvider.GetRequiredService<IDbConnection>();

        if (dbConnection.State != ConnectionState.Open)
        {
            dbConnection.Open();
        }

        using (var transaction = dbConnection.BeginTransaction())
        {
            try
            {
                // Check if table exists
                var tableExists = await dbConnection.ExecuteScalarAsync<int>(
                    "SELECT COUNT(*) FROM information_schema.tables " +
                    "WHERE table_schema = 'public' AND table_name = @TableName",
                    new { TableName = _tableName });

                if (tableExists > 0)
                {
                    transaction.Commit();
                    return false; // Table already existed
                }

                // Create table
                await dbConnection.ExecuteAsync(
                    $"CREATE TABLE IF NOT EXISTS {_tableName} (" +
                    $"{Db.MotionEvents.Columns.StartTime} timestamptz NOT NULL, " +
                    $"{Db.MotionEvents.Columns.EndTime} timestamptz NULL)");

                // Create hypertable
                await dbConnection.ExecuteAsync($"SELECT create_hypertable('{_tableName}', '{Db.MotionEvents.Columns.StartTime}')");

                transaction.Commit();
                return true; // Table was created
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating table {TableName} for ONVIF camera {CameraId}", _tableName, _cameraId);
                throw;
            }
        }
    }

    private Task SubscribeToMotionEventsAsync()
    {
        if (_eventClient == null)
        {
            _logger.LogWarning("Cannot subscribe to motion events: event client is null for ONVIF camera {CameraId}", _cameraId);
            return Task.CompletedTask;
        }

        try
        {
            // Subscribe to motion events
            _motionEventsSubscription = _eventClient.MotionEvents
                .DistinctUntilChanged(e => e.IsMotion)
                .Subscribe(
                    // Event handler
                    motionEvent =>
                    {
                        Task.Run(async () =>
                        {
                            try
                            {
                                await SaveMotionEventAsync(motionEvent);
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError(ex, "Error saving motion event for ONVIF camera {CameraId}", _cameraId);
                            }
                        });
                    },
                    // Error handler
                    ex =>
                    {
                        _logger.LogError(ex, "Error in motion events subscription for ONVIF camera {CameraId}", _cameraId);
                        _externalQueue.Enqueue(Signal.Reconnect);
                    },
                    // Completion handler
                    () =>
                    {
                        _logger.LogInformation("Motion events subscription completed for ONVIF camera {CameraId}", _cameraId);
                        Task.Run(async () =>
                        {
                            try
                            {
                                // Close all open motion events
                                await CloseOpenMotionEventAsync();
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError(ex, "Error closing open motion events on subscription completion for ONVIF camera {CameraId}", _cameraId);
                            }
                        });
                    });

            return Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to subscribe to motion events for ONVIF camera {CameraId}", _cameraId);
            throw;
        }
    }

    private void ChangeState(State state)
    {
        if (_state != state)
        {
            _state = state;
        }
    }

    public enum State
    {
        Disabled,
        Disconnected,
        Connecting,
        Reconnecting,
        Connected
    }

    public enum Signal
    {
        Disconnect,
        Connect,
        Connected,
        Reconnect
    }

    public enum Status
    {
        Disabled,
        Stopped,
        Starting,
        Running,
        Problem
    }
}