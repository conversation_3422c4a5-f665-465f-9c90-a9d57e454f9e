﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Teslametrics.App.Web.Locales.Features.Main.AccessControl.Roles.Drawer.Create.CamerasPermissions {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class CamerasConcretePermissionsComponent {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal CamerasConcretePermissionsComponent() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Teslametrics.App.Web.Locales.Features.Main.AccessControl.Roles.Drawer.Create.Came" +
                            "rasPermissions.CamerasConcretePermissionsComponent", typeof(CamerasConcretePermissionsComponent).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create camera presets.
        /// </summary>
        public static string Main_CameraPresets_Create {
            get {
                return ResourceManager.GetString("Main.CameraPresets.Create", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete camera presets.
        /// </summary>
        public static string Main_CameraPresets_Delete {
            get {
                return ResourceManager.GetString("Main.CameraPresets.Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Read cameras presets.
        /// </summary>
        public static string Main_CameraPresets_Read {
            get {
                return ResourceManager.GetString("Main.CameraPresets.Read", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit camera presets.
        /// </summary>
        public static string Main_CameraPresets_Update {
            get {
                return ResourceManager.GetString("Main.CameraPresets.Update", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Connect camera.
        /// </summary>
        public static string Main_Cameras_Connect {
            get {
                return ResourceManager.GetString("Main.Cameras.Connect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create camera.
        /// </summary>
        public static string Main_Cameras_Create {
            get {
                return ResourceManager.GetString("Main.Cameras.Create", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete camera.
        /// </summary>
        public static string Main_Cameras_Delete {
            get {
                return ResourceManager.GetString("Main.Cameras.Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Disconnect camera.
        /// </summary>
        public static string Main_Cameras_Disconnect {
            get {
                return ResourceManager.GetString("Main.Cameras.Disconnect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Move camera.
        /// </summary>
        public static string Main_Cameras_Move {
            get {
                return ResourceManager.GetString("Main.Cameras.Move", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Camera view.
        /// </summary>
        public static string Main_Cameras_Read {
            get {
                return ResourceManager.GetString("Main.Cameras.Read", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit camera.
        /// </summary>
        public static string Main_Cameras_Update {
            get {
                return ResourceManager.GetString("Main.Cameras.Update", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create folder.
        /// </summary>
        public static string Main_Folders_Create {
            get {
                return ResourceManager.GetString("Main.Folders.Create", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete folder.
        /// </summary>
        public static string Main_Folders_Delete {
            get {
                return ResourceManager.GetString("Main.Folders.Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Move folder.
        /// </summary>
        public static string Main_Folders_Move {
            get {
                return ResourceManager.GetString("Main.Folders.Move", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Folder view.
        /// </summary>
        public static string Main_Folders_Read {
            get {
                return ResourceManager.GetString("Main.Folders.Read", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit folder.
        /// </summary>
        public static string Main_Folders_Update {
            get {
                return ResourceManager.GetString("Main.Folders.Update", resourceCulture);
            }
        }
    }
}
