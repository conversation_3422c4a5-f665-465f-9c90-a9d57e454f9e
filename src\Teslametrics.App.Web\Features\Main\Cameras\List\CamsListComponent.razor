@using Teslametrics.App.Web.Features.Main.Cameras.List.CameraCard
@using Teslametrics.Shared
@inherits InteractiveBaseComponent
<div class="cams_list_component mud-height-full">
    <MudStack Row="true"
              Class="mud-width-full pb-4 list_header">
        <MudText Typo="Typo.h3"
                 Class="align-content-center">Камеры</MudText>
        <MudSpacer />

        <SubscriptionErrorComponent Show="@(!_subscribing && (_subscriptionResult is null || !_subscriptionResult.IsSuccess))"
                                    Class="d-sm-none d-md-flex"
                                    RetrySubscribe="SubscribeAsync" />
        <MudTooltip Text="@($"Время последнего обновления: {_lastRefreshTime.ToLocalTime()}")"
                    RootClass="d-none d-sm-flex d-md-none"
                    Arrow="true"
                    Placement="Placement.Left">
            @if (IsLoading)
            {
                <MudProgressCircular Color="Color.Default"
                                     Indeterminate="true" />
            }
            else
            {
                <MudIconButton OnClick="RefreshAsync"
                               Icon="@Icons.Material.Outlined.Refresh" />
            }
        </MudTooltip>
        <AuthorizeView Policy="@AppPermissions.Main.Cameras.Create.GetEnumPermissionString()"
                       Resource="@(new PolicyRequirementResource(OrganizationId, null))"
                       Context="innerContext">
            <MudButton OnClick="Import"
                       Variant="Variant.Text"
                       Class="d-none d-sm-flex d-md-none"
                       StartIcon="@Icons.Material.Outlined.Download">Импорт</MudButton>
        </AuthorizeView>
        <MudButton OnClick="Export"
                   Variant="Variant.Text"
                   Class="d-none d-sm-flex d-md-none"
                   StartIcon="@Icons.Material.Outlined.Upload">Экспорт</MudButton>

        <MudTooltip Text="@(_orderDirection == OrderDirection.Ascending ? "Сортировка по алфавиту" : "Сортировка против алфавита")"
                    Class="d-none d-sm-flex d-md-none"
                    Arrow="true"
                    Placement="Placement.Left">
            <MudBadge Icon="@(_orderDirection == OrderDirection.Ascending ? Icons.Material.Filled.ArrowUpward : Icons.Material.Filled.ArrowDownward)"
                      Color="Color.Primary"
                      Overlap="true"
                      Origin="Origin.BottomLeft"
                      Bordered="true"
                      BadgeClass="badge_position">
                <MudIconButton Icon="@Icons.Material.Filled.SortByAlpha"
                               OnClick="AlphabetSortToggleAsync" />
            </MudBadge>
        </MudTooltip>
        <div style="height: fit-content;padding-top: 6px;"
             class="d-none d-sm-flex d-md-none">
            @if (FolderId is not null)
            {
                <AuthorizeView Policy="@AppPermissions.Main.Cameras.Create.GetEnumPermissionString()"
                               Resource="@(new PolicyRequirementResource(OrganizationId, FolderId))"
                               Context="innerContext">
                    <MudBadge Color="Color.Primary"
                              Icon="@Icons.Material.Outlined.Add"
                              Overlap="true"
                              Origin="Origin.BottomLeft"
                              Bordered="true">
                        <MudButton OnClick="CreateCamera"
                                   StartIcon="@Icons.Material.Outlined.Camera"
                                   Variant="Variant.Outlined"
                                   Color="Color.Secondary">Добавить камеру</MudButton>
                    </MudBadge>
                </AuthorizeView>
            }
            else
            {
                <AuthorizeView Policy="@AppPermissions.Main.Folders.Create.GetEnumPermissionString()"
                               Resource="@(new PolicyRequirementResource(OrganizationId, FolderId))"
                               Context="innerContext">
                    <MudBadge Color="Color.Primary"
                              Icon="@Icons.Material.Outlined.Add"
                              Overlap="true"
                              Origin="Origin.BottomLeft"
                              Bordered="true">
                        <MudButton OnClick="CreateFolder"
                                   StartIcon="@Icons.Material.Outlined.Folder"
                                   Variant="Variant.Outlined"
                                   Color="Color.Secondary">Добавить директорию
                        </MudButton>
                    </MudBadge>
                </AuthorizeView>
            }
        </div>

        <MudMenu Icon="@Icons.Material.Filled.MoreVert"
                 AnchorOrigin="@Origin.CenterRight"
                 Variant="Variant.Outlined"
                 TransformOrigin="@Origin.TopRight"
                 Class="d-sm-none d-md-flex">
            @if (FolderId is not null)
            {
                <AuthorizeView Policy="@AppPermissions.Main.Cameras.Create.GetEnumPermissionString()"
                               Resource="@(new PolicyRequirementResource(OrganizationId, FolderId))"
                               Context="innerContext">
                    <MudMenuItem OnClick="CreateCamera"
                                 Icon="@Icons.Material.Outlined.Add"
                                 IconColor="Color.Secondary">
                        Добавить камеру
                    </MudMenuItem>
                </AuthorizeView>
            }
            else
            {
                <AuthorizeView Policy="@AppPermissions.Main.Cameras.Create.GetEnumPermissionString()"
                               Resource="@(new PolicyRequirementResource(OrganizationId, FolderId))"
                               Context="innerContext">
                    <MudMenuItem OnClick="CreateFolder"
                                 Icon="@Icons.Material.Outlined.Add"
                                 IconColor="Color.Secondary">
                        Добавить директорию
                    </MudMenuItem>
                </AuthorizeView>
            }
            <AuthorizeView Policy="@AppPermissions.Main.Cameras.Create.GetEnumPermissionString()"
                           Resource="@(new PolicyRequirementResource(OrganizationId, null))"
                           Context="innerContext">
                <MudMenuItem OnClick="Import"
                             Icon="@Icons.Material.Outlined.Download"
                             IconColor="Color.Primary">
                    Импорт
                </MudMenuItem>
            </AuthorizeView>
            <MudMenuItem OnClick="Export"
                         Icon="@Icons.Material.Outlined.Upload">
                Экспорт
            </MudMenuItem>
            <MudMenuItem OnClick="RefreshAsync"
                         Disabled="IsLoading"
                         Icon="@Icons.Material.Outlined.Refresh">
                Принудительное обновление
            </MudMenuItem>
        </MudMenu>
    </MudStack>
    <div class="cameras_grid">
        @if (_listResponse is not null && _listResponse.IsSuccess && _listResponse.Items.Count > 0)
        {
            @foreach (var item in _listResponse.Items)
            {
                <CameraCardComponent CameraId="@item.Id"
                                     OrganizationId="OrganizationId"
                                     @key="item.Id" />
            }
        }
        @if (_listResponse is not null && _listResponse.IsSuccess && _listResponse.Items.Count == 0)
        {
            <MudStack AlignItems="AlignItems.Center">
                <MudText Typo="Typo.subtitle1">Нет элементов</MudText>
                <MudText Typo="Typo.body1">Добавьте элементы и попробуйте снова</MudText>
                <MudText Typo="Typo.body2">Время последнего обновления: @_lastRefreshTime.ToLocalTime()</MudText>

                <MudButton OnClick="RefreshAsync"
                           Variant="Variant.Filled"
                           Color="Color.Primary"
                           StartIcon="@Icons.Material.Outlined.Refresh">Обновить</MudButton>
            </MudStack>
        }
        @if ((_listResponse is null || !_listResponse.IsSuccess || _listResponse.Items.Count == 0) && IsLoading)
        {
            <CamsListComponentLoader />
        }
    </div>
</div>