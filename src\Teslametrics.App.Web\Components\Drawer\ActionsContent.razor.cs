using Microsoft.AspNetCore.Components;
using System.Text;

namespace Teslametrics.App.Web.Components.Drawer;

public partial class ActionsContent
{
	// TODO:: переписать в TreeBuilder и спрятать в DrawerComponent чтобы не было возможность достать извне
	private DrawerActions? _actions;
	private StringBuilder _classBuilder => new StringBuilder("d-flex flex-row align-items-center gap-3 drawer_actions pa-4 ")
		.Append(Actions?.Class);

	[Parameter]
	public DrawerActions? Actions { get; set; }

	[CascadingParameter(Name = DrawerConsts.InstanceName)]
	public DrawerComponent Drawer { get; set; } = null!;

	protected override void OnParametersSet()
	{
		if (_actions != Actions)
		{
			if (_actions is not null)
			{
				_actions.OnChange -= OnHeaderChange;
			}
			_actions = Actions;
			if (_actions is not null)
			{
				_actions.OnChange += OnHeaderChange;
			}
		}
		base.OnParametersSet();
	}

	private void OnHeaderChange()
	{
		StateHasChanged();
	}
}

