using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using System.Text.Json;
using Teslametrics.App.Web.Services.DomainEventBus;
using Teslametrics.App.Web.Services.Persistence;

namespace Teslametrics.App.Web.Services.Outbox;

public static class OutboxModule
{
    public static void Install(IServiceCollection services)
    {
        services.AddTransient<IOutbox, DefaultOutbox>();

        services.AddTransient<IOutboxStorage, EfCoreStorage<CommandAppDbContext>>();

        services.AddHostedService<Miner>();
    }

    public class OutboxMessageEntityTypeConfiguration : IEntityTypeConfiguration<OutboxMessage>
    {
        public void Configure(EntityTypeBuilder<OutboxMessage> builder)
        {
            builder.ToTable(Db.OutboxMessages.Table);
            builder.Property(e => e.Id)
                .HasColumnName(Db.OutboxMessages.Columns.Id)
                .ValueGeneratedOnAdd()
                .IsRequired();
            builder.HasKey(e => e.Id);
            builder.Property(e => e.EventType)
                .HasColumnName(Db.OutboxMessages.Columns.EventType);
            builder.Property(e => e.Content)
                .HasColumnName(Db.OutboxMessages.Columns.Content);
        }
    }

    public class DefaultOutbox : IOutbox
    {
        private readonly IOutboxStorage _outboxStorage;

        public DefaultOutbox(IOutboxStorage outboxStorage)
        {
            _outboxStorage = outboxStorage;
        }

        public async Task AddRangeAsync(IEnumerable<object> events)
        {
            await _outboxStorage.AddRangeAsync(events.Select(e => new OutboxMessage(e.GetType().FullName!, JsonSerializer.Serialize(e))));
            await _outboxStorage.SaveChangesAsync();
        }
    }

    public class EfCoreStorage<TDbContext> : IOutboxStorage
        where TDbContext : DbContext
    {
        private readonly TDbContext _dbContext;

        public EfCoreStorage(TDbContext dbContext)
        {
            _dbContext = dbContext;
        }

        public Task<List<OutboxMessage>> GetListAsync() =>
            _dbContext.Set<OutboxMessage>()
                .OrderBy(entity => entity.Id)
                .ToListAsync();

        public Task<OutboxMessage?> GetNextAsync() =>
            _dbContext.Set<OutboxMessage>()
                .OrderBy(entity => entity.Id)
                .FirstOrDefaultAsync();

        public OutboxMessage Add(OutboxMessage outboxMessage) =>
            _dbContext.Set<OutboxMessage>()
                .Add(outboxMessage).Entity;

        public async Task<OutboxMessage> AddAsync(OutboxMessage outboxMessage) =>
            (await _dbContext.Set<OutboxMessage>()
                .AddAsync(outboxMessage)).Entity;

        public void AddRange(IEnumerable<OutboxMessage> outboxMessages) =>
            _dbContext.Set<OutboxMessage>()
                .AddRange(outboxMessages);

        public Task AddRangeAsync(IEnumerable<OutboxMessage> outboxMessages) =>
            _dbContext.Set<OutboxMessage>()
                .AddRangeAsync(outboxMessages);

        public void Remove(OutboxMessage outboxMessage) =>
            _dbContext.Set<OutboxMessage>()
                .Remove(outboxMessage);

        public void RemoveRange(IEnumerable<OutboxMessage> outboxMessages) =>
            _dbContext.Set<OutboxMessage>()
                .RemoveRange(outboxMessages);

        public Task SaveChangesAsync() =>
            _dbContext.SaveChangesAsync();
    }

    public class Miner : BackgroundService
    {
        private readonly IServiceScopeFactory _serviceScopeFactory;
        private readonly IDomainEventBus _domainEventBus;
        private readonly ILogger<Miner> _logger;

        public Miner(IServiceScopeFactory serviceScopeFactory,
                     IDomainEventBus domainEventBus,
                     ILogger<Miner> logger)
        {
            _serviceScopeFactory = serviceScopeFactory;
            _domainEventBus = domainEventBus;
            _logger = logger;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    using var scope = _serviceScopeFactory.CreateScope();
                    var outboxStorage = scope.ServiceProvider.GetRequiredService<IOutboxStorage>();

                    var outboxMessage = await outboxStorage.GetNextAsync();

                    if (outboxMessage is null)
                    {
                        await Task.Delay(500, stoppingToken);
                        continue;
                    }

                    var returnType = Type.GetType(outboxMessage.EventType)!;

                    var @event = JsonSerializer.Deserialize(outboxMessage.Content, returnType);
                    await _domainEventBus.PublishAsync(@event!);

                    outboxStorage.Remove(outboxMessage);
                    await outboxStorage.SaveChangesAsync();
                }
                catch (TaskCanceledException)
                {
                    continue;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing outbox message");
                    await Task.Delay(1000, stoppingToken); // Delay longer on error
                }
            }

            _domainEventBus.Dispose();
        }
    }
}