@using Teslametrics.App.Web.Features.Main.AccessControl.Roles.Drawer.View.CameraViews
@using Teslametrics.App.Web.Features.Main.AccessControl.Roles.Drawer.View.OrganizationsConcretePermissionsComponent
@using Teslametrics.App.Web.Features.Main.AccessControl.Roles.Drawer.View.CamerasPermissions
@inherits InteractiveBaseComponent<RoleViewComponent>
<DrawerHeader>
	<MudStack Spacing="0">
		<MudText Typo="Typo.h1">Просмотр роли</MudText>
		@if (IsLoading)
		{
			<MudSkeleton Width="60%"
						 Height="calc(var(--mud-typography-subtitle1-lineheight) * var(--mud-typography-subtitle1-size))" />
		}
		@if (!IsLoading && _model is not null && _model.IsSuccess)
		{
			<MudText Typo="Typo.subtitle1">@_model.Name</MudText>
		}
		@if (!IsLoading && (_model is null || !_model.IsSuccess))
		{
			<MudText Typo="Typo.subtitle1">Не удалось получить настройки роли</MudText>
		}
	</MudStack>
	<MudSpacer />
	@if (!_subscribing && (_subscriptionResult is null || !_subscriptionResult.IsSuccess))
	{
		<MudTooltip Arrow="true"
					Placement="Placement.Start"
					Text="Ошибка подписки на события">
			<MudIconButton OnClick="SubscribeAsync"
						   Icon="@Icons.Material.Filled.ErrorOutline"
						   Color="Color.Error" />
		</MudTooltip>
	}
	<MudTooltip Text="@($"Время последнего обновления: {_lastRefreshTime.ToLocalTime()}")"
				Arrow="true"
				Placement="Placement.Start">
		<MudIconButton OnClick="RefreshAsync"
					   Icon="@Icons.Material.Filled.Refresh"
					   Color="Color.Primary" />
	</MudTooltip>
	@if (!IsLoading && _model is not null && _model.IsSuccess)
	{
		<AuthorizeView Policy="@_contextMenuAuthPolicyString"
					   Resource="new PolicyRequirementResource(OrganizationId, RoleId)"
					   Context="menuContext">
			<MudMenu Icon="@Icons.Material.Filled.MoreVert"
					 AriaLabel="Действия с выбранной ролью"
					 Color="Color.Primary"
					 Variant="Variant.Outlined">
				<AuthorizeView Policy="@(Fqdn<AppPermissions>.GetNames(AppPermissions.Main.AccessControl.Roles.Update).Last())"
							   Resource="new PolicyRequirementResource(OrganizationId, RoleId)"
							   Context="innerContext">
					<MudMenuItem OnClick="Edit"
								 Icon="@Icons.Material.Outlined.PanoramaFishEye">Редактировать роль</MudMenuItem>
				</AuthorizeView>
				<AuthorizeView Policy="@(Fqdn<AppPermissions>.GetNames(AppPermissions.Main.AccessControl.Roles.Delete).Last())"
							   Resource="new PolicyRequirementResource(OrganizationId, RoleId)"
							   Context="innerContext">
					<MudDivider Class="my-4" />
					<MudMenuItem OnClick="Delete"
								 Icon="@Icons.Material.Outlined.Delete"
								 IconColor="Color.Warning">Удалить</MudMenuItem>
				</AuthorizeView>
			</MudMenu>
		</AuthorizeView>
	}
</DrawerHeader>
<MudStack Spacing="8">
	@if (!IsLoading && _model is not null && _model.IsSuccess)
	{
		<FormSectionComponent Title="Описание роли">
			<MudTextField Value="_model.Name"
						  Clearable="false"
						  ReadOnly="true"
						  Disabled="true"
						  InputType="InputType.Text"
						  Immediate="true"
						  Label="Название роли"
						  Required="true"
						  RequiredError="Поле является обязательным"
						  HelperText="Укажите наименование роли на латинице" />

			<MudCheckBox T="bool"
						 Value="_model.IsAdmin"
						 Label="Роль администратора"
						 Color="Color.Primary"
						 Class="ml-n4"
						 ReadOnly="true" />
		</FormSectionComponent>
		<FormSectionComponent Title="Список пользователей с данной ролью">
			@if (_model.Users.Count == 0)
			{
				<MudStack AlignItems="AlignItems.Center"
						  Justify="Justify.Center"
						  Class="py-8">
					<MudIcon Icon="@Icons.Material.Outlined.PersonOff"
							 Style="font-size: 4rem;"
							 Class="mb-2" />
					<MudText Typo="Typo.subtitle1">Пользователи не назначены на данную роль</MudText>
				</MudStack>
			}
			else
			{
				<MudList T="string"
						 ReadOnly="true">
					@foreach (var user in _model.Users)
					{
						<MudListItem OnClick="() => SelectUser(user.Id)"
									 Text="@user.Username"
									 @key="user"
									 Icon="@Icons.Material.Outlined.Person" />
					}
				</MudList>
			}
		</FormSectionComponent>

		<div>
			<MudStack>
				<MudText Typo="Typo.h6">Права доступа</MudText>
				<MudText Typo="Typo.body2">
					Права доступа пользователя бывают двух типов:<br />
					@if (OrganizationId == SystemConsts.RootOrganizationId)
					{
						<MudText>1. Глобальные - на все ресурсы всех организаций</MudText>
						<MudText>2. Ресурсные - на конкретный объект в определённой области</MudText>
					}
					else
					{
						<MudText>1. Общие - на все ресурсы организации</MudText>
						<MudText>2. Ресурсные - на конкретный объект в организации</MudText>
					}
				</MudText>
			</MudStack>

			<MudTabs ApplyEffectsToContainer="true"
					 PanelClass="tab_banel pt-6 d-flex flex-column gap-5"
					 TabHeaderClass="tabs"
					 KeepPanelsAlive="false">
				<MudTabPanel Text="Контроль доступа"
							 Icon="@Icons.Material.Outlined.Key">
					<FormSectionComponent Subtitle="@(OrganizationId == SystemConsts.RootOrganizationId ? "Глобальные" : "Общие")">
						@if (OrganizationId == SystemConsts.RootOrganizationId)
						{
							<WildcardPermissionComponents TEnum="AppPermissions.Main.AccessControl.Organizations"
														  Title="Организации"
														  Selected="_model.Permissions" />
							<NoPermissionsComponent PermissionEnumTypes="@([typeof(AppPermissions.Main.AccessControl.Organizations), typeof(AppPermissions.Main.AccessControl.Users), typeof(AppPermissions.Main.AccessControl.Roles)])"
													Selected="_model.Permissions"
													IsWildcard="true" />
						}
						else
						{
							<NoPermissionsComponent PermissionEnumTypes="@([typeof(AppPermissions.Main.AccessControl.Users), typeof(AppPermissions.Main.AccessControl.Roles)])"
													Selected="_model.Permissions"
													IsWildcard="true" />
						}
						<WildcardPermissionComponents TEnum="AppPermissions.Main.AccessControl.Users"
													  Title="Пользователи"
													  Selected="_model.Permissions" />
						<WildcardPermissionComponents TEnum="AppPermissions.Main.AccessControl.Roles"
													  Title="Роли"
													  Selected="_model.Permissions" />
					</FormSectionComponent>
					@if (OrganizationId == SystemConsts.RootOrganizationId)
					{
						<FormSectionComponent Subtitle="Ресурсные">
							<OrganizationsConcretePermissionsComponent Selected="_model.Permissions"
																	   RoleId="_model.Id" />
							<NoPermissionsComponent PermissionEnumTypes="@([typeof(AppPermissions.Main.AccessControl.Organizations), typeof(AppPermissions.Main.AccessControl.Users), typeof(AppPermissions.Main.AccessControl.Roles)])"
													Selected="_model.Permissions"
													IsWildcard="false" />
						</FormSectionComponent>
					}
				</MudTabPanel>
				<MudTabPanel Text="Камеры"
							 Icon="@Icons.Material.Outlined.Camera">
					<FormSectionComponent Subtitle="@(OrganizationId == SystemConsts.RootOrganizationId ? "Глобальные" : "Общие")">
						<WildcardPermissionComponents TEnum="AppPermissions.Main.Cameras"
													  Title="Камеры"
													  Selected="_model.Permissions" />
						<WildcardPermissionComponents TEnum="AppPermissions.Main.Folders"
													  Title="Директории"
													  Selected="_model.Permissions" />
						<NoPermissionsComponent PermissionEnumTypes="@([typeof(AppPermissions.Main.Folders), typeof(AppPermissions.Main.Cameras)])"
												Selected="_model.Permissions"
												IsWildcard="true" />
					</FormSectionComponent>

					<FormSectionComponent Subtitle="Ресурсные"
										  CardContentClass="pa-0">
						<CamerasConcretePermissionsComponent Selected="_model.Permissions"
															 OrganizationId="OrganizationId"
															 ShowAdminTaggedValues="_model.IsAdmin"
															 RoleId="_model.Id" />
						<NoPermissionsComponent PermissionEnumTypes="@([typeof(AppPermissions.Main.Cameras), typeof(AppPermissions.Main.Folders)])"
												Selected="_model.Permissions"
												IsWildcard="false" />
					</FormSectionComponent>
				</MudTabPanel>

				@if (OrganizationId == SystemConsts.RootOrganizationId)
				{
					<MudTabPanel Text="Пресеты"
								 Icon="@Icons.Material.Outlined.Camera">
						<FormSectionComponent Subtitle="Глобальные">
							<WildcardPermissionComponents TEnum="AppPermissions.Main.CameraPresets"
														  Title="Пресеты камер"
														  Selected="_model.Permissions" />
							<NoPermissionsComponent PermissionEnumTypes="@([typeof(AppPermissions.Main.CameraPresets)])"
													Selected="_model.Permissions"
													IsWildcard="true" />
						</FormSectionComponent>
					</MudTabPanel>
				}

				@if (OrganizationId == SystemConsts.RootOrganizationId)
				{
					<MudTabPanel Text="Квота"
								 Icon="@Icons.Material.Outlined.Dataset">
						<FormSectionComponent Subtitle="Глобальные">
							<WildcardPermissionComponents TEnum="AppPermissions.Main.CameraQuotas"
														  Title="Квоты камер"
														  Selected="_model.Permissions" />
						</FormSectionComponent>
					</MudTabPanel>
				}

				@if (OrganizationId == SystemConsts.RootOrganizationId)
				{
					<MudTabPanel Text="Публичный доступ к камерам"
								 Icon="@Icons.Material.Outlined.Dataset">
						<FormSectionComponent Subtitle="Глобальные">
							<WildcardPermissionComponents TEnum="AppPermissions.Main.CameraPublicAccess"
														  Title="Публичный доступ к камерам"
														  Selected="_model.Permissions" />
						</FormSectionComponent>
					</MudTabPanel>
				}

				<MudTabPanel Text="Виды"
							 Icon="@Icons.Material.Filled.ViewModule">
					<FormSectionComponent Subtitle="@(OrganizationId == SystemConsts.RootOrganizationId ? "Глобальные" : "Общие")">
						<WildcardPermissionComponents TEnum="AppPermissions.Main.CameraViews"
													  Title="Виды"
													  Selected="_model.Permissions" />
					</FormSectionComponent>

					<FormSectionComponent Subtitle="Ресурсные"
										  CardContentClass="pa-0">
						<CameraViewsConcretePermissionsComponent Selected="_model.Permissions"
																 RoleId="RoleId"
																 OrganizationId="OrganizationId"
																 ShowAdminTaggedValues="_model.IsAdmin" />
					</FormSectionComponent>
				</MudTabPanel>
			</MudTabs>
		</div>
	}
	<FormLoadingComponent IsLoading="IsLoading && (_model is null || !_model.IsSuccess)" />
	<NoItemsFoundComponent HasItems="_model is not null && _model.IsSuccess"
						   LastRefreshTime="_lastRefreshTime"
						   RefreshAsync="RefreshAsync" />
</MudStack>
<DrawerActions>
	<MudSpacer />
	<MudButton OnClick="CancelAsync"
			   Variant="Variant.Outlined"
			   StartIcon="@Icons.Material.Outlined.Close">Закрыть</MudButton>
	@if (IsLoading)
	{
		<MudSkeleton Width="150px"
					 Height="36.5px" />
	}
	else
	{
		<AuthorizeView Policy="@AppPermissions.Main.AccessControl.Roles.Update.GetEnumPermissionString()"
					   Resource="new PolicyRequirementResource(OrganizationId, RoleId)"
					   Context="innerContext">
			<MudButton OnClick="Edit"
					   Color="Color.Secondary"
					   Variant="Variant.Outlined">Редактировать</MudButton>
		</AuthorizeView>
	}
</DrawerActions>