using Microsoft.AspNetCore.Components;
using System.Reactive;
using System.Text;
using Teslametrics.App.Web.Components.Drawer;
using Teslametrics.App.Web.Events.Presets;
using Teslametrics.App.Web.Extensions;
using Teslametrics.App.Web.Shared;

namespace Teslametrics.App.Web.Features.Main.Presets.Drawer.View;

public partial class PresetViewComponent
{
	[CascadingParameter(Name = DrawerConsts.InstanceName)]
	private DrawerComponent Drawer { get; set; } = null!;
	private string _contextMenuAuthPolicyString => new StringBuilder()
		.Append("," + AppPermissions.Main.CameraPresets.Update.GetEnumPermissionString())
		.Append("," + AppPermissions.Main.CameraPresets.Delete.GetEnumPermissionString())
		.ToString();// Есть вероятность, что спереди будет запятая.

	private DateTime _lastUpdateTime = DateTime.UtcNow;
	private bool _subscribing;
	private GetCameraPresetUseCase.Response? _model;
	private SubscribeCameraPresetUseCase.Response? _subscriptionResult;

	#region Parameters
	[Parameter]
	[EditorRequired]
	public Guid PresetId { get; set; }
	#endregion

	protected override async Task OnParametersSetAsync()
	{
		await base.OnParametersSetAsync();

		if (_model is null || _model.Id != PresetId)
		{
			_model = null;
			await FetchAsync();
		}
	}


	private async Task FetchAsync()
	{
		try
		{
			await SetLoadingAsync(true);
			_model = await ScopeFactory.MediatorSend(new GetCameraPresetUseCase.Query(PresetId));
			_lastUpdateTime = DateTime.UtcNow;
			await SetLoadingAsync(false);
			switch (_model.Result)
			{
				case GetCameraPresetUseCase.Result.Success:
					await SubscribeAsync();
					break;
				case GetCameraPresetUseCase.Result.ValidationError:
					Snackbar.Add("Ошибка валидации данных", MudBlazor.Severity.Error);
					break;
				case GetCameraPresetUseCase.Result.Unknown:
				default:
					throw new Exception($"Unexpected result in {nameof(GetCameraPresetUseCase)}: {_model.Result}");
			}
		}
		catch (Exception ex)
		{
			await SetLoadingAsync(false);
			Snackbar.Add("Не удалось получить пресет из-за непредвиденной ошибки. Повторите попытку", MudBlazor.Severity.Error);
			Logger.LogError(ex, ex.Message);
		}
	}


	private async Task SubscribeAsync()
	{
		try
		{
			Unsubscribe();

			await SetSubscribingAsync(true);
			_subscriptionResult = await ScopeFactory.MediatorSend(new SubscribeCameraPresetUseCase.Request(Observer.Create<object>(OnAppEventHandler, OnError), PresetId));
			await SetSubscribingAsync(false);
			switch (_subscriptionResult.Result)
			{
				case SubscribeCameraPresetUseCase.Result.Success:
					CompositeDisposable.Add(_subscriptionResult.Subscription!);
					break;
				case SubscribeCameraPresetUseCase.Result.ValidationError:
					Snackbar.Add("Ошибка валидации при подписке на события", MudBlazor.Severity.Error);
					break;
				case SubscribeCameraPresetUseCase.Result.PresetNotFound:
					Snackbar.Add("Ошибка подписки на события пресета", MudBlazor.Severity.Error);
					break;
				case SubscribeCameraPresetUseCase.Result.Unknown:
				default:
					throw new Exception($"Unexpected result in {nameof(SubscribeCameraPresetUseCase)}: {_subscriptionResult.Result}");
			}
		}
		catch (Exception ex)
		{
			await SetSubscribingAsync(false);
			Snackbar.Add("Не удалось получить подписку на события пресета из-за непредвиденной ошибки. Повторите попытку", MudBlazor.Severity.Error);
			Logger.LogError(ex, ex.Message);
		}
	}
	private void Unsubscribe()
	{
		if (_subscriptionResult?.Subscription is not null)
		{
			CompositeDisposable.Remove(_subscriptionResult.Subscription);
			_subscriptionResult.Subscription.Dispose();
		}
	}

	protected Task SetSubscribingAsync(bool isLoading = true) => UpdateViewAsync(() =>
	{
		_subscribing = isLoading;
	});

	#region [Actions]
	private Task RefreshAsync() => FetchAsync();

	private Task CancelAsync()
	{
		Unsubscribe();
		_model = null;
		return Drawer.HideAsync();
	}

	private void Edit() => EventSystem.Publish(new PresetEditEto(PresetId));

	private void Delete() => EventSystem.Publish(new PresetDeleteEto(PresetId));
	#endregion

	#region [Event Handlers]
	private async void OnAppEventHandler(object appEvent)
	{
		switch (appEvent)
		{
			case SubscribeCameraPresetUseCase.UpdatedEvent updatedEto:
				await FetchAsync();
				await UpdateViewAsync();
				break;

			case SubscribeCameraPresetUseCase.DeletedEvent deletedEto:
				Snackbar.Add("Просматриваемый пресет был удалён", MudBlazor.Severity.Warning);
				await CancelAsync();
				break;

			default:
				Snackbar.Add("Было получено непредвиденное событие.", MudBlazor.Severity.Warning);
				await FetchAsync();
				await UpdateViewAsync();
				Logger.LogWarning("Unexpected event in {UseCase}: {Event}", nameof(SubscribeCameraPresetUseCase), nameof(appEvent));
				break;
		}
	}

	private void OnError(Exception exc)
	{
		Snackbar.Add("Ошибка при подписке на события", MudBlazor.Severity.Error);
		Logger.LogError(exc, exc.Message);
	}
	#endregion [Event Handlers]
}
