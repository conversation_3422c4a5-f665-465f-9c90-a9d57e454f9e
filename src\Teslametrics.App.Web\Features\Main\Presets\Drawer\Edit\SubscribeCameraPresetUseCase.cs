using System.Reactive.Linq;
using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Domain.CameraPresets.Events;
using Teslametrics.App.Web.Exceptions;
using Teslametrics.App.Web.Services.DomainEventBus;

namespace Teslametrics.App.Web.Features.Main.Presets.Drawer.Edit;

public static class SubscribeCameraPresetUseCase// TODO: Kirill | реализация подписки на измнение пресета
{
    public record Request(IObserver<object> Observer, Guid PresetId) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public IDisposable? Subscription { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(IDisposable subscription)
        {
            Subscription = subscription;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Subscription = null;
            Result = result;
        }
    }

    public record UpdatedEvent(Guid Id);
    public record DeletedEvent(Guid Id);

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        PresetNotFound
    }

    public class Validator : AbstractValidator<Request>
    {
        public Validator()
        {
            RuleFor(r => r.Observer).NotEmpty();
            RuleFor(r => r.PresetId).NotEmpty();
        }
    }

        public class Handler : IRequestHandler<Request, Response>
    {
        private readonly IDomainEventBus _domainEventBus;
        private readonly IValidator<Request> _validator;

        public Handler(IDomainEventBus domainEventBus,
                       IValidator<Request> validator)
        {
            _domainEventBus = domainEventBus;
            _validator = validator;
        }

        public async Task<Response> Handle(Request request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            var presetId = request.PresetId;

            var eventStream = await _domainEventBus.GetEventStreamAsync();

            var subscription = eventStream
                .Where(e => e switch
                {
                    PresetUpdatedEvent @event => @event.Id == presetId,
                    PresetDeletedEvent @event => @event.Id == presetId,
                    _ => false
                })
                .Select<object, object>(e => e switch
                {
                    PresetUpdatedEvent @event => new UpdatedEvent(@event.Id),
                    PresetDeletedEvent @event => new DeletedEvent(@event.Id),
                    _ => throw new AppException("Invalid event type")
                })
                .Subscribe(request.Observer);

            return new Response(subscription);
        }
    }
}
