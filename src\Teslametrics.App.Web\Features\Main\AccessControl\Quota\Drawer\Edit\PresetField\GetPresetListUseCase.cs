using Dapper;
using FluentValidation;
using MediatR;
using System.Data;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.Shared;
using Teslametrics.Core.Services.Persistence;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Quota.Drawer.Edit.PresetField;

public static class GetPresetListUseCase
{
    public record Query(int Offset, int Limit, string Filter) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public List<Item> Items { get; init; }

        public int TotalCount { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(List<Item> items, int totalCount)
        {
            Items = items;
            TotalCount = totalCount;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Result = result;

            Items = [];
            TotalCount = 0;
        }

        public record Item(Guid Id, string Name);
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError
    }

    public class Validator : AbstractValidator<Query>
    {
        public Validator()
        {
            RuleFor(q => q.Offset).GreaterThanOrEqualTo(0);
            RuleFor(q => q.Limit).GreaterThan(0);
            RuleFor(q => q.Filter).MaximumLength(60);
        }
    }

    public class Handler : IRequestHandler<Query, Response>
    {
        private readonly IValidator<Query> _validator;
        private readonly IDbConnection _dbConnection;

        public Handler(IValidator<Query> validator,
                       IDbConnection dbConnection)
        {
            _validator = validator;
            _dbConnection = dbConnection;
        }

        public async Task<Response> Handle(Query request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            var builder = SqlQueryBuilder.Create()
                .WhereIf(!string.IsNullOrEmpty(request.Filter), Db.Presets.Props.Name, "CONCAT('%', :Filter, '%')", SqlOperator.Like, new { request.Filter });

            var countTemplate = builder.Build(QueryType.Standard, Db.Presets.Table, RowSelection.AllRows, [$"COUNT(DISTINCT {Db.Presets.Props.Id})"]);
            var totalCount = await _dbConnection.ExecuteScalarAsync<int>(countTemplate.RawSql, countTemplate.Parameters);

            var selectTemplate = builder.Build(QueryType.Paginated,
                                               Db.Presets.Table,
                                               RowSelection.AllRows,
                                               [
                                                   Db.Presets.Props.Id,
                                                   Db.Presets.Props.Name
                                               ],
                                               new { request.Limit, request.Offset });
            var presets = await _dbConnection.QueryAsync<CameraPresetsModel>(selectTemplate.RawSql, selectTemplate.Parameters);

            return new Response(presets.Select(q => new Response.Item(q.Id, q.Name)).ToList(), totalCount);
        }
    }

    public record CameraPresetsModel(Guid Id, string Name);
}