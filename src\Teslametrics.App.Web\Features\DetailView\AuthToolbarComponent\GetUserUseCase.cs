﻿using Dapper;
using FluentValidation;
using MediatR;
using System.Data;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Data.Sql;
using Teslametrics.App.Web.Services.Persistence;

namespace Teslametrics.App.Web.Features.DetailView.AuthToolbarComponent;

public static class GetUserUseCase
{
	public record Query(Guid Id) : BaseRequest<Response>;

	public record Response : BaseResponse
	{
		public Guid Id { get; init; }

		public string Username { get; init; }

		public Result Result { get; init; }

		public bool IsSuccess => Result == Result.Success;

		public Response(Guid id, string username)
		{
			Id = id;
			Username = username;
			Result = Result.Success;
		}

		public Response(Result result)
		{
			if (result == Result.Success)
			{
				throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
			}

			Result = result;

			Id = Guid.Empty;
			Username = string.Empty;
		}

		public record Role(Guid Id, string Name, IEnumerable<string> Permissions);
	}

	public enum Result
	{
		Unknown = 0,
		Success,
		ValidationError,
		UserNotFound
	}

	public class Validator : AbstractValidator<Query>
	{
		public Validator()
		{
			RuleFor(q => q.Id).NotEmpty();
		}
	}

	public class Handler : IRequestHandler<Query, Response>
	{
		private readonly IValidator<Query> _validator;
		private readonly IDbConnection _dbConnection;

		public Handler(IValidator<Query> validator,
					   IDbConnection dbConnection)
		{
			_validator = validator;
			_dbConnection = dbConnection;
		}

		public async Task<Response> Handle(Query request, CancellationToken cancellationToken)
		{
			if (!_validator.Validate(request).IsValid)
			{
				return new Response(Result.ValidationError);
			}

			var template = SqlQueryBuilder.Create()
				.Select(Db.Users.Props.Id)
				.Select(Db.Users.Props.Name)
				.Where(Db.Users.Props.Id, ":Id", SqlOperator.Equals, new { request.Id })
				.Build(QueryType.Standard, Db.Users.Table, RowSelection.AllRows);

			var user = await _dbConnection.QuerySingleOrDefaultAsync<UserModel>(template.RawSql, template.Parameters);

			if (user is null)
			{
				return new Response(Result.UserNotFound);
			}

			return new Response(user.Id,
								user.Name);
		}
	}

	public record UserModel(Guid Id, string Name);
}