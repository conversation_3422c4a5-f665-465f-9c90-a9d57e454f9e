
using Microsoft.AspNetCore.Components;
using Teslametrics.App.Web.Components.Drawer;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Organizations.Drawer;

public partial class FailedToRetrieveDataComponent
{
	#region Parameters

	[CascadingParameter(Name = DrawerConsts.InstanceName)]
	private DrawerComponent Drawer { get; set; } = null!;
	#endregion

	#region [Action]
	private Task CancelAsync() => Drawer.HideAsync();
	#endregion
}

