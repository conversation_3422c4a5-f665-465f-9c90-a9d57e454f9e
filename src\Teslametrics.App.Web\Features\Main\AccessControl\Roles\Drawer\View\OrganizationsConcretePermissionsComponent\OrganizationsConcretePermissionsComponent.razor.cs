using Microsoft.AspNetCore.Components;
using MudBlazor;
using System.Reactive;
using System.Text;
using Teslametrics.App.Web.Domain.AccessControl;
using Teslametrics.App.Web.Exceptions;
using Teslametrics.App.Web.Extensions;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Roles.Drawer.View.OrganizationsConcretePermissionsComponent;

public partial class OrganizationsConcretePermissionsComponent
{
	private IEnumerable<ResourcePermission> _wildcardPermissions => Selected.Where(x => x.ResourceId.IsWildcard);

	private record Organization(Guid Id, string Name);

	private GetOrganizationListUseCase.Response? _response;

	[Parameter]
	[EditorRequired]
	public IEnumerable<ResourcePermission> Selected { get; set; } = [];

	[Parameter]
	[EditorRequired]
	public Guid RoleId { get; set; }

	protected override async Task OnInitializedAsync()
	{
		await base.OnInitializedAsync();
		await SubscribeAsync();
		await FetchAsync();
	}

	private async Task FetchAsync()
	{
		try
		{
			await SetLoadingAsync();
			var userId = await GetCurrentUserIdAsync() ?? throw new NotAuthorizedException();
			_response = await ScopeFactory.MediatorSend(new GetOrganizationListUseCase.Query(RoleId));
			switch (_response.Result)
			{
				case GetOrganizationListUseCase.Result.Success:
					await SubscribeAsync();
					break;
				case GetOrganizationListUseCase.Result.ValidationError:
					Snackbar.Add("Ошибка валидации при получении списка организаций", Severity.Error);
					break;
				default:
				case GetOrganizationListUseCase.Result.Unknown:
					throw new Exception($"Unexpected result in {nameof(GetRoleUseCase)}: {_response.Result}");
			}
		}
		catch (Exception ex)
		{
			Snackbar.Add("Не удалось получить список организаций повторите попытку. Если проблема сохраняется - обратитесь к администратору", Severity.Error);
			Logger.LogError(ex, ex.Message);
		}
	}

	private async Task SubscribeAsync()
	{
		var userId = await GetCurrentUserIdAsync() ?? throw new NotAuthorizedException();
		var result = await ScopeFactory.MediatorSend(new SubscribeOrganizationListUseCase.Request(Observer.Create<object>(OnAppEventHandler, OnError), userId));
		switch (result.Result)
		{
			case SubscribeOrganizationListUseCase.Result.Success:
				CompositeDisposable.Add(result.Subscription!);
				break;
			case SubscribeOrganizationListUseCase.Result.ValidationError:
				Snackbar.Add("Ошибка валидации при подписке на события", Severity.Error);
				break;

			case SubscribeOrganizationListUseCase.Result.Unknown:
			default:
				Snackbar.Add("Не удалось получить подписку на обновления из-за непредвиденной ошибки:" + result.Result.ToString(), Severity.Error);
				break;
		}
	}

	private string GetPermissionString(AppPermissions.Main.AccessControl.Organizations value)
	{
		return new StringBuilder("Main.AccessControl.Organizations.").Append(value).ToString();
	}

	private bool ContainsPermission(IEnumerable<ResourcePermission> permissions, AppPermissions.Main.AccessControl.Organizations value)
	{
		return permissions.Any(x => x.Permission.Value == GetPermissionString(value));
	}

	private bool ContainsPermission(IEnumerable<ResourcePermission> permissions, AppPermissions.Main.AccessControl.Organizations value, Guid resourceId)
	{
		return permissions.Any(x => x.ResourceId.Value == resourceId && x.Permission.Value == GetPermissionString(value));
	}

	#region [EventHandlers]
	private async void OnAppEventHandler(object appEvent)
	{
		await FetchAsync();
		await UpdateViewAsync();
	}

	private void OnError(Exception exc)
	{
		Logger.LogError(exc, exc.Message);
		Snackbar.Add("Ошибка валидации при подписке на события", Severity.Error);
	}
	#endregion
}
