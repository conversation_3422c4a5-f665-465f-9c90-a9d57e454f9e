@using MudBlazor
@using System.Text
@using Teslametrics.App.Web.Abstractions
@using Microsoft.JSInterop

<div class="d-contents">
	<MudDrawer Open="@Open"
			   Overlay="@Options.Overlay"
			   OverlayAutoClose="Options.OverlayAutoClose"
			   OpenChanged="OpenToggle"
			   Class="drawer"
			   Width="@Options.Width"
			   Anchor="Options.Anchor"
			   Elevation="1"
			   Variant="@Options.Variant"
			   ClipMode="Options.ClipMode"
			   UserAttributes="@_drawerAttributes">
		<MudStack Row="true"
				  Spacing="0"
				  Class="mud-height-full"
				  Reverse="Options.Anchor == Anchor.Left">
			<MudDivider Vertical="true"
						Class="dragger"
						@onclick:stopPropagation="true"
						@onmousemove:stopPropagation="true"
						@onmousedown:stopPropagation="true" />
			<div class="mud-width-full drawer_content">
				<CascadingValue Name="@DrawerConsts.InstanceName"
								Value="this">
					<HeaderContent Header="_header" />
					<DrawerBody ChildContent="@ChildContent" />
					<ActionsContent Actions="_actions" />
				</CascadingValue>
			</div>
		</MudStack>
	</MudDrawer>
</div>

@code
{
	DrawerHeader? _header;
	DrawerActions? _actions;

	public void SetHeader(DrawerHeader? header)
	{
		_header = header;
		Update();
	}

	public void SetActions(DrawerActions? actions)
	{
		_actions = actions;
		Update();
	}

	public void Update() => StateHasChanged();

	#region User Attributes
	private Dictionary<string, object> _closeAttributes => new() { { "data-id", _instanceId } };
	#endregion

	private Anchor Anchor => FromRighth ? Anchor.End : Anchor.Start;

	private void OnHeaderChange() => Update();
}
