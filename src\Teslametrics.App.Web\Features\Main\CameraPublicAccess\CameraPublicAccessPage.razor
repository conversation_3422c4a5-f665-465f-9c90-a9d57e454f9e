﻿@using Teslametrics.App.Web.Features.Main.CameraPublicAccess.DeleteDialog
@using Teslametrics.App.Web.Features.Main.CameraPublicAccess.Drawer
@using Teslametrics.App.Web.Features.Main.CameraPublicAccess.ReissueUrlDialog
@attribute [Route(RouteConstants.CameraPublicAccess)]
@attribute [AppAuthorize(AppPermissions.Main.CameraPublicAccess.Read)]
<MudContainer MaxWidth="MaxWidth.False"
			  Class="mud-height-full pt-4 pb-4 ">
	<MudStack Spacing="2"
			  Class="mud-height-full">
		<MudPaper>
			<TreeTableView />
		</MudPaper>
	</MudStack>
</MudContainer>
<DeletePublicAccessDialog />
<ReissueUrlDialog />
<CameraPublicAccessDrawer />