using ArchUnitNET.Domain;
using ArchUnitNET.Domain.Extensions;
using ArchUnitNET.Loader;
using ArchUnitNET.xUnit;
using static ArchUnitNET.Fluent.ArchRuleDefinition;

namespace ArchTests;

public class OrleansTests
{
    private static readonly Architecture Architecture;

    private static readonly IObjectProvider<Interface> GrainInterface;

    static OrleansTests()
    {
        Architecture = new ArchLoader().LoadAssemblies(
            System.Reflection.Assembly.Load("Teslametrics.App.Web")
        ).Build();

        GrainInterface = Interfaces().That().AreAssignableTo(Architecture.GetInterfaceOfType(typeof(IGrainWithGuidKey)));
    }

    [Fact]
    public void DtoShouldBeRecord() =>
        Classes()
        .That()
        .AreNestedIn(GrainInterface)
        .Should()
        .BeRecord()
        .Check(Architecture);

    [Fact]
    public void DtoShouldHaveGenerateSerializerAttribute() =>
        Classes()
        .That()
        .AreNestedIn(GrainInterface)
        .Should()
        .HaveAnyAttributes(typeof(GenerateSerializerAttribute))
        .Check(Architecture);

    [Fact]
    public void EtoShouldBeRecord() =>
        Classes()
        .That()
        .HaveNameEndingWith("Event")
        .And()
        .ResideInNamespace(@"/*.Orleans.Events$", true)
        .And()
        .DoNotHaveFullNameContaining("Codec_")
        .And()
        .DoNotHaveFullNameContaining("Copier_")
        .Should()
        .BeRecord()
        .Check(Architecture);

    [Fact]
    public void EtoShouldHaveGenerateSerializerAttribute() =>
        Classes()
        .That()
        .HaveNameEndingWith("Event")
        .And()
        .ResideInNamespace(@"/*.Orleans.Events$", true)
        .And()
        .DoNotHaveFullNameContaining("Codec_")
        .And()
        .DoNotHaveFullNameContaining("Copier_")
        .Should()
        .HaveAnyAttributes(typeof(GenerateSerializerAttribute))
        .Check(Architecture);
}