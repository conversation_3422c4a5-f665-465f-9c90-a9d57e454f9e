using FFMpegNET;
using Orleans.Concurrency;
using Orleans.Streams;
using Orleans.Utilities;

namespace Teslametrics.App.Web.Orleans.Camera;

public class CameraStreamPreviewGrain : Grain, ICameraStreamPreviewGrain
{
    private readonly ILogger<CameraStreamPreviewGrain> _logger;
    private readonly MicroSegmentPreviewGenerator _previewGenerator;
    private readonly ObserverManager<IPreviewObserver> _observerManager;
    private byte[]? _preview;
    private IAsyncStream<MicroSegment>? _videoStream;

    public CameraStreamPreviewGrain(ILogger<CameraStreamPreviewGrain> logger,
                                    MicroSegmentPreviewGenerator previewGenerator)
    {
        _logger = logger;
        _previewGenerator = previewGenerator;

        _observerManager = new ObserverManager<IPreviewObserver>(TimeSpan.FromMinutes(5), logger);
    }

    public override async Task OnActivateAsync(CancellationToken cancellationToken)
    {
        var streamProvider = this.GetStreamProvider(StreamNames.VideoLiveStream);

        var provider = this.GetStreamProvider(StreamNames.VideoLiveStream);
        _videoStream = provider.GetStream<MicroSegment>(StreamId.Create(StreamNamespaces.CameraStreams, this.GetPrimaryKey()));

        var sub = await _videoStream.SubscribeAsync(async (segment, token) =>
        {
            var microSegment = new FFMpegNET.MicroSegment(segment.Payload, segment.StartTime, segment.Duration);

            var newPreview = _previewGenerator.GeneratePreview(microSegment);

            if (newPreview is not null)
            {
                _preview = newPreview;
                await _observerManager.Notify(s => s.PreviewUpdated(new Events.CameraPreviewImageUpdatedEvent(this.GetPrimaryKey())));
            }
        });

        await base.OnActivateAsync(cancellationToken);
    }

    public override async Task OnDeactivateAsync(DeactivationReason reason, CancellationToken cancellationToken)
    {
        _previewGenerator.Dispose();
        await base.OnDeactivateAsync(reason, cancellationToken);
    }

    public Task<byte[]?> GetPreviewAsync()
    {
        return Task.FromResult(_preview);
    }

    public Task SubscribeAsync(ICameraStreamPreviewGrain.SubscribeRequest request)
    {
        _observerManager.Subscribe(request.Observer, request.Observer);
        _observerManager.ClearExpired();

        return Task.CompletedTask;
    }

    public Task UnsubscribeAsync(ICameraStreamPreviewGrain.UnsubscribeRequest request)
    {
        _observerManager.Unsubscribe(request.Observer);
        _observerManager.ClearExpired();

        return Task.CompletedTask;
    }
}

[Alias("Teslametrics.App.Web.Orleans.ICameraStreamPreviewGrain")]
public interface ICameraStreamPreviewGrain : IGrainWithGuidKey
{
    [ReadOnly]
    [Alias("GetPreviewAsync")]
    public Task<byte[]?> GetPreviewAsync();

    [Alias("SubscribeAsync")]
    Task SubscribeAsync(SubscribeRequest request);

    [Alias("UnsubscribeAsync")]
    Task UnsubscribeAsync(UnsubscribeRequest request);

    [GenerateSerializer]
    [Alias("Teslametrics.App.Web.Orleans.ICameraStreamPreviewGrain.SubscribeRequest")]
    public record SubscribeRequest(IPreviewObserver Observer);

    [GenerateSerializer]
    [Alias("Teslametrics.App.Web.Orleans.Camera.ICameraStreamPreviewGrain.UnsubscribeRequest")]
    public record UnsubscribeRequest(IPreviewObserver Observer);
}