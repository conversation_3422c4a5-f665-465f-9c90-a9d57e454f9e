using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;

namespace Teslametrics.App.Web.Components;

public partial class ImageComponent : IAsyncDisposable
{
    /// <summary>Путь к заглушке, если ещё ни одна картинка не загрузилась.</summary>
    [Parameter] public string PlaceholderUrl { get; set; } = "/no-image.png";

    private ElementReference _imgElement;
    private readonly string _imageId = $"img_{Guid.NewGuid()}";
    private IJSObjectReference? _imageLoaderModule;
    private DotNetObjectReference<ImageComponent>? _dotNetReference;
    private string _currentUrl = string.Empty;          // то, что показано пользовательскому <img>
    private string _pendingUrl = string.Empty;          // url, который пытаемся получить прямо сейчас
    private bool _triggerLoad;

    [Inject]
    public IJSRuntime JSRuntime { get; set; } = null!;

    [Parameter]
    [EditorRequired]
    public string ImageUrl { get; set; } = string.Empty;

    [Parameter]
    public string ErrorMessage { get; set; } = "Изображение недоступно. Нажмите для повтора.";

    protected override void OnInitialized()
    {
        // пока не было ни одной удачной картинки – показываем заглушку
        _currentUrl = PlaceholderUrl;
    }

    protected override void OnParametersSet()
    {
        if (string.IsNullOrWhiteSpace(_currentUrl) && !string.IsNullOrWhiteSpace(ImageUrl))
            _currentUrl = ImageUrl;

        if (!string.IsNullOrWhiteSpace(ImageUrl) && ImageUrl != _pendingUrl)
        {
            _pendingUrl = ImageUrl;
            _triggerLoad = true;
        }
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            try
            {
                _dotNetReference = DotNetObjectReference.Create(this);
                _imageLoaderModule = await JSRuntime.InvokeAsync<IJSObjectReference>("import", "./Components/ImageComponent.razor.js");
            }
            catch (JSDisconnectedException) // https://learn.microsoft.com/en-us/aspnet/core/blazor/javascript-interoperability/?view=aspnetcore-9.0
            {
            }
            catch (TaskCanceledException)
            {
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, ex.Message);
            }
        }
        try
        {
            if (_triggerLoad && _imageLoaderModule is not null)
            {
                await _imageLoaderModule.InvokeVoidAsync("loadImageWithRetry", _imgElement, _pendingUrl);
            }
        }
        catch (TaskCanceledException)
        {
        }
        catch (JSDisconnectedException) // https://learn.microsoft.com/en-us/aspnet/core/blazor/javascript-interoperability/?view=aspnetcore-9.0
        {
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, ex.Message);
        }
    }

    public async ValueTask DisposeAsync()
    {
        try
        {
            if (_imageLoaderModule != null)
            {
                await _imageLoaderModule.DisposeAsync();
            }

            _dotNetReference?.Dispose();
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Ошибка при освобождении ресурсов: {ex.Message}");
        }

        GC.SuppressFinalize(this);
    }
}
