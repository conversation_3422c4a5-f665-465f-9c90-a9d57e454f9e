# Двухфакторная аутентификация (2FA) в Teslametrics

## Обзор

Данная документация описывает реализацию двухфакторной аутентификации (2FA) на основе TOTP (Time-based One-Time Password) алгоритма в проекте Teslametrics. Реализация использует библиотеку Otp.NET для генерации и проверки TOTP кодов.

## Компоненты системы

1. **TwoFactorAuthService** - сервис для работы с 2FA
2. **TwoFactorAuthRepository** - репозиторий для хранения данных 2FA
3. **UserTwoFactorAuthEntity** - модель данных для хранения информации о 2FA пользователя
4. **TwoFactorAuthSetup.razor** - компонент Blazor для настройки 2FA
5. **TwoFactorAuthVerify.razor** - компонент Blazor для проверки 2FA при входе

## Процесс настройки 2FA пользователем

1. Пользователь переходит на страницу настройки 2FA (`/account/two-factor-auth/setup`)
2. Система генерирует секретный ключ и QR-код
3. Пользователь сканирует QR-код с помощью приложения аутентификатора (Google Authenticator, Microsoft Authenticator, Authy и др.)
4. Пользователь вводит код из приложения для подтверждения настройки
5. Система проверяет код и активирует 2FA для пользователя
6. Система генерирует резервные коды восстановления и показывает их пользователю

## Процесс входа с 2FA

1. Пользователь вводит логин и пароль
2. Если для пользователя включена 2FA, система перенаправляет его на страницу проверки 2FA (`/login/verify-2fa`)
3. Пользователь вводит код из приложения аутентификатора
4. Система проверяет код и, если он верный, завершает процесс входа

## Использование резервных кодов восстановления

Если пользователь потерял доступ к приложению аутентификатора, он может использовать один из резервных кодов восстановления:

1. На странице проверки 2FA пользователь нажимает "Использовать код восстановления"
2. Пользователь вводит один из резервных кодов
3. Система проверяет код и, если он верный, завершает процесс входа
4. Использованный код восстановления удаляется из системы

## Отключение 2FA

Пользователь может отключить 2FA в настройках своего профиля:

1. Пользователь переходит на страницу настроек профиля
2. Пользователь нажимает кнопку "Отключить 2FA"
3. Система запрашивает подтверждение
4. После подтверждения система отключает 2FA для пользователя

## API для работы с 2FA

### TwoFactorAuthService

```csharp
// Генерация секретного ключа
string secretKey = _twoFactorAuthService.GenerateSecretKey();

// Создание URL для QR-кода
string qrCodeUrl = _twoFactorAuthService.GenerateQrCodeUrl(secretKey, userEmail, "Teslametrics");

// Настройка 2FA для пользователя
var twoFactorAuthInfo = await _twoFactorAuthService.SetupTwoFactorAuthAsync(userId, secretKey);

// Проверка TOTP кода
bool isValid = _twoFactorAuthService.VerifyTotpCode(secretKey, userCode);

// Включение 2FA
await _twoFactorAuthService.EnableTwoFactorAuthAsync(userId);

// Отключение 2FA
await _twoFactorAuthService.DisableTwoFactorAuthAsync(userId);

// Проверка, включена ли 2FA
bool isEnabled = await _twoFactorAuthService.IsTwoFactorEnabledAsync(userId);

// Проверка кода восстановления
bool isValidRecoveryCode = await _twoFactorAuthService.VerifyAndRemoveRecoveryCodeAsync(userId, recoveryCode);

// Обновление кодов восстановления
List<string> newRecoveryCodes = await _twoFactorAuthService.RegenerateRecoveryCodesAsync(userId);
```

## Миграция базы данных

Для применения миграции базы данных выполните следующую команду:

```bash
cd src/Teslametrics.DbMigrator
dotnet run
```

## Рекомендации по безопасности

1. **Секретный ключ** - храните секретный ключ в зашифрованном виде или используйте защищенное хранилище
2. **Резервные коды** - храните резервные коды в хешированном виде
3. **Проверка кодов** - используйте временное окно для проверки кодов (RFC рекомендует ±1 шаг)
4. **Одноразовое использование** - каждый код должен использоваться только один раз
5. **Защита от перебора** - ограничьте количество попыток ввода кода

## Зависимости

- **Otp.NET** - библиотека для генерации и проверки TOTP кодов
- **QR-код** - для генерации QR-кодов используется Google Chart API

## Дополнительные ресурсы

- [RFC 6238](https://tools.ietf.org/html/rfc6238) - TOTP: Time-Based One-Time Password Algorithm
- [RFC 4226](https://tools.ietf.org/html/rfc4226) - HOTP: An HMAC-Based One-Time Password Algorithm
- [Otp.NET на GitHub](https://github.com/kspearrin/Otp.NET)
- [Google Authenticator Key URI Format](https://github.com/google/google-authenticator/wiki/Key-Uri-Format)
