﻿@inherits InteractiveBaseComponent
<MudStack Row="true"
		  AlignItems="AlignItems.Center"
		  Class="mud-width-full mb-4">
	<MudStack Spacing="0">
		<MudText>Добавление пользователя в организацию</MudText>
	</MudStack>
</MudStack>

<MudForm Model="_model"
		 Validation="_validator.ValidateValue"
		 @bind-IsValid="_isValid"
		 Class="flex-1"
		 OverrideFieldValidation="true">
	<MudStack Spacing="0"
			  Class="mb-2">
		<MudText Typo="Typo.h6">Описание пользователя</MudText>
		<MudText Typo="Typo.subtitle2">Настройки, которые влияют только на восприятие человеком</MudText>
	</MudStack>

	<MudCard>
		<MudCardContent>
			@* <MudTextField @bind-Value="_model.Username"
						  For="@(() => _model.Username)"
						  Clearable="true"
						  InputType="InputType.Text"
						  Immediate="true"
						  Label="Логин"
						  HelperText="Текст в данном поле будет использовать пользователь для входа в систему"
						  RequiredError="Данное поле обязательно"
						  Required="true" /> *@
		</MudCardContent>
	</MudCard>

	<MudStack Spacing="0"
			  Class="mb-2 mt-6">
		<MudText Typo="Typo.h6">Параметры пользователя</MudText>
		<MudText Typo="Typo.subtitle2">Данные настройки важны для работы в системе</MudText>
	</MudStack>
	<MudCard>
		<MudCardContent>
			@* <MudSelect T="RoleModel"
					   Label="Роли пользователя"
					   MultiSelection="true"
					   Immediate="true"
					   HelperText="Укажите роли пользователя в переносимую организацию"
					   Required="true"
					   @bind-SelectedValues="_model.Roles"
					   ToStringFunc="(item) => item.Name"
					   RequiredError="Поле является обязательным"
					   SelectAll="true"
					   Clearable="@false"
					   SelectAllText="Выбрать все">
				@foreach (var item in _roles)
				{
					<MudSelectItem T="RoleModel" Value="@item" @key="item">@item.Name</MudSelectItem>
				}
			</MudSelect> *@
		</MudCardContent>
	</MudCard>
</MudForm>

<MudStack Row="true"
		  Class="mt-4">
	<MudSpacer />
	@* 	<AuthorizeView Policy="@(Fqdn<AppPermissions>.GetNames(AppPermissions.Main.AccessControl.Users.Create).Last())" Context="innerContext">
		<MudButton OnClick="SubmitAsync" Disabled="@(!_isValid)" Color="Color.Secondary" Variant="Variant.Outlined">Сохранить</MudButton>
	</AuthorizeView> *@
</MudStack>