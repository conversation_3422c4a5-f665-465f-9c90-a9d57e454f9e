using System.Data;
using Dapper;
using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.Shared;
using Teslametrics.Core.Services.Persistence;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Quota.Drawer.Edit;

public static class GetQuotaUseCase
{
    public record Query(Guid QuotaId) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
        public Guid? PresetId { get; set; }
        public string? PresetName { get; set; }
        public int UsedQuota { get; set; }
        public int TotalQuota { get; set; }
        public int RetentionPeriodDays { get; set; }
        public int StorageLimitMb { get; set; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(Guid id, string name, Guid? presetId, string? presetName, int usedQuota, int totalQuota, int rentionPeriodDays, int storageLimitMb)
        {
            Id = id;
            Name = name;
            PresetId = presetId;
            PresetName = presetName;
            UsedQuota = usedQuota;
            TotalQuota = totalQuota;
            RetentionPeriodDays = rentionPeriodDays;
            StorageLimitMb = storageLimitMb;

            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Result = result;

            Id = Guid.Empty;
            Name = string.Empty;
            PresetName = string.Empty;
            UsedQuota = 0;
            TotalQuota = 0;
            RetentionPeriodDays = 0;
        }
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        QuotaNotFound
    }

    public class Validator : AbstractValidator<Query>
    {
        public Validator()
        {
            RuleFor(r => r.QuotaId).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Query, Response>
    {
        private readonly IValidator<Query> _validator;
        private readonly IDbConnection _dbConnection;

        public Handler(IValidator<Query> validator,
                       IDbConnection dbConnection)
        {
            _validator = validator;
            _dbConnection = dbConnection;
        }

        public async Task<Response> Handle(Query request, CancellationToken cancellationToken = default)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            var template = SqlQueryBuilder.Create()
                .Select(Db.CameraQuotas.Props.Id)
                .Select(Db.CameraQuotas.Props.Name)
                .Select(Db.CameraQuotas.Props.PresetId, "preset_id")
                .Select(Db.Presets.Props.Name, "preset_name")
                .Select($"COUNT(DISTINCT {Db.Cameras.Props.Id}) AS used_quota")
                .Select(Db.CameraQuotas.Props.Limit)
                .Select(Db.CameraQuotas.Props.RetentionPeriodDays)
                .Select(Db.CameraQuotas.Props.StorageLimitMb)
                .LeftJoin(Db.Presets.Table, Db.Presets.Props.Id, Db.CameraQuotas.Props.PresetId, SqlOperator.Equals)
                .LeftJoin(Db.Cameras.Table, Db.Cameras.Props.QuotaId, Db.CameraQuotas.Props.Id, SqlOperator.Equals)
                .Where(Db.CameraQuotas.Props.Id, ":QuotaId", SqlOperator.Equals, new { request.QuotaId })
                .GroupBy(Db.CameraQuotas.Props.Id, Db.CameraQuotas.Props.Name, "preset_id", "preset_name", Db.CameraQuotas.Props.Limit, Db.CameraQuotas.Props.RetentionPeriodDays, Db.CameraQuotas.Props.StorageLimitMb)
                .Build(QueryType.Standard, Db.CameraQuotas.Table, RowSelection.AllRows);

            var quota = await _dbConnection.QuerySingleOrDefaultAsync<CameraQuotaModel>(template.RawSql, template.Parameters);

            if (quota is null)
            {
                return new Response(Result.QuotaNotFound);
            }

            return new Response(quota.Id,
                                quota.Name,
                                quota.PresetId,
                                quota.PresetName,
                                (int)quota.UsedQuota,
                                quota.Limit,
                                quota.RetentionPeriodDays,
                                quota.StorageLimitMb);
        }
    }

    public record CameraQuotaModel(Guid Id,
                                   string Name,
                                   Guid? PresetId,
                                   string? PresetName,
                                   long UsedQuota,
                                   int Limit,
                                   int RetentionPeriodDays,
                                   int StorageLimitMb);
}