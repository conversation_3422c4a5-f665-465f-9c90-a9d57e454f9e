using Orleans;

namespace Teslametrics.Shared;

[GenerateSerializer]
public class LeakModel : BaseSensorModel
{
    // Конструктор без параметров для System.Text.Json
    public LeakModel()
        : base(Guid.Empty, string.Empty, null)
    {
    }

    public LeakModel(string name = "", string? displayName = null)
        : this(GuidGenerator.New(), name, displayName)
    {
    }

    public LeakModel(Guid id, string name, string? displayName)
        : base(id, name, displayName)
    {
    }
}
