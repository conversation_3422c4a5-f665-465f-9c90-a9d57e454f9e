using Dapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Teslametrics.Core.Services.Persistence;
using Teslametrics.DbMigrator.Data;

namespace Teslametrics.DbMigrator;

public class DbMigratorModule
{
    private readonly IConfiguration _configuration;

    public DbMigratorModule(IConfiguration configuration)
    {
        _configuration = configuration;
    }

    public void ConfigureServices(IServiceCollection services)
    {
        ConfigureDbContext(services);

        services.AddSingleton<DbMigratorHostedService>();
        services.AddSingleton<TeslametricsDbMigrationService>();
        services.AddHostedService(sp => sp.GetRequiredService<DbMigratorHostedService>());
    }

    private void ConfigureDbContext(IServiceCollection services)
    {
        var connectionString = _configuration.GetConnectionString("Default");
        if (string.IsNullOrEmpty(connectionString))
        {
            throw new InvalidOperationException("Connection string 'Default' not found.");
        }

        DefaultTypeMap.MatchNamesWithUnderscores = true;

        services.AddDbContext<CommandAppDbContext>(options =>
        {
            options.UseNpgsql(connectionString, b => b.MigrationsAssembly(typeof(DbMigratorModule).Assembly.FullName));
            options.UseSnakeCaseNamingConvention();
        }, ServiceLifetime.Singleton);
    }
}