﻿using Microsoft.AspNetCore.Components;
using System.Linq.Expressions;

namespace Teslametrics.App.Web.Components.Form;

public abstract partial class BaseAutocompleteComponent<TModelType> : BaseComponent
	where TModelType : class
{
	private TModelType? _value;

	protected bool IsLoading { get; private set; } = true;

	protected IEnumerable<TModelType> Items = new List<TModelType>();

	protected Func<TModelType?, string?> DisplayFunction => DisplayExpression.Compile();

	[Parameter]
	public Dictionary<string, object?>? UserAttributes { get; set; }

	[Parameter]
	public string? Label { get; set; } = null;
	[Parameter]
	public TModelType? Value { get; set; }

	[Parameter]
	public EventCallback<TModelType?> ValueChanged { get; set; }

	[Parameter]
	public bool Disabled { get; set; } = false;

	[Parameter]
	public object? Validation { get; set; }

	[Parameter]
	public Expression<Func<TModelType?>>? For { get; set; }

	[Parameter]
	public int MaxItems { get; set; } = 20;

	[Parameter]
	public bool Clearable { get; set; } = false;

	[Parameter]
	public bool Immediate { get; set; }

	[Parameter]
	public string? HelperText { get; set; }

	[Parameter]
	public Expression<Func<TModelType?, string?>> DisplayExpression { get; set; } = null!;

	protected abstract bool IsObjectEquals(TModelType element, TModelType element2);

	protected override Task OnParametersSetAsync()
	{
		if (!ReferenceEquals(Value, _value))
		{
			_value = Value;
			StateHasChanged();
			return CheckElementAvaliable();
		}

		return Task.CompletedTask;
	}

	protected virtual Task CheckElementAvaliable()
	{
		if (Value is null || IsLoading) return Task.CompletedTask;

		if (!Items.Any())
		{
			return ClearValue();
		}

		var value = Items.FirstOrDefault(element => IsObjectEquals(Value, element));
		if (value is null)
		{
			return ClearValue();
		}

		if (value.Equals(Value))
		{
			return Task.CompletedTask;
		}

		return UpdateValue(value, false);
	}

	protected virtual Task ClearValue() => InvokeAsync(() => UpdateValue(null, true));

	protected virtual Task ValueChangedHandler(TModelType suggestion) => UpdateValue(suggestion, true);

	protected virtual Task SetLoadingState(bool isLoading) => InvokeAsync(() =>
	{
		IsLoading = isLoading;
		StateHasChanged();
	});

	protected Task UpdateValue(TModelType? newValue, bool invoke = false)
	{
		_value = newValue;
		Value = _value;
		if (invoke)
			return ValueChanged.InvokeAsync(Value);
		else
			return Task.CompletedTask;
	}
}
