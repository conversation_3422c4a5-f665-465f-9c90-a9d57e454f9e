﻿@if (IsLoading)
{
	<MudProgressLinear Color="Color.Primary" Indeterminate="true" Class="mb-7" />
	<div class="mud-height-full">
		<MudStack Spacing="5" Class="px-2">
			<div>
				<MudSkeleton Width="80%" Height="32px;" />
				<MudSkeleton Width="40%" Height="32px;" />
			</div>
			<div>
				<MudSkeleton Width="20%" Height="32px;" />
				<MudSkeleton Width="56%" Height="32px;" />
			</div>
			<div>
				<MudSkeleton Width="80%" Height="32px;" />
				<MudSkeleton Width="50%" Height="32px;" />
			</div>
			<div>
				<MudSkeleton Width="80%" Height="32px;" />
				<MudSkeleton Width="100%" Height="32px;" />
			</div>
			<div>
				<MudSkeleton Width="40%" Height="32px;" />
				<MudSkeleton Width="70%" Height="32px;" />
			</div>
		</MudStack>
	</div>
}
@code {
	[Parameter]
	[EditorRequired]
	public bool IsLoading { get; set; }
}