﻿using Serilog;
using Teslametrics.Core.Domain;
using Teslametrics.Core.Services.Outbox;
using Teslametrics.Core.Services.Persistence;
using Teslametrics.Core.Services.TransactionManager;
using Teslametrics.MediaServer.FileStorage;
using Teslametrics.MediaServer.Orleans;

namespace Teslametrics.MediaServer;

public class Program
{
    public static void Main(string[] args)
    {
        var environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");

        environment = ValidateEnvironment(environment);

        var config = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
            .AddJsonFile($"appsettings.{environment}.json", optional: true, reloadOnChange: true)
            .Build();

        Log.Logger = new LoggerConfiguration()
            .ReadFrom.Configuration(config)
            .CreateBootstrapLogger();

        AppDomain currentDomain = AppDomain.CurrentDomain;
        currentDomain.UnhandledException += new UnhandledExceptionEventHandler((object sender, UnhandledExceptionEventArgs args) =>
        {
            Log.Logger.Error((Exception)args.ExceptionObject, "Unhandled exception");
        });

        try
        {
            Log.Information($"Starting application in {environment} environment...");

            var builder = WebApplication.CreateBuilder(args);

            builder.Host.UseSerilog();

            PostgresDomainPersistenceModule.Install(builder.Services, builder.Configuration);

            OutboxModule.Install(builder.Services, false);

            TransactionManagerModule.Install(builder.Services);

            MediaServerModule.Install(builder);

            MinioFileStorageModule.Install(builder.Services, builder.Configuration);

            OpenTelemetryModule.Install(builder);

            MediaServerModule.Initialize();

            var app = builder.Build();

            app.MapPrometheusScrapingEndpoint();

            app.Run();

            MediaServerModule.Dispose();
            MediaServerModule.DisposeServices(app.Services);
        }
        catch (Exception ex) when (ex is not HostAbortedException && ex.Source != "Microsoft.EntityFrameworkCore.Design")
        {
            Log.Fatal(ex, "Host terminated unexpectedly!");
        }
        finally
        {
            Log.CloseAndFlush();
        }
    }

    private static string ValidateEnvironment(string? environment)
    {
        if (string.IsNullOrEmpty(environment))
        {
            throw new ArgumentOutOfRangeException(nameof(environment), "Environment variable ASPNETCORE_ENVIRONMENT is not set");
        }

        return environment switch
        {
            "Local" => environment,
            "Development" => environment,
            "Perftest" => environment,
            "Caviardev" => environment,
            "Stage" => environment,
            "Production" => environment,
            _ => throw new ArgumentOutOfRangeException(nameof(environment))
        };
    }
}