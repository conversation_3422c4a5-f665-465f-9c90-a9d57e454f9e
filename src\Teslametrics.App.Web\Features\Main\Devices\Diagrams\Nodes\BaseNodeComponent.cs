using Microsoft.AspNetCore.Components;
using Teslametrics.App.Web.Components;

namespace Teslametrics.App.Web.Features.Main.Devices.Diagrams.Nodes;

public abstract class BaseNodeComponent<TElement> : InteractiveBaseComponent where TElement : PlanElementBase
{
    [Parameter]
    public bool IsSelected { get; set; }

    [Parameter]
    public EventCallback<TElement?> OnSelect { get; set; }

    [Parameter]
    public bool ReadOnly { get; set; }

    [Parameter]
    [EditorRequired]
    public TElement Element { get; set; } = null!;

    protected virtual async Task SelectAsync()
    {
        if (ReadOnly) return;

        if (OnSelect.HasDelegate)
            await OnSelect.InvokeAsync(Element);
    }

    protected static string ToCssValue(double value) => value.ToString(System.Globalization.CultureInfo.InvariantCulture);
}
