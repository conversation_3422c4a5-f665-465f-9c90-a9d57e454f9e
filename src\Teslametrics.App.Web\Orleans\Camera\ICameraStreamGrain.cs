namespace Teslametrics.App.Web.Orleans.Camera;

[<PERSON><PERSON>("Teslametrics.App.Web.Orleans.ICameraStreamGrain")]
public interface ICameraStreamGrain : IGrainWithGuidKey
{
    [<PERSON><PERSON>("GetStatusAsync")]
    public Task<CameraStreamState> GetStatusAsync();

    [<PERSON><PERSON>("ConnectAsync")]
    public Task ConnectAsync(ConnectRequest request);

    [<PERSON><PERSON>("DisconnectAsync")]
    public Task DisconnectAsync();

    [GenerateSerializer]
    [<PERSON><PERSON>("Teslametrics.App.Web.Orleans.ICameraStreamGrain.ConnectRequest")]
    public record ConnectRequest(Guid CameraId, string Uri, StreamType StreamType);
}
