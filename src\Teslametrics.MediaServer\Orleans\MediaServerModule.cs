using Dapper;
using FFMpegNET;
using Orleans.Configuration;
using Orleans.Streams.Kafka.Config;
using System.Data;
using System.Text.Json;
using Teslametrics.Core.Services.Persistence;
using Teslametrics.MediaServer.Mqtt;
using Teslametrics.MediaServer.Orleans.Camera;
using Teslametrics.MediaServer.Orleans.Wirenboard;
using Teslametrics.Shared;

namespace Teslametrics.MediaServer.Orleans;

public static class MediaServerModule
{
    public class WirenboardSettings
    {
        public const string SectionName = "Wirenboard";

        public string? BrokerAddress { get; set; }
        public int? Port { get; set; }
    }

    public static void Install(IHostApplicationBuilder builder)
    {
        var kafkaConnection = builder.Configuration.GetConnectionString("Kafka") ?? string.Empty;

        builder.UseOrleans(builder =>
        {
            builder.UseLocalhostClustering();
            builder.Configure<ClusterOptions>(options =>
            {
                options.ClusterId = "MediaServerCluster";
                options.ServiceId = "MediaServer";
            });

            builder.AddMemoryGrainStorageAsDefault();
            builder.UseInMemoryReminderService();
            builder.AddMemoryGrainStorage("PubSubStore");

            builder.AddKafka(StreamNames.PersistentStream)
                .WithOptions(options =>
                {
                    options.BrokerList = [kafkaConnection];
                    options.ConsumerGroupId = "orleans-kafka";
                    options.ConsumeMode = ConsumeMode.LastCommittedMessage;
                    options.SecurityProtocol = SecurityProtocol.Plaintext;
                    options.AddExternalTopic<S3EventNotification>(StreamTopicNames.MinioEvents, new TopicCreationConfig { AutoCreate = true, Partitions = 1, ReplicationFactor = 1 });
                })
                .AddJson()
                .Build();

            builder.AddLiveStreams(StreamNames.VideoLiveStream, configure =>
            {
                configure.SetMaxQueueLength(3);
                configure.SetTimeInCache(TimeSpan.FromSeconds(1), TimeSpan.FromSeconds(5));
            });

            builder.AddLiveStreams(StreamNames.CameraEventLiveStream, configure =>
            {
                configure.SetMaxQueueLength(3);
                configure.SetTimeInCache(TimeSpan.FromSeconds(1), TimeSpan.FromSeconds(5));
            });
        });

        builder.Services.AddTransient<RtspMicroSegmenter>();
        builder.Services.AddTransient<MicroSegmentAccumulator>();
        builder.Services.AddTransient<MicroSegmentPreviewGenerator>();
        builder.Services.AddTransient<MqttClientService>();
        builder.Services.AddHostedService<MediaServerBackgroundService>();
        builder.Services.AddHostedService<WirenboardBackgroundService>();

        builder.Services.Configure<WirenboardSettings>(
            builder.Configuration.GetSection(WirenboardSettings.SectionName));
    }

    public static void Initialize()
    {
        FFMpegSetup.InitializeFFMpeg();
    }

    public static void Dispose()
    {
        // Очищаем пулы MemoryOutputBuffer
        MemoryOutputBuffer.ClearPools();

        FFMpegSetup.FinalizeFFMpeg();
    }

    public static void DisposeServices(IServiceProvider serviceProvider)
    {
        // Освобождаем MqttClientService
        var mqttClientService = serviceProvider.GetService<MqttClientService>();
        mqttClientService?.Dispose();
    }

    public class MediaServerBackgroundService : BackgroundService
    {
        private readonly IServiceScopeFactory _serviceScopeFactory;
        private readonly ILogger<MediaServerBackgroundService> _logger;

        public MediaServerBackgroundService(
            IServiceScopeFactory serviceScopeFactory,
            ILogger<MediaServerBackgroundService> logger)
        {
            _serviceScopeFactory = serviceScopeFactory;
            _logger = logger;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            try
            {
                using var serviceScope = _serviceScopeFactory.CreateScope();
                {
                    var dbConnection = serviceScope.ServiceProvider.GetRequiredService<IDbConnection>();

                    var template = SqlQueryBuilder.Create()
                        .Select(Db.Cameras.Props.Id)
                        .Select(Db.Cameras.Props.ArchiveUri)
                        .Select(Db.Cameras.Props.ViewUri)
                        .Select(Db.Cameras.Props.PublicUri)
                        .Select(Db.Cameras.Props.OnvifEnabled)
                        .Select(Db.Cameras.Props.OnvifSettings)
                        .Where(Db.Cameras.Props.AutoStart, ":AutoStart", SqlOperator.Equals, new { AutoStart = true })
                        .Where(Db.Cameras.Props.IsBlocked, ":IsBlocked", SqlOperator.Equals, new { IsBlocked = false })
                        .Build(QueryType.Standard, Db.Cameras.Table, RowSelection.AllRows);

                    var cameras = await dbConnection.QueryAsync<CameraModel>(template.RawSql, template.Parameters);

                    var clusterClient = serviceScope.ServiceProvider.GetRequiredService<IClusterClient>();
                    var grain = clusterClient.GetGrain<IMediaServerGrain>(Guid.Empty);
                    foreach (var camera in cameras)
                    {
                        await grain.ConnectRtspAsync(new IMediaServerGrain.CameraConnectRtspRequest(camera.Id, camera.ArchiveUri, camera.ViewUri, camera.PublicUri));
                        if (camera.OnvifEnabled)
                        {
                            var onvifSettings = JsonSerializer.Deserialize<OnvifSettings>(camera.OnvifSettings!);
                            await grain.ConnectOnvifAsync(new IMediaServerGrain.CameraConnectOnvifRequest(camera.Id, onvifSettings!.Host, onvifSettings.Port, onvifSettings.Username, onvifSettings.Password));
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error while starting cameras");
            }
        }
    }

    public class WirenboardBackgroundService : BackgroundService
    {
        private readonly IServiceScopeFactory _serviceScopeFactory;
        private readonly ILogger<WirenboardBackgroundService> _logger;

        public WirenboardBackgroundService(
            IServiceScopeFactory serviceScopeFactory,
            ILogger<WirenboardBackgroundService> logger)
        {
            _serviceScopeFactory = serviceScopeFactory;
            _logger = logger;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            try
            {
                // Грейн запустится автоматически при первом вызове ConfigureHistoryCollectionAsync
                // При остановке хоста грейн будет автоматически деактивирован системой Orleans

                using var serviceScope = _serviceScopeFactory.CreateScope();
                {
                    var dbConnection = serviceScope.ServiceProvider.GetRequiredService<IDbConnection>();

                    if (!await CheckTableExistsAsync(dbConnection))
                    {
                        return;
                    }

                    var template = SqlQueryBuilder.Create()
                        .Select(Db.Plans.Props.Page)
                        .Build(QueryType.Standard, Db.Plans.Table, RowSelection.AllRows);

                    var pageJson = await dbConnection.ExecuteScalarAsync<string?>(template.RawSql);

                    if (string.IsNullOrEmpty(pageJson))
                    {
                        return;
                    }

                    var page = JsonSerializer.Deserialize<PageModel>(pageJson)!;

                    var topicInfos = new List<SensorTopicInfo>();

                    foreach (var city in page.Cities)
                    {
                        foreach (var building in city.Buildings)
                        {
                            foreach (var floor in building.Floors)
                            {
                                foreach (var room in floor.Rooms)
                                {
                                    // Добавляем датчики из комнаты
                                    foreach (var sensor in room.Sensors)
                                    {
                                        topicInfos.Add(new SensorTopicInfo(
                                            topic: sensor.Name,
                                            valueType: GetSensorValueType(sensor),
                                            sensorModel: sensor,
                                            cityId: city.Id,
                                            city: city.Name,
                                            buildingId: building.Id,
                                            building: building.Address,
                                            floorId: floor.Id,
                                            floor: floor.Number,
                                            roomId: room.Id,
                                            room: room.Name,
                                            deviceId: null,
                                            device: null,
                                            sensorId: sensor.Id
                                        ));
                                    }

                                    // Добавляем датчики из холодильников в комнате
                                    foreach (var fridge in room.Fridges)
                                    {
                                        foreach (var sensor in fridge.Sensors)
                                        {
                                            topicInfos.Add(new SensorTopicInfo(
                                                topic: sensor.Name,
                                                valueType: GetSensorValueType(sensor),
                                                sensorModel: sensor,
                                                cityId: city.Id,
                                                city: city.Name,
                                                buildingId: building.Id,
                                                building: building.Address,
                                                floorId: floor.Id,
                                                floor: floor.Number,
                                                roomId: room.Id,
                                                room: room.Name,
                                                deviceId: fridge.Id,
                                                device: fridge.Name,
                                                sensorId: sensor.Id
                                            ));
                                        }
                                    }
                                }
                            }
                        }
                    }

                    if (topicInfos.Count == 0)
                    {
                        return;
                    }

                    // Получаем клиент Orleans
                    var clusterClient = serviceScope.ServiceProvider.GetRequiredService<IClusterClient>();

                    // Настраиваем топики для основного грейна датчиков
                    var sensorGrain = clusterClient.GetGrain<IWirenboardSensorDataGrain>(Guid.Empty);
                    await sensorGrain.ConfigureSensorTopicsAsync(new IWirenboardSensorDataGrain.ConfigureSensorTopicsRequest(topicInfos));

                    // Обновляем топики для грейна истории
                    var historyManagerGrain = clusterClient.GetGrain<IWirenboardHistoryManagerGrain>(Guid.Empty);

                    // Создаем список HistoryTopicInfo на основе SensorTopicInfo
                    var historyTopics = topicInfos.Select(t => new HistoryTopicInfo(t.Topic, t.ValueType)).ToList();
                    await historyManagerGrain.ConfigureHistoryCollectionAsync(new IWirenboardHistoryManagerGrain.ConfigureHistoryCollectionRequest(historyTopics));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error while starting mqtt");
            }
        }

        private static async Task<bool> CheckTableExistsAsync(IDbConnection dbConnection)
        {
            // Check if table exists
            var tableExists = await dbConnection.ExecuteScalarAsync<int>(
                "SELECT COUNT(*) FROM information_schema.tables " +
                "WHERE table_schema = 'public' AND table_name = @TableName",
                new { TableName = Db.Plans.Table });

            return tableExists > 0;
        }

        /// <summary>
        /// Определяет тип значения датчика на основе его типа
        /// </summary>
        /// <param name="sensor">Модель датчика</param>
        /// <returns>Тип значения датчика</returns>
        private static SensorValueType GetSensorValueType(ISensorModel sensor)
        {
            return sensor switch
            {
                TemperatureModel => SensorValueType.Double,
                HumidityModel => SensorValueType.Double,
                DoorModel => SensorValueType.Bool,
                LeakModel => SensorValueType.Bool,
                PowerModel => SensorValueType.Bool,
                _ => SensorValueType.String
            };
        }
    }

    public record CameraModel(Guid Id, string ArchiveUri, string ViewUri, string PublicUri, bool OnvifEnabled, string? OnvifSettings);
    public record OnvifSettings(string Host, int Port, string Username, string Password);
}