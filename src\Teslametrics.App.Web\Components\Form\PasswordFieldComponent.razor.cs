using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Web;
using MudBlazor;

namespace Teslametrics.App.Web.Components.Form;

public partial class PasswordFieldComponent : MudTextField<string>, IDisposable
{
	private System.Timers.Timer? _hideTimer;
	private bool _isShow;

	[Parameter]
	public int MSBeforeAutoHide { get; set; } = 30000;

	protected override void OnInitialized()
	{
		base.InputType = InputType.Password;
		base.Adornment = Adornment.End;
		base.AdornmentIcon = Icons.Material.Filled.VisibilityOff;
		base.OnAdornmentClick = EventCallback.Factory.Create<MouseEventArgs>(this, ToggleField);
	}

	public void Dispose()
	{
		_hideTimer?.Dispose();
	}

	protected void ToggleField()
	{
		if (_isShow)
		{
			HideValue();
			PauseTimer();
		}
		else
		{
			ShowValue();
			StartTimer();
		}
	}

	protected void ShowValue()
	{
		_isShow = true;
		base.AdornmentIcon = Icons.Material.Filled.Visibility;
		base.InputType = InputType.Text;
	}

	protected void StartTimer()
	{
		if (MSBeforeAutoHide <= 0)
		{
			return;
		}

		if (_hideTimer is null)
		{
			_hideTimer = new System.Timers.Timer(MSBeforeAutoHide);
			_hideTimer.Elapsed += CountDownTimer;
		}

		_hideTimer.Enabled = true;
	}

	protected void PauseTimer()
	{
		if (_hideTimer is null)
			return;

		_hideTimer.Enabled = false;
	}

	protected async void CountDownTimer(Object? source, System.Timers.ElapsedEventArgs e)
	{
		await InvokeAsync(() =>
		{
			HideValue();
			PauseTimer();
			StateHasChanged();
		});
	}

	protected void HideValue()
	{
		_isShow = false;
		base.AdornmentIcon = Icons.Material.Filled.VisibilityOff;
		base.InputType = InputType.Password;
	}
}
