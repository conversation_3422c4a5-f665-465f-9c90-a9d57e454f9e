namespace Teslametrics.App.Web.Services.Authentication;

public class AuthenticationResult(AuthenticationResult.ResultCode result)
{
	public enum ResultCode
	{
		Unknown = 0,
		Success,
		ValidationError,
		UserNotFound,
		WrongPassword,
		UserLockedout,
		ForceChangePassword,
		FailedToCreateSession,
		FailedToSubscribeOnAuthenticationState,
	}

	public ResultCode Result { get; private set; } = result;
	public bool IsSuccess => Result == ResultCode.Success;
	public static AuthenticationResult Success() => new(ResultCode.Success);
	public static AuthenticationResult Failure(ResultCode resultCode)
	{
		if (resultCode == ResultCode.Success)
			throw new ArgumentException("Expected an error, but provided a successful result");

		return new(resultCode);
	}
}
