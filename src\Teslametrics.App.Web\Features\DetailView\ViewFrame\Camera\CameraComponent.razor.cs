
using System.Reactive;
using Microsoft.AspNetCore.Components;
using Teslametrics.App.Web.Events.Cameras;
using Teslametrics.App.Web.Extensions;
using Teslametrics.App.Web.Orleans.Camera;

namespace Teslametrics.App.Web.Features.DetailView.ViewFrame.Camera;

public partial class CameraComponent
{
	private string _playerId = $"player-{Guid.NewGuid()}";
	[Parameter]
	[EditorRequired]
	public Guid? CameraId { get; set; }

	[Parameter]
	[EditorRequired]
	public Guid OrganizationId { get; set; }

	private bool _subscribing;
	private DateTime _lastRefreshTime;

	private SubscribeCameraUseCase.Response? _subscriptionResult;
	private GetCameraUseCase.Response? _viewResponse;
	private List<GetViewUseCase.Response.Cell> _cells = new List<GetViewUseCase.Response.Cell>();

	protected override async Task OnInitializedAsync()
	{
		await base.OnInitializedAsync();

		await FetchAsync();
		await SubscribeAsync();
	}

	private async Task FetchAsync()
	{
		try
		{
			if (CameraId is null || CameraId == Guid.Empty) return;
			await SetLoadingAsync(true);
			_lastRefreshTime = DateTime.Now;
			_viewResponse = await ScopeFactory.MediatorSend(new GetCameraUseCase.Query(CameraId.Value));
			switch (_viewResponse.Result)
			{
				case GetCameraUseCase.Result.Success:
					break;
				case GetCameraUseCase.Result.ValidationError:
					Snackbar.Add("Ошибка валидации данных", MudBlazor.Severity.Error);
					break;
				case GetCameraUseCase.Result.CameraNotFound:
					Snackbar.Add("Камера не найдена", MudBlazor.Severity.Error);
					break;
				case GetCameraUseCase.Result.Unknown:
				default:
					throw new Exception($"Unexpected result in {nameof(GetCameraUseCase)}: {_viewResponse.Result}");
			}
		}
		catch (Exception exc)
		{
			Logger.LogError(exc, exc.Message);
			Snackbar.Add("Не удалось получить список камер из-за непредвиденной ошибки.", MudBlazor.Severity.Error);
		}
		finally
		{
			await SetLoadingAsync(false);
		}
	}

	private async Task SubscribeAsync()
	{
		try
		{
			Unsubscribe();

			if (_viewResponse is null || CameraId is null || CameraId == Guid.Empty) return;

			await SetSubscribingAsync(true);
			_subscriptionResult = await ScopeFactory.MediatorSend(new SubscribeCameraUseCase.Request(Observer.Create<object>(OnAppEventHandler, OnError), CameraId.Value));
			await SetSubscribingAsync(false);
			switch (_subscriptionResult.Result)
			{
				case SubscribeCameraUseCase.Result.Success:
					CompositeDisposable.Add(_subscriptionResult.Subscription!);
					break;
				case SubscribeCameraUseCase.Result.ValidationError:
					Snackbar.Add("Ошибка валидации при подписке на события", MudBlazor.Severity.Error);
					break;
				case SubscribeCameraUseCase.Result.Unknown:
				default:
					throw new Exception($"Unexpected result in {nameof(SubscribeCameraUseCase)}: {_subscriptionResult.Result}");
			}
		}
		catch (Exception ex)
		{
			await SetSubscribingAsync(false);
			Snackbar.Add("Не удалось получить подписку на события камеры из-за непредвиденной ошибки. Повторите попытку", MudBlazor.Severity.Error);
			Logger.LogError(ex, ex.Message);
		}
	}

	private void Unsubscribe()
	{
		if (_subscriptionResult?.Subscription is not null)
		{
			CompositeDisposable.Remove(_subscriptionResult.Subscription);
			_subscriptionResult.Subscription.Dispose();
		}
	}

	protected Task SetSubscribingAsync(bool isLoading = true) => UpdateViewAsync(() =>
	{
		_subscribing = isLoading;
	});

	#region [Actions]
	private Task RefreshAsync() => FetchAsync();

	private void ShowPlayer()
	{
		if (_viewResponse is not null && _viewResponse.CameraStatus == CameraStatus.Running)
			EventSystem.Publish(new ShowCameraStreamEto(_viewResponse.Id));
	}
	#endregion

	#region [Event Handlers]
	private async void OnAppEventHandler(object appEvent)
	{
		await FetchAsync();
		await UpdateViewAsync();
	}

	private void OnError(Exception exc)
	{
		Snackbar.Add("Ошибка при подписке на события", MudBlazor.Severity.Error);
		Logger.LogError(exc, exc.Message);
	}
	#endregion [Event Handlers]
}
