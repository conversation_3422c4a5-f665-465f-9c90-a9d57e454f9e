﻿@using MudBlazor.Internal

@namespace Teslametrics.App.Web.Components.Form.TimePicker

@inherits MudPicker<TimeOnly?>

@Render

@code {
	private void InitDefaultActions()
	{
		if (PickerVariant == PickerVariant.Static)
		{
			return;
		}

		// Стандартные Actions (нижняя панель), чтобы не перезаписывать параметр и не терять его обработчики
		PickerActions = (MudPicker<TimeOnly?> picker) =>
	@<MudGrid Justify="@(LockPickerMode ? Justify.FlexEnd : Justify.SpaceBetween)" Class="my-0 ps-1 pe-4 align-center">
		@if (!LockPickerMode)
	{
		<MudItem>
			<MudToggleIconButton Toggled="PickerMode == TimePickerMode.Input"
								 ToggledChanged="PickerModeChange"
							 	 UserAttributes="InputModeToggleButtonAttributes"
							 	 Icon="@Icons.Material.Outlined.AccessTime" Color="Color.Secondary"
							 	 ToggledIcon="@Icons.Material.Outlined.Keyboard" ToggledColor="Color.Secondary" />
		</MudItem>
	}
		<MudItem>
			<MudStack Row="true">
				<MudButton Variant="Variant.Text" Color="Color.Secondary" OnClick="@(() => picker.CloseAsync())" UserAttributes="CancelButtonAttributes">@CancelButtonText</MudButton>
				<MudButton Variant="Variant.Text" Color="Color.Secondary" OnClick="@(() => picker.CloseAsync(true))" UserAttributes="SubmitButtonAttributes">@SubmitButtonText</MudButton>
			</MudStack>
		</MudItem>
	</MudGrid>;
	}

	protected override RenderFragment PickerContent =>
	@<CascadingValue Value="@this" IsFixed="true">
		<div class="d-contents">
			<MudPickerToolbar Class="@ToolbarClass" Style="@Style" Orientation="@Orientation">
				<MudStack Row="false">
					@if(PickerVariant != PickerVariant.Static)
				{
					<MudText Typo="Typo.subtitle1" Class="headline" UserAttributes="HeadlineAttributes">@_headerText</MudText>
				}
					<MudStack Row="Orientation == Orientation.Portrait" Justify="Justify.Center" AlignItems="AlignItems.Center" Class="time_picker_inputs">
						<MudStack Row="true" Justify="Justify.Center" AlignItems="AlignItems.Center">
							<MudTextField T="int?"
									  	Text="@GetHourString()"
									  	Value="@_timeSet?.Hour"
									  	ValueChanged="OnHourInput"
									  	Variant="Variant.Outlined"
									  	Disabled="_hourInputDisabled"
									  	Immediate="true"
									  	Class="input_fields"
									  	MaxLength="2"
									  	UserAttributes="HoursInputFieldAttributes"
									  	Label="@_hourLabel"
									  	Pattern="@hourRegex"
									  	Mask="hourMask" />
							<MudText Typo="Typo.h3" Class="ds_timepicker_separator">:</MudText>
							<MudTextField T="int?"
									  	Text="@GetMinuteString()"
									  	Value="_timeSet?.Minute"
									  	ValueChanged="OnMinuteInput"
									  	Variant="Variant.Outlined"
									  	Disabled="_minuteInputDisabled"
									  	Immediate="true"
									  	Class="input_fields"
									  	MaxLength="2"
									  	UserAttributes="MinutesInputFieldAttributes"
									  	@ref="_minutesInputField"
									  	Label="@_minuteLabel"
									  	Pattern="@minuteRegex"
									  	Mask="minuteMask" />
						</MudStack>
						@AmPmButtonsGroup
					</MudStack>
				</MudStack>
			</MudPickerToolbar>
			@if (PickerMode == TimePickerMode.Select)
		{
			<MudPickerContent>
				<div class="mud-picker-time-container">
					<div class="mud-picker-time-clock">
						<div role="menu" tabindex="-1" class="mud-picker-time-clock-mask" @onmousedown="OnMouseDown" @onmouseup="OnMouseUp">
							@if (AmPm) // TODO:: AM/PM
						{
							@*Hours from 1 to 12*@
						for (int i = 1; i <= 12; ++i)
						{
						var _i = i;
						var angle = (6 - _i) * 30;
							<MudText Class="@GetNumberColor(_i)" Style="@GetTransform(angle, 109, 0, 5)">@_i</MudText>
						}
						for (int i = 1; i <= 12; ++i)
						{
						var _i = i;
							<TimePickerWheelItemComponent Class="mud-picker-stick mud-hour" Style="@($"transform: rotateZ({_i * 30}deg);")" Value="_i" OnClick="OnMouseClickHourAsync" OnMouseOver="OnMouseOverHourAsync" @key="@_i" />
						}
						}
						else
						{
							@*Hours from 13 to 24 (00)*@
						for (int i = 1; i <= 12; ++i)
						{
						var _i = i;
						var angle = (6 - _i) * 30;
							<MudText Class="@GetNumberColor((_i + 12) % 24)" Style="@GetTransform(angle, 109, 0, 5)">@(((_i + 12) % 24).ToString("D2"))</MudText>
						}
							@*Hours from 1 to 12*@
						for (int i = 1; i <= 12; ++i)
						{
						var _i = i;
						var angle = (6 - _i) * 30;
							<MudText Class="@GetNumberColor(_i)" Typo="Typo.body2" Style="@GetTransform(angle, 74, 0, 40)">@(_i.ToString("D2"))</MudText>
						}
						for (int i = 1; i <= 12; ++i)
						{
						var _i = i;
							<div class="mud-picker-stick" style="@($"transform: rotateZ({_i * 30}deg);")">
								<TimePickerWheelItemComponent Class="mud-picker-stick-inner mud-hour" Value="_i" OnClick="OnMouseClickHourAsync" OnMouseOver="OnMouseOverHourAsync" @key="@_i" />
								<TimePickerWheelItemComponent Class="mud-picker-stick-outer mud-hour" Value="@((_i + 12) % 24)" OnClick="OnMouseClickHourAsync" OnMouseOver="OnMouseOverHourAsync" @key="@((_i + 12) % 24)" />
							</div>
						}
						}
						</div>
					</div>
				</div>
			</MudPickerContent>
		}
		</div>
	</CascadingValue>;

	string hourRegex => AmPm ? @"^([0-9]|(?:0[1-9]|1[0-2])|00)$" : @"^([01]?[0-9]|2[0-3])$";
	string minuteRegex => @"^([0-5]?[0-9])$";
	private IMask hourMask => new RegexMask(hourRegex);
	private IMask minuteMask => new RegexMask(minuteRegex);

	private RenderFragment? AmPmButtonsGroup => AmPm
		?
	@<MudButtonGroup Color="Color.Secondary" Variant="Variant.Outlined" Vertical="Orientation == Orientation.Landscape">
		<MudButton>AM</MudButton>
		<MudButton>PM</MudButton>
	</MudButtonGroup>
	: null;

	private bool _hourInputDisabled => TimeEditMode == TimeEditMode.OnlyMinutes || PickerMode == TimePickerMode.Select || Disabled;
	private bool _minuteInputDisabled => TimeEditMode == TimeEditMode.OnlyHours || PickerMode == TimePickerMode.Select || Disabled;
	private string _headerText => PickerMode == TimePickerMode.Select ? SelectTimeModeText : InputTimeModeText;
	private string? _hourLabel => PickerMode == TimePickerMode.Select ? null : HourLabelText;
	private string? _minuteLabel => PickerMode == TimePickerMode.Select ? null : MinuteLabelText;

	private async Task PickerModeChange(bool isInputMode)
	{
		PickerMode = isInputMode ? TimePickerMode.Input : TimePickerMode.Select;
		if (PickerModeChanged.HasDelegate)
		{
			await PickerModeChanged.InvokeAsync(PickerMode);
		}
	}

	#region Attributes
	private Dictionary<string, object> InputModeToggleButtonAttributes => new Dictionary<string, object>() { { "id", $"input_mode_toggle_button_{Id}" } };

	private Dictionary<string, object> CancelButtonAttributes => new Dictionary<string, object>() { { "id", $"cancel_button_{Id}" } };

	private Dictionary<string, object> SubmitButtonAttributes => new Dictionary<string, object>() { { "id", $"submit_button_{Id}" } };

	private Dictionary<string, object> HeadlineAttributes => new Dictionary<string, object>() { { "id", $"headline_{Id}" } };

	private Dictionary<string, object> HoursInputFieldAttributes => new Dictionary<string, object>() { { "id", $"hours_input_field_{Id}" } };

	private Dictionary<string, object> MinutesInputFieldAttributes => new Dictionary<string, object>() { { "id", $"minutes_input_field_{Id}" } };
	#endregion
}
