using MediatR;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using MudBlazor;
using System.Reactive;
using Teslametrics.App.Web.Eto.Users;
using Teslametrics.App.Web.Events.Users;
using Teslametrics.App.Web.Exceptions;
using Teslametrics.App.Web.Extensions;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Users.List;

public partial class UserListComponent
{
	private bool _disposedValue;
	private Guid _organizationId;
	private DateTime _lastRefreshTime = DateTime.Now;
	private string _searchString = string.Empty;
	private GetUserListUseCase.Response? _response;

	#region Parameters
	[Parameter]
	public int Offset { get; set; } = 0;
	[Parameter]
	public EventCallback<int> OffsetChanged { get; set; }

	[Parameter]
	public int Limit { get; set; } = 25;
	[Parameter]
	public EventCallback<int> LimitChanged { get; set; }

	[Parameter]
	[EditorRequired]
	public Guid OrganizationId { get; set; }
	#endregion [Parameter]

	protected int CurrentPage => Offset / Limit;

	protected override async Task OnParametersSetAsync()
	{
		if (OrganizationId != _organizationId)
		{
			_organizationId = OrganizationId;
			await FetchAsync();
		}

		await base.OnParametersSetAsync();
	}

	protected override async Task OnInitializedAsync()
	{
		await base.OnInitializedAsync();

		await SubscribeAsync();
		await FetchAsync();

		AuthenticationStateProvider.AuthenticationStateChanged += OnAuthenticationStateChanged;
	}

	protected override void Dispose(bool disposing)
	{
		if (!_disposedValue)
		{
			if (disposing)
			{

				AuthenticationStateProvider.AuthenticationStateChanged -= OnAuthenticationStateChanged;
			}

			_disposedValue = true;
		}

		// Вызов базового метода Dispose
		base.Dispose(disposing);
	}

	protected virtual void CreateUser() => EventSystem.Publish<UserCreateEto>();

	protected virtual async Task FetchAsync()
	{
		try
		{
			if (IsLoading) return;

			await SetLoadingAsync();

			var userId = await GetCurrentUserIdAsync() ?? throw new NotAuthorizedException();
			_response = await ScopeFactory.MediatorSend(new GetUserListUseCase.Query(userId, OrganizationId, Offset, Limit, _searchString));
		}
		catch (Exception ex)
		{
			_response = null;
			Logger.LogError(ex, ex.Message);
			Snackbar.Add("Не удалось получить список пользователей из-за ошибки на сервере. Обратитесь к администратору", Severity.Error);
		}

		await SetLoadingAsync(false);
		if (_response is null) return;

		switch (_response.Result)
		{
			case GetUserListUseCase.Result.Success:
				_lastRefreshTime = DateTime.Now;
				break;
			case GetUserListUseCase.Result.ValidationError:
				Snackbar.Add("Ошибка валидации при получении списка пользователей", Severity.Error);
				break;
			case GetUserListUseCase.Result.Unknown:
				Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(UserListComponent), nameof(GetUserListUseCase));
				Snackbar.Add($"Не удалось получить список пользователей из-за непредвиденного ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
				break;
			default:
				Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(UserListComponent), nameof(GetUserListUseCase), _response.Result);
				Snackbar.Add($"Не удалось получить список пользователей из-за ошибки: {_response.Result}. Обратитесь к администратору.", MudBlazor.Severity.Error);
				break;
		}
	}

	private Task RefreshAsync() => FetchAsync();

	private Func<GetUserListUseCase.Response.Item, bool> _quickFilter => x =>
	{
		if (string.IsNullOrWhiteSpace(_searchString))
			return true;

		if (x.Username.Contains(_searchString, StringComparison.OrdinalIgnoreCase))
			return true;

		return false;
	};

	#region [Actions]
	private void Select(DataGridRowClickEventArgs<GetUserListUseCase.Response.Item> args) => EventSystem.Publish(new UserSelectEto(args.Item.Id));
	private void Select(GetUserListUseCase.Response.Item item) => EventSystem.Publish(new UserSelectEto(item.Id));
	private void Edit(GetUserListUseCase.Response.Item item) => EventSystem.Publish(new UserEditEto(item.Id));
	private void Delete(GetUserListUseCase.Response.Item item) => EventSystem.Publish(new UserDeleteEto(OrganizationId, item.Id));
	private void LoginpasswordChange(GetUserListUseCase.Response.Item item) => EventSystem.Publish(new ForcePasswordChangeEto(item.Id));
	private void ChangeUserPassword(GetUserListUseCase.Response.Item item) => EventSystem.Publish(new ChangeUserPasswordEto(item.Id));
	#endregion [Actions]

	private async Task SubscribeAsync()
	{
		var result = await ScopeFactory.MediatorSend(new SubscribeUserListUseCase.Request(Observer.Create<SubscribeUserListUseCase.UpdatedEvent>(OnAppEventHandler, OnError), OrganizationId));
		if (result.IsSuccess)
		{
			CompositeDisposable.Add(result.Subscription!);
			return;
		}

		switch (result.Result)
		{
			case SubscribeUserListUseCase.Result.ValidationError:
				Snackbar.Add("Ошибка валидации при подписке на события", Severity.Error);
				break;

			case SubscribeUserListUseCase.Result.Unknown:
			default:
				Snackbar.Add("Не удалось получить подписку на обновления из-за непредвиденной ошибки:" + result.Result.ToString(), Severity.Error);
				break;
		}
	}

	#region [Event Handlers]
	protected async void OnAppEventHandler(SubscribeUserListUseCase.UpdatedEvent appEvent)
	{
		await RefreshAsync();
		await UpdateViewAsync();
	}

	private Task RowsPerPageChanged(int limit)
	{
		Limit = limit;
		if (LimitChanged.HasDelegate)
		{
			return LimitChanged.InvokeAsync(limit);
		}
		return Task.CompletedTask;
	}

	protected void OnError(Exception exc)
	{
		Logger.LogError(exc, exc.Message);
		Snackbar.Add("Ошибка валидации при подписке на события", Severity.Error);
	}

	private async void OnAuthenticationStateChanged(Task<AuthenticationState> authState)
	{
		await FetchAsync();
	}
	#endregion [Event Handlers]
}
