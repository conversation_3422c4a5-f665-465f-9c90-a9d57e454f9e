using System.Reactive.Linq;

internal class Program
{
    private static async Task Main(string[] args)
    {
        Console.WriteLine("ONVIF Events API Client Example");

        try
        {
            // Параметры подключения к ONVIF-устройству
            string host = "*************";
            int port = 5080;
            string username = "admin";
            string password = "13041969Gg";

            // Создание клиента событий ONVIF
            var onvifClient = new OnvifEventClient(host, port, username, password);

            // Подписываемся на события движения с использованием Observable
            var subscription = onvifClient.MotionEvents.Subscribe(
                // OnNext
                eventArgs =>
                {
                    string motionState = eventArgs.IsMotion ? "ОБНАРУЖЕНО" : "ПРЕКРАЩЕНО";
                    Console.WriteLine($"{eventArgs.DeviceTime:yyyy-MM-dd HH:mm:ss.fff}: Движение {motionState}");
                },
                // OnError
                ex =>
                {
                    Console.WriteLine($"Ошибка при получении сообщений: {ex.Message}");
                    if (ex.InnerException != null)
                    {
                        Console.WriteLine($"Внутреннее исключение: {ex.InnerException.Message}");
                    }
                },
                // OnCompleted
                () =>
                {
                    Console.WriteLine("Получение событий завершено.");
                });

            // Получение возможностей ONVIF-устройства
            Console.WriteLine("Getting ONVIF device capabilities...");
            var capabilities = await onvifClient.GetCapabilitiesAsync();

            Console.WriteLine("ONVIF Events API capabilities:");
            Console.WriteLine($"- WSSubscription support: {capabilities.WSSubscriptionPolicySupport}");
            Console.WriteLine($"- WSPausableSubscription support: {capabilities.WSPausableSubscriptionManagerInterfaceSupport}");
            Console.WriteLine($"- Maximum number of PullPoints: {capabilities.MaxPullPoints}");

            // Создание подписки на события
            Console.WriteLine("\nCreating subscription to events...");
            string subscriptionUrl = await onvifClient.CreateSubscriptionAsync();
            Console.WriteLine("\nSubscription to events created successfully!");
            Console.WriteLine($"Subscription URL: {subscriptionUrl}");

            // Запуск получения событий (без фильтрации на стороне клиента, так как фильтрация уже выполнена на уровне запроса)
            Console.WriteLine("\nStarting event polling for motion events...");
            await onvifClient.StartEventPollingAsync(1000); // Интервал опроса 1 секунда, фильтрация уже на уровне запроса
            Console.WriteLine("Event polling started. Waiting for motion events (press Enter to exit)...");

            // Ожидание нажатия Enter для выхода
            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();

            // Отписываемся
            await onvifClient.UnsubscribeAsync();
            subscription.Dispose();
            Console.WriteLine("\nUnsubscribed from events.");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error: {ex.Message}");
            Console.WriteLine(ex.StackTrace);
        }
    }
}