using System.Reactive;
using Microsoft.AspNetCore.Components;
using Teslametrics.App.Web.Extensions;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Features.Main.Devices.RoomContent;

public partial class FridgeComponent
{
    private bool _subscribing;

    private SubscribeFridgeUseCase.Response? _subscriptionResult;
    private GetFridgeUseCase.Response? _response = null;

    private IEnumerable<GetFridgeUseCase.Response.DoorModel>? _door => _response?.Fridge.Sensors.OfType<GetFridgeUseCase.Response.DoorModel>();
    private IEnumerable<GetFridgeUseCase.Response.LeakModel>? _leak => _response?.Fridge.Sensors.OfType<GetFridgeUseCase.Response.LeakModel>();
    private IEnumerable<GetFridgeUseCase.Response.PowerModel>? _power => _response?.Fridge.Sensors.OfType<GetFridgeUseCase.Response.PowerModel>();

    private List<(GetFridgeUseCase.Response.TemperatureModel? Temperature, GetFridgeUseCase.Response.HumidityModel? Humidity)> _doubledCards = [];

    private IEnumerable<GetFridgeUseCase.Response.ISensorModel> _sensorsWithIncidents => _response?.Fridge.Sensors.Where(x => x.Incident is not null) ?? [];

    [Parameter]
    public Guid Fridge { get; set; }

    protected override async Task OnInitializedAsync()
    {
        await FetchAsync();
        await base.OnInitializedAsync();
    }

    private async Task FetchAsync()
    {
        if (IsLoading) return;
        try
        {
            await SetLoadingAsync(true);
            _response = await ScopeFactory.MediatorSend(new GetFridgeUseCase.Query(Fridge));
        }
        catch (Exception ex)
        {
            _response = null;
            Logger.LogError(ex, ex.Message);
            Snackbar.Add($"Не удалось получить данные холодильника из-за ошибки сообщения с сервером. Повторите попытку позже.", MudBlazor.Severity.Error);
        }

        await SetLoadingAsync(false);

        if (_response is null) return;

        switch (_response.Result)
        {
            case GetFridgeUseCase.Result.Success:
                _doubledCards.Clear();
                // Ищем Temperature и Humidity
                var temperatures = _response.Fridge.Sensors.OfType<GetFridgeUseCase.Response.TemperatureModel>().ToList();
                var humidities = _response.Fridge.Sensors.OfType<GetFridgeUseCase.Response.HumidityModel>().ToList();

                // Определяем максимальное количество карточек для пары Температура+Влажность
                int maxPairs = Math.Max(temperatures.Count, humidities.Count);

                for (int i = 0; i < maxPairs; i++)
                {
                    var temp = i < temperatures.Count ? temperatures[i] : null;
                    var hum = i < humidities.Count ? humidities[i] : null;

                    _doubledCards.Add(new(temp, hum));
                }
                await SubscribeAsync();
                break;
            case GetFridgeUseCase.Result.Unknown:
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(FridgeComponent), nameof(GetFridgeUseCase));
                Snackbar.Add($"Не удалось получить данные холодильника из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
                break;
            default:
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(FridgeComponent), nameof(GetFridgeUseCase), _response.Result);
                Snackbar.Add($"Не удалось получить данные холодильника из-за ошибки: {_response.Result}", MudBlazor.Severity.Error);
                break;
        }
    }
    private async Task SubscribeAsync()
    {
        Unsubscribe();
        await SetSubscribingAsync(true);

        try
        {
            _subscriptionResult = await ScopeFactory.MediatorSend(new SubscribeFridgeUseCase.Request(Observer.Create<object>(OnAppEventHandler, OnError), Fridge));
        }
        catch (Exception ex)
        {
            _subscriptionResult = null;
            Snackbar.Add($"Не удалось получить подписку на события холодильника из-за ошибки сообщения с сервером. Повторите попытку позже.", MudBlazor.Severity.Error);
            Logger.LogError(ex, ex.Message);
        }

        await SetSubscribingAsync(false);
        if (_subscriptionResult is null) return;

        switch (_subscriptionResult.Result)
        {
            case SubscribeFridgeUseCase.Result.Success:
                CompositeDisposable.Add(_subscriptionResult.Subscription!);
                break;
            case SubscribeFridgeUseCase.Result.ValidationError:
                Snackbar.Add("Ошибка валидации при подписке на события холодильника", MudBlazor.Severity.Error);
                break;
            case SubscribeFridgeUseCase.Result.Unknown:
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(FridgeComponent), nameof(SubscribeFridgeUseCase));
                Snackbar.Add($"Не удалось получить подписку на события холодильника из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
                break;

            default:
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(FridgeComponent), nameof(SubscribeFridgeUseCase), _subscriptionResult.Result);
                Snackbar.Add($"Не удалось получить подписку на события холодильника из-за ошибки: {_subscriptionResult.Result}", MudBlazor.Severity.Error);
                break;
        }
    }
    private void Unsubscribe()
    {
        if (_subscriptionResult?.Subscription is not null)
        {
            CompositeDisposable.Remove(_subscriptionResult.Subscription);
            _subscriptionResult.Subscription.Dispose();
        }
    }

    protected Task SetSubscribingAsync(bool isLoading = true) => UpdateViewAsync(() =>
    {
        _subscribing = isLoading;
    });

    #region [Event Handlers]
    private async void OnAppEventHandler(object appEvent)
    {
        await FetchAsync();
        await UpdateViewAsync();
    }

    private void OnError(Exception exc)
    {
        Snackbar.Add("Ошибка при подписке на события", MudBlazor.Severity.Error);
        Logger.LogError(exc, exc.Message);
    }
    #endregion [Event Handlers]
}
