using MediatR;
using Teslametrics.App.Web.Domain.AccessControl.Organizations.Events;
using Teslametrics.App.Web.Domain.Cameras;
using Teslametrics.MediaServer.Orleans.Camera;
using Teslametrics.App.Web.Services.Outbox;

namespace Teslametrics.App.Web.Features.EventHandlers;

public class DeleteCameraQuotaEventHandler : INotificationHandler<CameraQuotaDeletedEvent>
{
    private readonly ICameraRepository _cameraRepository;
    private readonly IPublisher _publisher;
    private readonly IOutbox _outbox;
    private readonly IClusterClient _clusterClient;

    public DeleteCameraQuotaEventHandler(ICameraRepository cameraRepository,
                                         IClusterClient clusterClient,
                                         IPublisher publisher,
                                         IOutbox outbox)
    {
        _cameraRepository = cameraRepository;
        _clusterClient = clusterClient;
        _publisher = publisher;
        _outbox = outbox;
    }

    public async Task Handle(CameraQuotaDeletedEvent notification, CancellationToken cancellationToken)
    {
        var cameras = await _cameraRepository.GetAllByQuotaIdAsync(notification.Id, cancellationToken);

        List<object> events = [];

        foreach (var camera in cameras)
        {
            var grain = _clusterClient.GetGrain<IMediaServerGrain>(Guid.Empty);
            await grain.DisconnectAsync(new IMediaServerGrain.CameraDisconnectRequest(camera.Id));
            events.AddRange(camera.Block());
        }

        await _cameraRepository.SaveChangesAsync(cancellationToken);

        foreach (var @event in events)
        {
            await _publisher.Publish(@event, cancellationToken);
        }

        await _outbox.AddRangeAsync(events);
    }
}