::deep .menu_icon {
    border-radius: 12px;
}

::deep .menu_icon>button {
    height: 48px;
    width: 48px;
    border-radius: 12px;
    box-shadow: none;
}

::deep .list {
    display: grid;
    grid-template-rows: auto 1fr;
    overflow: hidden;
    max-height: 400px;
}

/* list rows */
::deep .notif-item {
    border-bottom: 1px solid #eef3f8;
}

::deep .notif-item:hover {
    background: var(--mud-palette-background);
}

::deep .notif-item:last-child {
    border-bottom: none;
}

.dotted {
    width: 8px;
    height: 8px;
    background: var(--mud-palette-primary);
    border-radius: 50%;
}

/* misc tweaks */
::deep .fw-500 {
    font-weight: 500;
}

::deep .icon {
    fill: var(--color-neutral-40);
}

::deep .mud-badge-root {
    width: fit-content;
}

::deep .mud-badge-root .mud-badge-wrapper {
    margin-left: 8px;
}