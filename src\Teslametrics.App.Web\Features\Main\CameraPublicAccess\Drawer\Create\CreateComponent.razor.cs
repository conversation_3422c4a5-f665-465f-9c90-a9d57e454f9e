using System.Reactive;
using FluentValidation;
using Microsoft.AspNetCore.Components;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Components.Drawer;
using Teslametrics.App.Web.Events.CameraPublicAccess;
using Teslametrics.App.Web.Extensions;

namespace Teslametrics.App.Web.Features.Main.CameraPublicAccess.Drawer.Create;

public partial class CreateComponent
{
    private class Model
    {
        public string Name { get; set; } = string.Empty;
    }

    private class Validator : BaseFluentValidator<Model>
    {
        public Validator()
        {
            RuleFor(model => model.Name)
                .Length(3, 60)
                .WithMessage("наименование должно быть длиной от 3 до 60 символов");
        }
    }

    private bool _isValid;
    private Model _model = new();
    private Validator _validator = new();

    private bool _subscribing;
    private GetCameraPublicAccessUseCase.Response? _cameraModel;
    private SubscribeCameraPublicAccessUseCase.Response? _subscriptionResult;

    #region Parameters
    [Parameter]
    [EditorRequired]
    public Guid OrganizationId { get; set; }

    [Parameter]
    [EditorRequired]
    public Guid CameraId { get; set; }

    [CascadingParameter(Name = DrawerConsts.InstanceName)]
    private DrawerComponent Drawer { get; set; } = null!;
    #endregion

    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();

        _cameraModel = null;
        await FetchAsync();
        await SubscribeAsync();
    }

    protected async Task FetchAsync()
    {
        try
        {
            await SetLoadingAsync(true);
            _cameraModel = await ScopeFactory.MediatorSend(new GetCameraPublicAccessUseCase.Query(CameraId));
            await SetLoadingAsync(false);
            switch (_cameraModel.Result)
            {
                case GetCameraPublicAccessUseCase.Result.Success:
                    break;
                case GetCameraPublicAccessUseCase.Result.ValidationError:
                    Snackbar.Add("Ошибка валидации данных", MudBlazor.Severity.Error);
                    break;
                case GetCameraPublicAccessUseCase.Result.Unknown:
                default:
                    throw new Exception($"Unexpected result in {nameof(GetCameraPublicAccessUseCase)}: {_cameraModel.Result}");
            }
        }
        catch (Exception ex)
        {
            await SetLoadingAsync(false);
            Snackbar.Add("Не удалось получить камеру из-за непредвиденной ошибки. Повторите попытку", MudBlazor.Severity.Error);
            Logger.LogError(ex, ex.Message);
        }
    }
    private async Task SubscribeAsync()
    {
        try
        {
            Unsubscribe();

            await SetSubscribingAsync(true);
            _subscriptionResult = await ScopeFactory.MediatorSend(new SubscribeCameraPublicAccessUseCase.Request(Observer.Create<object>(OnAppEventHandler, OnError), CameraId));
            await SetSubscribingAsync(false);
            switch (_subscriptionResult.Result)
            {
                case SubscribeCameraPublicAccessUseCase.Result.Success:
                    CompositeDisposable.Add(_subscriptionResult.Subscription!);
                    break;
                case SubscribeCameraPublicAccessUseCase.Result.ValidationError:
                    Snackbar.Add("Ошибка валидации при подписке на события", MudBlazor.Severity.Error);
                    break;
                case SubscribeCameraPublicAccessUseCase.Result.Unknown:
                default:
                    throw new Exception($"Unexpected result in {nameof(SubscribeCameraPublicAccessUseCase)}: {_subscriptionResult.Result}");
            }
        }
        catch (Exception ex)
        {
            await SetSubscribingAsync(false);
            Snackbar.Add("Не удалось получить подписку на события камеры из-за непредвиденной ошибки. Повторите попытку", MudBlazor.Severity.Error);
            Logger.LogError(ex, ex.Message);
        }
    }
    private void Unsubscribe()
    {
        if (_subscriptionResult?.Subscription is not null)
        {
            CompositeDisposable.Remove(_subscriptionResult.Subscription);
            _subscriptionResult.Subscription.Dispose();
        }
    }

    protected Task SetSubscribingAsync(bool isLoading = true) => UpdateViewAsync(() =>
    {
        _subscribing = isLoading;
    });

    #region [Action]
    private Task RefreshAsync() => FetchAsync();

    private async Task SubmitAsync()
    {
        try
        {
            var response = await ScopeFactory.MediatorSend(new CreatePublicLinkUseCase.Command(CameraId, _model.Name));
            switch (response.Result)
            {
                case CreatePublicLinkUseCase.Result.Success:
                    Snackbar.Add("Камера успешно создана.", MudBlazor.Severity.Success);
                    EventSystem.Publish(new CameraPublicAccessSelectEto(OrganizationId, CameraId, response.Id));
                    break;
                case CreatePublicLinkUseCase.Result.ValidationError:
                    Snackbar.Add("Ошибка валидации, проверьте правильность заполнения полей", MudBlazor.Severity.Error);
                    break;
                case CreatePublicLinkUseCase.Result.Unknown:
                default:
                    throw new Exception($"Unexpected result in {nameof(CreatePublicLinkUseCase)}: {response.Result}");
            }
        }
        catch (Exception exc)
        {
            Logger.LogError(exc, exc.Message);
            Snackbar.Add("Не удалось создать ссылку публичного доступа из-за непредвиденной ошибки.", MudBlazor.Severity.Error);
        }
    }

    private Task CancelAsync() => Drawer.HideAsync();
    #endregion

    #region [Event Handlers]
    private async void OnAppEventHandler(object appEvent)
    {
        switch (appEvent)
        {
            case SubscribeCameraPublicAccessUseCase.UpdatedEvent updatedEto:
                await FetchAsync();
                await UpdateViewAsync();
                break;

            case SubscribeCameraPublicAccessUseCase.DeletedEvent deletedEto:
                Snackbar.Add("Просматриваемая вами камера была удалена", MudBlazor.Severity.Warning);
                await Drawer.HideAsync();
                break;

            default:
                Snackbar.Add("Было получено непредвиденное событие.", MudBlazor.Severity.Warning);
                await FetchAsync();
                await UpdateViewAsync();
                Logger.LogWarning($"Unexpected event in {nameof(SubscribeCameraPublicAccessUseCase)}: {nameof(appEvent)}");
                break;
        }
    }

    private void OnError(Exception exc)
    {
        Snackbar.Add("Ошибка при подписке на события", MudBlazor.Severity.Error);
        Logger.LogError(exc, exc.Message);
    }
    #endregion [Event Handlers]
}
