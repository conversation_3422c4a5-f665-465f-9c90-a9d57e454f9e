using Orleans;

namespace Teslametrics.Shared;

[GenerateSerializer]
public abstract class BaseSensorModel : ISensorModel
{
    [Id(0)]
    public Guid Id { get; set; }
    [Id(1)]
    public string Name { get; set; } = string.Empty;
    [Id(2)]
    public string? DisplayName { get; set; }

    public BaseSensorModel(Guid id, string name, string? displayName)
    {
        Id = id;
        Name = name;
        DisplayName = displayName;
    }
}
