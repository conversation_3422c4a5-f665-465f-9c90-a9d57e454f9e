using FluentValidation;
using MediatR;
using System.Reactive.Linq;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Domain.AccessControl.Organizations.Events;
using Teslametrics.App.Web.Domain.Cameras.Events;
using Teslametrics.App.Web.Domain.Folders.Events;
using Teslametrics.App.Web.Services.DomainEventBus;

namespace Teslametrics.App.Web.Features.Main.Cameras.TreeView;

public static class SubscribeTreeViewUseCase // TODO: Kirill
{
    public record Request(IObserver<UpdatedEvent> Observer, List<Guid> OrganizationIds) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public IDisposable? Subscription { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(IDisposable subscription)
        {
            Subscription = subscription;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Subscription = null;
            Result = result;
        }
    }

    public record UpdatedEvent();

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError
    }

    public class Validator : AbstractValidator<Request>
    {
        public Validator()
        {
            RuleFor(r => r.Observer).NotEmpty();
            RuleFor(r => r.OrganizationIds).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Request, Response>
    {
        private readonly IValidator<Request> _validator;
        private readonly IDomainEventBus _domainEventBus;

        public Handler(IValidator<Request> validator,
                       IDomainEventBus domainEventBus)
        {
            _validator = validator;
            _domainEventBus = domainEventBus;
        }

        public async Task<Response> Handle(Request request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            var eventStream = await _domainEventBus.GetEventStreamAsync();

            var subscription = eventStream
                .Where(e => e switch
                {
                    OrganizationCreatedEvent => true,
                    OrganizationUpdatedEvent @event => request.OrganizationIds.Contains(@event.Id),
                    OrganizationDeletedEvent @event => request.OrganizationIds.Contains(@event.Id),
                    FolderCreatedEvent @event => request.OrganizationIds.Contains(@event.OrganizationId),
                    FolderUpdatedEvent @event => request.OrganizationIds.Contains(@event.OrganizationId),
                    FolderParentChangedEvent @event => request.OrganizationIds.Contains(@event.OrganizationId),
                    FolderDeletedEvent @event => request.OrganizationIds.Contains(@event.OrganizationId),
                    CameraCreatedEvent @event => request.OrganizationIds.Contains(@event.OrganizationId),
                    CameraUpdatedEvent @event => request.OrganizationIds.Contains(@event.OrganizationId),
                    CameraFolderChangedEvent @event => request.OrganizationIds.Contains(@event.OrganizationId),
                    CameraDeletedEvent @event => request.OrganizationIds.Contains(@event.OrganizationId),
                    _ => false
                })
                .Select(e => new UpdatedEvent())
                .Subscribe(request.Observer);

            return new Response(subscription);
        }
    }
}