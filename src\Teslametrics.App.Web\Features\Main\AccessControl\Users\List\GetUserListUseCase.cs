using FluentValidation;
using MediatR;
using System.Data;
using Dapper;
using Teslametrics.Core.Services.Persistence;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Users.List;

public static class GetUserListUseCase
{
    public record Query(Guid UserId, Guid OrganizationId, int Offset, int Limit, string Filter) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public List<Item> Items { get; init; }

        public int TotalCount { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(List<Item> items, int totalCount)
        {
            Items = items;
            TotalCount = totalCount;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Result = result;

            Items = [];
            TotalCount = 0;
        }

        public record Item(Guid Id, string Username, bool IsSystem, bool IsOwner);
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError
    }

    public class Validator : AbstractValidator<Query>
    {
        public Validator()
        {
            RuleFor(q => q.UserId).NotEmpty();
            RuleFor(q => q.OrganizationId).NotEmpty();
            RuleFor(q => q.Offset).GreaterThanOrEqualTo(0);
            RuleFor(q => q.Limit).GreaterThan(0);
            RuleFor(q => q.Filter).MaximumLength(60);
        }
    }

    public class Handler : IRequestHandler<Query, Response>
    {
        private readonly IValidator<Query> _validator;
        private readonly IDbConnection _dbConnection;

        public Handler(IValidator<Query> validator,
                       IDbConnection dbConnection)
        {
            _validator = validator;
            _dbConnection = dbConnection;
        }

        public async Task<Response> Handle(Query request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            var hasPermission = request.UserId == SystemConsts.RootUserId
                || await HasPermissionAsync(request.OrganizationId, request.UserId);

            var template = SqlQueryBuilder.Create()
                .InnerJoin(Db.UserOrganizations.Table, Db.UserOrganizations.Props.UserId, Db.Users.Props.Id, SqlOperator.Equals)
                .InnerJoin(Db.Organizations.Table, Db.Organizations.Props.Id, Db.UserOrganizations.Props.OrganizationId, SqlOperator.Equals)
                .LeftJoin($"{Db.Organizations.Table} AS ow ON ow.{Db.Organizations.Columns.OwnerId} = {Db.Users.Props.Id} AND ow.{Db.Organizations.Columns.Id} = :OrganizationId", new { request.OrganizationId })
                .Where($"({Db.UserOrganizations.Props.OrganizationId} = :OrganizationId OR {Db.Users.Props.Id} = ow.{Db.Organizations.Columns.OwnerId})", new { request.OrganizationId })
                .WhereIf(request.UserId != SystemConsts.RootUserId, $"({Db.Organizations.Props.OwnerId} = :UserId OR :HasPermission is TRUE)", new { request.UserId, hasPermission })
                .WhereIf(!string.IsNullOrEmpty(request.Filter), Db.Users.Props.Name, "CONCAT('%', :Filter, '%')", SqlOperator.Like, new { request.Filter });

            var countTemplate = template.Build(QueryType.Standard, Db.Users.Table, RowSelection.AllRows, [$"COUNT(DISTINCT {Db.Users.Props.Id})"]);
            var totalCount = await _dbConnection.ExecuteScalarAsync<int>(countTemplate.RawSql, countTemplate.Parameters);

            var selectTemplate = template.Build(QueryType.Paginated,
                                                Db.Users.Table,
                                                RowSelection.UniqueRows,
                                                [
                                                    Db.Users.Props.Id,
                                                    Db.Users.Props.Name,
                                                    $"ow.{Db.Organizations.Columns.OwnerId} IS NOT NULL AS IsOwner"
                                                ],
                                                new { request.Limit, request.Offset });
            var users = await _dbConnection.QueryAsync<UserModel>(selectTemplate.RawSql, selectTemplate.Parameters);

            return new Response(users.Select(u =>
                new Response.Item(u.Id,
                                  u.Name,
                                  u.Id == SystemConsts.RootUserId,
                                  u.IsOwner)).ToList(), totalCount);
        }

        private async Task<bool> HasPermissionAsync(Guid organizationId, Guid userId)
        {
            var template = SqlQueryBuilder.Create()
                .Select($"COUNT(DISTINCT {Db.RolePermissions.Props.ResourceId}) > 0")
                .InnerJoin(Db.UserRoles.Table, Db.UserRoles.Props.RoleId, Db.RolePermissions.Props.RoleId, SqlOperator.Equals)
                .InnerJoin(Db.Roles.Table, Db.Roles.Props.Id, Db.RolePermissions.Props.RoleId, SqlOperator.Equals)
                .Where($"({Db.Roles.Props.OrganizationId} = :OrganizationId OR {Db.Roles.Props.OrganizationId} = :RootOrganizationId)", new { organizationId, SystemConsts.RootOrganizationId })
                .Where(Db.RolePermissions.Props.Permission, ":Permission", SqlOperator.Equals, new { Permission = Fqdn<AppPermissions>.GetName(AppPermissions.Main.AccessControl.Users.Read) })
                .Where(Db.UserRoles.Props.UserId, ":UserId", SqlOperator.Equals, new { userId })
                .Build(QueryType.Standard, Db.RolePermissions.Table, RowSelection.AllRows);

            return await _dbConnection.ExecuteScalarAsync<bool>(template.RawSql, template.Parameters);
        }
    }

    public record UserModel(Guid Id, string Name, bool IsOwner);
}