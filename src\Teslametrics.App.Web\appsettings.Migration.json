{"Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Exceptions"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Information", "Microsoft.EntityFrameworkCore": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"theme": "Serilog.Sinks.SystemConsole.Themes.AnsiConsoleTheme::Code, Serilog.Sinks.Console", "outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} <s:{SourceContext}>{NewLine}{Exception}"}}], "Enrich": ["FromLogContext", "WithExceptionDetails"]}, "Kestrel": {"Endpoints": {"HttpEndpoint": {"Url": "http://localhost:80"}}}, "ConnectionStrings": {"Default": "Host=localhost;Port=5432;Database=teslametrics;Username=admin;Password=****************"}, "AllowedHosts": "*"}