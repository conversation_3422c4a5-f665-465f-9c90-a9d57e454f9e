.plan-component-container {
    width: 100%;
    height: 100%;
    min-height: 400px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.plan-container {
    width: 100%;
    height: 100%;
    overflow: hidden;
    position: relative;
    background-color: rgb(248 252 255);
    user-select: none;
}

.plan-content {
    position: absolute;
    transform-origin: 0 0;
    min-width: 100%;
    min-height: 100%;
}


::deep .plan-object {
    z-index: 3;
}

::deep .plan_room {
    z-index: 2;
}

.plan-container {
    position: relative;
    width: 100%;
    height: 100%;
}

.plan-controls {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 1000;
}

.plan-reset-button {
    background-color: #fff;
    border: 1px solid #ccc;
    border-radius: 4px;
    padding: 5px 10px;
    cursor: pointer;
    font-size: 14px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.plan-reset-button:hover {
    background-color: #f0f0f0;
}