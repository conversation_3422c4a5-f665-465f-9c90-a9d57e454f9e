using FluentValidation;
using Microsoft.AspNetCore.Components;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Components.Drawer;
using Teslametrics.App.Web.Domain.AccessControl;
using Teslametrics.App.Web.Eto.Roles;
using Teslametrics.App.Web.Extensions;
using Teslametrics.App.Web.Shared;
using Severity = MudBlazor.Severity;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Roles.Drawer.Create;

public partial class RoleCreateComponent
{
	private class CreateValidator : BaseFluentValidator<CreateModel>
	{
		public CreateValidator()
		{
			RuleFor(model => model.Name)
				.Length(3, 60)
				.WithMessage("Название роли должно быть от 3 до 60 символов");

			RuleFor(model => model.Permissions)
				.NotEmpty()
				.WithMessage("Добавьте хотя бы 1 право доступа");
		}
	}
	private class CreateModel
	{
		public string Name { get; set; }

		public bool IsAdmin { get; set; }

		public IEnumerable<ResourcePermission> Permissions { get; set; }

		public CreateModel()
		{
			Name = string.Empty;
			Permissions = [];
		}
	}

	[CascadingParameter(Name = DrawerConsts.InstanceName)]
	private DrawerComponent Drawer { get; set; } = null!;

	private bool _isValid => Validator.Validate(_model).IsValid;
	private CreateModel _model { get; set; } = new();
	private CreateValidator Validator = new();

	[Parameter]
	public Guid OrganizationId { get; set; }

	private Task CancelAsync() => Drawer.HideAsync();

	private async Task SubmitAsync()
	{
		if (IsLoading || !_isValid) return;
		try
		{
			await SetLoadingAsync();
			var permissions = _model.Permissions.ToList();
			var response = await ScopeFactory.MediatorSend(new CreateRoleUseCase.Command(OrganizationId, _model.Name, _model.IsAdmin, permissions));
			switch (response.Result)
			{
				case CreateRoleUseCase.Result.Success:
					Snackbar.Add("Роль успешно создана", Severity.Success);
					await Drawer.HideAsync();
					break;
				case CreateRoleUseCase.Result.ValidationError:
					Snackbar.Add("Ошибка валидации данных", Severity.Error);
					break;
				case CreateRoleUseCase.Result.OrganizationNotFound:
					Snackbar.Add("Указанной огранизации не существует", Severity.Error);
					break;
				case CreateRoleUseCase.Result.RoleNameAlreadyExists:
					Snackbar.Add("Роль уже создана", Severity.Error);
					break;
				case CreateRoleUseCase.Result.Unknown:
				default:
					throw new Exception($"Unexpected result in {nameof(CreateRoleUseCase)}: {response.Result}");
			}
		}
		catch (Exception ex)
		{
			Snackbar.Add("Не удалось создать роль, повторите попытку", Severity.Error);
			Logger.LogError(ex, ex.Message);
		}
		finally
		{
			await SetLoadingAsync(false);
		}
	}

	private void IsAdminChanged(bool isAdmin)
	{
		if (_model is null)
			return;

		if (isAdmin)
		{
			_model.IsAdmin = true;
		}
		else
		{
			_model.IsAdmin = false;
			var adminPermissions = AppPermissions.GetAdmin();
			_model.Permissions = _model.Permissions.Where(x => !adminPermissions.Contains(x.Permission.Value));
		}
	}
}