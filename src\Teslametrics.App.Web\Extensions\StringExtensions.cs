﻿namespace Teslametrics.App.Web.Extensions;

public static class StringExtensions
{
	public static string Join(this IEnumerable<string> @this, string? separator = null)
	{
		return string.Join(separator, @this);
	}

    public static string ToSnakeCase(this string input)
    {
        if (string.IsNullOrEmpty(input)) return input;

        return string.Concat(input.Select((x, i) => i > 0 && char.IsUpper(x) ? "_" + x.ToString() : x.ToString())).ToLower();
    }
}