using FluentValidation;
using MediatR;
using Orleans.Streams;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Shared;
using Teslametrics.MediaServer.Orleans.Camera;
using Teslametrics.MediaServer.Orleans.Camera.Events;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Features.Main.Cameras.List.CameraCard;

public static class SubscribeCameraStatusUseCase
{
    public record Request(IObserver<StatusChangedEvent> Observer, Guid CameraId) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public IAsyncDisposable? Subscription { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(IAsyncDisposable subscription)
        {
            Subscription = subscription;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Subscription = null;
            Result = result;
        }
    }

    public record StatusChangedEvent(Guid Id, CameraStatus Status);

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        CameraNotFound
    }

    public class Validator : AbstractValidator<Request>
    {
        public Validator()
        {
            RuleFor(r => r.Observer).NotEmpty();
            RuleFor(r => r.CameraId).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Request, Response>
    {
        private readonly IValidator<Request> _validator;
        private readonly IClusterClient _clusterClient;

        public Handler(IValidator<Request> validator,
                       IClusterClient clusterClient)
        {
            _validator = validator;
            _clusterClient = clusterClient;
        }

        public async Task<Response> Handle(Request request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            var streamProvider = _clusterClient.GetStreamProvider(StreamNames.CameraEventLiveStream);

            var streamId = StreamId.Create(StreamNamespaces.CameraStreams, Guid.Empty);
            var stream = streamProvider.GetStream<CameraStatusChangedEvent>(streamId);

            var subscription = await stream.SubscribeAsync((@event, token) =>
            {
                if (@event.Id == request.CameraId)
                {
                    request.Observer.OnNext(new StatusChangedEvent(@event.Id, @event.Status));
                }

                return Task.CompletedTask;
            });

            return new Response(new DisposableSubscription<CameraStatusChangedEvent>(subscription));
        }
    }
}