﻿@using Teslametrics.App.Web.Exceptions
@inherits InteractiveBaseComponent
<div>
    <MudBadge Content="@(_response?.Incidents.Count ?? null)"
              Overlap="true"
              Visible="@_visible"
              Color="Color.Primary">
        <MudMenu Icon="@TeslaIcons.Notifications.Bell"
                 Label="Уведомления"
                 Class="bg_light_blue_surface menu_icon"
                 MaxHeight="200"
                 Size="Size.Large"
                 Variant="Variant.Filled">
            <MudStack Class="notif-header px-6 py-5">
                <MudBadge Content="@(_response?.Incidents.Count ?? null)"
                          Visible="@_visible"
                          Origin="Origin.CenterRight"
                          Color="Color.Primary">
                    <MudText Typo="Typo.h2"
                             Style="width: fit-content;">Уведомления</MudText>
                </MudBadge>
                @* <MudText Typo="Typo.subtitle2"
                         Class="pa-0">
                    Отметить все, как прочитанные
                </MudText> *@
            </MudStack>
            @if (_response is not null && _response.Incidents.Count != 0)
            {
                <MudStack Class="list overflow-auto"
                          Spacing="0">
                    @foreach (var row in _response.Incidents)
                    {
                        <MudStack Row="true"
                                  Class="notif-item py-3 px-6"
                                  Justify="Justify.FlexEnd"
                                  Spacing="3"
                                  Style="min-width: 400px;"
                                  AlignItems="AlignItems.Center"
                                  @onclick="@(() => ShowDetails(row))">
                            <!-- icon -->
                            <MudIcon Icon="@TeslaIcons.Notifications.Bell"
                                     Class="icon"
                                     Size="Size.Medium" />

                            <!-- text block -->
                            <div class="flex-grow-1 lh-1">
                                @switch (row.IncidentType)
                                {
                                    case Teslametrics.Shared.IncidentType.Door:
                                        <MudText Typo="Typo.subtitle1"
                                                 Class="fw-500">Превышено допустимое время с открытой дверью</MudText>
                                        break;
                                    case Teslametrics.Shared.IncidentType.Temperature:
                                        <MudText Typo="Typo.subtitle1"
                                                 Class="fw-500">Температура вышла за пределы допустимого диапазона</MudText>
                                        break;
                                    case Teslametrics.Shared.IncidentType.Humidity:
                                        <MudText Typo="Typo.subtitle1"
                                                 Class="fw-500">Влажность вышла за пределы допустимого диапазона</MudText>
                                        break;
                                    case Teslametrics.Shared.IncidentType.Leak:
                                        <MudText Typo="Typo.subtitle1"
                                                 Class="fw-500">Завиксирована протечка</MudText>
                                        break;
                                    case Teslametrics.Shared.IncidentType.Power:
                                        <MudText Typo="Typo.subtitle1"
                                                 Class="fw-500">Пропало питание</MudText>
                                        break;
                                }

                                <MudText Typo="Typo.subtitle2">
                                    @row.Device
                                </MudText>
                            </div>

                            <MudStack Justify="Justify.SpaceBetween"
                                      AlignItems="AlignItems.End">
                                <MudText Typo="Typo.caption"
                                         Class="color_neutral_40">
                                    @row.Date.ToString("dd.MM.yyyy HH:mm")
                                </MudText>
                                <div class="dotted"></div>
                            </MudStack>
                        </MudStack>
                    }
                </MudStack>
            }
            else
            {
                <MudStack AlignItems="AlignItems.Center"
                          Justify="Justify.Center"
                          Class="mud-height-full px-4 pb-4">
                    <MudIcon Icon="@TeslaIcons.Notifications.Bell"
                             Style="font-size: 2rem;" />
                    <MudText>Уведомлений нет</MudText>
                </MudStack>
            }
        </MudMenu>
    </MudBadge>
</div>