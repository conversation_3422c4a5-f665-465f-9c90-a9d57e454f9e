using System.ComponentModel.DataAnnotations;
using System.Reflection;
using Teslametrics.App.Web.Shared;

namespace Teslametrics.App.Web.Extensions;

public static class EnumExtensions
{
	public static string? GetName<T>(this T source) where T : IConvertible
	{
		if (!typeof(T).IsEnum)
			throw new ArgumentException("T must be an enumerated type");

		if (source is null)
			return null;

		string? sourceName = source.ToString();
		if (sourceName is null)
			return null;

		FieldInfo? fieldInfo = source.GetType().GetField(sourceName);
		if (fieldInfo == null)
			return sourceName;

		DisplayAttribute? displayNameAttribute = (DisplayAttribute?)fieldInfo.GetCustomAttribute(typeof(DisplayAttribute));
		if (displayNameAttribute is not null && displayNameAttribute.Name is not null)
			return displayNameAttribute.Name;

		return sourceName;
	}

	/// <summary>
	///     A generic extension method that aids in reflecting
	///     and retrieving any attribute that is applied to an `Enum`.
	/// </summary>
	public static TAttribute? GetAttribute<TAttribute>(this Enum enumValue)
			where TAttribute : Attribute
	{
		return enumValue.GetType()
						.GetMember(enumValue.ToString())
						.First()
						.GetCustomAttribute<TAttribute>();
	}

	public static bool IsAdminTagged(this Enum value) => value.HasAttribute<AdminAttribute>();
	public static bool IsAdminTagged<TEnum>() where TEnum : struct, Enum => typeof(TEnum).GetCustomAttribute<AdminAttribute>() != null;
	public static bool IsAdminTagged(this Type type) => type.GetCustomAttribute<AdminAttribute>() != null;

	public static bool HasAttribute<TAttribute>(this Enum value) where TAttribute : Attribute
	{
		// Получаем тип enum
		var enumType = value.GetType();

		// Получаем информацию о поле enum
		var fieldInfo = enumType.GetField(value.ToString());

		if (fieldInfo is null) return false;

		return fieldInfo.GetCustomAttributes(typeof(TAttribute), false).Length != 0;
	}

	public static string GetEnumPermissionString(this Enum value)
	{
		var valueType = value.GetType();
		var valueName = value.ToString();
		return Fqdn<AppPermissions>.GetNames((type, member) => type.FullName == valueType.FullName && member.Name == valueName).Single();
	}

	public static bool IsNullableEnum(this Type t)
	{
		return t.IsGenericType &&
			   t.GetGenericTypeDefinition() == typeof(Nullable<>) &&
			   t.GetGenericArguments()[0].IsEnum;
	}
}
