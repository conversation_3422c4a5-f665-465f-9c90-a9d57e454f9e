@inherits InteractiveBaseComponent
<div class="d_contents">
	@if (_response is not null)
	{
		<MudDataGrid T="GetIncidentListUseCase.Response.Incident"
					 Items="@_response.Incidents"
					 Elevation="0"
					 Filterable="false"
					 SortMode="@SortMode.None"
					 Groupable="false"
					 SelectedItem="_selectedIncident"
					 RowClick="OnSelectedIncidentChanged"
					 MultiSelection="false"
					 Hover="true"
					 Loading="IsLoading"
					 Virtualize="true"
					 FixedHeader="true"
					 Height="100%"
					 class="overflow-auto mud-height-full mx-4"
					 RowClassFunc="@GetRowClass">
			<Columns>
				<TemplateColumn CellClass="d-flex justify-start"
								Title="Статус">
					<CellTemplate>
						<MudIcon Icon="@(context.Item.IsResolved? TeslaIcons.State.Success : TeslaIcons.State.Warning)"
								 Color="@(context.Item.IsResolved? Color.Default: Color.Error)" />
					</CellTemplate>
				</TemplateColumn>
				<PropertyColumn Property="x => x.City"
								Title="Город" />
				<PropertyColumn Property="x => x.Address"
								Title="Здание" />
				<PropertyColumn Property="x => x.Floor"
								Title="Этаж" />
				<PropertyColumn Property="x => x.Room"
								Title="Помещение" />
				<PropertyColumn Property="x => x.Device"
								Title="Оборудование" />
				<PropertyColumn Property="x => x.Date.Date.ToShortDateString()"
								Title="Дата" />
				<PropertyColumn Property="x => x.Time"
								Title="Время" />
				<TemplateColumn CellClassFunc="@((context) => context.IsViewed ? "d-flex justify-space-between" : "d-flex justify-space-between unread")"
								Title="Тип происшествия">
					<CellTemplate>
						<MudStack Row>
							<MudIcon Icon="@GetIncidentIcon(context.Item.IncidentType)"
									 Color="@(context.Item.IsResolved? Color.Default: Color.Error)" />
							@context.Item.IncidentType.GetName()
						</MudStack>
					</CellTemplate>
				</TemplateColumn>
			</Columns>
			<NoRecordsContent>
				@if (IsLoading)
				{
					<MudPaper Class="d-flex flex-column align-center justify-center pa-6"
							  Style="min-height: 200px; width: 100%; text-align: center;">
						<MudSkeleton Width="50%"
									 Height="24px"
									 Class="mb-2" />
						<MudSkeleton Width="60%"
									 Height="20px"
									 Class="mb-1" />
						<MudSkeleton Width="40%"
									 Height="20px" />
					</MudPaper>
				}
				else
				{
					<MudText Typo="Typo.body1"
							 Align="Align.Center"
							 Color="Color.Secondary">
						Нет данных для отображения
					</MudText>
				}
			</NoRecordsContent>
		</MudDataGrid>
	}
</div>