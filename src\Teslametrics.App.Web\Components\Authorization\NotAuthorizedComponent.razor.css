/*.container {
	display: grid;
	align-items: start;
	grid-template-columns: auto 1fr;
}*/
.container {
	container: grid / inline-size;
	min-height: fit-content;
}

.grid {
	display: grid;
	height: fit-content;
	align-items: start;
	justify-content: start;
}

@container grid (width >= 600px) {
	.grid {
		grid-template-columns: auto 1fr;
		align-items: start;
		justify-content: start;
	}
}

@container grid (width < 600px) {
	.grid {
		grid-template-columns: 1fr;
		align-items: center;
		justify-items: center;
	}
}
