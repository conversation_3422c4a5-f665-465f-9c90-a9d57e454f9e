using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Domain.AccessControl.Organizations;
using Teslametrics.App.Web.Domain.AccessControl.Organizations.Events;
using Teslametrics.App.Web.Services.Outbox;
using Teslametrics.App.Web.Services.TransactionManager;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Organizations.DeleteDialog;

public static class DeleteOrganizationUseCase
{
    public record Command(Guid Id) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(Result result)
        {
            Result = result;
        }
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        CannotDeleteSystemOrganization
    }

    public class Validator : AbstractValidator<Command>
    {
        public Validator()
        {
            RuleFor(c => c.Id).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Command, Response>
    {
        private readonly IValidator<Command> _validator;
        private readonly IOrganizationRepository _organizationRepository;
        private readonly ITransactionManager _transactionManager;
        private readonly IPublisher _publisher;
        private readonly IOutbox _outbox;

        public Handler(IValidator<Command> validator,
                       IOrganizationRepository organizationRepository,
                       ITransactionManager transactionManager,
                       IPublisher publisher,
                       IOutbox outbox)
        {
            _validator = validator;
            _organizationRepository = organizationRepository;
            _transactionManager = transactionManager;
            _publisher = publisher;
            _outbox = outbox;
        }

        public async Task<Response> Handle(Command request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            if (request.Id == SystemConsts.RootOrganizationId)
            {
                return new Response(Result.CannotDeleteSystemOrganization);
            }

            using var transaction = await _transactionManager.CreateTransactionAsync();

            await _organizationRepository.DeleteAsync(request.Id, cancellationToken);

            List<object> events = [new OrganizationDeletedEvent(request.Id)];

            await _organizationRepository.SaveChangesAsync(cancellationToken);

            foreach (var @event in events)
            {
                await _publisher.Publish(@event, cancellationToken);
            }

            await _outbox.AddRangeAsync(events);

            await transaction.CommitAsync();

            return new Response(Result.Success);
        }
    }
}