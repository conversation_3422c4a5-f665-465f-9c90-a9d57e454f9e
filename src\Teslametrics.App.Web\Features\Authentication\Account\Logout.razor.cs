﻿using Microsoft.AspNetCore.Components;
using Teslametrics.App.Web.Extensions;
using Teslametrics.App.Web.Services.Authentication;

namespace Teslametrics.App.Web.Features.Authentication.Account;

public partial class Logout
{

	[Inject]
	protected IHttpContextAccessor ContextAccessor { get; set; } = null!;

	protected override async Task OnInitializedAsync()
	{
		await ScopeFactory.MediatorSend(new UserLogOutUseCase.Command());
		ContextAccessor.HttpContext!.Response.Redirect(RouteConstants.LogInPage);
	}
}
