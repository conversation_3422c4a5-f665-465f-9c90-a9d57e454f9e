using System.Reactive.Linq;
using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.Core.Domain.Folders.Events;
using Teslametrics.App.Web.Exceptions;
using Teslametrics.Core.Services.DomainEventBus;

namespace Teslametrics.App.Web.Features.Main.Cameras.DeleteFolderDialog;

public static class SubscribeFolderUseCase
{
    public record Request(IObserver<object> Observer, Guid FolderId) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public IDisposable? Subscription { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(IDisposable subscription)
        {
            Subscription = subscription;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Subscription = null;
            Result = result;
        }
    }

	public record UpdatedEvent(Guid Id);
	public record DeletedEvent(Guid Id);

	public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError
    }

    public class Validator : AbstractValidator<Request>
    {
        public Validator()
        {
            RuleFor(r => r.Observer).NotEmpty();
            RuleFor(r => r.FolderId).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Request, Response>
    {
        private readonly IValidator<Request> _validator;
        private readonly IDomainEventBus _domainEventBus;

        public Handler(IValidator<Request> validator,
                       IDomainEventBus domainEventBus)
        {
            _validator = validator;
            _domainEventBus = domainEventBus;
        }

        public async Task<Response> Handle(Request request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            var eventStream = await _domainEventBus.GetEventStreamAsync();

            var subscription = eventStream
                .Where(e => e switch
                {
                    FolderUpdatedEvent @event => @event.Id == request.FolderId,
                    FolderDeletedEvent @event => @event.Id == request.FolderId,
                    _ => false
                })
                .Select<object, object>( e => e switch
                {
                    FolderUpdatedEvent @event => new UpdatedEvent(@event.Id),
                    FolderDeletedEvent @event => new DeletedEvent(@event.Id),
                    _ => throw new AppException("Invalid event type")
                })
                .Subscribe(request.Observer);

                return new Response(subscription);
        }
    }
}