using MudBlazor;
using Teslametrics.App.Web.Events.Users;

namespace Teslametrics.App.Web.Features.Authentication.Account;

public partial class UserBannedDialog
{
	private DialogOptions _dialogOptions = new() { CloseOnEscapeKey = true, FullWidth = true, MaxWidth = MaxWidth.Medium, CloseButton = true };
	private bool _isVisible;

	protected override void OnInitialized()
	{
		CompositeDisposable.Add(EventSystem.Subscribe<UserBannedEto>(OnUserBanned));

		base.OnInitialized();
	}

	private void OnUserBanned(UserBannedEto eto)
	{
		_isVisible = true;
		StateHasChanged();
	}
}
