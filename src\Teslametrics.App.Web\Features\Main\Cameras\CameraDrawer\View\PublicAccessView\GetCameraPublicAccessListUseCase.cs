using System.Data;
using Dapper;
using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.Shared;
using Teslametrics.Core.Services.Persistence;

namespace Teslametrics.App.Web.Features.Main.Cameras.CameraDrawer.View.PublicAccessView;

public static class GetCameraPublicAccessListUseCase
{
    public record Query(Guid CameraId) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public List<Item> Items { get; init; } // Массив публичных ссылок

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(List<Item> items)
        {
            Result = Result.Success;
            Items = items;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Result = result;
            Items = [];
        }

        public record Item(Guid Id, string Name);
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        AccessNotFound
    }

    public class Validator : AbstractValidator<Query>
    {
        public Validator()
        {
            RuleFor(q => q.CameraId).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Query, Response>
    {
        private readonly IValidator<Query> _validator;
        private readonly IDbConnection _dbConnection;

        public Handler(IValidator<Query> validator,
                       IDbConnection dbConnection)
        {
            _validator = validator;
            _dbConnection = dbConnection;
        }

        public async Task<Response> Handle(Query request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            var template = SqlQueryBuilder.Create()
                .Select(Db.PublicLinks.Props.Id)
                .Select(Db.PublicLinks.Props.Name)
                .Where(Db.PublicLinks.Props.CameraId, ":CameraId", SqlOperator.Equals, new { request.CameraId })
                .Build(QueryType.Standard, Db.PublicLinks.Table, RowSelection.AllRows);

            var publicLinks = await _dbConnection.QueryAsync<PublicLinkModel>(template.RawSql, template.Parameters);

            return new Response(publicLinks.Select(pl => new Response.Item(pl.Id, pl.Name)).ToList());
        }
    }

    public record PublicLinkModel(Guid Id, string Name);
}