﻿@using Teslametrics.App.Web.Features.DatabaseMigrations
@layout DatabaseMigrationsLayout
@{
	#if DEBUG
}
@attribute [Route("/migrations")]
@{
	#endif
}
@attribute [StreamRendering(true)]
@inherits BaseComponent
<div class="d_contents">
	<MudContainer MaxWidth="MaxWidth.False" Class="py-3">
		<MudGrid>
			<MudItem xs="12" md="6">
				<MudDataGrid Items="@_pendingMigrations" Loading="_isLoading" Virtualize="true" FixedHeader="true" Height="85vh">
					<ToolBarContent>
						<MudText Typo="Typo.h6">Неприменённые миграции (<MudText Inline="true" Color="@(_pendingMigrations.Count == 0 ? Color.Success : Color.Warning)">@_pendingMigrations.Count</MudText>)</MudText>
						<MudSpacer />
						<MudButton OnClick="ApplyMigrationsAsync" Disabled="_pendingMigrations.Count == 0">Применить миграции</MudButton>
					</ToolBarContent>
					<Columns>
						<PropertyColumn Property="x => x.DbContextName" Title="Context name" />
						<PropertyColumn Property="x => x.Migration" Title="Migraion" />
						<PropertyColumn Property="x => x.Applied">
							<CellTemplate>
								<MudStack Row="true" Justify="Justify.Center" AlignItems="AlignItems.Center">
									<MudIcon Icon="@Icons.Material.Filled.Warning" Color="Color.Warning" />
									<MudText>НЕ применена</MudText>
								</MudStack>
							</CellTemplate>
						</PropertyColumn>
					</Columns>
					<NoRecordsContent>
						<MudStack AlignItems="AlignItems.Center" Justify="Justify.Center" Class="mud-height-full">
							<MudIcon Icon="@Icons.Material.Filled.Check" Style="font-size: 8rem;" Color="Color.Success" />
							<MudText Typo="Typo.body1">Ничего не найдено</MudText>
							<MudText Typo="Typo.subtitle1">Все миграции применены</MudText>
						</MudStack>
					</NoRecordsContent>
					<PagerContent>
						<MudDataGridPager T="Element" InfoFormat="{first_item}-{last_item} из {all_items}" RowsPerPageString="Строк на страницу:" />
					</PagerContent>
				</MudDataGrid>
			</MudItem>

			<MudItem xs="12" md="6">
				<MudDataGrid Items="@_appliedMigrations" Loading="_isLoading" Virtualize="true" FixedHeader="true" Height="85vh">
					<ToolBarContent>
						<MudText Typo="Typo.h6">Применённые миграции (<MudText Inline="true">@_appliedMigrations.Count</MudText>)</MudText>
						<MudSpacer />
					</ToolBarContent>
					<Columns>
						<PropertyColumn Property="x => x.DbContextName" Title="Context name" />
						<PropertyColumn Property="x => x.Migration" Title="Migraion" />
						<PropertyColumn Property="x => x.Applied">
							<CellTemplate>
								<MudStack Row="true" Justify="Justify.Center" AlignItems="AlignItems.Center">
									<MudIcon Icon="@Icons.Material.Filled.Check" Color="Color.Success" />
									<MudText>Применена</MudText>
								</MudStack>
							</CellTemplate>
						</PropertyColumn>
					</Columns>
					<NoRecordsContent>
						<MudStack AlignItems="AlignItems.Center" Justify="Justify.Center" Class="mud-height-full">
							<MudIcon Icon="@Icons.Material.Filled.ErrorOutline" Style="font-size: 8rem;" Color="Color.Warning" />
							<MudText Typo="Typo.body1">Нет применённых миграций</MudText>
							<MudText Typo="Typo.subtitle1">Выполните миграцию</MudText>
						</MudStack>
					</NoRecordsContent>
					<PagerContent>
						<MudDataGridPager T="Element" InfoFormat="{first_item}-{last_item} из {all_items}" RowsPerPageString="Строк на страницу:" />
					</PagerContent>
				</MudDataGrid>
			</MudItem>
		</MudGrid>
	</MudContainer>
</div>
