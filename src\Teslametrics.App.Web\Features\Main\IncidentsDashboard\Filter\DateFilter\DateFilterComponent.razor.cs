using Microsoft.AspNetCore.Components;

namespace Teslametrics.App.Web.Features.Main.IncidentsDashboard.Filter.DateFilter;

public partial class DateFilterComponent
{
    private enum FilterDate
    {
        Today,
        Yesterday,
        Week,
        Month,
        Custom
    };

    private MudBlazor.DateRange _range = new(DateTime.Today, DateTime.Today.AddDays(1).AddMilliseconds(-1));
    private string _rangeText => $"{_range.Start:dd.MM} — {_range.End:dd.MM}";
    private FilterDate _quick = FilterDate.Today;

    [Parameter]
    public MudBlazor.DateRange DateRange { get; set; } = new(DateTime.Today, DateTime.Today.AddDays(1).AddMilliseconds(-1));

    [Parameter]
    public EventCallback<MudBlazor.DateRange> DateRangeChanged { get; set; }

    protected override void OnInitialized()
    {
        _range = DateRange;
        UpdateQuickByRange(_range);
        base.OnInitialized();
    }

    protected override void OnParametersSet()
    {
        if (_range != DateRange)            // сравнивайте Start/End, если == не перегружен
        {
            _range = DateRange;
            UpdateQuickByRange(_range);
        }
        base.OnParametersSet();
    }

    private async Task OnDateRangeChanged(MudBlazor.DateRange range)
    {
        _quick = FilterDate.Custom;

        var newRange = range;

        if (newRange.End.HasValue)
            newRange.End = newRange.End.Value.Date.AddDays(1).AddMilliseconds(-1);

        if (DateRangeChanged.HasDelegate)
            await DateRangeChanged.InvokeAsync(newRange);
    }

    private async Task OnQuickChanged(FilterDate value)
    {
        _quick = value;
        _range = value switch
        {
            FilterDate.Today => new(DateTime.Today, DateTime.Today.AddDays(1).AddMilliseconds(-1)),
            FilterDate.Yesterday => new(DateTime.Today.AddDays(-1), DateTime.Today.AddMilliseconds(-1)),
            FilterDate.Week => new(DateTime.Today.AddDays(-7), DateTime.Today.AddDays(1).AddMilliseconds(-1)),
            FilterDate.Month => new(new DateTime(DateTime.Today.Year, DateTime.Today.Month, 1), DateTime.Today.AddDays(1).AddMilliseconds(-1)),
            _ => _range
        };
        DateRange = _range;
        if (DateRangeChanged.HasDelegate)
            await DateRangeChanged.InvokeAsync(DateRange);
    }

    private void UpdateQuickByRange(MudBlazor.DateRange r)
    {
        // Вспомогательные "канонические" диапазоны
        var today = new MudBlazor.DateRange(
                            DateTime.Today,
                            DateTime.Today.AddDays(1).AddMilliseconds(-1));
        var yesterday = new MudBlazor.DateRange(
                            DateTime.Today.AddDays(-1),
                            DateTime.Today.AddMilliseconds(-1));
        var week = new MudBlazor.DateRange(
                            DateTime.Today.AddDays(-7),
                            DateTime.Today.AddDays(1).AddMilliseconds(-1));
        var month = new MudBlazor.DateRange(
                            new DateTime(DateTime.Today.Year, DateTime.Today.Month, 1),
                            DateTime.Today.AddDays(1).AddMilliseconds(-1));

        // Сравниваем "точь-в-точь"; если End может отличаться на миллисекунду, 
        // приведите оба диапазона к одному формату прежде чем сравнивать.
        _quick = r == today ? FilterDate.Today
              : r == yesterday ? FilterDate.Yesterday
              : r == week ? FilterDate.Week
              : r == month ? FilterDate.Month
              : FilterDate.Custom;
    }
}
