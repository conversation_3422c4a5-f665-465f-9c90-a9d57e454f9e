using Microsoft.AspNetCore.Components;
using DateRange = MudBlazor.DateRange;

namespace Teslametrics.App.Web.Features.Main.IncidentsDashboard.Filter;

public partial class FilterComponent
{
    [Parameter]
    public DateRange DateRange { get; set; } = new(DateTime.Today, DateTime.Today.AddDays(1).AddMilliseconds(-1));

    [Parameter]
    public EventCallback<DateRange> DateRangeChanged { get; set; }

    [Parameter]
    public Guid? CityId { get; set; }

    [Parameter]
    public EventCallback<Guid?> CityIdChanged { get; set; }

    [Parameter]
    public Guid? BuildingId { get; set; }

    [Parameter]
    public EventCallback<Guid?> BuildingIdChanged { get; set; }

    [Parameter]
    public Guid? FloorId { get; set; }

    [Parameter]
    public EventCallback<Guid?> FloorIdChanged { get; set; }

    [Parameter]
    public Guid? RoomId { get; set; }

    [Parameter]
    public EventCallback<Guid?> RoomIdChanged { get; set; }

    [Parameter]
    public Guid? DeviceId { get; set; }

    [Parameter]
    public EventCallback<Guid?> DeviceIdChanged { get; set; }

}
