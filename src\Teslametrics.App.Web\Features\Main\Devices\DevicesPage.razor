@using Teslametrics.App.Web.Features.Main.Devices.FloorContent
@using Teslametrics.App.Web.Features.Main.Devices.RoomContent
@using Teslametrics.App.Web.Features.Main.Devices.Toolbar
@using Teslametrics.App.Web.Features.Main.Devices.FloorSelector
@using Teslametrics.App.Web.Features.Main.Devices.Diagrams
@using Teslametrics.App.Web.Features.Main.Devices.Breadcrumbs

@page "/devices"

@attribute [Authorize]
<div class="container mud-height-full mud-height-full pl-12 pr-10 pb-12 overflow-auto layout">
	<ToolBarComponent City="@_cityId"
					  CityChanged="OnCityChanged"
					  Building="@_buildingId"
					  BuildingChanged="OnBuildingChanged" />
	<BuildingPlaceholderComponent CityId="@_cityId"
								  BuildingId="_buildingId" />
	<FloorPlaceholderComponent CityId="@_cityId"
							   BuildingId="_buildingId"
							   FloorId="@_floorId" />
	@if (_cityId is not null && _buildingId is not null)
	{
		@if (@_floorId is not null)
		{
			<MudPaper Elevation="0"
					  Outlined="true"
					  Class="mud-height-full overflow-auto">
				<div class="floor_info px-3 py-4">
					<BreadcrumbsComponent CityId="@_cityId.Value"
										  BuildingId="@_buildingId.Value"
										  FloorId="@_floorId.Value"
										  RoomId="@_roomId" />
					@if (_roomId.HasValue)
					{
						<RoomContentComponent RoomId="_roomId.Value" />
					}
					else
					{
						<FloorContentComponent OnRoomSelected="OnRoomSelect"
											   FloorId="@_floorId.Value" />
					}
				</div>
			</MudPaper>
		}
		<MudPaper Elevation="0"
				  Outlined="true"
				  Class="mud-height-full relative overflow-hidden">
			<FloorsComponent CityId="@_cityId.Value"
							 BuildingId="@_buildingId.Value"
							 Floor="@_floorId"
							 FloorChanged="OnFloorSelect" />
			<PlanComponent CityId="@_cityId"
						   BuildingId="_buildingId"
						   FloorId="@_floorId"
						   RoomId="@_roomId"
						   RoomIdChanged="OnRoomSelect" />
			<FloorPlanPlaceholderComponent CityId="@_cityId"
										   BuildingId="_buildingId"
										   FloorId="@_floorId" />
		</MudPaper>
	}
</div>