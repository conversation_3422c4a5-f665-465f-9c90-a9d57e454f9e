using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using MudBlazor;
using System.Reactive;
using Teslametrics.App.Web.Exceptions;
using Teslametrics.App.Web.Extensions;
using Teslametrics.App.Web.Features.Main.Organization.Organizations.List;

namespace Teslametrics.App.Web.Features.Main.Organization.Organizations;

public partial class OrganizationsListComponent
{
	private record Organization(Guid Id, string Name, string? Owner);

	private bool _disposedValue;
	private IDisposable? _subscription;
	private DateTime _lastRefreshTime = DateTime.Now;
	private List<Organization> _organizations = [];

	protected int CurrentPage => Offset / Limit;

	#region Parameters
	[Parameter]
	public int Offset { get; set; } = 0;
	[Parameter]
	public EventCallback<int> OffsetChanged { get; set; }
	[Parameter]
	public int Limit { get; set; } = 25;
	[Parameter]
	public EventCallback<int> LimitChanged { get; set; }
	[Parameter]
	public string? SearcString { get; set; } = string.Empty;
	[Parameter]
	public EventCallback<string?> SearcStringChanged { get; set; }
	#endregion
	protected override async Task OnInitializedAsync()
	{
		await base.OnInitializedAsync();
		await FetchAsync();
		await SubscribeAsync();

		AuthenticationStateProvider.AuthenticationStateChanged += OnAuthenticationStateChanged;
	}

	protected override void Dispose(bool disposing)
	{
		if (!_disposedValue)
		{
			if (disposing)
			{
				AuthenticationStateProvider.AuthenticationStateChanged -= OnAuthenticationStateChanged;
			}

			_disposedValue = true;
		}

		// Вызов базового метода Dispose
		base.Dispose(disposing);
	}

	protected virtual async Task FetchAsync()
	{
		if (IsLoading) return;

		try
		{
			await SetLoadingAsync();
			_organizations.Clear();
			var userId = await GetCurrentUserIdAsync() ?? throw new NotAuthorizedException();
			var _response = await ScopeFactory.MediatorSend(new GetOrganizationListUseCase.Query(userId, Offset, Limit, SearcString ?? string.Empty));
			switch (_response.Result)
			{
				case GetOrganizationListUseCase.Result.Success:
					_lastRefreshTime = DateTime.Now;
					_organizations = _response.Items.Select(item => new Organization(item.Id, item.Name, item.Owner)).ToList();
					break;

				case GetOrganizationListUseCase.Result.ValidationError:
					Snackbar.Add("Ошибка валидации при получении списка организаций", Severity.Error);
					break;

				default:
				case GetOrganizationListUseCase.Result.Unknown:
					Snackbar.Add("Не удалось получить список организаций повторите попытку. Если проблема сохраняется - обратитесь к администратору", Severity.Error);
					break;
			}
		}
		catch (Exception ex)
		{
			Snackbar.Add("Не удалось получить список организаций повторите попытку. Если проблема сохраняется - обратитесь к администратору", Severity.Error);
			Logger.LogError(ex, ex.Message);
		}
		finally
		{
			await SetLoadingAsync(false);
		}
	}

	private async Task SubscribeAsync()
	{
		Unsubscribe();
		var userId = await GetCurrentUserIdAsync() ?? throw new NotAuthorizedException();
		var result = await ScopeFactory.MediatorSend(new SubscribeOrganizationListUseCase.Request(Observer.Create<object>(OnAppEventHandler, OnError), userId));
		switch (result.Result)
		{
			case SubscribeOrganizationListUseCase.Result.Success:
				_subscription = result.Subscription!;
				CompositeDisposable.Add(_subscription);
				break;
			case SubscribeOrganizationListUseCase.Result.ValidationError:
				Snackbar.Add("Ошибка валидации при подписке на события", Severity.Error);
				break;

			case SubscribeOrganizationListUseCase.Result.Unknown:
			default:
				Snackbar.Add("Не удалось получить подписку на обновления из-за непредвиденной ошибки:" + result.Result.ToString(), Severity.Error);
				break;
		}
	}

	private void Unsubscribe()
	{
		if (_subscription is not null)
		{
			CompositeDisposable.Remove(_subscription);
			_subscription?.Dispose();
			_subscription = null;
		}
	}

	#region [Actions]
	private Task RefreshAsync() => FetchAsync();
	#endregion

	#region [EventHandlers]
	private async Task OnSearchChanged(string? _searchString)
	{
		SearcString = _searchString;
		if (SearcStringChanged.HasDelegate)
			await SearcStringChanged.InvokeAsync(SearcString);

		await FetchAsync();
	}
	private Task RowsPerPageChanged(int limit)
	{
		Limit = limit;
		if (LimitChanged.HasDelegate)
		{
			return LimitChanged.InvokeAsync(limit);
		}
		return Task.CompletedTask;
	}

	private async void OnAppEventHandler(object appEvent)
	{
		await RefreshAsync();
		await UpdateViewAsync();
	}

	private void OnError(Exception exc)
	{
		Logger.LogError(exc, exc.Message);
		Snackbar.Add("Ошибка валидации при подписке на события", Severity.Error);
	}

	private async void OnAuthenticationStateChanged(Task<AuthenticationState> authState)
	{
		await FetchAsync();
	}
	#endregion
}
