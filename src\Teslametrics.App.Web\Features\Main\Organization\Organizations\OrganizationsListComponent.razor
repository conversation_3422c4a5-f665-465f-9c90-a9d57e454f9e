﻿@inherits InteractiveBaseComponent
@attribute [StreamRendering]
<MudDataGrid T="Organization"
			 Items="@(_organizations ?? [])"
			 Filterable="false"
			 SortMode="SortMode.None"
			 Outlined="false"
			 Elevation="0"
			 CurrentPage="CurrentPage"
			 RowsPerPage="Limit"
			 RowsPerPageChanged="RowsPerPageChanged"
			 Loading="IsLoading"
			 Hover="true">
	<ToolBarContent>
		<MudText Typo="Typo.h6">Список организаций</MudText>
		<MudSpacer />
		<MudStack Row="true"
				  AlignItems="AlignItems.Center"
				  Justify="Justify.Center	">
			<MudTextField T="string"
						  Value="SearcString"
						  ValueChanged="OnSearchChanged"
						  Placeholder="Поиск"
						  Label="Поиск"
						  Adornment="Adornment.Start"
						  AdornmentIcon="@Icons.Material.Filled.Search"
						  IconSize="Size.Medium"
						  Immediate="true"
						  Class="mb-0" />
		</MudStack>
	</ToolBarContent>
	<Columns>
		<PropertyColumn Property="x => x.Name"
						Title="Наименование" />
		<PropertyColumn Property="x => x.Owner"
						Title="Владелец">
			<HeaderTemplate>
				<MudText>Владелец</MudText>
				<MudSpacer />
				<MudTooltip Text="@($"Время последнего обновления: {_lastRefreshTime.ToLocalTime()}")"
							Arrow="true"
							Placement="Placement.Left">
					<MudIconButton OnClick="RefreshAsync"
								   Icon="@Icons.Material.Outlined.Refresh" />
				</MudTooltip>
			</HeaderTemplate>
		</PropertyColumn>
	</Columns>
	<RowLoadingContent>
		<MudSkeleton />
	</RowLoadingContent>
	<NoRecordsContent>
		<MudStack Class="mud-width-full"
				  AlignItems="AlignItems.Center"
				  Justify="Justify.Center">
			<MudButton OnClick="RefreshAsync"
					   Variant="Variant.Filled"
					   Color="Color.Primary">Обновить</MudButton>
		</MudStack>
	</NoRecordsContent>
	<PagerContent>
		<MudDataGridPager T="Organization"
						  InfoFormat="{first_item}-{last_item} из {all_items}"
						  RowsPerPageString="Строк на страницу:" />
	</PagerContent>
</MudDataGrid>