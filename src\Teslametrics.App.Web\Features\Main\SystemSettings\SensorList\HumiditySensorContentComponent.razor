@if (_model is not null)
{
	<div class="d-flex flex-row align-center gap-3">
		<MudTextField @bind-Value="_model.MinHumidity" Label="Минимальная влажность" Variant="Variant.Outlined" />
		<MudTextField @bind-Value="_model.MaxHumidity" Label="Максимальная влажность" Variant="Variant.Outlined" />
	</div>
}

@code {
	private HumidityModel? _model => SensorModel as HumidityModel;

	[Parameter]
	public ISensorModel? SensorModel { get; set; }
}
