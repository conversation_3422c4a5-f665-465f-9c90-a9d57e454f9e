using Microsoft.AspNetCore.Components;
using System.Reactive.Disposables;
using Teslametrics.App.Web.Events;
using Teslametrics.App.Web.Events.Presets;

namespace Teslametrics.App.Web.Features.Main.Presets.Drawer;

public partial class PresetDrawerComponent
{
	private DrawerMode _mode;
	private Guid? _organizationId = null;
	private Guid? _resourceId = null;

	public bool IsOpened => _mode != DrawerMode.Hidden;

	public enum DrawerMode
	{
		Hidden,
		Create,
		Edit,
		View
	}
	protected override void OnInitialized()
	{
		CompositeDisposable.Add(EventSystem.Subscribe<PresetCreateEto>(OnEventHandler));
		CompositeDisposable.Add(EventSystem.Subscribe<PresetSelectEto>(OnEventHandler));
		CompositeDisposable.Add(EventSystem.Subscribe<PresetEditEto>(OnEventHandler));
		base.OnInitialized();
	}

	public Task ShowCreateAsync() => InvokeAsync(() =>
	{
		_resourceId = null;
		_mode = DrawerMode.Create;
		StateHasChanged();
	});

	public Task ShowEditAsync(Guid resourceId) => InvokeAsync(() =>
	{
		_resourceId = resourceId;
		_mode = DrawerMode.Edit;
		StateHasChanged();
	});

	public Task ShowViewAsync(Guid resourceId) => InvokeAsync(() =>
	{
		_resourceId = resourceId;
		_mode = DrawerMode.View;
		StateHasChanged();
	});

	public Task ShowAsync(DrawerMode mode, Guid resourceId) => InvokeAsync(() =>
	{
		if (mode == DrawerMode.Hidden || mode == DrawerMode.Create)
		{
			throw new ArgumentException($"DrawerMode Edit or View expected {mode.ToString()} provided");
		}

		_mode = mode;
		_resourceId = resourceId;
		StateHasChanged();
	});

	public Task CloseAsync() => InvokeAsync(() =>
	{
		_mode = DrawerMode.Hidden;
		_resourceId = null;
		StateHasChanged();
	});

	private Task ShowAsync() => InvokeAsync(() =>
	{
		_mode = DrawerMode.Create;
		StateHasChanged();
	});

	#region [Event Handlers]
	private async void OnEventHandler(BaseEto eto)
	{
		switch (eto)
		{
			case PresetEditEto editEto:
				await ShowEditAsync(editEto.PresetId);
				break;

			case PresetCreateEto createEto:
				await ShowCreateAsync();
				break;

			case PresetSelectEto selectEto:
				await ShowViewAsync(selectEto.PresetId);
				break;

			default:
				await CloseAsync();
				break;
		}
	}

	private Task OnOpenChanged(bool opened)
	{
		if (opened)
		{
			if (_mode != DrawerMode.Hidden && _resourceId.HasValue)
			{
				return ShowAsync(_mode, _resourceId.Value);
			}
			else
			{
				return ShowAsync();
			}
		}
		else
		{
			return CloseAsync();
		}
	}
	#endregion [Event Handlers]
}
