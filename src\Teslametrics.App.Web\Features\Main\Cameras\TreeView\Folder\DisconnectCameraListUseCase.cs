using System.Data;
using Dapper;
using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.Shared;
using Teslametrics.MediaServer.Orleans.Camera;
using Teslametrics.Core.Services.Persistence;

namespace Teslametrics.App.Web.Features.Main.Cameras.TreeView.Folder;

public static class DisconnectCameraListUseCase
{
    public record Command(Guid FolderId) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(Result result)
        {
            Result = result;
        }
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError
    }

    public class Validator : AbstractValidator<Command>
    {
        public Validator()
        {
            RuleFor(c => c.FolderId).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Command, Response>
    {
        private readonly IValidator<Command> _validator;
        private readonly IDbConnection _dbConnection;
        private readonly IClusterClient _clusterClient;

        public Handler(IValidator<Command> validator,
                       IDbConnection dbConnection,
                       IClusterClient clusterClient)
        {
            _validator = validator;
            _dbConnection = dbConnection;
            _clusterClient = clusterClient;
        }

        public async Task<Response> Handle(Command request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            var template = SqlQueryBuilder.Create()
                .Select(Db.Cameras.Props.Id)
                .Where(Db.Cameras.Props.FolderId, ":FolderId", SqlOperator.Equals, new { request.FolderId })
                .Build(QueryType.Standard, Db.Cameras.Table, RowSelection.AllRows);

            var cameras = await _dbConnection.QueryAsync<CameraModel>(template.RawSql, template.Parameters);

            var mediaServerGrain = _clusterClient.GetGrain<IMediaServerGrain>(Guid.Empty);

            foreach (var camera in cameras)
            {
                await mediaServerGrain.DisconnectAsync(new IMediaServerGrain.CameraDisconnectRequest(camera.Id));
                await Task.Delay(500);
            }

            return new Response(Result.Success);
        }
    }

    public record CameraModel(Guid Id);
}