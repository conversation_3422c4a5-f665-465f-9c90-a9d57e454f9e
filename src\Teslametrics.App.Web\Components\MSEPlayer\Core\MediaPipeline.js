// Components/MSEPlayer/core/MediaPipeline.js
/* eslint-disable no-console */
// mitt доступен глобально через CDN

/**
 * MediaPipeline
 *  ─ принимает фрагменты в формате fMP4 / MPEG-TS,
 *  ─ скармливает их SourceBuffer’у,
 *  ─ поддерживает карту «mediaTime ↔ absTime»,
 *  ─ очищает «хвост» буфера (trim) по скользящему окну keep сек,
 *  ─ предоставляет арифметику seekAbs() / seekLive().
 *
 * События mitt-шины (`this.ev`):
 *   • timeupdate   (Date abs) — раз в ~500 мс или при seek
 *   • buffer       ({start:number,end:number}) — когда ranges изменились
 *   • error        (Error)
 */
export default class MediaPipeline {
  /**
   * @param {HTMLVideoElement} video
   * @param {string} mime                         – например "video/mp4; codecs=\"avc1.640028\""
   * @param {{keep?:number, timeupdateInterval?:number}} [opts]
   */
  constructor(video, mime, opts = {}) {
    /** публичная шина для PlayerCore/плагинов */
    this.ev = mitt();

    this.video = video;
    this.mime = mime;
    this.keep = opts.keep ?? 60; // секунд храним в буфере
    this.timeupdateInterval = opts.timeupdateInterval ?? 500;

    /** система плагинов */
    this.plugins = new Map(); // Map<pluginName, pluginInstance>

    /* карта абсолютного времени:
         mediaTimelineSecs ► abs Date             */
    this._t0Media = 0; // mediaTime   первого сегмента
    this._t0Abs = null; // abs Date    первого сегмента

    /* улучшенная синхронизация времени */
    this._segmentTimestamps = new Map(); // Map<mediaTime, absoluteTime>
    this._lastKnownSegmentTime = null; // последнее известное время сегмента
    this._lastKnownMediaTime = null; // соответствующее media время
    this._performanceStartTime = performance.now(); // для высокоточных вычислений

    this._mediaSource = new MediaSource();
    this._sourceBuffer = null; // создаём после sourceopen
    this._pending = []; // очередь пока updating === true
    this._trimTimer = null; // setInterval id
    this._timeTimer = null; // setInterval id

    this._initMediaSource();
  }

  /* ----------------------------------------------------- *
   *                   PUBLIC API                          *
   * ----------------------------------------------------- */

  /**
   * @param {Uint8Array} chunk              – сырой сегмент
   * @param {Date} [absTime]                – wall-clock начала сегмента
   */
  push(chunk, absTime) {
    if (this._sourceBuffer?.updating) {
      this._pending.push({ data: chunk, ts: absTime });
      return;
    }
    this._appendChunk({ data: chunk, ts: absTime });
  }

  /** seek к абсолютному времени записи */
  seekAbs(date) {
    if (!this._t0Abs) return;
    const delta = (date - this._t0Abs) / 1000; // сек
    const t = this._t0Media + delta;
    this.video.currentTime = Math.max(t, this.bufferStart + 0.1);
    this._emitTimeupdate();
  }

  /** перейти на «землю живых» — последнюю отбивку -0.5 с */
  seekLive() {
    const livePos = this.bufferEnd - 0.5;
    if (livePos > 0) this.video.currentTime = livePos;
  }

  /** текущее абсолютное время или null */
  get currentAbsTime() {
    if (!this._t0Abs) return null;

    const videoCurrentTime = this.video.currentTime;

    // Используем улучшенную логику с интерполяцией между известными временными метками
    const interpolatedTime = this._interpolateAbsoluteTime(videoCurrentTime);
    if (interpolatedTime) {
      return interpolatedTime;
    }

    // Fallback к старой логике, если интерполяция недоступна
    const elapsedMs = (videoCurrentTime - this._t0Media) * 1000;
    const absoluteTime = new Date(this._t0Abs.getTime() + elapsedMs);

    // Дополнительная проверка на разумность времени
    const systemTime = new Date();
    const timeDiff = Math.abs(absoluteTime.getTime() - systemTime.getTime());

    // Если разница больше 1 дня, возможно проблема с синхронизацией
    if (timeDiff > 24 * 60 * 60 * 1000) {
      console.warn("[MediaPipeline] Большая разница времени:", {
        absoluteTime: absoluteTime.toISOString(),
        systemTime: systemTime.toISOString(),
        diffHours: timeDiff / (60 * 60 * 1000),
        videoCurrentTime,
        t0Media: this._t0Media,
      });
    }

    return absoluteTime;
  }

  /**
   * Интерполирует абсолютное время на основе известных временных меток сегментов
   * @param {number} mediaTime - текущее время в медиа-потоке
   * @returns {Date|null} - интерполированное абсолютное время или null
   */
  _interpolateAbsoluteTime(mediaTime) {
    if (this._segmentTimestamps.size < 2) {
      return null; // Нужно минимум 2 точки для интерполяции
    }

    // Находим ближайшие временные метки
    let prevTime = null,
      nextTime = null;
    let prevAbs = null,
      nextAbs = null;

    for (const [segmentMediaTime, segmentAbsTime] of this._segmentTimestamps) {
      if (segmentMediaTime <= mediaTime) {
        if (!prevTime || segmentMediaTime > prevTime) {
          prevTime = segmentMediaTime;
          prevAbs = segmentAbsTime;
        }
      }
      if (segmentMediaTime >= mediaTime) {
        if (!nextTime || segmentMediaTime < nextTime) {
          nextTime = segmentMediaTime;
          nextAbs = segmentAbsTime;
        }
      }
    }

    // Если есть точное совпадение
    if (prevTime === mediaTime && prevAbs) {
      return new Date(prevAbs.getTime());
    }

    // Интерполяция между двумя точками
    if (
      prevTime !== null &&
      nextTime !== null &&
      prevAbs &&
      nextAbs &&
      prevTime !== nextTime
    ) {
      const ratio = (mediaTime - prevTime) / (nextTime - prevTime);
      const timeDiff = nextAbs.getTime() - prevAbs.getTime();
      const interpolatedMs = prevAbs.getTime() + timeDiff * ratio;

      return new Date(interpolatedMs);
    }

    // Экстраполяция от последней известной точки
    if (prevTime !== null && prevAbs) {
      const elapsedMs = (mediaTime - prevTime) * 1000;
      return new Date(prevAbs.getTime() + elapsedMs);
    }

    return null;
  }

  /** начало и конец буфера в секундах */
  get bufferStart() {
    return this._ranges()?.start(0) ?? 0;
  }
  get bufferEnd() {
    return this._ranges()?.end(0) ?? 0;
  }

  /**
   * Добавляет плагин к MediaPipeline
   * @param {string} name - имя плагина
   * @param {MediaPipelinePlugin} plugin - экземпляр плагина
   */
  addPlugin(name, plugin) {
    if (this.plugins.has(name)) {
      console.warn(`[MediaPipeline] Плагин '${name}' уже существует, заменяем`);
      this.removePlugin(name);
    }

    this.plugins.set(name, plugin);
    plugin.initialize();

    console.log(`[MediaPipeline] Плагин '${name}' добавлен и инициализирован`);
  }

  /**
   * Удаляет плагин из MediaPipeline
   * @param {string} name - имя плагина
   */
  removePlugin(name) {
    const plugin = this.plugins.get(name);
    if (plugin) {
      plugin.dispose();
      this.plugins.delete(name);
      console.log(`[MediaPipeline] Плагин '${name}' удален`);
    }
  }

  /**
   * Получает плагин по имени
   * @param {string} name - имя плагина
   * @returns {MediaPipelinePlugin|null} - экземпляр плагина или null
   */
  getPlugin(name) {
    return this.plugins.get(name) || null;
  }

  /**
   * Проверяет наличие плагина
   * @param {string} name - имя плагина
   * @returns {boolean} - true если плагин существует
   */
  hasPlugin(name) {
    return this.plugins.has(name);
  }

  dispose() {
    // Сначала освобождаем все плагины
    for (const [name, plugin] of this.plugins) {
      try {
        plugin.dispose();
      } catch (error) {
        console.error(
          `[MediaPipeline] Ошибка при освобождении плагина '${name}':`,
          error
        );
      }
    }
    this.plugins.clear();

    clearInterval(this._trimTimer);
    clearInterval(this._timeTimer);
    if (this._sourceBuffer) {
      try {
        this._sourceBuffer.abort();
      } catch {}
    }
    if (this._mediaSource.readyState === "open") {
      try {
        this._mediaSource.endOfStream();
      } catch {}
    }
    this.ev.all.clear();
  }

  /* ----------------------------------------------------- *
   *                 INTERNALS                             *
   * ----------------------------------------------------- */

  _initMediaSource() {
    this.video.src = URL.createObjectURL(this._mediaSource);

    this._mediaSource.addEventListener("sourceopen", () => {
      // Проверяем поддержку MIME типа
      if (!MediaSource.isTypeSupported(this.mime)) {
        const error = new Error(
          `MIME тип ${this.mime} не поддерживается браузером`
        );
        console.error(error.message);
        this.ev.emit("error", error);
        return;
      }

      try {
        this._sourceBuffer = this._mediaSource.addSourceBuffer(this.mime);
        this._sourceBuffer.mode = "segments";
      } catch (e) {
        console.error("Ошибка создания SourceBuffer:", e);
        this.ev.emit("error", e);
        return;
      }

      this._sourceBuffer.addEventListener("error", (e) => {
        console.error("SourceBuffer error:", e);
        this.ev.emit("error", e);
      });

      /* перемещаем накопленное при updating=true */
      this._sourceBuffer.addEventListener("updateend", () => {
        if (this._pending.length) {
          const packet = this._pending.shift();
          this._appendChunk(packet);
        }

        /* --- авто-jump выполняем здесь, когда ranges уже обновлены --- */
        if (!this._autoJumpDone && this.bufferStart > 0) {
          const target = this.bufferEnd - 0.3; // почти live-край
          this.video.currentTime = target;
          this._autoJumpDone = true;
          this.video.muted = true; // обход autoplay-policy
          this.video.play().catch(() => {});
          console.log("[Pipeline] first jump →", target.toFixed(2), "s");
        }

        this._scheduleTrim();

        const gap = this.bufferEnd - this.video.currentTime;
        if (gap > 8) {
          // секунды
          this.video.currentTime = this.bufferEnd - 0.3;
          console.log("[Pipeline] auto-seek, gap =", gap.toFixed(1), "s");
        }
      });

      /* запускаем таймеры */
      this._trimTimer = setInterval(() => this._trimBuffer(), 5_000);
      this._timeTimer = setInterval(
        () => this._emitTimeupdate(),
        this.timeupdateInterval
      );
    });
  }

  /** внутренняя загрузка сегмента */
  _appendChunk(packet) {
    // ----- 1. валидация -----
    if (!packet?.data || !(packet.data instanceof Uint8Array)) return;
    const chunk = packet.data;
    const absTime = packet.ts instanceof Date ? packet.ts : undefined;

    console.log(
      "[Pipeline] Получен сегмент размером",
      chunk.byteLength,
      "байт, absTime:",
      absTime ?? "—"
    );

    // ----- 2. appendBuffer с обработкой ошибок -----
    try {
      this._sourceBuffer.appendBuffer(chunk);
    } catch (e) {
      console.error("[Pipeline] appendBuffer error →", e.name);
      if (e.name === "QuotaExceededError") {
        // попытка почистить и повторить
        try {
          const start = this.bufferStart;
          const end = this.bufferEnd;
          this._pending.unshift(packet); // вернём в очередь
          this._sourceBuffer.remove(start, start + (end - start) / 2);
        } catch (e2) {
          this.ev.emit("error", e2);
          return;
        }
      } else {
        this.ev.emit("error", e);
        return;
      }
    }

    // ----- 3. первичная инициализация карты времени -----
    if (!this._t0Abs && absTime) {
      this._t0Abs = new Date(absTime.getTime()); // Создаем копию для избежания мутаций
      this._t0Media = this._ranges()?.start(0) ?? 0;

      console.log("[MediaPipeline] Инициализация временной карты:", {
        t0Abs: this._t0Abs.toISOString(),
        t0Media: this._t0Media,
        systemTime: new Date().toISOString(),
      });
    }

    // ----- 4. сохранение временной метки сегмента для интерполяции -----
    if (absTime) {
      const currentMediaTime =
        this._ranges()?.end(this._ranges().length - 1) ?? this._t0Media;

      // Сохраняем временную метку для интерполяции
      this._segmentTimestamps.set(
        currentMediaTime,
        new Date(absTime.getTime())
      );

      // Обновляем последнюю известную временную метку
      this._lastKnownSegmentTime = new Date(absTime.getTime());
      this._lastKnownMediaTime = currentMediaTime;

      // Ограничиваем количество сохраненных временных меток (последние 50)
      if (this._segmentTimestamps.size > 50) {
        const oldestKey = this._segmentTimestamps.keys().next().value;
        this._segmentTimestamps.delete(oldestKey);
      }

      console.debug("[MediaPipeline] Сохранена временная метка:", {
        mediaTime: currentMediaTime,
        absTime: absTime.toISOString(),
        totalTimestamps: this._segmentTimestamps.size,
      });
    }

    // ----- 5. однократный «прыжок» внутрь буфера -----
    if (!this._autoJumpDone) {
      const start = this.bufferStart;
      if (start > 0) {
        this._autoJumpDone = true;
        // небольшой зазор, чтобы декодер «успел»
        const target = start + 0.05;
        this.video.currentTime = target;
        console.log("[Pipeline] jump to", target.toFixed(2), "s");
        // запускаем воспроизведение (если autoplay разрешён браузером)
        this.video.play().catch(() => {});
      }
    }

    // ----- 6. нотификации -----
    this.ev.emit("buffer", { start: this.bufferStart, end: this.bufferEnd });
  }

  /** каждые 5 сек откусываем то, что старше keep */
  _trimBuffer() {
    const excess = this.bufferEnd - this.bufferStart - this.keep;
    if (excess <= 0 || this._sourceBuffer.updating) return;

    try {
      const safeGap = 5; // секунд запаса
      const candidate = Math.min(
        this.video.currentTime - safeGap,
        this.bufferStart + excess + 2
      );

      //   remove() только если end > start
      if (candidate > this.bufferStart + 0.1) {
        this._sourceBuffer.remove(this.bufferStart, candidate);
      }

      //const newStart = this.bufferStart + excess + 2; /*запас*/
      // const safeGap = 5; // секунд «запаса»
      // const newStart = Math.min(
      //   this.video.currentTime - safeGap,
      //   this.bufferStart + excess
      // );

      // if (newStart > this.bufferStart)
      //   this._sourceBuffer.remove(this.bufferStart, newStart);
    } catch (e) {
      console.warn("trimBuffer remove() error", e);
    }
  }

  _scheduleTrim() {
    /* асинхронный trim сразу после updateend, чтобы держать окно фиксированным */
    queueMicrotask(() => this._trimBuffer());
  }

  _emitTimeupdate() {
    const t = this.currentAbsTime;
    if (t) {
      // Добавляем проверку на валидность времени перед эмиссией
      if (!isNaN(t.getTime()) && t.getTime() > 0) {
        this.ev.emit("timeupdate", t);
      } else {
        console.warn("[MediaPipeline] Невалидное время для timeupdate:", t);
      }
    }
  }

  _ranges() {
    const sb = this._sourceBuffer;
    return sb?.buffered?.length ? sb.buffered : null;
  }
}
