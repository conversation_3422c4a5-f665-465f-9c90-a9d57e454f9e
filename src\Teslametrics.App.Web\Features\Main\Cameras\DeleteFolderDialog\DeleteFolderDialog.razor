@inherits InteractiveBaseComponent
﻿<MudDialog ActionsClass="mx-2"
		   ContentClass="pb-12"
		   Visible="_isVisible"
		   VisibleChanged="VisibilityChanged"
		   Options="_dialogOptions">
	<DialogContent>
		<MudStack Row=true
				  class="pt-3 pb-6 mr-n4">
			<MudIcon Icon="@Icons.Material.Filled.DeleteForever"
					 Class="mt-1" />
			<MudStack Spacing="0">
				<MudText Typo="Typo.h6">Удаление директории</MudText>
				@if (IsLoading)
				{
					<MudSkeleton Width="30%" />
				}
				else
				{
					<MudText Typo="Typo.subtitle2">@_model?.Name</MudText>
				}
			</MudStack>
			<MudSpacer />
			<div>
				@if (!_subscribing && (_subscriptionResult is null || !_subscriptionResult.IsSuccess))
				{
					<MudTooltip Arrow="true"
								Placement="Placement.Start"
								Text="Ошибка подписки на события">
						<MudIconButton OnClick="SubscribeAsync"
									   Icon="@Icons.Material.Filled.ErrorOutline"
									   Color="Color.Error" />
					</MudTooltip>
					<MudIconButton OnClick="RefreshAsync"
								   Icon="@Icons.Material.Filled.Refresh"
								   Color="Color.Primary" />
				}
				<MudIconButton OnClick="CancelAsync"
							   Icon="@Icons.Material.Outlined.Close" />
			</div>
		</MudStack>
		<MudStack AlignItems="AlignItems.Center"
				  Justify="Justify.Center"
				  Spacing="0"
				  Class="mud-height-full">
			@if (IsLoading)
			{
				<MudSkeleton Width="30%"
							 Height="42px" />
				<MudSkeleton Width="30%"
							 Height="42px" />
				<MudSkeleton Width="30%"
							 Height="42px" />
				<MudSkeleton Width="30%"
							 Height="42px" />
				<MudSkeleton Width="30%"
							 Height="42px" />
			}
			@if (!IsLoading && _model is not null)
			{
				<MudIcon Icon="@Icons.Material.Outlined.WarningAmber"
						 Color="Color.Warning"
						 Style="font-size: 8rem;"
						 Class="mb-2" />
				<MudText Typo="Typo.subtitle1"
						 Color="Color.Warning">Удаление директории!</MudText>
				<MudText Typo="Typo.body1">Вы уверены, что вы хотите удалить директорию <b>@_model?.Name</b>?</MudText>
				<MudText Typo="Typo.body2">Данное изменение необратимо.</MudText>
				<br />
				<MudText>Чтобы удалить директорию введите «<MudText Inline="true"
							 Color="Color.Warning">@_confirmationMessage.ToString()</MudText>»</MudText>
				<MudTextField @bind-Value="_input"
							  Label="Подтверждение"
							  Variant="Variant.Text"
							  Immediate="true"
							  Class="mt-2" />
			}
		</MudStack>
	</DialogContent>
	<DialogActions>
		<MudButton OnClick="CancelAsync">Отменить</MudButton>
		@if (IsLoading)
		{
			<MudSkeleton Width="160px"
						 Height="52px" />
		}
		@if (!IsLoading && _model is not null)
		{
			<AuthorizeView Policy="@AppPermissions.Main.Folders.Delete.GetEnumPermissionString()"
						   Resource="@(new PolicyRequirementResource(_organizationId, _id))"
						   Context="innerContext">
				@* Resource="new PolicyRequirementResource(OrganizationId, _model.Id)" *@
				<MudButton OnClick="SubmitAsync"
						   Color="Color.Warning"
						   Disabled="@(_confirmationMessage.ToString() != _input)">Подвердить</MudButton>
			</AuthorizeView>
		}
	</DialogActions>
</MudDialog>