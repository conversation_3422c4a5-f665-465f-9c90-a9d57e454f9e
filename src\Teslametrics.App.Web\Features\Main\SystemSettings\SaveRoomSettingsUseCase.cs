using System.Data;
using System.Text.Json;
using Dapper;
using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.MediaServer.Orleans.Wirenboard;
using Teslametrics.App.Web.Services.Outbox;
using Teslametrics.App.Web.Services.Persistence;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Features.Main.SystemSettings;

public static class SaveRoomSettingsUseCase
{
    public record Command(PageModel Page, List<Command.FloorImage> Images) : BaseRequest<Response>
    {
        public record FloorImage(Guid FloorId, byte[] Image, string ContentType);
    }

    public record Response : BaseResponse
    {
        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response()
        {
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Result = result;
        }
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
    }

    public class Validator : AbstractValidator<Command>
    {
        public Validator()
        {
        }
    }

    public class Handler : IRequestHandler<Command, Response>
    {
        private readonly IValidator<Command> _validator;
        private readonly IDbConnection _dbConnection;
        private readonly IOutbox _outbox;
        private readonly IGrainFactory _grainFactory;

        public Handler(IValidator<Command> validator,
                       IDbConnection dbConnection,
                       IOutbox outbox,
                       IGrainFactory grainFactory)
        {
            _validator = validator;
            _dbConnection = dbConnection;
            _outbox = outbox;
            _grainFactory = grainFactory;
        }

        public async Task<Response> Handle(Command request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            // Открываем соединение перед началом транзакции
            if (_dbConnection.State != ConnectionState.Open)
            {
                _dbConnection.Open();
            }

            using (var transaction = _dbConnection.BeginTransaction(IsolationLevel.Serializable))
            {
                await CreateTableIfNotExistsAsync();

                await _dbConnection.ExecuteAsync($"DELETE FROM {Db.Plans.Table}");

                await _dbConnection.ExecuteAsync($"INSERT INTO {Db.Plans.Table} ({Db.Plans.Columns.Page}) VALUES (:Page)", new { Page = JsonSerializer.Serialize(request.Page) });

                await _dbConnection.ExecuteAsync($"DELETE FROM {Db.PlanImages.Table}");

                foreach (var floorImage in request.Images)
                {
                    await _dbConnection.ExecuteAsync($"INSERT INTO {Db.PlanImages.Table} ({Db.PlanImages.Columns.FloorId}, {Db.PlanImages.Columns.Image}, {Db.PlanImages.Columns.ContentType}) VALUES (:FloorId, :Image, :ContentType)", new { floorImage.FloorId, floorImage.Image, floorImage.ContentType });
                }

                await _outbox.AddRangeAsync([new PlanUpdatedEvent()]);

                transaction.Commit();
            }

            // Создаем список SensorTopicInfo на основе данных из request.Page
            var topicInfos = new List<SensorTopicInfo>();

            foreach (var city in request.Page.Cities)
            {
                foreach (var building in city.Buildings)
                {
                    foreach (var floor in building.Floors)
                    {
                        foreach (var room in floor.Rooms)
                        {
                            // Добавляем датчики из комнаты
                            foreach (var sensor in room.Sensors)
                            {
                                topicInfos.Add(new SensorTopicInfo(
                                    topic: sensor.Name,
                                    valueType: GetSensorValueType(sensor),
                                    sensorModel: sensor,
                                    cityId: city.Id,
                                    city: city.Name,
                                    buildingId: building.Id,
                                    building: building.Address,
                                    floorId: floor.Id,
                                    floor: floor.Number,
                                    roomId: room.Id,
                                    room: room.Name,
                                    deviceId: null,
                                    device: null,
                                    sensorId: sensor.Id
                                ));
                            }

                            // Добавляем датчики из холодильников в комнате
                            foreach (var fridge in room.Fridges)
                            {
                                foreach (var sensor in fridge.Sensors)
                                {
                                    topicInfos.Add(new SensorTopicInfo(
                                        topic: sensor.Name,
                                        valueType: GetSensorValueType(sensor),
                                        sensorModel: sensor,
                                        cityId: city.Id,
                                        city: city.Name,
                                        buildingId: building.Id,
                                        building: building.Address,
                                        floorId: floor.Id,
                                        floor: floor.Number,
                                        roomId: room.Id,
                                        room: room.Name,
                                        deviceId: fridge.Id,
                                        device: fridge.Name,
                                        sensorId: sensor.Id
                                    ));
                                }
                            }
                        }
                    }
                }
            }

            // Настраиваем топики для основного грейна датчиков
            var sensorGrain = _grainFactory.GetGrain<IWirenboardSensorDataGrain>(Guid.Empty);
            await sensorGrain.ConfigureSensorTopicsAsync(new IWirenboardSensorDataGrain.ConfigureSensorTopicsRequest(topicInfos));

            // Настраиваем сбор истории для топиков
            var historyManagerGrain = _grainFactory.GetGrain<IWirenboardHistoryManagerGrain>(Guid.Empty);

            // Создаем список HistoryTopicInfo на основе SensorTopicInfo
            var historyTopics = topicInfos.Select(t => new HistoryTopicInfo(t.Topic, t.ValueType)).ToList();
            await historyManagerGrain.ConfigureHistoryCollectionAsync(new IWirenboardHistoryManagerGrain.ConfigureHistoryCollectionRequest(historyTopics));

            return new Response();
        }

        private async Task CreateTableIfNotExistsAsync()
        {
            // Check if table exists
            var tableExists = await CheckTableExistsAsync(Db.Plans.Table);

            if (!tableExists)
            {
                // Create table
                await _dbConnection.ExecuteAsync(
                    $"CREATE TABLE IF NOT EXISTS {Db.Plans.Table} (" +
                    $"{Db.Plans.Columns.Page} text NOT NULL)");
            }

            tableExists = await CheckTableExistsAsync(Db.PlanImages.Table);

            if (!tableExists)
            {
                await _dbConnection.ExecuteAsync(
                    $"CREATE TABLE IF NOT EXISTS {Db.PlanImages.Table} (" +
                    $"{Db.PlanImages.Columns.FloorId} uuid NOT NULL PRIMARY KEY, " +
                    $"{Db.PlanImages.Columns.Image} bytea NOT NULL, " +
                    $"{Db.PlanImages.Columns.ContentType} text NOT NULL)");
            }
        }

        private async Task<bool> CheckTableExistsAsync(string tableName)
        {
            // Check if table exists
            var tableExists = await _dbConnection.ExecuteScalarAsync<int>(
                "SELECT COUNT(*) FROM information_schema.tables " +
                "WHERE table_schema = 'public' AND table_name = @TableName",
                new { TableName = tableName });

            return tableExists > 0;
        }

        /// <summary>
        /// Определяет тип значения датчика на основе его типа
        /// </summary>
        /// <param name="sensor">Модель датчика</param>
        /// <returns>Тип значения датчика</returns>
        private static SensorValueType GetSensorValueType(ISensorModel sensor)
        {
            return sensor switch
            {
                TemperatureModel => SensorValueType.Double,
                HumidityModel => SensorValueType.Double,
                DoorModel => SensorValueType.Bool,
                LeakModel => SensorValueType.Bool,
                PowerModel => SensorValueType.Bool,
                _ => SensorValueType.String
            };
        }
    }
}

public record PlanUpdatedEvent;