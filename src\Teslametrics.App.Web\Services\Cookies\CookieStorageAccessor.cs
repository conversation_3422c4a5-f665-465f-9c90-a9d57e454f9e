using Microsoft.JSInterop;
using MudBlazor;
using System.ComponentModel;
using System.Net;

namespace Teslametrics.App.Web.Services.Cookies;

public class CookieStorageAccessor
{
	private readonly IJSRuntime _jsRuntime;
	private readonly IHttpContextAccessor _httpContextAccessor;

	public CookieStorageAccessor(IJSRuntime jsRuntime,
								 IHttpContextAccessor httpContextAccessor)
	{
		_jsRuntime = jsRuntime;
		_httpContextAccessor = httpContextAccessor;
	}

	public async Task<IEnumerable<Cookie>> GetAllAsync()
	{
		var raw = await _jsRuntime.InvokeAsync<string>("eval", "document.cookie");
		if (string.IsNullOrWhiteSpace(raw)) return [];

		return raw.Split("; ").Select(x =>
		{
			var parts = x.Split("=");
			if (parts.Length != 2) throw new Exception($"Invalid cookie format: '{x}'.");
			return new Cookie(parts[0], parts[1]);
		});
	}

	public async Task<T?> GetValueAsync<T>(string key)
	{
		T? result = default(T);
		string? value = null;
		if (_httpContextAccessor.HttpContext is null || _httpContextAccessor.HttpContext.Response.HasStarted)
		{
			try
			{
				var cookies = await GetAllAsync();
				var coockie = cookies.FirstOrDefault(x => x.Name == key);

				value = coockie?.Value;
			}
			catch
			{
				value = _httpContextAccessor.HttpContext?.Request.Cookies[key];
			}
		}
		else
		{
			value = _httpContextAccessor.HttpContext.Request.Cookies[key];
		}
		
		if(value is not null)
			result = (T?) TypeDescriptor.GetConverter(typeof(T?)).ConvertFromString(value);

		return result;
	}

	public async Task SetValueAsync<T>(string key, T value, DateTimeOffset? expiration = null)
	{
		if (_httpContextAccessor.HttpContext is null || _httpContextAccessor.HttpContext.Response.HasStarted)
		{
			try
			{
				if (string.IsNullOrWhiteSpace(key)) throw new Exception("Key is required when setting a cookie.");
				var expirationDate = expiration is not null ? expiration.ToString() : "31 Dec 9999 23:59:59 GMT"; // Если не установлено время, значит кука вечная. 
				await _jsRuntime.InvokeVoidAsyncIgnoreErrors("eval", $"document.cookie = \"{key}={value}; expires={expirationDate}; path=/\"");
			}
			catch
			{
				_httpContextAccessor.HttpContext?.Response.Cookies.Append(key, value!.ToString()!);
			}
		}
		else
		{
			_httpContextAccessor.HttpContext.Response.Cookies.Append(key, value!.ToString()!);
		}
	}

	public async Task RemoveAsync(string key)
	{
		if (_httpContextAccessor.HttpContext is null || _httpContextAccessor.HttpContext.Response.HasStarted)
		{
			try
			{
				if (string.IsNullOrWhiteSpace(key)) throw new Exception("Key is required when removing a cookie.");
				await _jsRuntime.InvokeVoidAsyncIgnoreErrors("eval", $"document.cookie = \"{key}=; expires=Thu, 01 Jan 1970 00:00:01 GMT; path=/\"");
			}
			catch
			{
				_httpContextAccessor.HttpContext?.Response.Cookies.Delete(key);
			}
		}
		else
		{
			_httpContextAccessor.HttpContext.Response.Cookies.Delete(key);
		}
	}
}