@using Teslametrics.App.Web.Features.Main.Home.Map
@using Teslametrics.App.Web.Features.Main.Home.Incidents
@using Teslametrics.App.Web.Features.Main.Home.AddressList

@page "/"

@attribute [Authorize]
@using Microsoft.AspNetCore.Authorization
@using MudBlazor

<PageTitle>Multimonitor : Главная</PageTitle>

<div class="mud-height-full mr-n3 overflow-auto layout gap-8 pl-2 pr-5">
    <div class="incident_boards">
        <IncidentBoardsComponent Buildings="@_selectedBuildings" />
    </div>
    <MudPaper Elevation="0"
              Outlined="true"
              Class="px-4 py-8 mud-width-full address">
        <AddressListComponent @bind-Selected="@_selectedBuildings" />
    </MudPaper>
    <MudPaper Elevation="0"
              Outlined="true"
              Class="mud-width-full mud-height-full overflow-hidden map">
        <MapComponent Buildings="_selectedBuildings" />
    </MudPaper>

</div>

@code {
    private IEnumerable<Guid> _selectedBuildings = [];
}