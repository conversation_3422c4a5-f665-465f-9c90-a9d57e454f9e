@using Teslametrics.App.Web.Features.Main.Home.Map
@using Teslametrics.App.Web.Features.Main.Home.Incidents
@using Teslametrics.App.Web.Features.Main.Home.AddressList

@page "/"

@attribute [Authorize]
@using Microsoft.AspNetCore.Authorization
@using MudBlazor

<PageTitle>Multimonitor : Главная</PageTitle>

<div class="d_contents">
	<MudStack Spacing="8"
			  Class="mud-height-full pl-12 pr-12 pb-12 overflow-auto layout">
		<IncidentBoardsComponent Buildings="@_selectedBuildings" />
		<MudStack Spacing="5"
				  Row="true">
			<MudPaper Elevation="0"
					  Outlined="true"
					  Class="pa-4 mud-width-full">
				<AddressListComponent @bind-Selected="@_selectedBuildings" />
			</MudPaper>
			<MudPaper Elevation="0"
					  Outlined="true"
					  Class="mud-width-full overflow-hidden">
				<MapComponent Buildings="_selectedBuildings" />
			</MudPaper>
		</MudStack>
	</MudStack>
</div>

@code {
	private IEnumerable<Guid> _selectedBuildings = [];
}