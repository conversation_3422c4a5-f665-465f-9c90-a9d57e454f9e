using Microsoft.EntityFrameworkCore;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Domain.Cameras;

namespace Teslametrics.App.Web.Services.Persistence;

public class CameraRepository : BaseRepository<CameraAggregate>, ICameraRepository
{
    public CameraRepository(CommandAppDbContext dbContext)
        : base(dbContext)
    {
    }

    public async Task<bool> HasCamerasInFolderAsync(Guid folderId,
                                                    CancellationToken cancellationToken = default) =>
        await DbContext.Set<CameraAggregate>()
            .AsNoTracking()
            .AnyAsync(entity => entity.FolderId == folderId, cancellationToken);

    public Task<int> GetCameraCountByQuotaAsync(Guid quotaId, CancellationToken cancellationToken) =>
        DbContext.Set<CameraAggregate>()
            .AsTracking()
            .CountAsync(entity => entity.QuotaId == quotaId, cancellationToken);

    public Task<List<CameraAggregate>> GetAllByQuotaIdAsync(Guid quotaId, CancellationToken cancellationToken = default) =>
        DbContext.Set<CameraAggregate>()
            .AsTracking()
            .Where(entity => entity.QuotaId == quotaId)
            .OrderBy(entity => entity.Id)
            .ToListAsync(cancellationToken);

    public Task<bool> IsCameraExistsAsync(Guid cameraId, CancellationToken cancellationToken = default) =>
        DbContext.Set<CameraAggregate>()
            .AsNoTracking()
            .AnyAsync(entity => entity.Id == cameraId, cancellationToken);
}