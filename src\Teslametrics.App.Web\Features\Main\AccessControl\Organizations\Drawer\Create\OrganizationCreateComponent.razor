@inherits InteractiveBaseComponent
<DrawerHeader>
	<MudText Typo="Typo.h1">Создание организации</MudText>
</DrawerHeader>
<MudForm Model="_model"
		 Validation="_validator.ValidateValue"
		 @bind-IsValid="_isValid"
		 Class="flex-1"
		 OverrideFieldValidation="true"
		 UserAttributes="@(new Dictionary<string, object>() {{"autocomplete", "off"}, {"aria-autocomplete", "none" }})"
		 Spacing="8">
	<FormSectionComponent Title="Описание организации">
		<MudTextField @bind-Value="_model.Name"
					  For="@(() => _model.Name)"
					  Clearable="true"
					  InputType="InputType.Text"
					  Immediate="true"
					  Label="Наименование"
					  HelperText="Укажите наименование организации"
					  RequiredError="Данное поле обязательно"
					  Required="true" />

		<MudAutocomplete T="UserModel"
						 @bind-Value="_model.Owner"
						 For="() => _model.Owner"
						 Required="true"
						 Label="Владелец организации"
						 Clearable="true"
						 Placeholder="Введите название пользователя"
						 ResetValueOnEmptyText="true"
						 ToStringFunc="@(e=> e==null?null : e.Name)"
						 SearchFunc="@SearchOwnerAsync"
						 DebounceInterval="200"
						 ShowProgressIndicator="true"
						 AutoFocus="false"
						 CoerceValue="false"
						 CoerceText="false"
						 UserAttributes="@(new Dictionary<string, object>() {{"autocomplete", "off"}, {"name", "roles"}, {"aria-autocomplete", "none" }})"
						 AdornmentIcon="@Icons.Material.Filled.Search"
						 AdornmentColor="Color.Primary">
			<NoItemsTemplate>
				<MudText Align="Align.Center"
						 Class="px-4 py-1">
					Не найдено пользователей с данным именем
				</MudText>
			</NoItemsTemplate>
		</MudAutocomplete>
	</FormSectionComponent>
</MudForm>
<DrawerActions>
	<MudSpacer />
	<MudButton OnClick="CancelAsync"
			   Variant="Variant.Outlined"
			   StartIcon="@Icons.Material.Outlined.Close">Закрыть</MudButton>
	<MudButton OnClick="SubmitAsync"
			   Disabled="@(!_isValid)"
			   Color="Color.Secondary"
			   Variant="Variant.Outlined">Сохранить</MudButton>
</DrawerActions>