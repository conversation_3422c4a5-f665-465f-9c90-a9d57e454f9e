@using System.Reflection
@using Teslametrics.App.Web.Domain.AccessControl
@using Teslametrics.Shared
@typeparam TEnum where TEnum : Enum
@inherits BaseComponent<Teslametrics.App.Web.Locales.Features.Main.AccessControl.Roles.Drawer.View.WildcardPermissionComponents>
@if (_selected.Any())
{
	<MudGrid Spacing="1">
		<MudItem xs="12" md="2" Class="mt-4">
			<MudText Typo="Typo.subtitle2" Align="Align.End"><b>@Title</b></MudText>
		</MudItem>
		<MudItem xs="12" md="10">
			@foreach (var permission in _selected)
			{
				<MudCheckBox Value="true" T="bool" Label="@Localizer[permission.Permission.Value]" ReadOnly="true" />
			}
		</MudItem>
	</MudGrid>
}
@code
{
	private string _namespace;

	private IEnumerable<ResourcePermission> _selected => Selected.Where(x => x.Permission.Value.Contains(_namespace) && x.ResourceId == ResourceId.Wildcard);

	[Parameter]
	[EditorRequired]
	public string Title { get; set; } = string.Empty!;

	[Parameter]
	public IEnumerable<ResourcePermission> Selected { get; set; } = [];

	public WildcardPermissionComponents()
	{
		_namespace = Fqdn<AppPermissions>.GetRootNamespace(typeof(TEnum));
	}
}