﻿/* Стили для кастомного прогресс-бара */
::deep.video-js {
	height: 100vh !important;
	/* overflow: hidden; */
	/* display: grid
; */
	/* grid-template-rows: 1fr auto; */
	/* align-items: center; */
	/* justify-items: center; */
	/* justify-content: center; */
	/* align-content: center; */
	padding: 0 !important;
}
::deep .vjs-custom-progress-control {
	position: relative;
	width: 100%;
}
::deep .vjs-custom-seek-bar {
	position: relative;
	width: 100%;
	height: 5px; /* Высота прогресс-бара */
	background-color: #555; /* Цвет фона прогресс-бара */
	cursor: pointer;
}
::deep .vjs-segments-container {
	position: absolute;
	width: 100%;
	height: 100%;
}
::deep .vjs-segment {
	position: absolute;
	height: 100%;
	background-color: rgba(255, 255, 255, 0.3); /* Цвет доступных сегментов */
}
::deep .vjs-custom-progress {
	position: absolute;
	height: 100%;
	background-color: #2196F3; /* Цвет прогресса воспроизведения */
	width: 0%;
}
::deep .vjs-progress-holder {
	position: relative; /* Для позиционирования gap-сегментов */
}
::deep .vjs-gap-segment {
	position: absolute;
	display: block;
	height: 3px;
	z-index: 10000;
	background: var(--mud-palette-error);
	transition: all 0.1s;
}
::deep .video-js .vjs-control:hover .vjs-gap-segment {
	height: 5px;
}
::deep #timeline-container {
	position: relative;
	width: 100%;
	height: 40px;
	overflow-x: auto;
	background: black;
	margin-top: 10px;
	display: flex;
	align-items: center;
	cursor: grab;
}
::deep .vjs-time-labels-container {
	position: absolute;
	top: -20px; /* Расположение над полосой прогресса */
	width: 100%;
	height: 20px;
	pointer-events: none; /* Чтобы не мешать клику по прогресс-бару */
}
::deep .vjs-time-label {
	position: absolute;
	transform: translateX(-50%);
	color: white;
	font-size: 12px;
	background-color: rgba(0, 0, 0, 0.7);
	padding: 2px 5px;
	border-radius: 3px;
	pointer-events: none;
}
::deep .vjs-time-label::after {
	content: attr(data-time); /* Используем data-time для отображения метки времени */
	position: absolute;
	top: 15px;
	left: 50%;
	transform: translateX(-50%);
	font-size: 12px;
	color: white;
	width: 1px;
	height: 14px;
	background: white;
	margin-top: 4px;
}
::deep .vjs-progress-holder {
	position: relative;
}
::deep .vjs-progress-holder.vjs-slider.vjs-slider-horizontal {
	margin: 0;
}
::deep .video-js .vjs-control {
	margin: 0 10px;
}
::deep .vjs-my-full-bar {
	height: 100%;
	position: relative;
	align-content: center;
	cursor: pointer;
	flex: auto;
	display: flex;
	align-items: center;
	min-width: 4em;
	touch-action: none;
	margin: 0 10px;
}
::deep .vjs-my-buffer-bar {
	height: 4px;
	background: white;
	position: absolute;
}
::deep .vjs-my-played-bar {
	background: var(--mud-palette-primary);
	height: 4px;
	z-index: 1;
}
::deep .vjs-my-handle {
	height: 10px;
	width: 10px;
	position: absolute;
	background: var(--mud-palette-primary);
	border-radius: 5px;
	margin-left: -5px;
}
::deep .vjs-my-progress {
	height: 2px;
	background: white;
	width: 100%;
}