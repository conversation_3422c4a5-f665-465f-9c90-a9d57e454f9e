using Microsoft.AspNetCore.Components;
using MudBlazor;
using Teslametrics.App.Web.Events.Cameras;
using Teslametrics.App.Web.Extensions;
using Teslametrics.MediaServer.Orleans.Camera;
using Teslametrics.MediaServer.Orleans.Camera.Events;

namespace Teslametrics.App.Web.Features.Main.Cameras.TreeView.Camera;

public partial class CameraItemComponent : ICameraStatusObserver, IAsyncDisposable
{
	private ICameraStatusObserver? _observer;

	private SubscribeCameraStatusUseCase.Response? _subscriptionResult;

	[Inject]
	private IClusterClient _clusterClient { get; set; } = null!;

	private Color IconColor => _presenter.Status switch
	{
		CameraStatus.Running => Color.Success,
		CameraStatus.Starting => Color.Info,
		CameraStatus.Stopped => Color.Warning,
		CameraStatus.Problem => Color.Error,
		_ => Color.Error
	};

	private string _cameraStatusTooltipText => _presenter.Status switch
	{
		CameraStatus.Running => "Камера подключена",
		CameraStatus.Starting => "Камера подключается",
		CameraStatus.Problem => "Ошибка",
		CameraStatus.Stopped => "Камера отключена",
		_ => "Неизвестный статус"
	};

	private FolderViewComponent.TreeItemPresenter _presenter => (FolderViewComponent.TreeItemPresenter)Item;

	[Inject]
	public NavigationManager NavigationManager { get; set; } = null!;

	[Parameter]
	[EditorRequired]
	public TreeItemData<Guid> Item { get; set; } = null!;

	[Parameter]
	[EditorRequired]
	public EventCallback<TreeItemData<Guid>> OnSelect { get; set; }

	[Parameter]
	public Guid OrganizationId { get; set; }

	public Task StatusUpdated(CameraStatusChangedEvent @event)
	{
		return InvokeAsync(() =>
		{
			_presenter.Status = @event.Status;
			StateHasChanged();
		});
	}

	public async ValueTask DisposeAsync()
	{
		await UnsubscribeAsync();
		GC.SuppressFinalize(this);
	}

	protected override async Task OnInitializedAsync()
	{
		await SubscribeAsync();

		var timer = new System.Timers.Timer(300_000); // 5 минут
		timer.Elapsed += OnTimerElapsed;
		timer.AutoReset = true;
		timer.Start();

		CompositeDisposable.Add(timer);

		await base.OnInitializedAsync();
	}

	private async Task SelectHandler()
	{
		if (OnSelect.HasDelegate)
			await OnSelect.InvokeAsync(Item);
	}

	private void ShowArchive() => NavigationManager.NavigateTo($"/cameras/archive/{OrganizationId}/{Item.Value}");
	private void ShowPlayer()
	{
		if (_presenter.Status == CameraStatus.Running)
			EventSystem.Publish(new ShowCameraStreamEto(Item.Value));
	}
	private void Select() => EventSystem.Publish(new CameraSelectEto(OrganizationId, Item.Value));
	private void Delete() => EventSystem.Publish(new CameraDeleteEto(Item.Value));
	private void Edit() => EventSystem.Publish(new CameraEditEto(OrganizationId, Item.Value));

	private void ClearArchive() => EventSystem.Publish(new CameraClearArchiveEto(OrganizationId, Item.Value));

	private async Task ConnectAsync()
	{
		ConnectCameraUseCase.Response? response;
		try
		{
			response = await ScopeFactory.MediatorSend(new ConnectCameraUseCase.Command(Item.Value));
		}
		catch (Exception ex)
		{
			response = null;
			Snackbar.Add($"Не удалось подключить камеру из-за ошибки сообщения с сервером. Повторите попытку позже.", MudBlazor.Severity.Error);
			Logger.LogError(ex, ex.Message);
		}

		if (response is null) return;

		switch (response.Result)
		{
			case ConnectCameraUseCase.Result.Success:
				Snackbar.Add("Отправлен запрос на подключение камеры", Severity.Success);
				break;
			case ConnectCameraUseCase.Result.CameraNotFound:
				Snackbar.Add("Не удалось подключить камеру так как она не числится в системе", Severity.Error);
				break;
			case ConnectCameraUseCase.Result.ValidationError:
				Snackbar.Add("Не удалось подключить камеру из-за ошибки валидации", Severity.Error);
				break;
			case ConnectCameraUseCase.Result.CameraIsBlocked:
				Snackbar.Add("Камера заблокирована", Severity.Error);
				break;
			case ConnectCameraUseCase.Result.Unknown:
				Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(CameraItemComponent), nameof(ConnectCameraUseCase));
				Snackbar.Add($"Не удалось подключить камеру из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
				break;
			default:
				Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(CameraItemComponent), nameof(ConnectCameraUseCase), response.Result);
				Snackbar.Add($"Не удалось подключить камеру из-за ошибки: {response.Result}", MudBlazor.Severity.Error);
				break;
		}

	}
	private async Task DisconnectAsync()
	{
		DisconnectCameraUseCase.Response? response = null;
		try
		{
			response = await ScopeFactory.MediatorSend(new DisconnectCameraUseCase.Command(Item.Value));
		}
		catch (Exception ex)
		{
			response = null;
			Snackbar.Add($"Не удалось отключить камеру из-за ошибки сообщения с сервером. Повторите попытку позже.", MudBlazor.Severity.Error);
			Logger.LogError(ex, ex.Message);
		}

		if (response is null) return;
		switch (response.Result)
		{
			case DisconnectCameraUseCase.Result.Success:
				Snackbar.Add("Отправлен запрос на отключение камеры", Severity.Success);
				break;
			case DisconnectCameraUseCase.Result.CameraNotFound:
				Snackbar.Add("Не удалось отключить камеру так как она не числится в системе", Severity.Error);
				break;
			case DisconnectCameraUseCase.Result.ValidationError:
				Snackbar.Add("Не удалось отключить камеру из-за ошибки валидации", Severity.Error);
				break;
			case DisconnectCameraUseCase.Result.Unknown:
				Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(CameraItemComponent), nameof(DisconnectCameraUseCase));
				Snackbar.Add($"Не удалось отключить камеру из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
				break;
			default:
				Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(CameraItemComponent), nameof(DisconnectCameraUseCase), response.Result);
				Snackbar.Add($"Не удалось отключить камеру из-за ошибки: {response.Result}", MudBlazor.Severity.Error);
				break;
		}
	}

	private async Task SubscribeAsync()
	{
		try
		{
			// Ensure we have a clean state before subscribing
			if (_observer is not null)
				await UnsubscribeAsync();

			// Create a new observer reference
			_observer = _clusterClient.CreateObjectReference<ICameraStatusObserver>(this);
			_subscriptionResult = await ScopeFactory.MediatorSend(new SubscribeCameraStatusUseCase.Request(_observer, _presenter.Id));
		}
		catch (Exception ex)
		{
			_subscriptionResult = null;
			_observer = null; // Reset observer on failure
			Snackbar.Add($"Не удалось подписаться на события статуса камеры из-за ошибки сообщения с сервером. Повторите попытку позже.", MudBlazor.Severity.Error);
			Logger.LogError(ex, "Failed to subscribe to camera preview events");
		}

		switch (_subscriptionResult?.Result)
		{
			case SubscribeCameraStatusUseCase.Result.Success:
				break;
			case SubscribeCameraStatusUseCase.Result.ValidationError:
				Snackbar.Add("Ошибка валидации при подписке на события статуса камеры", MudBlazor.Severity.Error);
				break;
			case SubscribeCameraStatusUseCase.Result.Unknown:
				Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(CameraItemComponent), nameof(SubscribeCameraStatusUseCase));
				Snackbar.Add($"Не удалось получить подписку на события статуса камеры из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
				break;
			default:
				Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(CameraItemComponent), nameof(SubscribeCameraStatusUseCase), _subscriptionResult?.Result);
				Snackbar.Add($"Не удалось получить подписку на события статуса камеры из-за ошибки", MudBlazor.Severity.Error);
				break;
		}
	}

	private async Task UnsubscribeAsync()
	{
		if (_observer is null) return;

		var observerToDelete = _observer;
		UnsubscribeCameraStatusUseCase.Response? response = null;

		try
		{
			response = await ScopeFactory.MediatorSend(new UnsubscribeCameraStatusUseCase.Request(_observer, _presenter.Id));
		}
		catch (Exception ex)
		{
			Snackbar.Add($"Не удалось отменить подписку на события статуса камеры из-за ошибки сообщения с сервером. Повторите попытку позже.", MudBlazor.Severity.Error);
			Logger.LogError(ex, "Failed to unsubscribe from camera preview events");
		}

		// Always try to delete the object reference, even if unsubscribe failed
		try
		{
			if (observerToDelete is not null)
			{
				_clusterClient.DeleteObjectReference<IPreviewObserver>(observerToDelete);
			}
		}
		catch (Exception ex)
		{
			Logger.LogError(ex, "Failed to delete Orleans object reference for observer");
		}
		finally
		{
			// Always reset the observer reference to null after attempting deletion
			_observer = null;
		}

		switch (response?.Result)
		{
			case UnsubscribeCameraStatusUseCase.Result.Success:
				break;
			case UnsubscribeCameraStatusUseCase.Result.ValidationError:
				Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(CameraItemComponent), nameof(UnsubscribeCameraStatusUseCase));
				Snackbar.Add($"Не удалось отменить подписку на события статуса камеры из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
				break;
			case UnsubscribeCameraStatusUseCase.Result.Unknown:
				Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(CameraItemComponent), nameof(UnsubscribeCameraStatusUseCase));
				Snackbar.Add($"Не удалось отменить подписку на события статуса камеры из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
				break;
			default:
				Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(CameraItemComponent), nameof(UnsubscribeCameraStatusUseCase), response?.Result);
				Snackbar.Add($"Не удалось отменить подписку на события статуса камеры из-за ошибки.", MudBlazor.Severity.Error);
				break;
		}
	}

	private void OnTimerElapsed(object? sender, System.Timers.ElapsedEventArgs e)
	{
		InvokeAsync(async () =>
		{
			await SubscribeAsync();
			StateHasChanged();
		});
	}
}
