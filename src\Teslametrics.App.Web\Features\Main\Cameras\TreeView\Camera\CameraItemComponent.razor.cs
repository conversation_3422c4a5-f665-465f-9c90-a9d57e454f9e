using Microsoft.AspNetCore.Components;
using MudBlazor;
using Teslametrics.App.Web.Events.Cameras;
using Teslametrics.App.Web.Extensions;
using Teslametrics.App.Web.Orleans.Camera;

namespace Teslametrics.App.Web.Features.Main.Cameras.TreeView.Camera;

public partial class CameraItemComponent
{
	private Color IconColor => _presenter.Status switch
	{
		CameraStatus.Running => Color.Success,
		CameraStatus.Starting => Color.Info,
		CameraStatus.Stopped => Color.Warning,
		CameraStatus.Problem => Color.Error,
		_ => Color.Error
	};

	private string _cameraStatusTooltipText => _presenter.Status switch
	{
		CameraStatus.Running => "Камера подключена",
		CameraStatus.Starting => "Камера подключается",
		CameraStatus.Problem => "Ошибка",
		CameraStatus.Stopped => "Камера отключена",
		_ => "Неизвестный статус"
	};

	private FolderViewComponent.TreeItemPresenter _presenter => (FolderViewComponent.TreeItemPresenter)Item;

	[Inject]
	public NavigationManager NavigationManager { get; set; } = null!;

	[Parameter]
	[EditorRequired]
	public TreeItemData<Guid> Item { get; set; } = null!;

	[Parameter]
	[EditorRequired]
	public EventCallback<TreeItemData<Guid>> OnSelect { get; set; }

	[Parameter]
	public Guid OrganizationId { get; set; }

	private async Task SelectHandler()
	{
		if (OnSelect.HasDelegate)
			await OnSelect.InvokeAsync(Item);
	}

	private void ShowArchive() => NavigationManager.NavigateTo($"/cameras/archive/{OrganizationId}/{Item.Value}");
	private void ShowPlayer()
	{
		if (_presenter.Status == CameraStatus.Running)
			EventSystem.Publish(new ShowCameraStreamEto(Item.Value));
	}
	private void Select() => EventSystem.Publish(new CameraSelectEto(OrganizationId, Item.Value));
	private void Delete() => EventSystem.Publish(new CameraDeleteEto(Item.Value));
	private void Edit() => EventSystem.Publish(new CameraEditEto(OrganizationId, Item.Value));

	private void ClearArchive() => EventSystem.Publish(new CameraClearArchiveEto(OrganizationId, Item.Value));
	/* {
		try
		{
			await SetLoadingAsync(true);
			var resolution = await ScopeFactory.MediatorSend(new ClearArchiveUseCase.Command(CameraId));
			switch (resolution.Result)
			{
				case ClearArchiveUseCase.Result.Success:
					Snackbar.Add("Архив камеры очищён", Severity.Success);
					break;
				case ClearArchiveUseCase.Result.ValidationError:
					Snackbar.Add("Ошибка валидации при очистке архива камеры", Severity.Error);
					break;
				case ClearArchiveUseCase.Result.CameraNotFound:
					Snackbar.Add("Камера не найдена", Severity.Error);
					break;
				case ClearArchiveUseCase.Result.Unknown:
				default:
					throw new Exception($"Unexpected result in {nameof(ClearArchiveUseCase)}: {resolution.Result}");
			}
		}
		catch (Exception exc)
		{
			Logger.LogError(exc, exc.Message);
			Snackbar.Add("Не удалось очистить архив камеры из-за непредвиденной ошибки. Повторите попытку", Severity.Error);
		}
		finally
		{
			await SetLoadingAsync(false);
		}
	} */

	private async Task ConnectAsync()
	{
		ConnectCameraUseCase.Response? response;
		try
		{
			response = await ScopeFactory.MediatorSend(new ConnectCameraUseCase.Command(Item.Value));
		}
		catch (Exception ex)
		{
			response = null;
			Snackbar.Add($"Не удалось подключить камеру из-за ошибки сообщения с сервером. Повторите попытку позже.", MudBlazor.Severity.Error);
			Logger.LogError(ex, ex.Message);
		}

		if (response is null) return;

		switch (response.Result)
		{
			case ConnectCameraUseCase.Result.Success:
				Snackbar.Add("Отправлен запрос на подключение камеры", Severity.Success);
				break;
			case ConnectCameraUseCase.Result.CameraNotFound:
				Snackbar.Add("Не удалось подключить камеру так как она не числится в системе", Severity.Error);
				break;
			case ConnectCameraUseCase.Result.ValidationError:
				Snackbar.Add("Не удалось подключить камеру из-за ошибки валидации", Severity.Error);
				break;
			case ConnectCameraUseCase.Result.CameraIsBlocked:
				Snackbar.Add("Камера заблокирована", Severity.Error);
				break;
			case ConnectCameraUseCase.Result.Unknown:
				Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(CameraItemComponent), nameof(ConnectCameraUseCase));
				Snackbar.Add($"Не удалось подключить камеру из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
				break;
			default:
				Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(CameraItemComponent), nameof(ConnectCameraUseCase), response.Result);
				Snackbar.Add($"Не удалось подключить камеру из-за ошибки: {response.Result}", MudBlazor.Severity.Error);
				break;
		}

	}
	private async Task DisconnectAsync()
	{
		DisconnectCameraUseCase.Response? response = null;
		try
		{
			response = await ScopeFactory.MediatorSend(new DisconnectCameraUseCase.Command(Item.Value));
		}
		catch (Exception ex)
		{
			response = null;
			Snackbar.Add($"Не удалось отключить камеру из-за ошибки сообщения с сервером. Повторите попытку позже.", MudBlazor.Severity.Error);
			Logger.LogError(ex, ex.Message);
		}

		if (response is null) return;
		switch (response.Result)
		{
			case DisconnectCameraUseCase.Result.Success:
				Snackbar.Add("Отправлен запрос на отключение камеры", Severity.Success);
				break;
			case DisconnectCameraUseCase.Result.CameraNotFound:
				Snackbar.Add("Не удалось отключить камеру так как она не числится в системе", Severity.Error);
				break;
			case DisconnectCameraUseCase.Result.ValidationError:
				Snackbar.Add("Не удалось отключить камеру из-за ошибки валидации", Severity.Error);
				break;
			case DisconnectCameraUseCase.Result.Unknown:
				Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(CameraItemComponent), nameof(DisconnectCameraUseCase));
				Snackbar.Add($"Не удалось отключить камеру из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
				break;
			default:
				Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(CameraItemComponent), nameof(DisconnectCameraUseCase), response.Result);
				Snackbar.Add($"Не удалось отключить камеру из-за ошибки: {response.Result}", MudBlazor.Severity.Error);
				break;
		}
	}
}
