@using Teslametrics.App.Web.Extensions
@inherits InteractiveBaseComponent<Teslametrics.App.Web.Locales.Features.Main.AccessControl.Roles.Drawer.Create.CamerasPermissions.CamerasConcretePermissionsComponent>
<div class="d_contents">
	<MudTreeView T="Guid"
				 Items="@Items"
				 Class="tree overflow-auto"
				 ReadOnly="true">
		<ItemTemplate>
			@{
				var presenter = (TreeItemPresenter)context;
				<MudTreeViewItem @bind-Expanded="@presenter.Expanded"
								 CanExpand="@presenter.Expandable"
								 Items="@presenter.Children"
								 Icon="@presenter.Icon"
								 Class="tree_item"
								 Value="@presenter.Value">
					<BodyContent Context="body">
						<MudText>@presenter.Text</MudText>
						<MudSpacer />
						<div class="checkboxes">
							@if (presenter.IsFolder)
							{
								@foreach (var permission in _folderValues)
								{
									if (ContainsPermission(permission, context.Value))
									{
										<MudCheckBox Value="true"
													 Class="input_checkbox"
													 Label="@Localizer[permission.GetEnumPermissionString()]"
													 Disabled="true"
													 Color="Color.Primary" />
									}
								}
								if (_cameraValues.Contains(AppPermissions.Main.Cameras.Create))
								{
									if (ContainsPermission(AppPermissions.Main.Cameras.Create, context.Value))
									{
										<MudCheckBox T="bool"
													 Class="input_checkbox"
													 Disabled="true"
													 Value="true"
													 Label="@Localizer[AppPermissions.Main.Cameras.Create.GetEnumPermissionString()]"
													 Color="Color.Primary" />
									}
								}
							}
							@if (presenter.IsCamera)
							{
								@foreach (var permission in _cameraValues.Where(item => item != AppPermissions.Main.Cameras.Create))
								{
									if (ContainsPermission(permission, context.Value))
									{
										<MudCheckBox Value="true"
													 Class="input_checkbox"
													 Label="@Localizer[permission.GetEnumPermissionString()]"
													 Disabled="true"
													 Color="Color.Primary" />
									}
								}
							}
						</div>
					</BodyContent>
				</MudTreeViewItem>
			}
		</ItemTemplate>
	</MudTreeView>
</div>