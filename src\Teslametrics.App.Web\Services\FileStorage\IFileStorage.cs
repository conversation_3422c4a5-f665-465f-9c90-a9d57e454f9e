namespace Teslametrics.App.Web.Services.FileStorage;

public interface IFileStorage
{
    public Task CreateDirectoryIfNotExistsAsync(string name, int retentionDays);
    public Task<Stream> GetFileAsync(string directoryName, string fileName);
    public Task UploadFileAsync(string directoryName, string fileName, Stream stream, IDictionary<string, string> tags);
    public Task DeleteFilesAsync(string directoryName, List<string> names);
    public Task ClearDirectoryAsync(string directoryName);
    public Task<IDictionary<string, string>> GetFileTagsAsync(string directoryName, string fileName);
}