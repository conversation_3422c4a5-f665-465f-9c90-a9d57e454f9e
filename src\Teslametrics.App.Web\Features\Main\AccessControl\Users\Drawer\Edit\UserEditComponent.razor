@using Microsoft.AspNetCore.Components.Authorization
@using Teslametrics.Shared
@inherits InteractiveBaseComponent
<DrawerHeader>
	<MudStack Spacing="0">
		@if (IsLoading)
		{
			<MudSkeleton Width="40%"
						 Height="calc(var(--mud-typography-subtitle1-lineheight) * var(--mud-typography-subtitle1-size))" />
			<MudSkeleton Width="70%"
						 Height="calc(var(--mud-typography-body2-lineheight) * var(--mud-typography-body2-size))" />
		}
		@if (!IsLoading && _user is not null && _user.IsSuccess)
		{
            <MudText Typo="Typo.h3">@_user.Username</MudText>
			@if (_user.LastLoginTime is null)
			{
				<MudText Typo="Typo.body2">
					Пользователь не входил в систему
				</MudText>
			}
			else
			{
				<MudText Typo="Typo.body2">
					Последний вход: @_user.LastLoginTime?.ToLocalTime().ToString("dd/MM/yyyy HH:mm")
				</MudText>
			}
		}
		@if (!IsLoading && (_user is null || !_user.IsSuccess))
		{
			<MudText Typo="Typo.subtitle1">Не удалось получить пользователя</MudText>
		}
	</MudStack>
	<MudSpacer />
	<MudStack Row="true">
		@if (!IsLoading && _user is not null && _user.IsSuccess)
		{
			@if (!_subscribing && (_subscriptionResult is null || !_subscriptionResult.IsSuccess))
			{
				<MudTooltip Arrow="true"
							Placement="Placement.Start"
							Text="Ошибка подписки на события">
					<MudIconButton OnClick="SubscribeAsync"
								   Icon="@Icons.Material.Filled.ErrorOutline"
								   Color="Color.Error" />
				</MudTooltip>
				<MudIconButton OnClick="RefreshAsync"
							   Icon="@Icons.Material.Filled.Refresh"
							   Color="Color.Primary" />
			}
			@if (_user.LockedoutEnabled)
			{
				<MudChip T="string"
						 Variant="Variant.Outlined"
						 Color="Color.Error"
						 Icon="@Icons.Material.Outlined.Lock">Заблокирован</MudChip>
			}
			else
			{
				<MudChip T="string"
						 Variant="Variant.Outlined"
						 Color="Color.Success"
						 Icon="@Icons.Material.Outlined.LockOpen">Активен</MudChip>
			}
			<AuthorizeView Policy="@(Fqdn<AppPermissions>.GetNames(AppPermissions.Main.AccessControl.Users.Update).Last())"
						   Resource="new PolicyRequirementResource(OrganizationId, UserId)"
						   Context="updateContext">
				<MudMenu Icon="@Icons.Material.Filled.MoreVert"
						 AriaLabel="Действия с выбранным пользователем"
						 Color="Color.Primary"
						 Variant="Variant.Outlined">
					<MudMenuItem OnClick="Select"
								 Icon="@Icons.Material.Outlined.PanoramaFishEye">Просмотр пользователя</MudMenuItem>
					@if (!_user.IsSystem && !_sameUser)
					{
						@if (_user.LockedoutEnabled)
						{
							<AuthorizeView Policy="@(Fqdn<AppPermissions>.GetNames(AppPermissions.Main.AccessControl.Users.Unlock).Last())"
										   Resource="new PolicyRequirementResource(OrganizationId, UserId)"
										   Context="innerContext">
								<MudMenuItem OnClick="UnLockAsync"
											 Icon="@Icons.Material.Filled.LockOpen"
											 IconColor="Color.Warning">Разблокировать</MudMenuItem>
							</AuthorizeView>
						}
						else
						{
							<AuthorizeView Policy="@(Fqdn<AppPermissions>.GetNames(AppPermissions.Main.AccessControl.Users.Lock).Last())"
										   Resource="new PolicyRequirementResource(OrganizationId, UserId)"
										   Context="innerContext">
								<MudMenuItem OnClick="LockAsync"
											 Icon="@Icons.Material.Filled.Lock"
											 IconColor="Color.Warning">Заблокировать</MudMenuItem>
							</AuthorizeView>
						}
					}
					@if (!_sameUser)
					{
						<MudMenuItem OnClick="ChangeUserPassword"
									 Icon="@Icons.Material.Filled.Login"
									 IconColor="Color.Warning">Смена пароля пользователя</MudMenuItem>
					}
					<AuthorizeView Policy="@(Fqdn<AppPermissions>.GetNames(AppPermissions.Main.AccessControl.Users.ForceChangePassword).Last())"
								   Resource="new PolicyRequirementResource(OrganizationId, UserId)"
								   Context="innerContext">
						<MudMenuItem OnClick="LoginpasswordChange"
									 Icon="@Icons.Material.Filled.Login"
									 IconColor="Color.Warning">Смена пароля при входе</MudMenuItem>
					</AuthorizeView>
					@if (!_user.IsSystem)
					{
						<AuthorizeView Policy="@(Fqdn<AppPermissions>.GetNames(AppPermissions.Main.AccessControl.Users.Delete).Last())"
									   Resource="new PolicyRequirementResource(OrganizationId, UserId)"
									   Context="innerContext">
							<MudDivider Class="my-4" />
							<MudMenuItem OnClick="Delete"
										 Icon="@Icons.Material.Outlined.Delete"
										 IconColor="Color.Warning">Удалить</MudMenuItem>
						</AuthorizeView>
					}
				</MudMenu>
			</AuthorizeView>
		}
	</MudStack>
</DrawerHeader>

<MudForm Model="_model"
		 Validation="_validator.ValidateValue"
		 @bind-IsValid="_isValid"
		 Class="flex-1"
		 OverrideFieldValidation="true"
		 Spacing="8">
	@if (!IsLoading && _model is not null && _user is not null && _user.IsSuccess)
	{
		<FormSectionComponent Title="Описание пользователя"
							  Subtitle="Настройки, которые влияют только на восприятие человеком">
			<MudTextField Value="_user!.Username"
						  InputType="InputType.Text"
						  Disabled="true"
						  Label="Логин"
						  Required="true" />
		</FormSectionComponent>
		<FormSectionComponent Title="Параметры пользователя"
							  Subtitle="Данные настройки важны для работы в системе">

			<MudAutocomplete T="RoleModel"
							 Value="_search"
							 ValueChanged="OnRoleSelected"
							 Label="Роли пользователя"
							 Placeholder="Введите название роли"
							 ResetValueOnEmptyText="true"
							 ToStringFunc="@(e=> e==null ? null : e.Name)"
							 SearchFunc="@FetchRolesAsync"
							 DebounceInterval="500"
							 ShowProgressIndicator="true"
							 AutoFocus="false"
							 CoerceValue="false"
							 CoerceText="false"
							 UserAttributes="@(new Dictionary<string, object>() {{"autocomplete", "off"}, {"name", "roles"}, {"aria-autocomplete", "none" }})"
							 AdornmentIcon="@Icons.Material.Filled.Search"
							 AdornmentColor="Color.Primary">
				<NoItemsTemplate>
					<MudText Align="Align.Center"
							 Class="px-4 py-1">
						Не найдено ролей с данным названием
					</MudText>
				</NoItemsTemplate>
			</MudAutocomplete>
			<MudSelect T="RoleModel"
					   SelectedValues="@_model.Roles"
					   Required="true"
					   Style="display: none" /> <!-- Нужно для валидации -->

			<div class="d-flex flex-column overflow-auto my-4 roles">
				@if (_model.Roles.Count > 0)
				{
					@foreach (var role in _model.Roles)
					{
						<MudCheckBox T="bool"
									 Value="true"
									 ValueChanged="(isChecked) => _model.Roles.Remove(role)"
									 Label="@role.Name"
									 Color="Color.Primary"
									 @key="role" />
					}
				}
				else
				{
					<MudText Class="pa-3"
							 Color="Color.Error">Пользователь не имеет назначенных ролей в данной организации</MudText>
				}
			</div>
			<IsOwnerComponent IsOwner="_user.IsOwner" />
		</FormSectionComponent>
	}
	@if (IsLoading)
	{
		<UserFormLoadingComponent />
	}
	@if (!IsLoading && (_user is null || !_user.IsSuccess))
	{
		<NoUserFoundComponent />
	}
</MudForm>
<DrawerActions>
	<MudSpacer />
	<MudButton OnClick="CancelAsync"
			   Variant="Variant.Outlined"
			   StartIcon="@Icons.Material.Outlined.Close">Закрыть</MudButton>
	@if (IsLoading)
	{
		<MudSkeleton Width="150px"
					 Height="36.5px" />
	}
	else
	{
		@if (_model is not null)
		{
			<AuthorizeView Policy="@AppPermissions.Main.AccessControl.Users.Update.GetEnumPermissionString()"
						   Resource="new PolicyRequirementResource(OrganizationId, UserId)"
						   Context="innerContext">
				<MudButton OnClick="SubmitAsync"
						   Disabled="@(!_isValid)"
						   Color="Color.Secondary"
						   Variant="Variant.Outlined"
						   StartIcon="@Icons.Material.Outlined.Save">Сохранить</MudButton>
			</AuthorizeView>
		}
	}
</DrawerActions>