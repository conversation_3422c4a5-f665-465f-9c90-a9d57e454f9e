﻿using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Domain.AccessControl.Users;
using Teslametrics.App.Web.Services;
using Teslametrics.App.Web.Services.TransactionManager;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Users.ChangeUserPasswordDialog;

public static class ChangeUserPasswordUseCase
{
    public record Command(Guid Id, string NewPassword) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public Guid Id { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(Guid id)
        {
            Id = id;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Id = Guid.Empty;
            Result = result;
        }
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        UserNotFound
    }

    public class Validator : AbstractValidator<Command>
    {
        public Validator()
        {
            RuleFor(c => c.Id).NotEmpty();
            RuleFor(c => c.NewPassword).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Command, Response>
    {
        private readonly IValidator<Command> _validator;
        private readonly IUserRepository _userRepository;
        private readonly ITransactionManager _transactionManager;

        public Handler(IValidator<Command> validator,
                       IUserRepository userRepository,
                       ITransactionManager transactionManager)
        {
            _validator = validator;
            _userRepository = userRepository;
            _transactionManager = transactionManager;
        }

        public async Task<Response> Handle(Command request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            using var transaction = await _transactionManager.CreateTransactionAsync();

            var user = await _userRepository.FindAsync(request.Id, cancellationToken);
            if (user is null)
            {
                return new Response(Result.UserNotFound);
            }

            var hashedPassword = PasswordHasher.HashPassword(request.NewPassword);

            user.ChangePassword(hashedPassword);

            user.SetForcePasswordChange();

            await _userRepository.SaveChangesAsync(cancellationToken);

            await transaction.CommitAsync();

            return new Response(user.Id);
        }
    }
}