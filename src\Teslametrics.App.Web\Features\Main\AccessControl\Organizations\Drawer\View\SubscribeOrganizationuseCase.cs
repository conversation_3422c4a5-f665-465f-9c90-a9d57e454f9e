using FluentValidation;
using MediatR;
using System.Reactive.Linq;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.Core.Domain.AccessControl.Organizations.Events;
using Teslametrics.App.Web.Exceptions;
using Teslametrics.Core.Services.DomainEventBus;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Organizations.Drawer.View;

public static class SubscribeOrganizationUseCase
{
	public record Request(IObserver<object> Observer, Guid OrganizationId) : BaseRequest<Response>;

	public record Response : BaseResponse
	{
		public IDisposable? Subscription { get; init; }

		public Result Result { get; init; }

		public bool IsSuccess => Result == Result.Success;

		public Response(IDisposable subscription)
		{
			Subscription = subscription;
			Result = Result.Success;
		}

		public Response(Result result)
		{
			if (result == Result.Success)
			{
				throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
			}

			Subscription = null;
			Result = result;
		}
	}

	public record UpdatedEvent(Guid Id);
	public record DeletedEvent(Guid Id);

	public enum Result
	{
		Unknown = 0,
		Success,
        ValidationError
    }

    public class Validator : AbstractValidator<Request>
    {
        public Validator()
        {
            RuleFor(r => r.Observer).NotEmpty();
            RuleFor(r => r.OrganizationId).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Request, Response>
    {
        private readonly IDomainEventBus _domainEventBus;
        private readonly IValidator<Request> _validator;

        public Handler(IDomainEventBus domainEventBus,
                       IValidator<Request> validator)
        {
            _domainEventBus = domainEventBus;
            _validator = validator;
        }

        public async Task<Response> Handle(Request request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            var organizationdId = request.OrganizationId;

            var eventStream = await _domainEventBus.GetEventStreamAsync();

            var subscription = eventStream
                .Where(e => e switch
                {
                    OrganizationUpdatedEvent @event => @event.Id == organizationdId,
                    OrganizationOwnerChangedEvent @event => @event.OrganizationId == organizationdId,
                    OrganizationDeletedEvent @event => @event.Id == organizationdId,
                    _ => false
                })
                .Select<object, object>(e => e switch
                {
                    OrganizationUpdatedEvent @event => new UpdatedEvent(@event.Id),
                    OrganizationOwnerChangedEvent @event => new UpdatedEvent(@event.OrganizationId),
                    OrganizationDeletedEvent @event => new DeletedEvent(@event.Id),
                    _ => throw new AppException("Invalid event type")
                })
                .Subscribe(request.Observer);

            return new Response(subscription);
        }
    }
}