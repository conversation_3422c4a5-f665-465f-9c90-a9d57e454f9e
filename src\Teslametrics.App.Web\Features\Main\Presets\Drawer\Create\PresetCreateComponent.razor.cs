using FluentValidation;
using Microsoft.AspNetCore.Components;
using MudBlazor;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Components.Drawer;
using Teslametrics.App.Web.Extensions;

namespace Teslametrics.App.Web.Features.Main.Presets.Drawer.Create;

public partial class PresetCreateComponent
{
	private class Model
	{
		public string Name { get; set; } = string.Empty;

		public PresetFormComponent.Config ArchiveStreamConfig { get; set; } = new();
		public PresetFormComponent.Config ViewStreamConfig { get; set; } = new();
		public PresetFormComponent.Config PublicStreamConfig { get; set; } = new();
	}

	private class Validator : BaseFluentValidator<Model>
	{
		public Validator()
		{
			RuleFor(model => model.Name)
				.Length(3, 60)
				.WithMessage("наименование должно быть длиной от 3 до 60 символов");
		}
	}

	[CascadingParameter(Name = DrawerConsts.InstanceName)]
	private DrawerComponent Drawer { get; set; } = null!;

	private MudTextField<string>? _nameFieldRef;

	private bool _isValid;
	private Model _model = new();
	private Validator _validator = new();

	#region [Action]
	private async Task SubmitAsync()
	{
		try
		{
			var archiveStreamConfig = new CreateCameraPresetUseCase.Command.StreamConfig(
				_model.ArchiveStreamConfig.Resolution,
				_model.ArchiveStreamConfig.VideoCodec,
				_model.ArchiveStreamConfig.FrameRate,
				_model.ArchiveStreamConfig.SceneDynamic,
				_model.ArchiveStreamConfig.AudioCodec
			);
			var viewStreamConfig = new CreateCameraPresetUseCase.Command.StreamConfig(
				_model.ViewStreamConfig.Resolution,
				_model.ViewStreamConfig.VideoCodec,
				_model.ViewStreamConfig.FrameRate,
				_model.ViewStreamConfig.SceneDynamic,
				_model.ViewStreamConfig.AudioCodec
			);
			var publicStreamConfig = new CreateCameraPresetUseCase.Command.StreamConfig(
				_model.PublicStreamConfig.Resolution,
				_model.PublicStreamConfig.VideoCodec,
				_model.PublicStreamConfig.FrameRate,
				_model.PublicStreamConfig.SceneDynamic,
				_model.PublicStreamConfig.AudioCodec
			);
			var request = new CreateCameraPresetUseCase.Command(
				_model.Name,
				archiveStreamConfig,
				viewStreamConfig,
				publicStreamConfig
			);
			var response = await ScopeFactory.MediatorSend(request);
			switch (response.Result)
			{
				case CreateCameraPresetUseCase.Result.Success:
					Snackbar.Add("Пресет камеры успешно создан.", MudBlazor.Severity.Success);
					await Drawer.HideAsync();
					break;
				case CreateCameraPresetUseCase.Result.ValidationError:
					Snackbar.Add("Ошибка валидации, проверьте правильность заполнения полей", MudBlazor.Severity.Error);
					break;
				case CreateCameraPresetUseCase.Result.CameraPresetNameAlreadyExists:
					if (_nameFieldRef is not null)
					{
						await _nameFieldRef.SetErrorAsync("Данное имя пресета уже существует", true);
					}
					break;
				case CreateCameraPresetUseCase.Result.Unknown:
				default:
					throw new Exception($"Unexpected result in {nameof(CreateCameraPresetUseCase)}: {response.Result}");
			}
		}
		catch (Exception exc)
		{
			Logger.LogError(exc, exc.Message);
			Snackbar.Add("Не удалось создать пресет камеры из-за непредвиденной ошибки.", MudBlazor.Severity.Error);
		}
	}

	private Task CancelAsync() => Drawer.HideAsync();
	#endregion
}
