@inherits InteractiveBaseComponent
<MudAutocomplete T="Preset"
				 Label="Квота"
				 Value="Selected"
				 ValueChanged="OnSelectedValudeChanged"
				 SearchFunc="@SearchAsync"
				 DebounceInterval="500"
				 ToStringFunc="item => item?.Title ?? string.Empty"
				 HelperText="Введите название пресета"
				 MaxItems="25"
				 Required="true"
				 RequiredError="Поле обязательно к заполнению"
				 ShowProgressIndicator="true">
	<NoItemsTemplate>
		Нет подходящих элементов
	</NoItemsTemplate>
</MudAutocomplete>