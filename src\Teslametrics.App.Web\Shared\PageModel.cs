namespace Teslametrics.App.Web.Shared;

public class PageModel
{
    public List<City> Cities { get; set; } = [];

    public Fridge? GetFridge(Guid fridgeId) =>
        Cities.SelectMany(c =>
            c.Buildings.SelectMany(b =>
                b.Floors.SelectMany(f =>
                    f.Rooms.SelectMany(r =>
                        r.Fridges)))).FirstOrDefault(f => f.Id == fridgeId);

    public Floor? GetFloor(Guid floorId) =>
        Cities.SelectMany(c =>
            c.Buildings.SelectMany(b =>
                b.Floors)).FirstOrDefault(f => f.Id == floorId);

    public Room? GetRoom(Guid roomId) =>
        Cities.SelectMany(c =>
            c.Buildings.SelectMany(b =>
                b.Floors.SelectMany(f =>
                    f.Rooms))).FirstOrDefault(r => r.Id == roomId);

    public Room? GetRoomByFridgeId(Guid fridgeId) =>
        Cities.SelectMany(c =>
            c.Buildings.SelectMany(b =>
                b.Floors.SelectMany(f =>
                    f.Rooms))).FirstOrDefault(r => r.Fridges.Any(f => f.Id == fridgeId));
}

public record City
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public List<Building> Buildings { get; set; } = [];
}

public record Building
{
    public Guid Id { get; set; }
    public string Address { get; set; } = string.Empty;
    public List<Floor> Floors { get; set; } = [];
    public Coordinates? Coordinates { get; set; }
}

public record Floor
{
    public Guid Id { get; set; }
    public int Number { get; set; }
    public List<Room> Rooms { get; set; } = [];

    public PlanModel? Plan { get; set; }

    public record PlanModel(Point Position, Size Size);
}

public record Room
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public List<Fridge> Fridges { get; set; } = [];
    public List<ISensorModel> Sensors { get; set; } = [];
    public Cam? Camera { get; set; } // TODO: Нужен список камер
    public Point Position { get; set; } = new Point();
    public IEnumerable<Point> ZonePoints { get; set; } = [];
}

public record Fridge
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public List<ISensorModel> Sensors { get; set; } = [];
    public Point Position { get; set; } = null!;
}

public record Point(double X = 0, double Y = 0);

public record Size(double Width, double Height);

public record Cam
{
    public Guid Id { get; set; }
    public string Name { get; set; }
    public Point Position { get; set; }

    public Cam(Guid id, string name, Point position)
    {
        Id = id;
        Name = name;
        Position = position;
    }
}