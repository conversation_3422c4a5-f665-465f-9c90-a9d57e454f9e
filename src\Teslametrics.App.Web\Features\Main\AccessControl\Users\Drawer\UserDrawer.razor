@using Teslametrics.App.Web.Features.Main.AccessControl.Users.Drawer.Create
@using Teslametrics.App.Web.Features.Main.AccessControl.Users.Drawer.Edit
@using Teslametrics.App.Web.Features.Main.AccessControl.Users.Drawer.View
@inherits InteractiveBaseComponent
<DrawerComponent Open="IsOpened"
				 OpenChanged="OnOpenChanged">
	<CascadingValue IsFixed="true"
					Value="this">
		<div class="mud-height-full px-4 py-4">
			@switch (_mode)
			{
				case DrawerMode.View:
					@if (_userId.HasValue)
					{
						<AuthorizeView Policy="@(Fqdn<AppPermissions>.GetNames(AppPermissions.Main.AccessControl.Users.Read).Last())"
									   Resource="new PolicyRequirementResource(OrganizationId, _userId.Value)">
							<UserViewComonent UserId="_userId.Value"
											  OrganizationId="@OrganizationId" />
						</AuthorizeView>
					}
					break;

				case DrawerMode.Edit:
					@if (_userId.HasValue)
					{
						<AuthorizeView Policy="@(Fqdn<AppPermissions>.GetNames(AppPermissions.Main.AccessControl.Users.Read).Last())"
									   Resource="new PolicyRequirementResource(OrganizationId, _userId.Value)">
							<UserEditComponent UserId="_userId.Value"
											   OrganizationId="@OrganizationId" />
						</AuthorizeView>
					}
					break;

				case DrawerMode.Create:
					<AuthorizeView Policy="@(Fqdn<AppPermissions>.GetNames(AppPermissions.Main.AccessControl.Users.Read).Last())"
								   Resource="new PolicyRequirementResource(OrganizationId)">
						<UserCreateComponent OrganizationId="@OrganizationId" />
					</AuthorizeView>
					break;
			}
		</div>
	</CascadingValue>
</DrawerComponent>