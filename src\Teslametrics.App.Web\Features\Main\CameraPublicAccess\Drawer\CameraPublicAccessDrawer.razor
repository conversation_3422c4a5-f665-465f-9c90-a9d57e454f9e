﻿@using Teslametrics.App.Web.Features.Main.CameraPublicAccess.Drawer.Create
@using Teslametrics.App.Web.Features.Main.CameraPublicAccess.Drawer.Edit
@using Teslametrics.App.Web.Features.Main.CameraPublicAccess.Drawer.View
@inherits InteractiveBaseComponent
<DrawerComponent Open="IsOpened"
                 OpenChanged="OnOpenChanged">
    <CascadingValue IsFixed="true"
                    Value="this">
        <div class="mud-height-full px-4 py-4">
            @switch (_mode)
            {
                case DrawerMode.View:
                    if (_organizationId.HasValue && _cameraId.HasValue && _accessId.HasValue)
                    {
                        <AuthorizeView Policy="@AppPermissions.Main.CameraPublicAccess.Read.GetEnumPermissionString()"
                                       Resource="new PolicyRequirementResource(_organizationId.Value, _accessId.Value)">
                            <ViewComponent OrganizationId="@_organizationId.Value"
                                           CameraId="@_cameraId.Value"
                                           AccessId="@_accessId.Value" />
                        </AuthorizeView>
                    }
                    else
                    {
                        <FailedToRetrieveDataComponent />
                    }
                    break;

                case DrawerMode.Edit:
                    if (_organizationId.HasValue && _cameraId.HasValue && _accessId.HasValue)
                    {
                        <AuthorizeView Policy="@AppPermissions.Main.CameraPublicAccess.Update.GetEnumPermissionString()"
                                       Resource="new PolicyRequirementResource(_organizationId.Value, _accessId.Value)">
                            <EditComponent OrganizationId="@_organizationId.Value"
                                           CameraId="@_cameraId.Value"
                                           AccessId="@_accessId.Value" />
                        </AuthorizeView>
                    }
                    else
                    {
                        <FailedToRetrieveDataComponent />
                    }
                    break;

                case DrawerMode.Create:
                    if (_organizationId.HasValue && _cameraId.HasValue)
                    {
                        <AuthorizeView Policy="@AppPermissions.Main.CameraPublicAccess.Create.GetEnumPermissionString()"
                                       Resource="new PolicyRequirementResource(_organizationId.Value, null)">
                            <CreateComponent OrganizationId="@_organizationId.Value"
                                             CameraId="@_cameraId.Value" />
                        </AuthorizeView>
                    }
                    else
                    {
                        <FailedToRetrieveDataComponent />
                    }
                    break;
            }
        </div>
    </CascadingValue>
</DrawerComponent>