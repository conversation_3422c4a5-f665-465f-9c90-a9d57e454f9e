using System.Buffers.Binary;
using System.Collections.Concurrent;
using System.IO.Hashing;
using System.Runtime.InteropServices;
using System.Text;
using Microsoft.CodeAnalysis.Options;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Orleans.Configuration;
using Orleans.Providers.Streams.Common;
using Orleans.Runtime;
using Orleans.Streams;

namespace Orleans.Providers
{
    /// <summary>
    /// Adapter factory for in memory stream provider.
    /// This factory acts as the adapter and the adapter factory.  The events are stored in an in-memory grain that
    /// behaves as an event queue, this provider adapter is primarily used for testing
    /// </summary>
    public class LiveAdapterFactory<TSerializer> : IQueueAdapterFactory, IQueueAdapter, IQueueAdapterCache
        where TSerializer : class, ILiveMessageBodySerializer
    {
        private readonly StreamCacheEvictionOptions cacheOptions;
        private readonly StreamStatisticOptions statisticOptions;
        private readonly HashRingStreamQueueMapperOptions queueMapperOptions;
        private readonly StreamQueueOptions _queueOptions;
        private readonly IGrainFactory grainFactory;
        private readonly ILoggerFactory loggerFactory;
        private readonly ILogger logger;
        private readonly TSerializer serializer;
        private readonly ulong _nameHash;
        private IStreamQueueMapper? streamQueueMapper;
        private ConcurrentDictionary<QueueId, ILiveStreamQueueGrain>? queueGrains;
        private IObjectPool<FixedSizeBuffer>? bufferPool;
        private BlockPoolMonitorDimensions? blockPoolMonitorDimensions;
        private IStreamFailureHandler? streamFailureHandler;
        private TimePurgePredicate? purgePredicate;

        /// <inheritdoc />
        public string Name { get; }

        /// <inheritdoc />
        public bool IsRewindable => false;

        /// <inheritdoc />
        public StreamProviderDirection Direction => StreamProviderDirection.ReadWrite;

        /// <summary>
        /// Creates a failure handler for a partition.
        /// </summary>
        protected Func<string, Task<IStreamFailureHandler>>? StreamFailureHandlerFactory { get; set; }

        /// <summary>
        /// Create a cache monitor to report cache related metrics
        /// Return a ICacheMonitor
        /// </summary>
        protected Func<CacheMonitorDimensions, ICacheMonitor>? CacheMonitorFactory;

        /// <summary>
        /// Create a block pool monitor to monitor block pool related metrics
        /// Return a IBlockPoolMonitor
        /// </summary>
        protected Func<BlockPoolMonitorDimensions, IBlockPoolMonitor>? BlockPoolMonitorFactory;

        /// <summary>
        /// Create a monitor to monitor QueueAdapterReceiver related metrics
        /// Return a IQueueAdapterReceiverMonitor
        /// </summary>
        protected Func<ReceiverMonitorDimensions, IQueueAdapterReceiverMonitor>? ReceiverMonitorFactory;

        public LiveAdapterFactory(
            string providerName,
            StreamCacheEvictionOptions cacheOptions,
            StreamStatisticOptions statisticOptions,
            HashRingStreamQueueMapperOptions queueMapperOptions,
            IOptions<StreamQueueOptions> queueOptions,
            IServiceProvider serviceProvider,
            IGrainFactory grainFactory,
            ILoggerFactory loggerFactory)
        {
            Name = providerName;
            this.queueMapperOptions = queueMapperOptions ?? throw new ArgumentNullException(nameof(queueMapperOptions));
            _queueOptions = queueOptions.Value;
            this.cacheOptions = cacheOptions ?? throw new ArgumentNullException(nameof(cacheOptions));
            this.statisticOptions = statisticOptions ?? throw new ArgumentException(nameof(statisticOptions));
            this.grainFactory = grainFactory ?? throw new ArgumentNullException(nameof(grainFactory));
            this.loggerFactory = loggerFactory ?? throw new ArgumentNullException(nameof(loggerFactory));
            logger = loggerFactory.CreateLogger<ILogger<LiveAdapterFactory<TSerializer>>>();
            serializer = LiveMessageBodySerializerFactory<TSerializer>.GetOrCreateSerializer(serviceProvider);

            var nameBytes = BitConverter.IsLittleEndian ? MemoryMarshal.AsBytes(Name.AsSpan()) : Encoding.Unicode.GetBytes(Name);
            XxHash64.Hash(nameBytes, MemoryMarshal.AsBytes(MemoryMarshal.CreateSpan(ref _nameHash, 1)));
        }

        /// <summary>
        /// Initializes this instance.
        /// </summary>
        public void Init()
        {
            queueGrains = new ConcurrentDictionary<QueueId, ILiveStreamQueueGrain>();
            if (CacheMonitorFactory == null)
                CacheMonitorFactory = (dimensions) => new DefaultCacheMonitor(dimensions);
            if (BlockPoolMonitorFactory == null)
                BlockPoolMonitorFactory = (dimensions) => new DefaultBlockPoolMonitor(dimensions);
            if (ReceiverMonitorFactory == null)
                ReceiverMonitorFactory = (dimensions) => new DefaultQueueAdapterReceiverMonitor(dimensions);
            purgePredicate = new TimePurgePredicate(cacheOptions.DataMinTimeInCache, cacheOptions.DataMaxAgeInCache);
            streamQueueMapper = new HashRingBasedStreamQueueMapper(queueMapperOptions, Name);
        }

        private void CreateBufferPoolIfNotCreatedYet()
        {
            if (bufferPool == null)
            {
                // 1 meg block size pool
                blockPoolMonitorDimensions = new BlockPoolMonitorDimensions($"BlockPool-{Guid.NewGuid()}");
                var oneMb = 1 << 22;
                var objectPoolMonitor = new ObjectPoolMonitorBridge(BlockPoolMonitorFactory!(blockPoolMonitorDimensions), oneMb);
                bufferPool = new ObjectPool<FixedSizeBuffer>(() => new FixedSizeBuffer(oneMb), objectPoolMonitor, statisticOptions.StatisticMonitorWriteInterval);
            }
        }

        /// <inheritdoc />
        public Task<IQueueAdapter> CreateAdapter()
        {
            return Task.FromResult<IQueueAdapter>(this);
        }

        /// <inheritdoc />
        public IQueueAdapterCache GetQueueAdapterCache()
        {
            return this;
        }

        /// <inheritdoc />
        public IStreamQueueMapper GetStreamQueueMapper()
        {
            return streamQueueMapper!;
        }

        /// <inheritdoc />
        public IQueueAdapterReceiver CreateReceiver(QueueId queueId)
        {
            var dimensions = new ReceiverMonitorDimensions(queueId.ToString());
            var receiverLogger = loggerFactory.CreateLogger($"{typeof(LiveAdapterReceiver<TSerializer>).FullName}.{Name}.{queueId}");
            var receiverMonitor = ReceiverMonitorFactory!(dimensions);
            IQueueAdapterReceiver receiver = new LiveAdapterReceiver<TSerializer>(GetQueueGrain(queueId), receiverLogger, serializer, receiverMonitor);
            return receiver;
        }

        /// <inheritdoc />
        public async Task QueueMessageBatchAsync<T>(StreamId streamId, IEnumerable<T> events, StreamSequenceToken token, Dictionary<string, object> requestContext)
        {
            try
            {
                var queueId = streamQueueMapper!.GetQueueForStream(streamId);
                ArraySegment<byte> bodyBytes = serializer.Serialize(new LiveMessageBody(events.Cast<object>(), requestContext));
                var messageData = LiveMessageData.Create(streamId, bodyBytes);
                ILiveStreamQueueGrain queueGrain = GetQueueGrain(queueId);
                await queueGrain.Enqueue(messageData, _queueOptions.MaxQueueLength);
            }
            catch (Exception exc)
            {
                logger.LogError(exc, "Exception thrown in LiveAdapterFactory.QueueMessageBatchAsync.");
                throw;
            }
        }

        /// <inheritdoc />
        public IQueueCache CreateQueueCache(QueueId queueId)
        {
            //move block pool creation from init method to here, to avoid unnecessary block pool creation when stream provider is initialized in client side.
            CreateBufferPoolIfNotCreatedYet();
            var logger = loggerFactory.CreateLogger($"{typeof(LivePooledCache<TSerializer>).FullName}.{Name}.{queueId}");
            var monitor = CacheMonitorFactory!(new CacheMonitorDimensions(queueId.ToString(), blockPoolMonitorDimensions!.BlockPoolId));
            return new LivePooledCache<TSerializer>(bufferPool!, purgePredicate!, logger, serializer, monitor, statisticOptions.StatisticMonitorWriteInterval, cacheOptions.MetadataMinTimeInCache);
        }

        /// <inheritdoc />
        public Task<IStreamFailureHandler> GetDeliveryFailureHandler(QueueId queueId)
        {
            return Task.FromResult(streamFailureHandler ?? (streamFailureHandler = new NoOpStreamDeliveryFailureHandler()));
        }

        /// <summary>
        /// Generate a deterministic Guid from a queue Id.
        /// </summary>
        private Guid GenerateDeterministicGuid(QueueId queueId)
        {
            Span<byte> bytes = stackalloc byte[16];
            MemoryMarshal.Write(bytes, in _nameHash);
            BinaryPrimitives.WriteUInt32LittleEndian(bytes[8..], queueId.GetUniformHashCode());
            BinaryPrimitives.WriteUInt32LittleEndian(bytes[12..], queueId.GetNumericId());
            return new(bytes);
        }

        /// <summary>
        /// Get a MemoryStreamQueueGrain instance by queue Id.
        /// </summary>
        private ILiveStreamQueueGrain GetQueueGrain(QueueId queueId)
        {
            return queueGrains!.GetOrAdd(queueId, (id, arg) => arg.grainFactory.GetGrain<ILiveStreamQueueGrain>(arg.instance.GenerateDeterministicGuid(id)), (instance: this, grainFactory));
        }

        /// <summary>
        /// Creates a new <see cref="LiveAdapterFactory{TSerializer}"/> instance.
        /// </summary>
        /// <param name="services">The services.</param>
        /// <param name="name">The provider name.</param>
        /// <returns>A mew <see cref="LiveAdapterFactory{TSerializer}"/> instance.</returns>
        public static LiveAdapterFactory<TSerializer> Create(IServiceProvider services, string name)
        {
            var cachePurgeOptions = services.GetOptionsByName<StreamCacheEvictionOptions>(name);
            var statisticOptions = services.GetOptionsByName<StreamStatisticOptions>(name);
            var queueMapperOptions = services.GetOptionsByName<HashRingStreamQueueMapperOptions>(name);
            var factory = ActivatorUtilities.CreateInstance<LiveAdapterFactory<TSerializer>>(services, name, cachePurgeOptions, statisticOptions, queueMapperOptions);
            factory.Init();
            return factory;
        }
    }
}
