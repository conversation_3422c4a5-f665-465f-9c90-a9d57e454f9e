using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Teslametrics.App.Web.Domain.AccessControl.Organizations;
using Teslametrics.App.Web.Domain.AccessControl.Users;
using Teslametrics.App.Web.Services;
using Teslametrics.App.Web.Services.Persistence;
using Teslametrics.App.Web.Shared;

namespace Teslametrics.DbMigrator.Data;

public class TeslametricsDbMigrationService
{
    private readonly ILogger<TeslametricsDbMigrationService> _logger;
    private readonly CommandAppDbContext _context;


    public TeslametricsDbMigrationService(ILogger<TeslametricsDbMigrationService> logger,
                                          CommandAppDbContext context)
    {
        _logger = logger;
        _context = context;
    }

    public async Task MigrateAsync(bool recreateDatabase = false)
    {
        if (recreateDatabase)
        {
            _logger.LogInformation("Deleting existing database...");
            await _context.Database.EnsureDeletedAsync();
            _logger.LogInformation("Database deleted");
        }

        var pendingMigrations = _context.Database.GetPendingMigrations();
        var appliedMigrations = await _context.Database.GetAppliedMigrationsAsync();

        _logger.LogInformation("Current migrations:");
        foreach (var migration in appliedMigrations)
        {
            _logger.LogInformation("  Applied: {Migration}", migration);
        }

        if (pendingMigrations.Any())
        {
            _logger.LogInformation("Found new migrations:");
            foreach (var migration in pendingMigrations)
            {
                _logger.LogInformation("  Pending: {Migration}", migration);
            }

            _logger.LogInformation("Applying migrations...");
            await _context.Database.MigrateAsync();
            _logger.LogInformation("Migrations applied successfully");
        }
        else
        {
            _logger.LogInformation("No new migrations to apply");
        }

        await SeedDataAsync();
    }

    private async Task SeedDataAsync()
    {
        await using var transaction = await _context.Database.BeginTransactionAsync();

        try
        {
            if (!await _context.Set<OrganizationAggregate>().AnyAsync())
            {
                var (organization, _) = OrganizationAggregate.Create(SystemConsts.RootOrganizationId, SystemConsts.RootUserId, "Root");
                await _context.Set<OrganizationAggregate>().AddAsync(organization);
            }

            if (!await _context.Set<UserAggregate>().AnyAsync())
            {
                var hashedPassword = PasswordHasher.HashPassword(SystemConsts.RootUserDefaultPassword);
                var (root, _) = UserAggregate.Create(SystemConsts.RootUserId, SystemConsts.RootUserName, hashedPassword, SystemConsts.RootOrganizationId, []);
                await _context.Set<UserAggregate>().AddAsync(root);
            }

            await _context.SaveChangesAsync();
            await transaction.CommitAsync();
        }
        catch
        {
            await transaction.RollbackAsync();
            throw;
        }
    }
}
