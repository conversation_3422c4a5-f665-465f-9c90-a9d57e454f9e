@inherits InteractiveBaseComponent
<MudDialog ActionsClass="mx-2"
		   ContentClass="pb-12"
		   Visible="_isVisible"
		   VisibleChanged="VisibilityChanged"
		   Options="_deleteDialogOptions">
	<DialogContent>
		<MudStack Row=true>
			<MudIcon Icon="@Icons.Material.Filled.DeleteForever"
					 Class="mt-1" />
			<MudStack Spacing="0">
				<MudText Typo="Typo.h6">Удаление роли</MudText>
				@if (IsLoading)
				{
					<MudSkeleton Width="30%"
								 Height="42px" />
				}
				@if (!IsLoading && (_model is null || _model.IsSuccess))
				{
					<MudText Typo="Typo.subtitle2">Не удалось получить роль</MudText>
				}
				@if (!IsLoading && _model is not null && _model.IsSuccess)
				{
					<MudText Typo="Typo.subtitle2">@_model?.Name</MudText>
				}
			</MudStack>
			<MudSpacer />
			<div>
				@if (_model is not null && _model.IsSuccess && !_subscribing && (_subscriptionResult is null ||
									!_subscriptionResult.IsSuccess))
				{
					<MudTooltip Arrow="true"
								Placement="Placement.Start"
								Text="Ошибка подписки на события">
						<MudIconButton OnClick="SubscribeAsync"
									   Icon="@Icons.Material.Filled.ErrorOutline"
									   Color="Color.Error" />
					</MudTooltip>
					<MudIconButton OnClick="RefreshAsync"
								   Icon="@Icons.Material.Filled.Refresh"
								   Color="Color.Primary" />
				}
				<MudIconButton OnClick="CancelAsync"
							   Icon="@Icons.Material.Outlined.Close" />
			</div>
		</MudStack>

		<MudStack AlignItems="AlignItems.Center"
				  Justify="Justify.Center"
				  Spacing="0"
				  Class="mud-height-full">
			<LoadingComponent IsLoading="IsLoading" />
			<NotFoundComponent IsFound="!IsLoading && _model is not null && _model.IsSuccess" />
			@if (!IsLoading && _model is not null && _model.IsSuccess)
			{
				<MudIcon Icon="@Icons.Material.Outlined.WarningAmber"
						 Color="Color.Warning"
						 Style="font-size: 8rem;"
						 Class="mb-2" />
				<MudText Typo="Typo.subtitle1"
						 Color="Color.Warning">Удаление роли!</MudText>
				<MudText Typo="Typo.body1">Вы уверены, что вы хотите удалить роль <b>@_model?.Name</b>?</MudText>
				<MudText Typo="Typo.body2">Данное изменение необратимо.</MudText>
				<br />
				<MudText>Чтобы удалить роль введите «<MudText Inline="true"
							 Color="Color.Warning">@_confirmationMessage.ToString()</MudText>»</MudText>
				<MudTextField @bind-Value="_input"
							  Label="Подтверждение"
							  Variant="Variant.Text"
							  Immediate="true"
							  Class="mt-2" />
			}
		</MudStack>
	</DialogContent>
	<DialogActions>
		<MudButton OnClick="CancelAsync">Отменить</MudButton>
		@if (IsLoading)
		{
			<MudSkeleton Width="160px"
						 Height="52px" />
		}
		@if (!IsLoading && _model is not null && _model.IsSuccess)
		{
			<MudButton OnClick="SubmitAsync"
					   Color="Color.Warning"
					   Disabled="@(_confirmationMessage.ToString() != _input)">Подвердить</MudButton>
		}
	</DialogActions>
</MudDialog>