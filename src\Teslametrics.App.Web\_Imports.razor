@using System.Net.Http
@using System.Net.Http.Json
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Routing
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Components.Web.Virtualization
@using Microsoft.JSInterop
@using Teslametrics.App.Web
@using Teslametrics.App.Web.Features
@using Microsoft.AspNetCore.Authorization
@using Teslametrics.App.Web.Services.Authorization
@using System.Globalization

@using Teslametrics.App.Web.Components.CSVImport
@using Teslametrics.App.Web.Components.Authorization
@using Teslametrics.App.Web.Components.DragAndDrop.Sortable
@using Teslametrics.App.Web.Components.MSEPlayer
@using Teslametrics.App.Web.Components.Drawer
@using Teslametrics.App.Web.Components.Form
@using Teslametrics.App.Web.Components

@using Teslametrics.App.Web.Shared
@using Teslametrics.App.Web.Extensions

@using MudBlazor
@using Size = MudBlazor.Size

@using static Microsoft.AspNetCore.Components.Web.RenderMode