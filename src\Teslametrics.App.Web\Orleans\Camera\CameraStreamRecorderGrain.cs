using System.Data;
using System.Globalization;
using Confluent.Kafka;
using Dapper;
using FFMpegNET;
using Microsoft.Extensions.Options;
using Teslametrics.App.Web.Data.Sql;
using Teslametrics.App.Web.Services.FileStorage;
using Teslametrics.App.Web.Services.Persistence;
using static Teslametrics.App.Web.Orleans.IoTModule;

namespace Teslametrics.App.Web.Orleans.Camera;

public class CameraStreamRecorderGrain : Grain, ICameraStreamRecorderGrain
{
    private IConsumer<Ignore, MicroSegment>? _consumer;
    private Task? _processTask;
    private MicroSegmentAccumulator _accumulator;
    private Guid _cameraId;
    private CancellationTokenSource? _cts;
    private readonly ILogger<CameraStreamRecorderGrain> _logger;
    private readonly OrleansKafkaSerializer _serializer;
    private readonly IServiceScopeFactory _serviceScopeFactory;
    private readonly IFileStorage _fileStorage;
    private readonly string _kafkaConnectionString;
    private bool _isStarted;
    private Task? _runTask;

    public CameraStreamRecorderGrain(ILogger<CameraStreamRecorderGrain> logger,
                                     ILogger<MicroSegmentAccumulator> accumulatorLogger,
                                     OrleansKafkaSerializer serializer,
                                     IServiceScopeFactory serviceScopeFactory,
                                     IFileStorage fileStorage,
                                     IOptions<KafkaSettings> settings)
    {
        _logger = logger;
        _serializer = serializer;
        _serviceScopeFactory = serviceScopeFactory;
        _fileStorage = fileStorage;
        _accumulator = new MicroSegmentAccumulator(accumulatorLogger, Options.Create(MicroSegmentAccumulator.Options.Default));

        ArgumentNullException.ThrowIfNull(settings, nameof(settings));
        ArgumentNullException.ThrowIfNull(settings.Value.ConnectionString, nameof(settings.Value.ConnectionString));

        _kafkaConnectionString = settings.Value.ConnectionString;
    }

    public override Task OnActivateAsync(CancellationToken cancellationToken)
    {
        return base.OnActivateAsync(cancellationToken);
    }

    public override async Task OnDeactivateAsync(DeactivationReason reason, CancellationToken cancellationToken)
    {
        await StopAsync();
        _accumulator.Dispose();

        await base.OnDeactivateAsync(reason, cancellationToken);
    }

    public async Task StartAsync()
    {
        if (_isStarted)
        {
            return;
        }

        _cts = new CancellationTokenSource();

        _cameraId = this.GetPrimaryKey();

        await CreateTableIfNotExistsAsync(_cameraId);

        var retentionDays = await GetRetentionDaysAsync(_cameraId);
        await _fileStorage.CreateDirectoryIfNotExistsAsync(_cameraId.ToString("N"), retentionDays);

        _runTask = _accumulator.Run(async segment =>
        {
            var startTime = segment.StartTime;
            var endTime = startTime.AddSeconds(segment.Duration);

            var tags = new Dictionary<string, string>
            {
                { "StartTime", startTime.ToUnixTimeMilliseconds().ToString() },
                { "EndTime", endTime.ToUnixTimeMilliseconds().ToString() },
                { "Duration", segment.Duration.ToString(CultureInfo.InvariantCulture) }
            };

            var fileName = segment.StartTime.ToString("dd-MM-yyyy_HH-mm-ss-fff") + ".ts";

            try
            {
                segment.Stream.Position = 0;

                // Upload the file with the memory stream that has a known length
                await _fileStorage.UploadFileAsync(_cameraId.ToString("N"), fileName, segment.Stream, tags);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading segment to storage: {FileName}", fileName);
            }
        }, CancellationToken.None);

        var config = new ConsumerConfig
        {
            BootstrapServers = _kafkaConnectionString,
            GroupId = "teslametrics-recorder",
            AutoOffsetReset = AutoOffsetReset.Earliest,
            AllowAutoCreateTopics = true
        };

        _consumer = new ConsumerBuilder<Ignore, MicroSegment>(config).SetValueDeserializer(_serializer).Build();

        _processTask = Task.Run(() => Process(_cts.Token));

        _isStarted = true;
    }

    public async Task StopAsync()
    {
        if (_processTask is not null)
        {
            _accumulator.RequestGracefulStop();

            _cts!.Cancel();

            await _runTask!;
            _runTask = null;

            await _processTask!;
            _processTask = null;

            _consumer!.Dispose();
        }

        _isStarted = false;
    }

    private void Process(CancellationToken token)
    {
        _consumer!.Subscribe(_cameraId.ToString());

        while (!token.IsCancellationRequested)
        {
            try
            {
                var result = _consumer!.Consume(token);

                if (result != null)
                {
                    if (result.IsPartitionEOF)
                    {
                        continue;
                    }

                    var microSegment = new FFMpegNET.MicroSegment(result.Message.Value.Payload, result.Message.Value.StartTime, result.Message.Value.Duration);
                    _accumulator.AddMicroSegment(microSegment);
                }
                else
                {

                }
            }
            catch (OperationCanceledException)
            {
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing segment of archive camera stream. CameraId: {CameraId}", this.GetPrimaryKey());
            }
        }
    }

    public Task PingAsync()
    {
        return Task.CompletedTask;
    }

    private async Task CreateTableIfNotExistsAsync(Guid cameraId)
    {
        var tableName = $"{Db.StreamSegments.Table}_{cameraId.ToString("N")}";

        using var scope = _serviceScopeFactory.CreateScope();
        using var dbConnection = scope.ServiceProvider.GetRequiredService<IDbConnection>();

        if (dbConnection.State != ConnectionState.Open)
        {
            dbConnection.Open();
        }

        var exists = await dbConnection.ExecuteScalarAsync<bool>("SELECT EXISTS (" +
                                                                    "SELECT FROM pg_tables " +
                                                                    "WHERE schemaname = 'public' " +
                                                                    "AND tablename = @tableName" +
                                                                ")", new { tableName });

        if (!exists)
        {
            using var transaction = dbConnection.BeginTransaction(System.Data.IsolationLevel.Serializable);
            try
            {
                await dbConnection.ExecuteAsync($"create table {tableName} " +
                                                $"({Db.StreamSegments.Columns.SegmentIndex} BIGSERIAL, " +
                                                $"{Db.StreamSegments.Columns.FileName} text, " +
                                                $"{Db.StreamSegments.Columns.StartTime} timestamptz PRIMARY KEY, " +
                                                $"{Db.StreamSegments.Columns.EndTime} timestamptz)");

                // Создаем индексы до преобразования в гипертаблицу
                await dbConnection.ExecuteAsync($"CREATE INDEX idx_{tableName}_filename ON {tableName} ({Db.StreamSegments.Columns.FileName})");

                // Создаем составной индекс по времени
                // EndTime используется в условиях WHERE чаще, поэтому ставим его первым
                await dbConnection.ExecuteAsync($"CREATE INDEX idx_{tableName}_time ON {tableName} ({Db.StreamSegments.Columns.EndTime}, {Db.StreamSegments.Columns.StartTime})");

                // Преобразуем в гипертаблицу после создания индексов
                await dbConnection.ExecuteAsync($"SELECT create_hypertable('{tableName}', '{Db.StreamSegments.Columns.StartTime}')");

                transaction.Commit();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to create table {TableName} and hypertable", tableName);
                transaction.Rollback();
                throw;
            }
        }
    }

    private async Task<int> GetRetentionDaysAsync(Guid cameraId)
    {
        using var scope = _serviceScopeFactory.CreateScope();
        using var dbConnection = scope.ServiceProvider.GetRequiredService<IDbConnection>();

        if (dbConnection.State != ConnectionState.Open)
        {
            dbConnection.Open();
        }

        var retentionPeriodTemplate = SqlQueryBuilder.Create()
            .Select(Db.CameraQuotas.Columns.RetentionPeriodDays)
            .InnerJoin(Db.Cameras.Table, Db.Cameras.Props.QuotaId, Db.CameraQuotas.Props.Id, SqlOperator.Equals)
            .Where(Db.Cameras.Props.Id, ":Id", SqlOperator.Equals, new { Id = cameraId })
            .Build(QueryType.Standard, Db.CameraQuotas.Table, RowSelection.AllRows);

        return await dbConnection.ExecuteScalarAsync<int>(retentionPeriodTemplate.RawSql, retentionPeriodTemplate.Parameters);
    }
}

[Alias("Teslametrics.App.Web.Orleans.ICameraStreamRecorderGrain")]
public interface ICameraStreamRecorderGrain : IGrainWithGuidKey
{
    [Alias("StartAsync")]
    public Task StartAsync();

    [Alias("StopAsync")]
    public Task StopAsync();

    [Alias("PingAsync")]
    public Task PingAsync();
}