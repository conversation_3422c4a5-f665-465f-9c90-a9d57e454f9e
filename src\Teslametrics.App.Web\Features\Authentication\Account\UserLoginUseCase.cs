using System.Security.Claims;
using FluentValidation;
using MediatR;
using Microsoft.AspNetCore.Components.Authorization;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.Core.Domain.AccessControl;
using Teslametrics.Core.Domain.AccessControl.Users;
using Teslametrics.App.Web.Services;
using Teslametrics.App.Web.Services.Authentication;
using Teslametrics.App.Web.Services.Cookies;
using Teslametrics.Core.Services.TransactionManager;
using Teslametrics.App.Web.Services.UserSession;

namespace Teslametrics.App.Web.Features.Authentication.Account;

public static class UserLoginUseCase
{
    public record Command(string Username, string Password) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public Result Result { get; private set; }
        public bool IsSuccess => Result == Result.Success;
        public Guid? SessionId { get; private set; }
        public ClaimsPrincipal? ClaimsPrincipal { get; private set; }

        public Response(ClaimsPrincipal claimsPrincipal, Guid? sessionId, Result result)
        {
            ClaimsPrincipal = claimsPrincipal;
            SessionId = sessionId;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            Result = result;
        }
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        UserNotFound,
        WrongPassword,
        UserLockedout,
        ForceChangePassword,
        SuccessRequared2FA,
        SuccessRequared2FASetup,
        FailedToCreateSession,
        FailedToSubscribeOnAuthenticationState,
    }

    public class Validator : AbstractValidator<Command>
    {
        public Validator()
        {
            RuleFor(c => c.Username).NotEmpty();
            RuleFor(c => c.Password).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Command, Response>
    {
        private readonly IValidator<Command> _validator;
        private readonly IUserRepository _userRepository;
        private readonly ITransactionManager _transactionManager;
        private readonly LogInDomainService _logInDomainService;
        private readonly ISessionProvider _sessionProvider;
        private readonly CookieStorageAccessor _cookieStorageAccessor;
        private readonly IHostEnvironmentAuthenticationStateProvider _authStateProvider;
        private readonly ILogger<Handler> _logger;

        public Handler(IValidator<Command> validator,
                       IUserRepository userRepository,
                       ITransactionManager transactionManager,
                       LogInDomainService logInDomainService,
                       ISessionProvider sessionProvider,
                       IHostEnvironmentAuthenticationStateProvider authStateProvider,
                       CookieStorageAccessor cookieStorageAccessor,
                       ILogger<Handler> logger)
        {
            _validator = validator;
            _userRepository = userRepository;
            _transactionManager = transactionManager;
            _logInDomainService = logInDomainService;
            _sessionProvider = sessionProvider;
            _authStateProvider = authStateProvider;
            _cookieStorageAccessor = cookieStorageAccessor;
            _logger = logger;
        }

        public async Task<Response> Handle(Command request, CancellationToken cancellationToken)
        {
            try
            {
                if (!_validator.Validate(request).IsValid)
                {
                    return new Response(Result.ValidationError);
                }

                using var transaction = await _transactionManager.CreateTransactionAsync();

                var user = await _userRepository.FindByUsernameAsync(request.Username, cancellationToken);
                if (user is null)
                {
                    return new Response(Result.UserNotFound);
                }

                if (user.LockoutEnabled)
                {
                    return new Response(Result.UserLockedout);
                }

                var verifyHashedPassword = PasswordHasher.VerifyHashedPassword(user.Password, request.Password);

                if (verifyHashedPassword is PasswordVerificationResult.Failed)
                {
                    return new Response(Result.WrongPassword);
                }

                if (user.ForcePasswordChange)
                {
                    return new Response(Result.ForceChangePassword);
                }

                if (verifyHashedPassword is PasswordVerificationResult.Success && user.Is2faEnabled && !user.Setup2FAIsCompleted)
                {
                    return new Response(Result.SuccessRequared2FASetup);
                }

                if (verifyHashedPassword is PasswordVerificationResult.Success && user.Is2faEnabled)
                {
                    return new Response(Result.SuccessRequared2FA);
                }

                var permissions = await _logInDomainService.LogInAsync(user, cancellationToken);

                var claims = new List<Claim>();
                claims.AddRange(permissions.Select(x => new Claim(x.Item1, x.Item2)));

                Guid userId = Guid.Parse(claims.Single(x => x.Type == ClaimTypes.NameIdentifier).Value);

                Guid? sessionId = _sessionProvider.GetSessionByUserId(userId)?.Id;
                ClaimsPrincipal userPrincipal = new(new ClaimsIdentity(claims, "Custom"));
                if (sessionId is null)
                {
                    SessionResult? sessionResult = _sessionProvider.CreateSession(userPrincipal);
                    if (!sessionResult.IsSuccess)
                        return new Response(Result.FailedToCreateSession);
                    sessionId = sessionResult.SessionId;
                }

                // await _cookieStorageAccessor.SetValueAsync(AuthenticationStorageNames.SessionId, sessionId);
                // _authStateProvider.SetAuthenticationState(Task.FromResult(new AuthenticationState(userPrincipal)));

                await transaction.CommitAsync();

                return new Response(userPrincipal, sessionId, Result.Success);
            }
            catch (Exception exc)
            {
                _logger.LogError(exc, exc.Message);
                return new Response(Result.Unknown);
            }
        }
    }
}