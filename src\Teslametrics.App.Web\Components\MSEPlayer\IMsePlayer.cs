using Teslametrics.App.Web.Orleans.Camera;

namespace Teslametrics.App.Web.Components.MSEPlayer;

/// <summary>
///     Unified contract for controlling an in-browser <b>Media Source Extensions</b>-based
///     camera player from the Blazor side.
///     A concrete implementation proxies every call to the underlying JavaScript
///     module and keeps the two-way state (status, volume, mute, etc.) in sync.
/// </summary>
public interface IMsePlayer
{
    #region Nested types
    /// <summary>
    ///     High-level playback state of the stream **as seen by the player**, not by the server.<br/>
    ///     <list type="bullet">
    ///     <item><see cref="Playing"/>  – the &lt;video&gt; element is actively decoding and rendering frames.</item>
    ///     <item><see cref="Paused"/>   – a user / code-initiated pause; buffering continues but time does not advance.</item>
    ///     <item><see cref="Stopped"/>  – stream is disconnected, no further fragments are accepted until
    ///           <see cref="ResumeStream"/> is called (starts a new <c>PlayStreamAsync</c>).</item>
    ///     </list>
    /// </summary>
    public enum StreamStatus
    {
        Playing,
        Paused,
        Stopped
    }
    #endregion

    #region Read-only bindings (mirrors of JS state)
    /// <summary>
    ///     Gets or sets the mute flag of the underlying <c>&lt;video&gt;</c> element
    ///     (<see cref="HTMLMediaElement.muted"/> in the browser).
    ///     <para>
    ///         The property is a <b>direct proxy</b>: assigning <c>true</c> immediately turns off
    ///         the audio track in JavaScript; assigning <c>false</c> enables audio if autoplay
    ///         policy allows.
    ///     </para>
    ///     <para>
    ///         **No other volume controls exist in the interface** — muting/unmuting is the only
    ///         permitted way to affect loudness.
    ///     </para>
    /// </summary>
    bool Muted { get; }

    /// <summary>
    /// <para><c>true</c> – the stream is requested immediately after initialisation
    /// (equivalent to the <c>autoplay</c> attribute on HTMLVideoElement).</para>
    /// <para>Changing the value after construction has no effect; use
    /// <see cref="ResumeStream"/> / <see cref="PauseStream"/> instead.</para>
    /// </summary>
    bool Autoplay { get; }

    /// <summary>
    ///     Current output volume in the range 0 … 1
    ///     (matches <c>HTMLMediaElement.volume</c> on the client side).
    /// </summary>
    float Volume { get; }

    /// <summary>Identifier of the camera whose stream is bound to this player instance.</summary>
    Guid CameraId { get; }

    /// <summary>
    ///     Physical encoding type that the back-end delivers
    ///     (<c>H264_TS</c>, <c>H264_FMP4</c>, &amp;c.).
    ///     The value is fixed for the lifetime of the instance and is used to select the proper
    ///     transmuxing / SourceBuffer MIME type.
    /// </summary>
    StreamType Type { get; }

    /// <summary>Current high-level playback state (see <see cref="StreamStatus"/>).</summary>
    StreamStatus Status { get; }

    /// <summary>
    ///     Указывает, находится ли видеоэлемент в полноэкранном режиме.
    /// </summary>
    bool IsFullscreen { get; }
    #endregion

    #region Events
    /// <summary>
    ///     Fired every time <see cref="Status"/> changes inside the JavaScript player
    ///     (pause, resume, reconnect, manual stop).
    ///     <para>Use it to update UI controls (play/pause buttons, LIVE badge, &amp;c.).</para>
    /// </summary>
    event EventHandler<StreamStatus> StreamStatusChanged;

    /// <summary>
    ///     Срабатывает при изменении состояния полноэкранного режима.
    ///     <para>Используйте для обновления UI элементов (кнопка полноэкранного режима).</para>
    /// </summary>
    event EventHandler<bool> FullscreenChanged;
    #endregion

    #region Control methods
    /// <summary>
    ///     Resumes the media pipeline:<br/>
    ///     <list type="bullet">
    ///     <item>unpauses the underlying <c>&lt;video&gt;</c> tag;</item>
    ///     <item>requests the server to send <c>PlayStreamAsync</c>;</item>
    ///     <item>clears the <see cref="Paused"/> flag &amp; raises <see cref="StreamStatusChanged"/>.</item>
    ///     </list>
    ///     No-op when the player is already in <see cref="StreamStatus.Playing"/>.
    /// </summary>
    Task ResumeStream();

    /// <summary>
    ///     Pauses <c>&lt;video&gt;</c> and sends <c>StopStreamAsync</c> so that
    ///     no further fragments are pushed until <see cref="ResumeStream"/> is called.
    ///     No data is lost – the current buffer is kept in memory.
    /// </summary>
    Task PauseStream();

    /// <summary>
    ///     Sets the audio output level on the client side.
    /// </summary>
    /// <param name="volume">
    ///     Target volume: value from <c>0.0</c> (silence) to <c>1.0</c> (maximum).
    ///     Values outside the range are clamped.
    /// </param>
    Task SetVolumeAsync(float volume);

    /// <summary>
    ///     Переключает полноэкранный режим для видеоэлемента.
    ///     Если видео не в полноэкранном режиме, переводит его в полноэкранный режим.
    ///     Если видео уже в полноэкранном режиме, выходит из него.
    /// </summary>
    /// <returns>Task, представляющий асинхронную операцию</returns>
    Task ToggleFullscreenAsync();

    /// <summary>
    ///     Переходит к просмотру прямого эфира (live edge).
    ///     Полезно, когда пользователь поставил видео на паузу, а затем хочет вернуться к текущему моменту трансляции.
    /// </summary>
    /// <returns>Task, представляющий асинхронную операцию</returns>
    Task SeekToLiveAsync();

    /// <summary>
    ///     Возвращает время записи текущего сегмента видео.
    ///     Это время соответствует моменту, когда сегмент был записан камерой.
    /// </summary>
    /// <returns>Task, возвращающий строку с временем в формате ISO 8601 или null, если время не определено</returns>
    Task<string?> GetCurrentSegmentTimestampAsync();

    /// <summary>
    ///     Возвращает текущее абсолютное время воспроизведения видео.
    ///     Это время соответствует реальному времени записи кадра, который отображается в данный момент.
    /// </summary>
    /// <returns>Task, возвращающий строку с временем в формате ISO 8601 или null, если время не определено</returns>
    Task<string?> GetCurrentAbsoluteTimeAsync();
    #endregion
}