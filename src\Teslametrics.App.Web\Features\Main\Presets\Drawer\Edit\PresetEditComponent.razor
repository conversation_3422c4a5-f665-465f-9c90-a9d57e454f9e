@inherits InteractiveBaseComponent
<DrawerHeader>
	<MudStack Spacing="0">
		<MudText Typo="Typo.h1">Редактирование пресета</MudText>
		@if (IsLoading)
		{
			<MudSkeleton Width="60%"
						 Height="calc(var(--mud-typography-subtitle1-lineheight) * var(--mud-typography-subtitle1-size))" />
		}
		@if (!IsLoading && _response is not null && _response.IsSuccess)
		{
			<MudText Typo="Typo.subtitle1">@_response.Name</MudText>
		}
		@if (!IsLoading && (_response is null || !_response.IsSuccess))
		{
			<MudText Typo="Typo.subtitle1">Не удалось получить пресет</MudText>
		}
	</MudStack>
	<MudSpacer />
	@if (!_subscribing && (_subscriptionResult is null || !_subscriptionResult.IsSuccess))
	{
		<MudTooltip Arrow="true"
					Placement="Placement.Start"
					Text="Ошибка подписки на события">
			<MudIconButton OnClick="SubscribeAsync"
						   Icon="@Icons.Material.Filled.ErrorOutline"
						   Color="Color.Error" />
		</MudTooltip>
	}
	<MudTooltip Text="@($"Время последнего обновления: {_lastUpdateTime.ToLocalTime()}")"
				Arrow="true"
				Placement="Placement.Start">
		<MudIconButton OnClick="RefreshAsync"
					   Icon="@Icons.Material.Filled.Refresh"
					   Color="Color.Primary" />
	</MudTooltip>
</DrawerHeader>
@if (IsLoading)
{
	<MudProgressLinear Indeterminate="true" />
}
else
{
	<div style="height: 4px;"></div>
}
<div class="px-4 py-4">
	@if (_response is not null && _response.IsSuccess && _model is not null)
	{
		<MudForm Model="_model"
				 Validation="_validator.ValidateValue"
				 @bind-IsValid="_isValid"
				 OverrideFieldValidation="true"
				 Spacing="8">
			<FormSectionComponent Title="Описание пресета"
								  Subtitle="Настройки, которые влияют только на восприятие человеком">
				<MudTextField @bind-Value="_model.Name"
							  For="@(() => _model.Name)"
							  Clearable="true"
							  InputType="InputType.Text"
							  Immediate="true"
							  Label="Наименование"
							  HelperText="Необходимо для идентификации пресета человеком"
							  RequiredError="Данное поле обязательно"
							  Required="true"
							  @ref="_nameFieldRef" />
			</FormSectionComponent>
			@if (_model.ArchiveStreamConfig is not null)
			{
				<PresetFormComponent Title="Настройки потока архива"
									 Subtitle="Настройки, которые влияют на параметры хранения записей"
									 PresetConfig="_model.ArchiveStreamConfig" />
			}
			@if (_model.ViewStreamConfig is not null)
			{
				<PresetFormComponent Title="Настройки потока видов"
									 Subtitle="Настройки, которые влияют на восприятие человеком"
									 PresetConfig="_model.ViewStreamConfig" />
			}
			@if (_model.PublicStreamConfig is not null)
			{
				<PresetFormComponent Title="Настройки потока публичного доступа"
									 Subtitle="Настройки, которые влияют на восприятие человеком"
									 PresetConfig="_model.PublicStreamConfig" />
			}
		</MudForm>
	}
	<FormLoadingComponent IsLoading="IsLoading && (_response is null || !_response.IsSuccess || _model is null)" />
	<NoItemsFoundComponent HasItems="_response is not null && _response.IsSuccess && _model is not null"
						   LastRefreshTime="_lastUpdateTime"
						   RefreshAsync="RefreshAsync" />

	<MudStack Row="true"
			  Class="mt-4">
		<MudSpacer />
		<MudButton OnClick="CancelAsync"
				   Variant="Variant.Outlined"
				   StartIcon="@Icons.Material.Outlined.Close">Закрыть</MudButton>
		@if (!IsLoading && _model is not null)
		{
			<AuthorizeView Policy="@AppPermissions.Main.CameraPresets.Update.GetEnumPermissionString()"
						   Resource="new PolicyRequirementResource(null, PresetId)"
						   Context="saveContext">
				<MudButton OnClick="SubmitAsync"
						   Disabled="@(!_isValid || IsLoading)"
						   Color="Color.Secondary"
						   Variant="Variant.Outlined">Сохранить</MudButton>
			</AuthorizeView>
		}
		@if (IsLoading)
		{
			<MudSkeleton Width="150px"
						 Height="36.5px" />
		}
	</MudStack>
</div>