using Microsoft.EntityFrameworkCore;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Domain.Notifications;

namespace Teslametrics.App.Web.Services.Persistence;

/// <summary>
/// Репозиторий для работы с уведомлениями об инцидентах
/// </summary>
public class IncidentNotificationRepository : BaseRepository<IncidentNotificationAggregate>, IIncidentNotificationRepository
{
    public IncidentNotificationRepository(CommandAppDbContext dbContext)
        : base(dbContext)
    {
    }

    /// <inheritdoc />
    public Task<int> GetNotificationCountForUserAsync(Guid userId,
                                                      CancellationToken cancellationToken = default) =>
        DbContext.Set<IncidentNotificationAggregate>()
            .AsNoTracking()
            .Where(n => n.UserId == userId)
            .CountAsync(cancellationToken);

    /// <inheritdoc />
    public async Task<bool> DeleteByIncidentIdAndUserIdAsync(Guid incidentId,
                                                             Guid userId,
                                                             CancellationToken cancellationToken = default)
    {
        var notification = await DbContext.Set<IncidentNotificationAggregate>()
            .AsTracking()
            .FirstOrDefaultAsync(n => n.IncidentId == incidentId && n.UserId == userId, cancellationToken);

        if (notification != null)
        {
            DbContext.Set<IncidentNotificationAggregate>().Remove(notification);
            return true;
        }

        return false;
    }

    public async Task<List<Guid>> DeleteAllByUserIdAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        var notifications = await DbContext.Set<IncidentNotificationAggregate>()
            .AsTracking()
            .Where(n => n.UserId == userId)
            .ToListAsync(cancellationToken);

        var deletedNotificationIds = new List<Guid>();

        foreach (var notification in notifications)
        {
            DbContext.Set<IncidentNotificationAggregate>().Remove(notification);
            deletedNotificationIds.Add(notification.IncidentId);
        }

        return deletedNotificationIds;
    }
}