@using Teslametrics.App.Web.Features.DetailView.ViewFrame.Camera
@inherits InteractiveBaseComponent
<div class="mud-height-full overflow-hidden grid_container">
	@if (_viewResponse is not null && _viewResponse.IsSuccess && _viewResponse.Cells.Count > 0)
	{
		<div class="@($"grid {_viewResponse.GridType.ToString()}")"
			 style="@($"--cols: {_viewResponse?.ColumnCount};--rows: {_viewResponse?.RowCount};")">
			@foreach (var cell in _cells.OrderBy(x => x.CellIndex))
			{
				<div @key="cell">
					<CameraComponent CameraId="@cell.CameraId"
									 OrganizationId="@OrganizationId" />
				</div>
			}
		</div>
	}
	@if (_viewResponse is not null && _viewResponse.IsSuccess && _viewResponse.Cells.Count == 0)
	{
		<MudStack AlignItems="AlignItems.Center">
			<MudText Typo="Typo.subtitle1">Нет элементов</MudText>
			<MudText Typo="Typo.body1">Добавьте элементы и попробуйте снова</MudText>
			<MudText Typo="Typo.body2">Время последнего обновления: @_lastRefreshTime.ToLocalTime()</MudText>

			<MudButton OnClick="RefreshAsync"
					   Variant="Variant.Filled"
					   Color="Color.Primary"
					   StartIcon="@Icons.Material.Outlined.Refresh">Обновить</MudButton>
		</MudStack>
	}
	@if (_viewResponse is not null && _viewResponse.Result == GetViewUseCase.Result.ViewNotFound)
	{
		<MudStack AlignItems="AlignItems.Center">
			<MudText Typo="Typo.subtitle1">Вид не найден
			</MudText>
			<MudText Typo="Typo.body1">Данный вид не существует</MudText>
			<MudText Typo="Typo.body2">Время последнего обновления: @_lastRefreshTime.ToLocalTime()</MudText>

			<MudButton OnClick="RefreshAsync"
					   Variant="Variant.Filled"
					   Color="Color.Primary"
					   StartIcon="@Icons.Material.Outlined.Refresh">Обновить</MudButton>
		</MudStack>
	}
	@if (_viewResponse is not null && _viewResponse.Result != GetViewUseCase.Result.ViewNotFound &&
			!_viewResponse.IsSuccess)
	{
		<MudStack AlignItems="AlignItems.Center">
			<MudText Typo="Typo.subtitle1">Вид не найден
			</MudText>
			<MudText Typo="Typo.body1">Нет удалось получить вид из-за внутренней ошибки</MudText>
			<MudText Typo="Typo.body2">Время последнего обновления: @_lastRefreshTime.ToLocalTime()</MudText>

			<MudButton OnClick="RefreshAsync"
					   Variant="Variant.Filled"
					   Color="Color.Primary"
					   StartIcon="@Icons.Material.Outlined.Refresh">Обновить</MudButton>
		</MudStack>
	}
</div>
