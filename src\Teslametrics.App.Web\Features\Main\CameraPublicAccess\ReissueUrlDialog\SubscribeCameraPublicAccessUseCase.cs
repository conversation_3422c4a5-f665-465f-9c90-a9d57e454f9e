using System.Reactive.Linq;
using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Domain.Cameras.Events;
using Teslametrics.App.Web.Domain.PublicLinks.Events;
using Teslametrics.App.Web.Exceptions;
using Teslametrics.App.Web.Services.DomainEventBus;

namespace Teslametrics.App.Web.Features.Main.CameraPublicAccess.ReissueUrlDialog;

public static class SubscribeCameraPublicAccessUseCase
{
    public record Request(IObserver<object> Observer, Guid CameraId, Guid AccessId) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public IDisposable? Subscription { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(IDisposable subscription)
        {
            Subscription = subscription;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Subscription = null;
            Result = result;
        }
    }

    public record UpdatedEvent(); // Обновилась камера ИЛИ ссылка
    public record AccessReissuedEvent(Guid Id); // Ссылка перевыпущена
    public record CameraDeletedEvent(Guid Id); // Удалилась камера
    public record AccessDeletedEvent(Guid Id); // Удалилась ссылка

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        AccessNotFound
    }

    public class Validator : AbstractValidator<Request>
    {
        public Validator()
        {
            RuleFor(r => r.Observer).NotEmpty();
            RuleFor(r => r.CameraId).NotEmpty();
            RuleFor(r => r.AccessId).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Request, Response>
    {
        private readonly IValidator<Request> _validator;
        private readonly IDomainEventBus _domainEventBus;

        public Handler(IValidator<Request> validator,
                       IDomainEventBus domainEventBus)
        {
            _validator = validator;
            _domainEventBus = domainEventBus;
        }

        public async Task<Response> Handle(Request request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            var eventStream = await _domainEventBus.GetEventStreamAsync();

            var subscription = eventStream
                .Where(e => e switch
                {
                    PublicLinkUpdatedEvent @event => @event.Id == request.AccessId,
                    PublicLinkReissuedEvent @event => @event.OldId == request.AccessId,
                    PublicLinkDeletedEvent @event => @event.Id == request.AccessId,
                    CameraUpdatedEvent @event => @event.Id == request.CameraId,
                    CameraDeletedEvent @event => @event.Id == request.CameraId,
                    _ => false
                })
                .Select<object, object>(e => e switch
                {
                    PublicLinkUpdatedEvent @event => new UpdatedEvent(),
                    PublicLinkReissuedEvent @event => new AccessReissuedEvent(@event.NewId),
                    PublicLinkDeletedEvent @event => new AccessDeletedEvent(@event.Id),
                    CameraUpdatedEvent @event => new UpdatedEvent(),
                    CameraDeletedEvent @event => new CameraDeletedEvent(@event.Id),
                    _ => throw new AppException("Invalid event type")
                })
                .Subscribe(request.Observer);

            return new Response(subscription);
        }
    }
}