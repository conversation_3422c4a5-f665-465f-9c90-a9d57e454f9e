let doorOpeningsChart = null;

/**
 * Initialize the door openings mixed chart (bar + line)
 * @param {Object} data - The chart data
 */
export function initDoorOpeningsChart(data) {
    if (doorOpeningsChart) {
        doorOpeningsChart.destroy();
    }

    const options = {
        chart: {
            type: 'line',
            height: 300,
            toolbar: {
                show: false
            }
        },
        stroke: {
            width: [0, 3],
            curve: 'smooth'
        },
        plotOptions: {
            bar: {
                columnWidth: '40%'
            }
        },
        colors: ['#FF5722', '#2196F3'],
        series: [
            {
                name: data.series[0].name,
                type: 'column',
                data: data.series[0].data
            },
            {
                name: data.series[1].name,
                type: 'line',
                data: data.series[1].data
            }
        ],
        xaxis: {
            categories: data.categories
        },
        yaxis: [
            {
                title: {
                    text: 'Количество открытий'
                },
                min: 0,
                max: Math.max(...data.series[0].data) * 1.2,
                tickAmount: 5
            },
            {
                opposite: true,
                title: {
                    text: 'Среднее время (мин)'
                },
                min: 0,
                max: Math.max(...data.series[1].data) * 1.2,
                tickAmount: 5
            }
        ],
        markers: {
            size: 4,
            strokeWidth: 0,
            hover: {
                size: 6
            }
        },
        legend: {
            position: 'top',
            horizontalAlign: 'right'
        },
        grid: {
            xaxis: {
                lines: {
                    show: true
                }
            },
            yaxis: {
                lines: {
                    show: true
                }
            }
        }
    };

    doorOpeningsChart = new ApexCharts(document.querySelector("#door-openings-chart"), options);
    doorOpeningsChart.render();
}