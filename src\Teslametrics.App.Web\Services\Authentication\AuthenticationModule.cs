using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Teslametrics.App.Web.Services.Authentication;
using Teslametrics.App.Web.Middleware;
using Teslametrics.App.Web.Services.Authorization;
using Teslametrics.App.Web.Services.Cookies;
using Teslametrics.App.Web.Shared;
using Teslametrics.App.Web.Services.Authentication.TwoFactorAuth;

namespace Teslametrics.App.Web.Services.Authentication;

public static class AuthenticationModule
{
    public static void Install(IServiceCollection services)
	{
		services.AddAuthentication().AddCookie(options =>
		{
			options.Cookie.Name = AuthenticationStorageNames.SessionId;
		});

		services.AddCascadingAuthenticationState();

		//services.AddScoped<TeslametricsAuthenticationService>();
		//services.AddScoped<IAuthenticationService>(services => services.GetRequiredService<TeslametricsAuthenticationService>());
		services.AddTransient<TwoFactorAuthService>();

		services.AddScoped<TeslametricsAuthenticationStateProvider>();
		services.AddScoped<AuthenticationStateProvider>(services => services.GetRequiredService<TeslametricsAuthenticationStateProvider>());
		services.AddScoped(sp => (IHostEnvironmentAuthenticationStateProvider)sp.GetRequiredService<TeslametricsAuthenticationStateProvider>());

		services.AddTransient<CustomAuthenticationMiddleware>();
		services.AddTransient<CookieStorageAccessor>();
	}
}
