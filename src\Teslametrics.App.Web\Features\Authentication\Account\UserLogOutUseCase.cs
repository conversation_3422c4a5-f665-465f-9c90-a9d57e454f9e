using System;
using System.Security.Claims;
using MediatR;
using Microsoft.AspNetCore.Components.Authorization;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Services.Authentication;
using Teslametrics.App.Web.Services.Cookies;
using Teslametrics.App.Web.Services.UserSession;

namespace Teslametrics.App.Web.Features.Authentication.Account;

public static class UserLogOutUseCase
{
    public record Command() : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public Result Result { get; private set; }
        public bool IsSuccess => Result == Result.Success;

        public Response(Result result)
        {
            Result = result;
        }
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        SessionIdNotSet
    }

    public class Handler : IRequestHandler<Command, Response>
    {
        private readonly ISessionProvider _sessionProvider;
        private readonly CookieStorageAccessor _cookieStorageAccessor;
        private readonly IHostEnvironmentAuthenticationStateProvider _authStateProvider;
        private readonly ILogger<Handler> _logger;

        public Handler(IHostEnvironmentAuthenticationStateProvider authStateProvider, CookieStorageAccessor cookieStorageAccessor, ISessionProvider sessionProvider, ILogger<Handler> logger)
        {
            _authStateProvider = authStateProvider;
            _cookieStorageAccessor = cookieStorageAccessor;
            _sessionProvider = sessionProvider;
            _logger = logger;
        }

        public async Task<Response> Handle(Command request, CancellationToken cancellationToken)
        {
            try
            {
                Guid? sessionId = await _cookieStorageAccessor.GetValueAsync<Guid?>(AuthenticationStorageNames.SessionId);
                if (sessionId is null)
                {
                    return new Response(Result.SessionIdNotSet);
                }

                _sessionProvider.KillSession(sessionId.Value);

                //await _cookieStorageAccessor.RemoveAsync(AuthenticationStorageNames.SessionId);
                // TODO Разобраться нужно ли это, так-как выше файрится событие
                //_authStateProvider.SetAuthenticationState(Task.FromResult(new AuthenticationState(new ClaimsPrincipal(new ClaimsIdentity()))));

                return new Response(Result.Success);
            }
            catch (Exception exc)
            {
                _logger.LogError(exc, exc.Message);
                return new Response(Result.Unknown);
            }
        }
    }
}
