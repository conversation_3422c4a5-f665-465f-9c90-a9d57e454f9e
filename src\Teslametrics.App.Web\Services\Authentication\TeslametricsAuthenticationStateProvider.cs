using MediatR;
using Microsoft.AspNetCore.Components.Authorization;
using System.Reactive;
using System.Security.Claims;
using Teslametrics.App.Web.Extensions;
using Teslametrics.App.Web.Services.Authentication;
using Teslametrics.App.Web.Services.Cookies;
using Teslametrics.App.Web.Services.UserSession;

namespace Teslametrics.App.Web.Services.Authentication;

// По-хорошему GetAuthenticationStateAsync Должен иметь вид GetAuthenticationStateAsync() => _authenticationStateTask;
// Всё остальное должно происходить в SetAuthenticationState | Реагировать на события
public class TeslametricsAuthenticationStateProvider(IServiceScopeFactory scopeFactory,
												   ILogger<TeslametricsAuthenticationStateProvider> logger,
												   ISessionProvider sessionProvider,
												   CookieStorageAccessor cookieStorageAccessor) : AuthenticationStateProvider, IDisposable, IHostEnvironmentAuthenticationStateProvider
{
	private static AuthenticationState _emptyUser => new(new ClaimsPrincipal(new ClaimsIdentity()));

	private bool _disposedValue;

	private IDisposable? _subscription;
	private IDisposable? _sessionProviderSubscription;

	private Task<AuthenticationState> _authenticationStateTask = Task.FromResult(_emptyUser);

	private readonly IServiceScopeFactory _scopeFactory = scopeFactory;
	private readonly ILogger _logger = logger;
	private readonly ISessionProvider _sessionProvider = sessionProvider;
	private readonly CookieStorageAccessor _cookieStorageAccessor = cookieStorageAccessor;

	#region [Realization]
	// Всё закоментировано, т.к. ... Оно не нужно. Первичный стейт я получаю из TeslametricsAuthenticationService или из CustomAuthenticationMiddleware. Дальнейшее изменение стейта идёт из подписки.
	// Данный провайдер исполняется на хосте, так что обрыв соединения с клиентом не страшен, если что-то упало на сервере, то вылетит Exception, который положит сервис.
	public override /*async*/ Task<AuthenticationState> GetAuthenticationStateAsync()
	{
		return _authenticationStateTask;
	}

	public async void SetAuthenticationState(Task<AuthenticationState> authenticationStateTask)
	{
		var authState = await authenticationStateTask ?? throw new ArgumentNullException(nameof(authenticationStateTask));

		if (!authState.User.Identity?.IsAuthenticated ?? true)
		{
			await _cookieStorageAccessor.RemoveAsync(AuthenticationStorageNames.SessionId);
			Unsubscribe();
		}

		if (authState.User.Identity?.IsAuthenticated ?? false)
		{
			var storedState = await _authenticationStateTask;
			var userId = authState.User.GetUserId()!;
			if (_subscription is not null || userId != storedState.User.GetUserId())
			{
				Unsubscribe();
			}

			_sessionProviderSubscription ??= _sessionProvider.Subscribe(userId.Value, Observer.Create<Guid>(OnSessionEventHandler, OnError));
			if (_subscription is null)
			{
				Guid currentUserGuid = authState.User.GetUserId()!.Value;
				var scope = _scopeFactory.CreateScope();
				var accessor = scope.ServiceProvider.GetService<IHttpContextAccessor>();
				if (accessor?.HttpContext is not null)
					accessor.HttpContext.User = authState.User;
				var result = await scope.ServiceProvider.GetRequiredService<IMediator>().Send(new SubscribeAuthenticationStateFunction.Request(Observer.Create<object>(OnAppEventHandler, OnError), userId.Value)); // Вот почему-то хочу подписываться в AuthService, а не тут
				scope.Dispose();
				if (result.IsSuccess)
				{
					_subscription = result.Subscription;
				}
				else
				{
					authState = _emptyUser;
					throw new AuthStateSubscriptionErrorException("Failed to subscribe on authentication state"); // Не уверен, что в данном случае сойдёт подобный эксептион. Нужен,  чтобы перехватить в AuthService и понять, почему нельзя сохранять куку.
				}
			}
		}

		_authenticationStateTask = Task.FromResult(authState);
		NotifyAuthenticationStateChanged(_authenticationStateTask);
	}
	#endregion [Realization]

	public void Dispose()
	{
		Dispose(disposing: true);
		GC.SuppressFinalize(this);
	}

	protected virtual void Dispose(bool disposing)
	{
		if (!_disposedValue)
		{
			if (disposing)
			{
				Unsubscribe();
			}
			_disposedValue = true;
		}
	}

	#region [Event Handlers]
	private void OnAppEventHandler(object appEvent)
	{
		SetAuthenticationState(Task.FromResult(_emptyUser));
	}

	private void OnSessionEventHandler(Guid userId)
	{
		var sessionResult = _sessionProvider.GetSessionByUserId(userId);
        if (sessionResult is not null)
        {
			SetAuthenticationState(Task.FromResult(new AuthenticationState(sessionResult.ClaimsPrincipal)));
		}
		else
		{
		SetAuthenticationState(Task.FromResult(_emptyUser));
		}
	}

	private async void OnError(Exception exc)
	{
		_logger.LogError(exc, exc.Message);
		await _cookieStorageAccessor.RemoveAsync(AuthenticationStorageNames.SessionId);
		SetAuthenticationState(Task.FromResult(_emptyUser));
	}
	#endregion [Event Handlers]

	private void Unsubscribe()
	{
		_subscription?.Dispose();
		_sessionProviderSubscription?.Dispose();
		_sessionProviderSubscription = null;
		_subscription = null;
	}
}

