using Confluent.Kafka;
using Orleans.Serialization;

namespace Teslametrics.App.Web.Orleans.Camera;

public class OrleansKafkaSerializer : ISerializer<MicroSegment>, IDeserializer<MicroSegment>
{
    private readonly Serializer _serializer;

    public OrleansKafkaSerializer(Serializer serializer)
    {
        _serializer = serializer;
    }

    public byte[] Serialize(MicroSegment data, SerializationContext context)
    {
        return _serializer.SerializeToArray(data);
    }

    public MicroSegment Deserialize(ReadOnlySpan<byte> data, bool isNull, SerializationContext context)
    {
        return _serializer.Deserialize<MicroSegment>(data);
    }
}