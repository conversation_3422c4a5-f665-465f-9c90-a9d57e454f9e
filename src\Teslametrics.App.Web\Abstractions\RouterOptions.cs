﻿using System.Reflection;

namespace Teslametrics.App.Web.Abstractions;

public class RouterOptions
{
    public Type? DefaultLayout { get; set; }
    public Assembly AppAssembly { get; set; } = default!;

    public List<Assembly> AdditionalAssemblies { get; set; }

    public RouterOptions()
    {
        AdditionalAssemblies = new();
    }

    public RouterOptions(Assembly appAssembly, List<Assembly> additionalAssemblies)
    {
        AppAssembly = appAssembly;
        AdditionalAssemblies = additionalAssemblies;
    }
}

