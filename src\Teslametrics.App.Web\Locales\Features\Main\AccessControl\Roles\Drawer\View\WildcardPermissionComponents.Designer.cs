﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Teslametrics.App.Web.Locales.Features.Main.AccessControl.Roles.Drawer.View {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class WildcardPermissionComponents {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal WildcardPermissionComponents() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Teslametrics.App.Web.Locales.Features.Main.AccessControl.Roles.Drawer.View.Wildca" +
                            "rdPermissionComponents", typeof(WildcardPermissionComponents).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create organizations.
        /// </summary>
        public static string Main_AccessControl_Organizations_Create {
            get {
                return ResourceManager.GetString("Main.AccessControl.Organizations.Create", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete organizations.
        /// </summary>
        public static string Main_AccessControl_Organizations_Delete {
            get {
                return ResourceManager.GetString("Main.AccessControl.Organizations.Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Read organizations.
        /// </summary>
        public static string Main_AccessControl_Organizations_Read {
            get {
                return ResourceManager.GetString("Main.AccessControl.Organizations.Read", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit organization.
        /// </summary>
        public static string Main_AccessControl_Organizations_Update {
            get {
                return ResourceManager.GetString("Main.AccessControl.Organizations.Update", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create roles.
        /// </summary>
        public static string Main_AccessControl_Roles_Create {
            get {
                return ResourceManager.GetString("Main.AccessControl.Roles.Create", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete roles.
        /// </summary>
        public static string Main_AccessControl_Roles_Delete {
            get {
                return ResourceManager.GetString("Main.AccessControl.Roles.Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Read roles.
        /// </summary>
        public static string Main_AccessControl_Roles_Read {
            get {
                return ResourceManager.GetString("Main.AccessControl.Roles.Read", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit roles.
        /// </summary>
        public static string Main_AccessControl_Roles_Update {
            get {
                return ResourceManager.GetString("Main.AccessControl.Roles.Update", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create user.
        /// </summary>
        public static string Main_AccessControl_Users_Create {
            get {
                return ResourceManager.GetString("Main.AccessControl.Users.Create", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete users.
        /// </summary>
        public static string Main_AccessControl_Users_Delete {
            get {
                return ResourceManager.GetString("Main.AccessControl.Users.Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Force the user to change their password upon login.
        /// </summary>
        public static string Main_AccessControl_Users_ForceChangePassword {
            get {
                return ResourceManager.GetString("Main.AccessControl.Users.ForceChangePassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lock users.
        /// </summary>
        public static string Main_AccessControl_Users_Lock {
            get {
                return ResourceManager.GetString("Main.AccessControl.Users.Lock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Read users.
        /// </summary>
        public static string Main_AccessControl_Users_Read {
            get {
                return ResourceManager.GetString("Main.AccessControl.Users.Read", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unlock users.
        /// </summary>
        public static string Main_AccessControl_Users_Unlock {
            get {
                return ResourceManager.GetString("Main.AccessControl.Users.Unlock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit user.
        /// </summary>
        public static string Main_AccessControl_Users_Update {
            get {
                return ResourceManager.GetString("Main.AccessControl.Users.Update", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create camera presets.
        /// </summary>
        public static string Main_CameraPresets_Create {
            get {
                return ResourceManager.GetString("Main.CameraPresets.Create", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete camera presets.
        /// </summary>
        public static string Main_CameraPresets_Delete {
            get {
                return ResourceManager.GetString("Main.CameraPresets.Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Read cameras presets.
        /// </summary>
        public static string Main_CameraPresets_Read {
            get {
                return ResourceManager.GetString("Main.CameraPresets.Read", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit camera presets.
        /// </summary>
        public static string Main_CameraPresets_Update {
            get {
                return ResourceManager.GetString("Main.CameraPresets.Update", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Connect cameras.
        /// </summary>
        public static string Main_Cameras_Connect {
            get {
                return ResourceManager.GetString("Main.Cameras.Connect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create cameras.
        /// </summary>
        public static string Main_Cameras_Create {
            get {
                return ResourceManager.GetString("Main.Cameras.Create", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete cameras.
        /// </summary>
        public static string Main_Cameras_Delete {
            get {
                return ResourceManager.GetString("Main.Cameras.Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Disconnect cameras.
        /// </summary>
        public static string Main_Cameras_Disconnect {
            get {
                return ResourceManager.GetString("Main.Cameras.Disconnect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Move to another folder.
        /// </summary>
        public static string Main_Cameras_Move {
            get {
                return ResourceManager.GetString("Main.Cameras.Move", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Read cameras.
        /// </summary>
        public static string Main_Cameras_Read {
            get {
                return ResourceManager.GetString("Main.Cameras.Read", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit cameras.
        /// </summary>
        public static string Main_Cameras_Update {
            get {
                return ResourceManager.GetString("Main.Cameras.Update", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create folder.
        /// </summary>
        public static string Main_Folders_Create {
            get {
                return ResourceManager.GetString("Main.Folders.Create", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete folder.
        /// </summary>
        public static string Main_Folders_Delete {
            get {
                return ResourceManager.GetString("Main.Folders.Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Move folder to another folder.
        /// </summary>
        public static string Main_Folders_Move {
            get {
                return ResourceManager.GetString("Main.Folders.Move", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Read folders.
        /// </summary>
        public static string Main_Folders_Read {
            get {
                return ResourceManager.GetString("Main.Folders.Read", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit folder.
        /// </summary>
        public static string Main_Folders_Update {
            get {
                return ResourceManager.GetString("Main.Folders.Update", resourceCulture);
            }
        }
    }
}
