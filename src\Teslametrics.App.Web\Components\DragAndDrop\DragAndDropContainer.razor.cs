using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Web;
using System.Linq.Expressions;

namespace Teslametrics.App.Web.Components.DragAndDrop;

public partial class DragAndDropContainer<TValue> where TValue : class
{
	private TValue? _draggingItem;

	//[Parameter]
	//public EventCallback<(DragEventArgs @event, TValue @value)> OnDragStart { get; set; }
	//[Parameter]
	//public EventCallback<(DragEventArgs @event, TValue @value)> OnDragOver { get; set; }
	//[Parameter]
	//public EventCallback<(DragEventArgs @event, TValue @value)> OnDragLeave { get; set; }
	public record OnDropHandler(DragEventArgs Event, TValue DraggedItem, TValue Target);

	public bool IsDragging => _draggingItem is not null;

	[Parameter]
	public EventCallback<OnDropHandler> OnDrop { get; set; }

	[Parameter]
	public RenderFragment? ChildContent { get; set; }

	/// <summary>
	/// Определяет, можно ли отпустить перетаскиваемый элемент на указанный элемент.
	/// </summary>
	/// <param name="dragging">Перетаскиваемый элемент.</param>
	/// <param name="onhover">Элемент, на который пользователь пытается перетащить.</param>
	/// <returns>Значение <see cref="bool"/>, указывающее, разрешено ли отпускание перетаскиваемого элемента.</returns>
	[Parameter]
	public Expression<Func<TValue, TValue, bool>>? CanDrop { get; set; } = null!;

	[Parameter]
	public Func<TValue, TValue, Task<bool>>? CanDropAsync { get; set; }

	[Parameter]
	public Expression<Func<TValue, bool>>? CanDrag { get; set; } = null!;

	[Parameter]
	public Func<TValue, Task<bool>>? CanDragAsync { get; set; }

	public void DragStart(DragEventArgs @event, TValue @value)
	{
		if (_draggingItem is not null) // Вернули на место
		{
			return;
		}

		_draggingItem = value;
	}

	public async Task DropAsync(DragEventArgs @event, TValue value)
	{
		if (_draggingItem is null) // Вернули на место
		{
			return;
		}

		if (value.Equals(_draggingItem))
		{
			_draggingItem = null;
			return;
		}

		var canDrop = await IsDroppableAsync(value);
		if (canDrop && OnDrop.HasDelegate)
		{
			await OnDrop.InvokeAsync(new OnDropHandler(@event, _draggingItem, value));
		}

		_draggingItem = null;
	}

	public async Task<bool> IsDraggableAsync(TValue value)
	{
		if (CanDragAsync is not null)
		{
			return await CanDragAsync(value);
		}
		if (CanDrag is not null)
		{
			return EvaluateExpression(CanDrag, value);
		}

		return false;
	}

	public async Task<bool> IsDroppableAsync(TValue value)
	{
		if (_draggingItem is null)
			return false;

		if (value.Equals(_draggingItem))
			return true;

		if (CanDropAsync is not null)
			return await CanDropAsync(_draggingItem, value);

		if (CanDrop is not null)
			return EvaluateExpression(CanDrop, _draggingItem, @value);

		return false;
	}

	private static bool EvaluateExpression<T>(Expression<Func<T, T, bool>> expression, T draggable, T instance)
	{
		return expression.Compile().Invoke(draggable, instance);
	}

	private static bool EvaluateExpression<T>(Expression<Func<T, bool>> expression, T instance)
	{
		return expression.Compile().Invoke(instance);
	}
}
