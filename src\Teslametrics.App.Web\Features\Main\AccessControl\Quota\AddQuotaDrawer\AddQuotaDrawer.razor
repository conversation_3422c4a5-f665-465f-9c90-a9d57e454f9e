@inherits InteractiveBaseComponent
<DrawerComponent Open="IsOpened"
                 OpenChanged="OpenChangedHandler">
    <DrawerHeader>
        Добавление квот камер в организацию
    </DrawerHeader>
    @if (_organizationId is not null)
    {
        <AuthorizeView Policy="@AppPermissions.Main.CameraQuotas.Create.GetEnumPermissionString()"
                       Context="innerContext"
                       Resource="new PolicyRequirementResource(null, null)">
            <PresetAddComponent OrganizationId="_organizationId.Value" />
        </AuthorizeView>
    }
</DrawerComponent>