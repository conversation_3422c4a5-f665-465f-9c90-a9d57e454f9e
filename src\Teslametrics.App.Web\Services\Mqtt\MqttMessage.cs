namespace Teslametrics.App.Web.Services.Mqtt;

/// <summary>
/// Представляет сообщение, полученное от MQTT брокера
/// </summary>
public class MqttMessage
{
    /// <summary>
    /// Название топика
    /// </summary>
    public string Topic { get; }

    /// <summary>
    /// Полученное значение
    /// </summary>
    public string Value { get; }

    public DateTimeOffset Timestamp { get; }

    public MqttMessage(string topic, string value, DateTimeOffset timestamp)
    {
        Topic = topic;
        Value = value;
        Timestamp = timestamp;
    }
}
