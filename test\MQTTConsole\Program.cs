using MQTTnet;
using MQTTnet.LowLevelClient;
using System;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using MQTTConsole;

internal class Program
{
    // Преобразует DateTime в Unix timestamp (секунды с 1 января 1970)
    private static long DateTimeToUnixTimestamp(DateTime dateTime)
    {
        DateTime unixEpoch = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
        return (long)(dateTime.ToUniversalTime() - unixEpoch).TotalSeconds;
    }

    private static async Task Main(string[] args)
    {
        var mqttService = new MqttClientService();

        // Подключение к брокеру (например, к публичному тестовому брокеру)
        await mqttService.ConnectAsync("192.168.126.200", 1883, "ClientId-" + Guid.NewGuid().ToString());

        // Создаем RPC клиент
        var rpcClient = new MqttRpcClient(mqttService.MqttClient);

        // Пример вызова RPC метода
        try
        {
            Console.WriteLine("Вызываем RPC метод...");

            // Асинхронный вызов
            var result = await rpcClient.CallAsync<JsonDocument>(
                "db_logger",
                "history",
                "get_values",
                new
                {
                    channels = new[] { new[] {"wb-m1w2_30", "External Sensor 1"} },
                    timestamp = new
                    {
                        gt = DateTimeToUnixTimestamp(DateTime.Now.AddMinutes(-5)),
                        lt = DateTimeToUnixTimestamp(DateTime.Now)
                    },
                    ver = 1,
                    limit = 10,
                    with_milliseconds = true
                }
            );

            Console.WriteLine($"Результат асинхронного вызова: {result?.RootElement}");
        }
        catch (MqttRpcTimeoutException)
        {
            Console.WriteLine("Таймаут при выполнении RPC вызова");
        }
        catch (MqttRpcException ex)
        {
            Console.WriteLine($"Ошибка RPC: {ex.Message}, Код: {ex.Code}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Общая ошибка: {ex.Message}");
        }

        // Подписка на обычные топики MQTT
        await mqttService.SubscribeAsync("/devices/wb-m1w2_30/controls/External Sensor 1",
                                         "/devices/wb-m1w2_30/controls/External Sensor 2",
                                         "/devices/wb-msw-v3_64/controls/Temperature");

        Console.WriteLine("Нажмите любую клавишу для выхода...");
        Console.ReadKey();

        // Отключение
        await mqttService.DisconnectAsync();
    }
}

public class MqttClientService
{
    private IMqttClient? _mqttClient;
    public IMqttClient MqttClient => _mqttClient ?? throw new InvalidOperationException("MQTT клиент не инициализирован");
    private MqttClientOptions? _options;
    private bool _intentionalDisconnect = false;

    public async Task ConnectAsync(string brokerAddress, int port, string clientId)
    {
        _intentionalDisconnect = false;

        // Создание фабрики клиента
        var factory = new MqttClientFactory();
        _mqttClient = factory.CreateMqttClient();

        // Настройка параметров подключения
        _options = new MqttClientOptionsBuilder()
            .WithTcpServer(brokerAddress, port)
            .WithClientId(clientId)
            .WithCleanSession()
            .Build();

        // Обработчики событий
        _mqttClient.ApplicationMessageReceivedAsync += e =>
        {
            Console.WriteLine($"Получено сообщение: {e.ApplicationMessage.ConvertPayloadToString()} из топика: {e.ApplicationMessage.Topic}");
            return Task.CompletedTask;
        };

        _mqttClient.DisconnectedAsync += async e =>
        {
            if (!_intentionalDisconnect)
            {
                Console.WriteLine("Соединение потеряно. Пытаемся переподключиться...");
                await Task.Delay(TimeSpan.FromSeconds(5));
                await ConnectAsync();
            }
        };

        // Подключение к брокеру
        await ConnectAsync();
    }

    private async Task ConnectAsync()
    {
        try
        {
            await _mqttClient!.ConnectAsync(_options, CancellationToken.None);
            Console.WriteLine("Подключено к MQTT брокеру.");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Ошибка подключения: {ex.Message}");
        }
    }

    public async Task SubscribeAsync(params string[] topics)
    {
        if (_mqttClient!.IsConnected && topics.Length > 0)
        {
            // Создаем опции подписки
            var subscribeOptionsBuilder = new MqttClientSubscribeOptionsBuilder();

            // Добавляем все топики в один запрос подписки
            foreach (var topic in topics)
            {
                subscribeOptionsBuilder.WithTopicFilter(new MqttTopicFilterBuilder()
                    .WithTopic(topic)
                    .WithQualityOfServiceLevel(MQTTnet.Protocol.MqttQualityOfServiceLevel.AtLeastOnce)
                    .Build());

                Console.WriteLine($"Добавлен топик для подписки: {topic}");
            }

            // Выполняем подписку
            var result = await _mqttClient.SubscribeAsync(subscribeOptionsBuilder.Build());

            Console.WriteLine("Подписка выполнена успешно");
            foreach (var item in result.Items)
            {
                Console.WriteLine($"Топик: {item.TopicFilter.Topic}, Результат: {item.ResultCode}");
            }
        }
    }

    public async Task DisconnectAsync()
    {
        if (_mqttClient is not null && _mqttClient.IsConnected)
        {
            // Устанавливаем флаг намеренного отключения перед вызовом Disconnect
            _intentionalDisconnect = true;

            await _mqttClient.DisconnectAsync();
            Console.WriteLine("Отключено от MQTT брокера.");
        }
    }
}