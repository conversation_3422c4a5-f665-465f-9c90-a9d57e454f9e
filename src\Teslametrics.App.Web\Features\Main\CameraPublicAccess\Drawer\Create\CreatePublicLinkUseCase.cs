using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.Core.Domain.Cameras;
using Teslametrics.Core.Domain.PublicLinks;
using Teslametrics.Core.Services.Outbox;
using Teslametrics.Core.Services.TransactionManager;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Features.Main;

public static class CreatePublicLinkUseCase
{
    public record Command(Guid CameraId, string Name) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public Guid Id { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(Guid id)
        {
            Id = id;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Id = Guid.Empty;
            Result = result;
        }
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        CameraNotFound,
        NameAlreadyExists
    }

    public class Validator : AbstractValidator<Command>
    {
        public Validator()
        {
            RuleFor(x => x.CameraId).NotEmpty();
            RuleFor(x => x.Name).Length(3, 60);
        }
    }

    public class Handler : IRequestHandler<Command, Response>
    {
        private readonly IValidator<Command> _validator;
        private readonly IPublicLinkRepository _publicLinkRepository;
        private readonly ICameraRepository _cameraRepository;
        private readonly ITransactionManager _transactionManager;
        private readonly IPublisher _publisher;
        private readonly IOutbox _outbox;

        public Handler(IValidator<Command> validator,
                       IPublicLinkRepository publicLinkRepository,
                       ICameraRepository cameraRepository,
                       ITransactionManager transactionManager,
                       IPublisher publisher,
                       IOutbox outbox)
        {
            _validator = validator;
            _publicLinkRepository = publicLinkRepository;
            _cameraRepository = cameraRepository;
            _transactionManager = transactionManager;
            _publisher = publisher;
            _outbox = outbox;
        }

        public async Task<Response> Handle(Command request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            using var transaction = await _transactionManager.CreateTransactionAsync();

            if (!await _cameraRepository.IsCameraExistsAsync(request.CameraId, cancellationToken))
            {
                return new Response(Result.CameraNotFound);
            }

            if (await _publicLinkRepository.IsNameExistsAsync(request.Name, cancellationToken))
            {
                return new Response(Result.NameAlreadyExists);
            }

            var (publicLink, events) = PublicLinkAggregate.Create(GuidGenerator.New(), request.CameraId, request.Name);

            await _publicLinkRepository.AddAsync(publicLink, cancellationToken);
            await _publicLinkRepository.SaveChangesAsync(cancellationToken);

            foreach (var @event in events)
            {
                await _publisher.Publish(@event, cancellationToken);
            }

            await _outbox.AddRangeAsync(events);

            await transaction.CommitAsync();

            return new Response(publicLink.Id);
        }
    }
}