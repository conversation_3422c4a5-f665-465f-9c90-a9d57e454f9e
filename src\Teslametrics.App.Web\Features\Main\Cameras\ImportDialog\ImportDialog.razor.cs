using CsvHelper;
using CsvHelper.Configuration;
using CsvHelper.TypeConversion;
using System.Globalization;
using System.Text.RegularExpressions;
using Teslametrics.App.Web.Components.CSVImport;
using Teslametrics.App.Web.Events.Cameras;
using Teslametrics.App.Web.Extensions;
using Teslametrics.Shared;
using Teslametrics.App.Web.Abstractions;
using FluentValidation;

namespace Teslametrics.App.Web.Features.Main.Cameras.ImportDialog;

public partial class ImportDialog
{
    #region [Type Definitions]
    private class CameraImportDto
    {
        public string Name { get; set; } = string.Empty;
        public string Directory { get; set; } = string.Empty;
        public string Quota { get; set; } = string.Empty;
        public string ArchiveUri { get; set; } = string.Empty;
        public string ViewUri { get; set; } = string.Empty;
        public string PublicUri { get; set; } = string.Empty;
        public TimeSpan TimeZone { get; set; } = TimeSpan.FromHours(3);
        public Coordinates? Coordinates { get; set; }
        public bool AutoStart { get; set; }
        public bool StartOnCreate { get; set; }
        public bool IsValid { get; set; } = true;
        public bool HasQuotaError { get; set; } = false;
        public string? QuotaErrorMessage { get; set; }
    };

    private class ImportValidator : BaseFluentValidator<CameraImportDto>
    {
        private static string? ExtractHostAndPort(string uri)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(uri)) return null;

                var rtspUri = new Uri(uri);
                if (rtspUri.Scheme != "rtsp") return null;

                return rtspUri.Authority;
            }
            catch
            {
                return null;
            }
        }

        private static bool ValidateUriHostMatch(string currentUri, CameraImportDto model)
        {
            if (string.IsNullOrWhiteSpace(currentUri))
                return true;

            var currentHost = ExtractHostAndPort(currentUri);
            if (currentHost == null)
                return true;

            var allUris = new[] { model.ArchiveUri, model.PublicUri, model.ViewUri };
            var allHosts = allUris
                .Where(uri => !string.IsNullOrWhiteSpace(uri))
                .Select(ExtractHostAndPort)
                .Where(h => h != null);

            return allHosts.All(h => h == currentHost);
        }

        public ImportValidator()
        {
            RuleFor(model => model.Name)
                .NotEmpty().WithMessage("Поле должно быть заполнено")
                .Length(3, 60).WithMessage("наименование должно быть длиной от 3 до 60 символов");

            RuleFor(model => model.Directory)
                .NotEmpty().WithMessage("Поле должно быть заполнено")
                .Length(3, 60).WithMessage("Имя директории должно быть длиной от 3 до 60 символов");

            RuleFor(model => model.ArchiveUri)
                .NotEmpty().WithMessage("Поле должно быть заполнено")
                .Must(uri => Uri.TryCreate(uri, UriKind.Absolute, out var parsed) && parsed.Scheme == "rtsp")
                .WithMessage("Некорректный формат RTSP URI")
                .Must((model, uri) => ValidateUriHostMatch(uri, model))
                .WithMessage("Все RTSP URI должны иметь одинаковый хост и порт");

            RuleFor(model => model.ViewUri)
                .NotEmpty().WithMessage("Поле должно быть заполнено")
                .Must(uri => Uri.TryCreate(uri, UriKind.Absolute, out var parsed) && parsed.Scheme == "rtsp")
                .WithMessage("Некорректный формат RTSP URI")
                .Must((model, uri) => ValidateUriHostMatch(uri, model))
                .WithMessage("Все RTSP URI должны иметь одинаковый хост и порт");

            RuleFor(model => model.PublicUri)
                .NotEmpty().WithMessage("Поле должно быть заполнено")
                .Must(uri => Uri.TryCreate(uri, UriKind.Absolute, out var parsed) && parsed.Scheme == "rtsp")
                .WithMessage("Некорректный формат RTSP URI")
                .Must((model, uri) => ValidateUriHostMatch(uri, model))
                .WithMessage("Все RTSP URI должны иметь одинаковый хост и порт");

            RuleFor(model => model.Quota)
                .NotEmpty().WithMessage("Поле должно быть заполнено");
        }
    }

    private class CoordinatesConverter : DefaultTypeConverter
    {
        public override object? ConvertFromString(string? text, IReaderRow row, MemberMapData memberMapData)
        {
            if (string.IsNullOrWhiteSpace(text))
                return null;

            try
            {
                // Убираем скобки и лишние пробелы
                var cleanText = text.Trim('(', ')', ' ');

                // Используем регулярное выражение для парсинга координат
                // Паттерн: число (с возможной дробной частью), затем запятая и пробел, затем еще одно число
                var pattern = @"^(-?\d+(?:[.,]\d+)?)\s*,\s*(-?\d+(?:[.,]\d+)?)$";
                var match = Regex.Match(cleanText, pattern);

                if (match.Success)
                {
                    var latText = match.Groups[1].Value.Replace(',', '.');
                    var lngText = match.Groups[2].Value.Replace(',', '.');

                    // Парсим широту и долготу
                    if (double.TryParse(latText, NumberStyles.Float, CultureInfo.InvariantCulture, out var latitude) &&
                        double.TryParse(lngText, NumberStyles.Float, CultureInfo.InvariantCulture, out var longitude))
                    {
                        return new Coordinates(latitude, longitude);
                    }
                }
            }
            catch
            {
                // Игнорируем ошибки парсинга
            }

            return null;
        }

        public override string? ConvertToString(object? value, IWriterRow row, MemberMapData memberMapData)
        {
            if (value is Coordinates coordinates)
            {
                return coordinates.ToString();
            }
            return string.Empty;
        }
    }

    private class ImportMap : ClassMap<CameraImportDto>
    {
        public ImportMap(IEnumerable<CsvHeaderMapper.HeaderMapping> mappings)
        {
            var nameMapping = mappings.FirstOrDefault(m => m.ModelField.Field == nameof(CameraImportDto.Name));
            if (nameMapping != null)
                Map(m => m.Name).Name(nameMapping.CsvHeader.Name);

            var directoryMapping = mappings.FirstOrDefault(m => m.ModelField.Field == nameof(CameraImportDto.Directory));
            if (directoryMapping != null)
                Map(m => m.Directory).Name(directoryMapping.CsvHeader.Name);

            var quotaMapping = mappings.FirstOrDefault(m => m.ModelField.Field == nameof(CameraImportDto.Quota));
            if (quotaMapping != null)
                Map(m => m.Quota).Name(quotaMapping.CsvHeader.Name);

            var archiveUriMapping = mappings.FirstOrDefault(m => m.ModelField.Field == nameof(CameraImportDto.ArchiveUri));
            if (archiveUriMapping != null)
                Map(m => m.ArchiveUri).Name(archiveUriMapping.CsvHeader.Name);

            var viewUriMapping = mappings.FirstOrDefault(m => m.ModelField.Field == nameof(CameraImportDto.ViewUri));
            if (viewUriMapping != null)
                Map(m => m.ViewUri).Name(viewUriMapping.CsvHeader.Name);

            var publicUriMapping = mappings.FirstOrDefault(m => m.ModelField.Field == nameof(CameraImportDto.PublicUri));
            if (publicUriMapping != null)
                Map(m => m.PublicUri).Name(publicUriMapping.CsvHeader.Name);

            var autoStartMapping = mappings.FirstOrDefault(m => m.ModelField.Field == nameof(CameraImportDto.AutoStart));
            if (autoStartMapping != null)
                Map(m => m.AutoStart).Name(autoStartMapping.CsvHeader.Name);

            var coordinatesMapping = mappings.FirstOrDefault(m => m.ModelField.Field == nameof(CameraImportDto.Coordinates));
            if (coordinatesMapping != null)
                Map(m => m.Coordinates).Name(coordinatesMapping.CsvHeader.Name).TypeConverter<CoordinatesConverter>();

            var timeZoneMapping = mappings.FirstOrDefault(m => m.ModelField.Field == nameof(CameraImportDto.TimeZone));
            if (timeZoneMapping != null)
                Map(m => m.TimeZone).Name(timeZoneMapping.CsvHeader.Name);
        }
    }
    #endregion

    #region [Dialog Options]
    private MudBlazor.DialogOptions _dialogOptions = new() { CloseOnEscapeKey = true, FullWidth = true, MaxWidth = MudBlazor.MaxWidth.Large, NoHeader = true };
    private bool _isVisible;
    #endregion

    private Guid _organizationId;

    private int _activePanelIndex = 0;
    private IEnumerable<CameraImportDto>? _importModels;
    private bool _isValid;
    private bool _hasValidationErrors = false;
    private readonly Dictionary<string, Dictionary<string, bool>> _fieldValidationErrors = new();
    private readonly Dictionary<string, Dictionary<string, List<string>>> _fieldValidationErrorMessages = new();

    private static readonly List<CsvHeaderMapper.ModelField> _expectedFields = [
        new (nameof(CameraImportDto.Name), "Название камеры", false),
        new (nameof(CameraImportDto.Directory), "Название директории", false),
        new (nameof(CameraImportDto.TimeZone), "Часовой пояс", false),
        new (nameof(CameraImportDto.Coordinates), "Координаты", false),
        new (nameof(CameraImportDto.Quota), "Квота", false),
        new (nameof(CameraImportDto.ViewUri), "Ссылка на поток для видов", false),
        new (nameof(CameraImportDto.PublicUri), "Ссылка на публичный поток", false),
        new (nameof(CameraImportDto.ArchiveUri), "Ссылка на архивный поток", false),
        new (nameof(CameraImportDto.AutoStart), "Автозапуск при перезапуске системы", false),
    ];

    private static readonly ImportValidator _validator = new();

    protected override void OnInitialized()
    {
        base.OnInitialized();

        CompositeDisposable.Add(EventSystem.Subscribe<CameraListImportEto>(OnEventHandler));
    }

    private static ImportMap GetImportMap(IEnumerable<CsvHeaderMapper.HeaderMapping> mappings)
    {
        return new ImportMap(mappings);
    }

    private void OnImportCanceled()
    {
        _importModels = [];
        _activePanelIndex = 0;
        _hasValidationErrors = false;
        _fieldValidationErrors.Clear();
        _fieldValidationErrorMessages.Clear();
    }

    private async void OnImport(IEnumerable<CameraImportDto> importModels)
    {
        _importModels = importModels;
        if (_importModels is null)
        {
            _activePanelIndex = 0;
            return;
        }

        int index = 0;
        foreach (var importModel in _importModels)
        {
            if (string.IsNullOrEmpty(importModel.Name))
            {
                importModel.Name = $"Камера {index}";
                index++;
            }
        }
        Snackbar.Add($"Найдено {_importModels.Count()} сущностей");
        _activePanelIndex = 1;

        // Выполняем валидацию всех моделей после импорта
        await ValidateAllModelsAsync();
    }

    private async Task<bool> IsValid()
    {
        if (_importModels is null) return false;

        var tasks = _importModels.Select(model => _validator.ValidateAsync(model));
        var results = await Task.WhenAll(tasks);
        return !results.All(model => !model.IsValid);
    }

    private async Task<bool> CheckValid()
    {
        bool isValid = await IsValid();
        if (!isValid)
        {
            Snackbar.Add("Некоторые из моделей имеют ошибки, пожалуйста, исправьте их перед импортом");
        }
        return isValid;
    }

    /// <summary>
    /// Проверяет наличие ошибок валидации во всех моделях камер
    /// </summary>
    private async Task<bool> HasValidationErrorsAsync()
    {
        if (_importModels is null || !_importModels.Any())
            return false;

        var validationTasks = _importModels.Select(model => _validator.ValidateAsync(model));
        var validationResults = await Task.WhenAll(validationTasks);

        return validationResults.Any(result => !result.IsValid);
    }

    /// <summary>
    /// Валидирует все модели камер и обновляет состояние валидации
    /// </summary>
    private async Task ValidateAllModelsAsync()
    {
        _hasValidationErrors = await HasValidationErrorsAsync();
        await UpdateFieldValidationStatesAsync();
        StateHasChanged();
    }

    /// <summary>
    /// Обновляет состояние валидации для всех полей всех моделей
    /// </summary>
    private async Task UpdateFieldValidationStatesAsync()
    {
        if (_importModels is null) return;

        _fieldValidationErrors.Clear();
        _fieldValidationErrorMessages.Clear();

        var validationTasks = _importModels.Select(async (model, index) =>
        {
            var modelKey = GetModelKey(model, index);
            var validationResult = await _validator.ValidateAsync(model);

            var fieldErrors = new Dictionary<string, bool>();
            var fieldErrorMessages = new Dictionary<string, List<string>>();

            // Проверяем каждое поле
            var fieldNames = new[]
            {
                nameof(CameraImportDto.Name),
                nameof(CameraImportDto.Directory),
                nameof(CameraImportDto.Quota),
                nameof(CameraImportDto.ArchiveUri),
                nameof(CameraImportDto.ViewUri),
                nameof(CameraImportDto.PublicUri),
                nameof(CameraImportDto.Coordinates)
            };

            foreach (var fieldName in fieldNames)
            {
                var errors = validationResult.Errors.Where(e => e.PropertyName == fieldName).ToList();
                fieldErrors[fieldName] = errors.Any();
                fieldErrorMessages[fieldName] = errors.Select(e => e.ErrorMessage).ToList();
            }

            return new { ModelKey = modelKey, FieldErrors = fieldErrors, FieldErrorMessages = fieldErrorMessages };
        });

        var results = await Task.WhenAll(validationTasks);

        foreach (var result in results)
        {
            _fieldValidationErrors[result.ModelKey] = result.FieldErrors;
            _fieldValidationErrorMessages[result.ModelKey] = result.FieldErrorMessages;
        }
    }

    /// <summary>
    /// Получает уникальный ключ для модели (используется для отслеживания состояния валидации)
    /// </summary>
    private static string GetModelKey(CameraImportDto model, int index)
    {
        return $"{index}_{model.Name}_{model.GetHashCode()}";
    }

    /// <summary>
    /// Проверяет наличие ошибки валидации для конкретного поля конкретной модели
    /// </summary>
    private bool HasFieldError(CameraImportDto model, string propertyName)
    {
        if (_importModels is null) return false;

        var index = _importModels.ToList().IndexOf(model);
        if (index == -1) return false;

        var modelKey = GetModelKey(model, index);

        return _fieldValidationErrors.TryGetValue(modelKey, out var fieldErrors) &&
               fieldErrors.TryGetValue(propertyName, out var hasError) &&
               hasError;
    }

    /// <summary>
    /// Получает список сообщений об ошибках валидации для конкретного поля конкретной модели
    /// </summary>
    private List<string> GetFieldErrorMessages(CameraImportDto model, string propertyName)
    {
        if (_importModels is null) return new List<string>();

        var index = _importModels.ToList().IndexOf(model);
        if (index == -1) return new List<string>();

        var modelKey = GetModelKey(model, index);

        if (_fieldValidationErrorMessages.TryGetValue(modelKey, out var fieldErrorMessages) &&
            fieldErrorMessages.TryGetValue(propertyName, out var errorMessages))
        {
            return errorMessages;
        }

        return new List<string>();
    }

    /// <summary>
    /// Получает детальную информацию об ошибках валидации для отображения пользователю
    /// </summary>
    private async Task<List<string>> GetValidationErrorsAsync()
    {
        var errors = new List<string>();

        if (_importModels is null || !_importModels.Any())
            return errors;

        var validationTasks = _importModels.Select(async (model, index) =>
        {
            var result = await _validator.ValidateAsync(model);
            return new { Index = index + 1, Model = model, Result = result };
        });

        var validationResults = await Task.WhenAll(validationTasks);

        foreach (var validation in validationResults.Where(v => !v.Result.IsValid))
        {
            foreach (var error in validation.Result.Errors)
            {
                errors.Add($"Камера {validation.Index} ({validation.Model.Name}): {error.ErrorMessage}");
            }
        }

        return errors;
    }

    private void OnAutoStartChanged(bool isChecked)
    {
        if (_importModels is null) return;

        foreach (var model in _importModels)
        {
            model.AutoStart = isChecked;
        }
    }

    private void OnStartOnCreateChanged(bool isChecked)
    {
        if (_importModels is null) return;

        foreach (var model in _importModels)
        {
            model.StartOnCreate = isChecked;
        }
    }


    private async Task SubmitAsync()
    {
        if (_importModels is null)
        {
            return;
        }

        // Проверяем валидацию перед отправкой
        if (await HasValidationErrorsAsync())
        {
            Snackbar.Add("Некоторые из моделей имеют ошибки валидации. Пожалуйста, исправьте их перед импортом.", MudBlazor.Severity.Warning);
            return;
        }

        ImportCameraListUseCase.Response? response = null;
        try
        {
            response = await ScopeFactory.MediatorSend(new ImportCameraListUseCase.Command(_organizationId, [.. _importModels!.Select(m => new ImportCameraListUseCase.Command.Camera(m.Name, m.TimeZone, m.Coordinates, m.Directory, m.ArchiveUri, m.ViewUri, m.PublicUri, m.Quota, m.AutoStart, m.StartOnCreate))]));
        }
        catch (Exception exc)
        {
            response = null;
            Logger.LogError(exc, exc.Message);
            Snackbar.Add("Не удалось импортировать камеры из-за ошибки сообщения с сервером. Повторите попытку позже.", MudBlazor.Severity.Error);
        }

        switch (response?.Result)
        {
            case ImportCameraListUseCase.Result.Success:
                if (response.CamerasWithIncorrectQuota.Count == 0)
                {
                    Snackbar.Add("Камеры успешно импортированы", MudBlazor.Severity.Success);
                    Cancel();
                }
                else
                {
                    Snackbar.Add($"Камеры успешно импортированы, но не удалось импортировать {response.CamerasWithIncorrectQuota.Count} камер из-за ошибки в квоте", MudBlazor.Severity.Error);

                    // Словарь для быстрого поиска ошибок
                    var incorrectQuotaSet = new HashSet<string>(response.CamerasWithIncorrectQuota);
                    var quotaLimitReachedSet = new HashSet<string>(response.CamerasQuotaLimitReached);

                    // Очистить и обновить список с ошибками
                    var updatedImportModels = new List<CameraImportDto>();

                    foreach (var camera in _importModels)
                    {
                        if (incorrectQuotaSet.Contains(camera.Name))
                        {
                            camera.HasQuotaError = true;
                            camera.QuotaErrorMessage = "Квота не найдена";
                            updatedImportModels.Add(camera);
                        }
                        else if (quotaLimitReachedSet.Contains(camera.Name))
                        {
                            camera.HasQuotaError = true;
                            camera.QuotaErrorMessage = "Лимит квоты исчерпан";
                            updatedImportModels.Add(camera);
                        }
                    }

                    // Обновить список
                    _importModels = updatedImportModels;
                }
                break;
            case ImportCameraListUseCase.Result.ValidationError:
                Snackbar.Add("Ошибка валидации при импорте камер", MudBlazor.Severity.Error);
                break;
            case ImportCameraListUseCase.Result.OrganizationNotFound:
                Snackbar.Add("Организация не найдена", MudBlazor.Severity.Error);
                break;
            case ImportCameraListUseCase.Result.Unknown:
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(ImportDialog), nameof(ImportCameraListUseCase));
                Snackbar.Add($"Не удалось импортировать камеры из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
                break;
            default:
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(ImportDialog), nameof(ImportCameraListUseCase), response.Result);
                Snackbar.Add($"Не удалось импортировать камеры из-за ошибки: {response.Result}", MudBlazor.Severity.Error);
                break;
        }
    }

    private void Cancel()
    {
        _activePanelIndex = 0;
        _isVisible = false;
        _importModels = [];
        _hasValidationErrors = false;
        _fieldValidationErrors.Clear();
        _fieldValidationErrorMessages.Clear();
    }

    private void OnEventHandler(CameraListImportEto eto)
    {
        _isVisible = true;
        _activePanelIndex = 0;
        _organizationId = eto.OrganizationId;
        StateHasChanged();
    }

    private void VisibilityChanged(bool isVisible)
    {
        _activePanelIndex = 0;
        _isVisible = isVisible;
        if (!isVisible)
        {
            _importModels = [];
            _hasValidationErrors = false;
            _fieldValidationErrors.Clear();
            _fieldValidationErrorMessages.Clear();
        }
    }

    private void OnError(Exception exc)
    {
        Logger.LogError(exc, exc.Message);
        Snackbar.Add($"При импорте произошла ошибка: {exc.Message}", MudBlazor.Severity.Error);
    }
}
