﻿@using Teslametrics.Shared
@inherits InteractiveBaseComponent
<DrawerComponent Open="IsOpened"
				 OpenChanged="OnOpenChanged">
	<CascadingValue IsFixed="true"
					Value="this">
		<div class="mud-height-full px-4 py-4"
			 style="grid-area: drawer_body_content;">
			@switch (_mode)
			{
				case DrawerMode.View:
					@if (_organizationId is not null)
					{
						<AuthorizeView Policy="@(Fqdn<AppPermissions>.GetNames(AppPermissions.Main.AccessControl.Organizations.Read).Last())">
							<Authorized>
								<Teslametrics.App.Web.Features.Main.AccessControl.Organizations.Drawer.View.OrganizationViewComponent OrganizationId="_organizationId.Value" />
							</Authorized>
							<NotAuthorized>
								<NotAuthorizedComponent Class="mud-height-full" />
							</NotAuthorized>
						</AuthorizeView>
					}
					else
					{
						<FailedToRetrieveDataComponent />
					}
					break;

				case DrawerMode.Edit:
					@if (_organizationId is not null)
					{
						<AuthorizeView Policy="@(Fqdn<AppPermissions>.GetNames(AppPermissions.Main.AccessControl.Organizations.Update).Last())"
									   Context="innerContext">
							<Authorized>
								<Teslametrics.App.Web.Features.Main.AccessControl.Organizations.Drawer.Edit.OrganizationEditComponent OrganizationId="_organizationId.Value" />
							</Authorized>
							<NotAuthorized>
								<NotAuthorizedComponent Class="mud-height-full" />
							</NotAuthorized>
						</AuthorizeView>
					}
					else
					{
						<FailedToRetrieveDataComponent />
					}
					break;

				case DrawerMode.Create:
					<AuthorizeView Policy="@(Fqdn<AppPermissions>.GetNames(AppPermissions.Main.AccessControl.Organizations.Create).Last())"
								   Context="innerContext">
						<Authorized>
							<Teslametrics.App.Web.Features.Main.AccessControl.Organizations.Drawer.Create.OrganizationCreateComponent />
						</Authorized>
						<NotAuthorized>
							<NotAuthorizedComponent Class="mud-height-full" />
						</NotAuthorized>
					</AuthorizeView>
					break;
			}
		</div>
	</CascadingValue>
</DrawerComponent>