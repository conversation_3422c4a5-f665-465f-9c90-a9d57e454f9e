﻿<FormSectionComponent Title="@Title"
					  Subtitle="@Subtitle">
	<SelectEnumComponent TEnum="Teslametrics.Shared.Resolution"
						 Label="Разрешение"
						 SelectedValue="PresetConfig.Resolution"
						 ReadOnly="true" />

	<SelectEnumComponent TEnum="Teslametrics.Shared.VideoCodec"
						 Label="Кодек видео"
						 SelectedValue="PresetConfig.VideoCodec"
						 ReadOnly="true" />

	<SelectEnumComponent TEnum="Teslametrics.Shared.AudioCodec"
						 Label="Кодек аудио"
						 SelectedValue="PresetConfig.AudioCodec"
						 ReadOnly="true" />

	<SelectEnumComponent TEnum="Teslametrics.Shared.FrameRate"
						 Label="Частота кадров"
						 SelectedValue="PresetConfig.FrameRate"
						 ReadOnly="true" />

	<SelectEnumComponent TEnum="Teslametrics.Shared.SceneDynamic"
						 Label="Ожидаемая динамика сцены"
						 SelectedValue="PresetConfig.SceneDynamic"
						 ReadOnly="true"
						 HelperText="В зависимости от реальной динамики сцены может измениться битрейт видео" />

	<MudField Label="Расчётный битрейт, кбит/с"
			  Variant="Variant.Text"
			  HelperText="Данное значение расчётное и может отличаться">@PresetConfig.Bitrate</MudField>
</FormSectionComponent>

@code
{
	[Parameter]
	public string Title { get; set; } = string.Empty;

	[Parameter]
	public string Subtitle { get; set; } = string.Empty;

	[Parameter]
	[EditorRequired]
	public GetCameraPresetUseCase.Response.StreamConfig PresetConfig { get; set; } = null!;
}