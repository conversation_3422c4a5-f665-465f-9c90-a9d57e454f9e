using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using Microsoft.JSInterop;
using MudBlazor;

namespace Teslametrics.App.Web.Features.DetailView;

public partial class DetailViewLayout : LayoutComponentBase
{
    [Inject] public IBrowserViewportService BrowserViewportService { get; set; } = null!;
    [Inject] public IStringLocalizer<DetailViewLayout> Localizer { get; set; } = null!;
    [Inject] public MudTheme CustomTheme { get; set; } = null!;

    #region [Drawer]
    private CancellationTokenSource? _cts;
    private bool _isMouseOverDrawer = false;
    private bool _open { get; set; }
    private Anchor _anchor;
    #endregion

    #region Theme mode
    private bool _isDarkMode;
    private MudThemeProvider? _mudThemeProvider;
    #endregion

    #region [Query parameters]
    [SupplyParameterFromQuery(Name = "OrganizationId")]
    public Guid? OrganizationId { get; set; }

    [SupplyParameterFromQuery(Name = "ViewId")]
    public Guid? ViewId { get; set; }
    #endregion

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            #region Theme mode
            if (_mudThemeProvider is not null)
            {
                _isDarkMode = await _mudThemeProvider.GetSystemDarkModeAsync();
                await _mudThemeProvider.WatchSystemDarkModeAsync(OnSystemPreferenceChanged);
            }
            #endregion
            StateHasChanged();
        }
    }
    private void OpenDrawer(Anchor anchor)
    {
        _cts?.Cancel(); // Отменяем предыдущий таймер закрытия
        _open = true;
        _anchor = anchor;
    }

    // Когда мышь уходит из Drawer, проверяем — если нет в hover-зоне, закрываем
    private Task OnDrawerMouseLeaveAsync()
    {
        _isMouseOverDrawer = false;
        return CloseDrawerDelayed();
    }

    private async Task CloseDrawerDelayed()
    {
        _cts = new CancellationTokenSource();
        var token = _cts.Token;

        try
        {
            await Task.Delay(100, token); // Задержка 100 мс
            if (!token.IsCancellationRequested && !_isMouseOverDrawer)
            {
                _open = false;
            }
        }
        catch (TaskCanceledException)
        {
            // Таймер был отменен, значит мышь вернулась в зону
        }
    }

    #region Theme mode
    private async Task OnSystemPreferenceChanged(bool newValue)
    {
        _isDarkMode = newValue;
        await InvokeAsync(StateHasChanged);
    }
    #endregion
}
