@using Teslametrics.Shared
@attribute [StreamRendering]
@inherits InteractiveBaseComponent
<div class="list mud-height-full">
	<MudStack Row="true"
			  Class="pr-3">
		<MudTextField Value="SearcString"
					  ValueChanged="OnSearchChanged"
					  T="string"
					  Label="Поиск"
					  Immediate="true"
					  Adornment="Adornment.Start"
					  AdornmentIcon="@Icons.Material.Filled.Search"
					  AdornmentColor="Color.Secondary"
					  Class="pa-4" />
		<div class="d-flex align-center mud-height-full">
			<MudTooltip Text="@($"Время последнего обновления: {_lastRefreshTime.ToLocalTime()}")"
						Arrow="true"
						Placement="Placement.Left">
				<MudIconButton OnClick="RefreshAsync"
							   Icon="@Icons.Material.Outlined.Refresh" />
			</MudTooltip>
			<AuthorizeView Policy="@(Fqdn<AppPermissions>.GetNames(AppPermissions.Main.AccessControl.Organizations.Create).Last())"
						   Context="innerContext">
				<MudIconButton OnClick="Create"
							   Icon="@Icons.Material.Outlined.Add"
							   Variant="Variant.Outlined"
							   Color="Color.Primary"
							   Size="Size.Medium" />
			</AuthorizeView>
		</div>
	</MudStack>
	<LoadingComponent IsLoading="IsLoading" />
	<NotFoundComponent IsFound="@(!IsLoading && _organizations.Any())"
					   LastRefreshTime="_lastRefreshTime"
					   RefreshAsync="RefreshAsync" />
	@if (!IsLoading && _organizations.Any())
	{
		<MudDivider />
		<MudList T="Guid?"
				 SelectionMode="SelectionMode.SingleSelection"
				 SelectedValue="Selected"
				 SelectedValueChanged="SelectAsync"
				 Class="mud-height-full overflow-x-auto">
			@foreach (var organization in _organizations)
			{
				<MudListItem T="Guid?"
							 Value="organization.Id"
							 @key="organization">
					<ChildContent>
						<MudStack Row="true">
							<MudIcon Icon="@Icons.Material.Outlined.Business"
									 Color="Color.Primary"
									 Size="Size.Medium" />
							<MudStack Spacing="0"
									  Class="mud-width-full">
								<MudText Typo="Typo.body1">Организация: <b>@organization.Name</b></MudText>
								<MudText Typo="Typo.subtitle2"
										 Class="mud-list-item-secondary-text">
									Владелец: <b>@organization.Owner</b>
								</MudText>
							</MudStack>
							<MudMenu Icon="@Icons.Material.Filled.MoreVert"
									 AriaLabel="Меню">
								<MudMenuItem OnClick="() => Show(organization.Id)"
											 Icon="@Icons.Material.Outlined.PanoramaFishEye">Просмотр</MudMenuItem>
								<AuthorizeView Policy="@(Fqdn<AppPermissions>.GetNames(AppPermissions.Main.AccessControl.Organizations.Update).Last())"
											   Resource="new PolicyRequirementResource(organization.Id, organization.Id)"
											   Context="innerContext">
									<MudMenuItem OnClick="() => Edit(organization.Id)"
												 Icon="@Icons.Material.Outlined.Edit">Редактировать</MudMenuItem>
								</AuthorizeView>
								<AuthorizeView Policy="@(Fqdn<AppPermissions>.GetNames(AppPermissions.Main.AccessControl.Organizations.Delete).Last())"
											   Resource="new PolicyRequirementResource(organization.Id, organization.Id)"
											   Context="innerContext">
									<MudDivider />
									<MudMenuItem OnClick="() => Delete(organization.Id)"
												 Icon="@Icons.Material.Outlined.Delete"
												 IconColor="Color.Warning">Удалить</MudMenuItem>
								</AuthorizeView>
							</MudMenu>
						</MudStack>
					</ChildContent>
				</MudListItem>
			}
		</MudList>
		@if (_totalPages > 1)
		{
			<div class="d-flex justify-center py-4">
				<MudPagination Selected="CurrentPage"
							   SelectedChanged="OnPageChanged"
							   Rectangular="true"
							   Variant="Variant.Text"
							   Count="@_totalPages" />
			</div>
		}
	}
</div>