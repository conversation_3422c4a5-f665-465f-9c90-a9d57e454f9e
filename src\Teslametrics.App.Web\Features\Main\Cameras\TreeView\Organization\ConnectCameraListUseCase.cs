using System.Data;
using System.Text.Json;
using Dapper;
using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Data.Sql;
using Teslametrics.App.Web.Orleans.Camera;
using Teslametrics.App.Web.Services.Persistence;

namespace Teslametrics.App.Web.Features.Main.Cameras.TreeView.Organization;

public class ConnectCameraListUseCase
{
    public record Command(Guid OrganizationId) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(Result result)
        {
            Result = result;
        }
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError
    }

    public class Validator : AbstractValidator<Command>
    {
        public Validator()
        {
            RuleFor(c => c.OrganizationId).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Command, Response>
    {
        private readonly IValidator<Command> _validator;
        private readonly IDbConnection _dbConnection;
        private readonly IClusterClient _clusterClient;

        public Handler(IValidator<Command> validator,
                       IDbConnection dbConnection,
                       IClusterClient clusterClient)
        {
            _validator = validator;
            _dbConnection = dbConnection;
            _clusterClient = clusterClient;
        }

        public async Task<Response> Handle(Command request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            var template = SqlQueryBuilder.Create()
                .Select(Db.Cameras.Props.Id)
                .Select(Db.Cameras.Props.ArchiveUri)
                .Select(Db.Cameras.Props.ViewUri)
                .Select(Db.Cameras.Props.PublicUri)
                .Select(Db.Cameras.Props.OnvifEnabled)
                .Select(Db.Cameras.Props.OnvifSettings)
                .Where(Db.Cameras.Props.OrganizationId, ":OrganizationId", SqlOperator.Equals, new { request.OrganizationId })
                .Where(Db.Cameras.Props.IsBlocked, ":IsBlocked", SqlOperator.Equals, new { IsBlocked = false })
                .Build(QueryType.Standard, Db.Cameras.Table, RowSelection.AllRows);

            var cameras = await _dbConnection.QueryAsync<CameraModel>(template.RawSql, template.Parameters);

            var mediaServerGrain = _clusterClient.GetGrain<IMediaServerGrain>(Guid.Empty);

            foreach (var camera in cameras)
            {
                await mediaServerGrain.ConnectRtspAsync(new IMediaServerGrain.CameraConnectRtspRequest(camera.Id, camera.ArchiveUri, camera.ViewUri, camera.PublicUri));
                if (camera.OnvifEnabled)
                {
                    var onvifSettings = JsonSerializer.Deserialize<OnvifSettings>(camera.OnvifSettings!);
                    await mediaServerGrain.ConnectOnvifAsync(new IMediaServerGrain.CameraConnectOnvifRequest(camera.Id, onvifSettings!.Host, onvifSettings.Port, onvifSettings.Username, onvifSettings.Password));
                }
            }

            return new Response(Result.Success);
        }
    }

    public record CameraModel(Guid Id, string ArchiveUri, string ViewUri, string PublicUri, bool OnvifEnabled, string? OnvifSettings);
    public record OnvifSettings(string Host, int Port, string Username, string Password);
}