﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Services.Persistence;

namespace Teslametrics.App.Web.Features.DatabaseMigrations;

public static class ApplyDatabaseMigrationsUseCase
{
    public record Command : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public Result Result { get; private set; }

        public bool IsSuccess => Result == Result.Success;

        public Response(Result result)
        {
            Result = result;
        }
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        Failure
    }

    public class Handler : IRequestHandler<Command, Response>
    {
        private readonly CommandAppDbContext _appDbContext;

        public Handler(CommandAppDbContext appDbContext)
        {
            _appDbContext = appDbContext;
        }

        public async Task<Response> Handle(Command request, CancellationToken cancellationToken)
        {
            try
            {
                await _appDbContext.Database.MigrateAsync(cancellationToken: cancellationToken);

                return new Response(Result.Success);
            }
            catch (Exception)
            {
                return new Response(Result.Failure);
            }
        }
    }
}