namespace Teslametrics.App.Web.Domain.AccessControl.Organizations;

public class RolePermissionEntity
{
    public Guid Id { get; private set; }

    public Guid OrganizationId { get; private set; }

    public Guid RoleId { get; private set; }

    public ResourcePermission ResourcePermission { get; set; }

    public static RolePermissionEntity Create(Guid id, Guid organizationId, Guid roleId, ResourcePermission resourcePermission)
    {
        return new RolePermissionEntity(id, organizationId, roleId, resourcePermission);
    }

    private RolePermissionEntity()
    {
    }

    private RolePermissionEntity(Guid id, Guid organizationId, Guid roleId, ResourcePermission permission)
    {
        Id = id;
        OrganizationId = organizationId;
        RoleId = roleId;
        ResourcePermission = permission;
    }
}