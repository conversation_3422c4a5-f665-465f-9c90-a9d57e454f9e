@using Teslametrics.App.Web.Features.Main.CameraViews.Drawer.Create.GridItem;
@using Teslametrics.Shared
@inherits InteractiveBaseComponent
<DrawerHeader>
    <MudText Typo="Typo.h3">Создание Вида</MudText>
</DrawerHeader>
<MudForm Model="_model"
         Validation="_validator.ValidateValue"
         @bind-IsValid="_isValid"
         Class="px-4 py-4 flex-1"
         OverrideFieldValidation="true"
         UserAttributes="@(new Dictionary<string, object>() {{"autocomplete", "off"}, {"aria-autocomplete", "none"}, {"role", "presentation"} })"
         Spacing="8">
    <FormSectionComponent title="Описания вида">
        <MudTextField @bind-Value="_model.Name"
                      For="@(() => _model.Name)"
                      Clearable="true"
                      InputType="InputType.Text"
                      Immediate="true"
                      Label="Наименование"
                      HelperText="Необходимо для идентификации камеры человеком"
                      RequiredError="Данное поле обязательно"
                      Required="true" />
    </FormSectionComponent>

    <FormSectionComponent title="Настройки отображения">
        <div>
            <MudButtonGroup Color="Color.Default"
                            Variant="Variant.Outlined">
                <MudButton StartIcon="@TeslaIcons.Grid.Custom"
                           IconColor="@(_model.Grid == GridType.GridCustom ? Color.Primary : Color.Default)"
                           OnClick="() => SetGridType(GridType.GridCustom)">Настраиваемая сетка</MudButton>
                <MudIconButton Icon="@TeslaIcons.Grid.Grid1Plus5"
                               Size="Size.Large"
                               Color="@(_model.Grid == GridType.Grid1Plus5 ? Color.Primary : Color.Default)"
                               OnClick="() => SetGridType(GridType.Grid1Plus5)" />

                <MudIconButton Icon="@TeslaIcons.Grid.Grid1Plus7"
                               Size="Size.Large"
                               Color="@(_model.Grid == GridType.Grid1Plus7 ? Color.Primary : Color.Default)"
                               OnClick="() => SetGridType(GridType.Grid1Plus7)" />

                <MudIconButton Icon="@TeslaIcons.Grid.Grid1Plus12"
                               Size="Size.Large"
                               Color="@(_model.Grid == GridType.Grid1Plus12 ? Color.Primary : Color.Default)"
                               OnClick="() => SetGridType(GridType.Grid1Plus12)" />

                <MudIconButton Icon="@TeslaIcons.Grid.Grid2Plus8"
                               Size="Size.Large"
                               Color="@(_model.Grid == GridType.Grid2Plus8 ? Color.Primary : Color.Default)"
                               OnClick="() => SetGridType(GridType.Grid2Plus8)" />

                <MudIconButton Icon="@TeslaIcons.Grid.Grid3Plus4"
                               Size="Size.Large"
                               Color="@(_model.Grid == GridType.Grid3Plus4 ? Color.Primary : Color.Default)"
                               OnClick="() => SetGridType(GridType.Grid3Plus4)" />
            </MudButtonGroup>
        </div>

        @if (_model.Grid == GridType.GridCustom)
        {
            <MudGrid>
                <MudItem xs="6">
                    <MudNumericField T="short"
                                     @bind-Value="_model.Rows"
                                     For="@(() => _model.Rows)"
                                     Immediate="true"
                                     Min="@((short) (_model.Columns > 1 ? 1 : 2))"
                                     Max="8"
                                     Label="Количество строк"
                                     RequiredError="Данное поле обязательно"
                                     Required="true" />
                </MudItem>

                <MudItem xs="6">
                    <MudNumericField T="short"
                                     @bind-Value="_model.Columns"
                                     For="@(() => _model.Columns)"
                                     Immediate="true"
                                     Min="@((short) (_model.Rows > 1 ? 1 : 2))"
                                     Max="8"
                                     Label="Количество колонок"
                                     RequiredError="Данное поле обязательно"
                                     Required="true" />
                </MudItem>
            </MudGrid>
        }
    </FormSectionComponent>

    <div>
        <Sortable Id="filtering"
                  Class="@($"grid {_model.Grid.ToString()}")"
                  Filter=".filtered"
                  Style="@($"--cols: {_model.Columns}; --rows: {_model.Rows};")"
                  Items="_model.Cells"
                  OnUpdate="@SortList"
                  Context="item">
            <SortableItemTemplate>
                <GridItemComponent OrganizationId="@OrganizationId"
                                   @bind-Camera="@item.Camera"
                                   @key="item" />
            </SortableItemTemplate>
        </Sortable>
    </div>
</MudForm>
<DrawerActions>
    <MudSpacer />
    <MudButton OnClick="CancelAsync"
               Variant="Variant.Outlined"
               StartIcon="@Icons.Material.Outlined.Close">Закрыть</MudButton>
    <MudButton OnClick="SubmitAsync"
               Disabled="@(!_isValid)"
               Color="Color.Secondary"
               Variant="Variant.Outlined">Сохранить</MudButton>
</DrawerActions>