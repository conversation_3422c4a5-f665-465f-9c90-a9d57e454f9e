using System.Data;
using System.Text.Json;
using Dapper;
using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Data.Sql;
using Teslametrics.App.Web.Services.Persistence;
using Teslametrics.App.Web.Shared;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Quota.Drawer.Create;

public static class GetCameraPresetListUseCase
{
    public record Query(Guid OrganizationId, int Offset, int Limit, string Filter) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public List<Item> Items { get; init; }

        public int TotalCount { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(List<Item> items, int totalCount)
        {
            Items = items;
            TotalCount = totalCount;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Result = result;

            Items = [];
            TotalCount = 0;
        }

        public record Item(Guid Id, string Name, StreamConfig? ArchiveStreamConfig, StreamConfig? ViewStreamConfig, StreamConfig? PublicStreamConfig);

        public record StreamConfig(Resolution Resolution, VideoCodec VideoCodec, FrameRate FrameRate, SceneDynamic SceneDynamic, AudioCodec AudioCodec);
    }
    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError
    }

    public class Validator : AbstractValidator<Query>
    {
        public Validator()
        {
            RuleFor(q => q.OrganizationId).NotEmpty();
            RuleFor(q => q.Offset).GreaterThanOrEqualTo(0);
            RuleFor(q => q.Limit).GreaterThan(0);
            RuleFor(q => q.Filter).MaximumLength(60);
        }
    }

    public class Handler : IRequestHandler<Query, Response>
    {
        private readonly IValidator<Query> _validator;
        private readonly IDbConnection _dbConnection;

        public Handler(IValidator<Query> validator,
                       IDbConnection dbConnection)
        {
            _validator = validator;
            _dbConnection = dbConnection;
        }

        public async Task<Response> Handle(Query request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            var builder = SqlQueryBuilder.Create()
                .WhereIf(!string.IsNullOrEmpty(request.Filter), Db.Presets.Props.Name, "CONCAT('%', :Filter, '%')", SqlOperator.Like, new { request.Filter });

            var countTemplate = builder.Build(QueryType.Standard, Db.Presets.Table, RowSelection.UniqueRows, ["COUNT(*)"]);
            var totalCount = await _dbConnection.ExecuteScalarAsync<int>(countTemplate.RawSql,
                new { request.Filter });

            var selectTemplate = builder.Build(QueryType.Paginated,
                                               Db.Presets.Table,
                                               RowSelection.UniqueRows,
                                               [
                                                   Db.Presets.Props.Id,
                                                   Db.Presets.Props.Name,
                                                   Db.Presets.Props.ArchiveStreamConfig,
                                                   Db.Presets.Props.ViewStreamConfig,
                                                   Db.Presets.Props.PublicStreamConfig
                                               ],
                                               new { request.Filter, request.Limit, request.Offset });
            var quotas = await _dbConnection.QueryAsync<CameraPresetModel>(selectTemplate.RawSql, selectTemplate.Parameters);

            return new Response(quotas.Select(u => new Response.Item(u.Id,
                                                                     u.Name,
                                                                     ToStreamConfigResponse(u.ArchiveStreamConfig),
                                                                     ToStreamConfigResponse(u.ViewStreamConfig),
                                                                     ToStreamConfigResponse(u.PublicStreamConfig))).ToList(), totalCount);
        }

        private static Response.StreamConfig? ToStreamConfigResponse(string streamConfig)
        {
            if (string.IsNullOrEmpty(streamConfig))
            {
                return null;
            }

            var streamConfigModel = JsonSerializer.Deserialize<Response.StreamConfig>(streamConfig);
            return new Response.StreamConfig(streamConfigModel!.Resolution, streamConfigModel.VideoCodec, streamConfigModel.FrameRate, streamConfigModel.SceneDynamic, streamConfigModel.AudioCodec);
        }
    }

    public record CameraPresetModel(Guid Id, string Name, string ArchiveStreamConfig, string ViewStreamConfig, string PublicStreamConfig);
}