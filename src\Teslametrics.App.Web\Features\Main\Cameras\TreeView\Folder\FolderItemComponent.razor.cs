using FluentValidation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components;
using MudBlazor;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Events.Cameras;
using Teslametrics.App.Web.Extensions;
using Teslametrics.App.Web.Services.Authorization;
using Teslametrics.App.Web.Shared;

namespace Teslametrics.App.Web.Features.Main.Cameras.TreeView.Folder;

public partial class FolderItemComponent
{
	private class UpdateFolderModel(Guid id, string name)
	{
		public Guid Id { get; init; } = id;
		public string Name { get; set; } = name;
	}
	private class Validator : BaseFluentValidator<UpdateFolderModel>
	{
		public Validator()
		{
			RuleFor(model => model.Name)
				.NotEmpty()
				.WithMessage("Название является обязательным")
				.Length(3, 120)
				.WithMessage("наименование должно быть длиной от 3 до 120 символов");
		}
	}

	private FolderViewComponent.TreeItemPresenter _presenter => (FolderViewComponent.TreeItemPresenter)Item;
	private MudTextField<string>? _inputRef;
	private bool _isValid => _model is not null && _validator.Validate(_model).IsValid;
	private bool _isEditing = false;
	private static readonly Validator _validator = new();
	private UpdateFolderModel? _model;

	#region [Parameters]
	[Parameter]
	[EditorRequired]
	public TreeItemData<Guid> Item { get; set; } = null!;

	[Parameter]
	public int CameraCount { get; set; }

	[Parameter]
	[EditorRequired]
	public EventCallback<TreeItemData<Guid>> OnSelect { get; set; }

	[Parameter]
	public Guid OrganizationId { get; set; }
	#endregion [Parameters]

	private async Task SelectHandler()
	{
		if (OnSelect.HasDelegate)
			await OnSelect.InvokeAsync(Item);
	}

	private void Create()
	{
		EventSystem.Publish(new CameraCreateEto(OrganizationId, Item.Value));
	}

	private void Delete() => EventSystem.Publish(new CameraGroupDeleteEto(OrganizationId, Item.Value));

	private void Cancel()
	{
		_isEditing = false;
	}

	private async Task SaveAsync()
	{
		if (!_isValid) return;

		if (_model!.Name == Item.Text)
		{
			_isEditing = false;
			return;
		}

		try
		{
			await SetLoadingAsync(true);
			var userId = await GetCurrentUserIdAsync() ?? throw new UnauthorizedAccessException();
			var response = await ScopeFactory.MediatorSend(new UpdateFolderUseCase.Command(Item.Value, _model.Name));
			switch (response.Result)
			{
				case UpdateFolderUseCase.Result.Success:
					Snackbar.Add("Имя директории изменено", MudBlazor.Severity.Success);
					_isEditing = false;
					break;
				case UpdateFolderUseCase.Result.FolderNameAlreadyExists:
					Snackbar.Add("Директория с данным именем уже существует.", MudBlazor.Severity.Error);
					if (_inputRef is not null)
					{
						var parameters = new Dictionary<string, object?>
						{
							{ "ErrorText", "Директория с данным именем уже существует." },
							{ "Error", true }
						};
						await _inputRef.SetParametersAsync(ParameterView.FromDictionary(parameters));
					}
					break;
				case UpdateFolderUseCase.Result.FolderNotFound:
					Snackbar.Add("Директория, которой вы хотите изменить наименование не найдна.", MudBlazor.Severity.Error);
					if (_inputRef is not null)
					{
						var parameters = new Dictionary<string, object?>
						{
							{ "ErrorText", "Директория с данным именем уже существует." },
							{ "Error", true }
						};
						await _inputRef.SetParametersAsync(ParameterView.FromDictionary(parameters));
					}
					break;
				case UpdateFolderUseCase.Result.ValidationError:
					Snackbar.Add("Не удалось изменить имя директории. Проверьте правильность заполнения.", MudBlazor.Severity.Error);
					if (_inputRef is not null)
					{
						var parameters = new Dictionary<string, object?>
						{
							{ "ErrorText", "Директория с данным именем уже существует." },
							{ "Error", true }
						};
						await _inputRef.SetParametersAsync(ParameterView.FromDictionary(parameters));
					}
					break;
				case UpdateFolderUseCase.Result.Unknown:
				default:
					throw new Exception($"Unexpected result in {nameof(UpdateFolderUseCase)}: {response.Result}");
			}
		}
		catch (Exception exc)
		{
			Snackbar.Add("Не удалось обновить наименование директории. Повторите попытку", MudBlazor.Severity.Error);
			Logger.LogError(exc, exc.Message);
		}
		finally
		{
			await SetLoadingAsync(false);
		}
	}

	private async Task EditAsync()
	{
		var state = await AuthenticationStateProvider.GetAuthenticationStateAsync();
		var canEdit = await AuthorizationService.AuthorizeAsync(state.User, resource: new PolicyRequirementResource(OrganizationId, Item.Value), policyName: AppPermissions.Main.Folders.Update.GetEnumPermissionString());
		if (canEdit.Succeeded)
		{
			_model = new(Item.Value, Item.Text!);
			_isEditing = true;
		}
	}

	// Кнопка ауторизована с помощью AuthorizeView
	private void Edit()
	{
		_model = new(Item.Value, Item.Text!);
		_isEditing = true;
	}

	private async Task DisconnectAsync()
	{
		if (IsLoading) return;

		DisconnectCameraListUseCase.Response? response;
		try
		{
			await SetLoadingAsync(true);
			response = await ScopeFactory.MediatorSend(new DisconnectCameraListUseCase.Command(Item.Value));
		}
		catch (Exception ex)
		{
			response = null;
			Snackbar.Add($"Не удалось отключить камеры из-за ошибки сообщения с сервером. Повторите попытку позже.", MudBlazor.Severity.Error);
			Logger.LogError(ex, ex.Message);
		}

		await SetLoadingAsync(false);

		if (response is null) return;
		switch (response.Result)
		{
			case DisconnectCameraListUseCase.Result.Success:
				Snackbar.Add("Отправлен запрос на отключение камер", MudBlazor.Severity.Success);
				break;
			case DisconnectCameraListUseCase.Result.ValidationError:
				Snackbar.Add("Не удалось отключить камеры из-за ошибки валидации", MudBlazor.Severity.Error);
				break;
			case DisconnectCameraListUseCase.Result.Unknown:
				Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(FolderItemComponent), nameof(DisconnectCameraListUseCase));
				Snackbar.Add($"Не удалось отключить камеры из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
				break;
			default:
				Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(FolderItemComponent), nameof(DisconnectCameraListUseCase), response.Result);
				Snackbar.Add($"Не удалось отключить камеры из-за ошибки: {response.Result}", MudBlazor.Severity.Error);
				break;
		}
	}

	private async Task ConnectAsync()
	{
		if (IsLoading) return;

		ConnectCameraListUseCase.Response? response;
		try
		{
			await SetLoadingAsync(true);
			response = await ScopeFactory.MediatorSend(new ConnectCameraListUseCase.Command(Item.Value));
		}
		catch (Exception ex)
		{
			response = null;
			Snackbar.Add($"Не удалось подключить камеры из-за ошибки сообщения с сервером. Повторите попытку позже.", MudBlazor.Severity.Error);
			Logger.LogError(ex, ex.Message);
		}

		await SetLoadingAsync(false);

		if (response is null) return;
		switch (response.Result)
		{
			case ConnectCameraListUseCase.Result.Success:
				Snackbar.Add("Отправлен запрос на подключение камер", MudBlazor.Severity.Success);
				break;
			case ConnectCameraListUseCase.Result.ValidationError:
				Snackbar.Add("Не удалось подключить камеры из-за ошибки валидации", MudBlazor.Severity.Error);
				break;
			case ConnectCameraListUseCase.Result.Unknown:
				Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(FolderItemComponent), nameof(ConnectCameraListUseCase));
				Snackbar.Add($"Не удалось подключить камеры из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
				break;
			default:
				Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(FolderItemComponent), nameof(ConnectCameraListUseCase), response.Result);
				Snackbar.Add($"Не удалось подключить камеры из-за ошибки: {response.Result}", MudBlazor.Severity.Error);
				break;
		}
	}
}
