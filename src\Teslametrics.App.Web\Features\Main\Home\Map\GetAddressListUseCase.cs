using System.Data;
using System.Text.Json;
using Dapper;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Data.Sql;
using Teslametrics.App.Web.Services.Persistence;
using Teslametrics.App.Web.Shared;

namespace Teslametrics.App.Web.Features.Main.Home.Map;

public static class GetAddressListUseCase
{
    public record Query(List<Guid> buildingIds) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public List<Building> Buildings { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(List<Building> buildings)
        {
            Buildings = buildings;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Result = result;
            Buildings = [];
        }

        public record Building(Guid Id, string Address, Coordinates? Coordinates);
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        PlanNotFound
    }

    public class Handler : IRequestHandler<Query, Response>
    {
        private readonly IDbConnection _dbConnection;

        public Handler(IDbConnection dbConnection)
        {
            _dbConnection = dbConnection;
        }

        public async Task<Response> Handle(Query request, CancellationToken cancellationToken)
        {

            if (!await CheckTableExistsAsync())
            {
                return new Response(Result.PlanNotFound);
            }

            var template = SqlQueryBuilder.Create()
                .Select(Db.Plans.Props.Page)
                .Build(QueryType.Standard, Db.Plans.Table, RowSelection.AllRows);

            var pageJson = await _dbConnection.ExecuteScalarAsync<string?>(template.RawSql);

            if (string.IsNullOrEmpty(pageJson))
            {
                return new Response(Result.PlanNotFound);
            }

            var page = JsonSerializer.Deserialize<PageModel>(pageJson)!;

            if (request.buildingIds.Count == 0)
            {
                var buildings = page.Cities
                    .SelectMany(c => c.Buildings)
                    .Select(b => new Response.Building(b.Id, b.Address, b.Coordinates))
                    .ToList();

                return new Response(buildings);
            }
            else
            {
                var buildings = page.Cities
                    .SelectMany(c => c.Buildings)
                    .Where(b => request.buildingIds.Contains(b.Id))
                    .Select(b => new Response.Building(b.Id, b.Address, b.Coordinates))
                    .ToList();

                return new Response(buildings);
            }
        }

        private async Task<bool> CheckTableExistsAsync()
        {
            // Check if table exists
            var tableExists = await _dbConnection.ExecuteScalarAsync<int>(
                "SELECT COUNT(*) FROM information_schema.tables " +
                "WHERE table_schema = 'public' AND table_name = @TableName",
                new { TableName = Db.Plans.Table });

            return tableExists > 0;
        }
    }

    public record IncidentModel(Guid BuildingId);
}
