@if (IsLoading)
{
	<FormSectionComponent Title="Описание камеры"
						  Subtitle="Настройки, которые влияют только на восприятие человеком">
		<MudSkeleton Width="60%"
					 Height="52px" />
	</FormSectionComponent>

	<FormSectionComponent Title="Параметры камеры"
						  Subtitle="Данные настройки важны для работы в системе">

		<MudSkeleton Width="60%"
					 Height="52px" />
		<MudSkeleton Width="60%"
					 Height="52px" />

		<MudSkeleton Width="60%"
					 Height="52px" />
	</FormSectionComponent>


	<FormSectionComponent Title="Статус камеры">
		<MudStack Spacing="8"
				  Row="true">
			<MudSkeleton Width="40%"
						 Height="52px" />
			<MudSpacer />

			<MudSkeleton Width="20%"
						 Height="52px" />
		</MudStack>
	</FormSectionComponent>

	<FormSectionComponent Title="Публичный доступ к камере"
						  Subtitle="Адреса, с помощью которых можно получить доступ к камере">
		@foreach (var i in Enumerable.Range(1, _rnd.Next(1, 6)))
		{
			<MudSkeleton Height="120px"
						 @key="i" />
		}
	</FormSectionComponent>
}