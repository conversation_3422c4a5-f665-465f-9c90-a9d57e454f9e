using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.WebUtilities;
using MudBlazor;
using MudBlazor.Services;
using Teslametrics.App.Web.Components;
using Teslametrics.App.Web.Services.UserDevice;

namespace Teslametrics.App.Web.Features.Main.CameraViews;

public partial class Page : IAsyncDisposable, IBrowserViewportObserver
{
    private bool _drawerOpen = false;
    private ResizerOrientation _orientation = ResizerOrientation.Horizontal;

    ResizeOptions IBrowserViewportObserver.ResizeOptions { get; } = new()
    {
        ReportRate = 250,
        NotifyOnBreakpointOnly = true
    };

    [Inject]
    private IBrowserViewportService BrowserViewportService { get; set; } = null!;

    [Inject]
    private NavigationManager NavigationManager { get; set; } = default!;

    [Inject]
    private IUserDeviceService UserDeviceService { get; set; } = null!;

    [SupplyParameterFromQuery(Name = "OrganizationId")]
    public Guid? OrganizationId { get; set; }

    [SupplyParameterFromQuery(Name = "ViewId")]
    public Guid? ViewId { get; set; }

    public readonly IEnumerable<Breakpoint> Markers = [Breakpoint.Xs, Breakpoint.Sm, Breakpoint.Md, Breakpoint.SmAndDown, Breakpoint.MdAndDown];

    public Guid Id { get; } = Guid.NewGuid();

    public async ValueTask DisposeAsync() => await BrowserViewportService.UnsubscribeAsync(this);

    public Task NotifyBrowserViewportChangeAsync(BrowserViewportEventArgs browserViewportEventArgs)
    {
        var orientation = Markers.Contains(browserViewportEventArgs.Breakpoint) ? ResizerOrientation.Vertical : ResizerOrientation.Horizontal;
        if (_orientation != orientation)
        {
            _orientation = orientation;
            StateHasChanged();
        }

        return Task.CompletedTask;
    }

    protected override void OnInitialized()
    {
        if (UserDeviceService.IsMobile)
        {
            _drawerOpen = true;
            _orientation = ResizerOrientation.Vertical;
        }

        base.OnInitialized();
    }


    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await BrowserViewportService.SubscribeAsync(this, fireImmediately: true);
        }

        await base.OnAfterRenderAsync(firstRender);
    }

    private void OnTreeParametersChanged((Guid? OrganizationId, Guid? ViewId) parameters)
    {
        _drawerOpen = false;

        OrganizationId = parameters.OrganizationId;
        ViewId = parameters.ViewId;

        var uri = NavigationManager.Uri;
        var uriWithoutQuery = uri.Split('?')[0];
        var queryParameters = new Dictionary<string, string?>();

        if (parameters.OrganizationId.HasValue)
            queryParameters.Add("OrganizationId", parameters.OrganizationId.Value.ToString());
        if (parameters.ViewId.HasValue)
            queryParameters.Add("ViewId", parameters.ViewId.Value.ToString());

        var newUri = QueryHelpers.AddQueryString(uriWithoutQuery, queryParameters);
        NavigationManager.NavigateTo(newUri, replace: true);
    }

    private void OnSwipeEnd(SwipeEventArgs e)
    {
        if (e.SwipeDirection == SwipeDirection.LeftToRight && !_drawerOpen)
        {
            _drawerOpen = true;
            StateHasChanged();
        }
        else if (e.SwipeDirection == SwipeDirection.RightToLeft && _drawerOpen)
        {
            _drawerOpen = false;
            StateHasChanged();
        }
    }
}
