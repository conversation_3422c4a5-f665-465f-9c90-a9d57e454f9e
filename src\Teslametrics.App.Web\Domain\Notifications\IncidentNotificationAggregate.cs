using Teslametrics.App.Web.Abstractions;

namespace Teslametrics.App.Web.Domain.Notifications;

/// <summary>
/// Агрегат для хранения уведомлений пользователей о инцидентах
/// </summary>
public class IncidentNotificationAggregate : IEntity
{
    /// <summary>
    /// Идентификатор уведомления
    /// </summary>
    public Guid Id { get; private set; }

    /// <summary>
    /// Идентификатор инцидента, к которому относится уведомление
    /// </summary>
    public Guid IncidentId { get; private set; }

    /// <summary>
    /// Идентификатор пользователя, которому адресовано уведомление
    /// </summary>
    public Guid UserId { get; private set; }

    private IncidentNotificationAggregate()
    {
    }

    /// <summary>
    /// Создает новое уведомление для пользователя
    /// </summary>
    public static IncidentNotificationAggregate Create(Guid id,
                                                       Guid incidentId,
                                                       Guid userId) =>
        new IncidentNotificationAggregate
        {
            Id = id,
            IncidentId = incidentId,
            UserId = userId
        };
}