using System.Data;
using Dapper;
using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Data.Sql;
using Teslametrics.App.Web.Services.Persistence;

namespace Teslametrics.App.Web.Features.Main.Cameras.Archive;

// UseCase может отменяться. С помощью передачи CancellationToken в запрос медиатору. Хз, нужна ли тебе эта инфа.
public static class GetEventsUseCase
{
    public record Query(Guid CameraId, DateTimeOffset Period) : BaseRequest<Response>; // ID камеры и день (день/месяц/год) для которого требуется список событий

    public record Response : BaseResponse
    {
        public record Event(string Name, DateTimeOffset StartTime, DateTimeOffset? EndTime); // Наименование события, дата начала и дата окончания

        public IEnumerable<Event> Events { get; init; } // список событий на указанную дату

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(IEnumerable<Event> events)
        {
            Result = Result.Success;
            Events = events;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Result = result;
            Events = [];
        }
    }

    public enum Result
    {
        Success,
        ValidationError,
        Unknown
    }

    public class Validator : AbstractValidator<Query>
    {
        public Validator()
        {
            RuleFor(q => q.CameraId).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Query, Response>
    {
        private readonly IValidator<Query> _validator;
        private readonly IDbConnection _dbConnection;

        public Handler(IValidator<Query> validator,
                       IDbConnection dbConnection)
        {
            _validator = validator;
            _dbConnection = dbConnection;
        }

        public async Task<Response> Handle(Query request, CancellationToken cancellationToken = default)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            IEnumerable<EventModel> events = [];

            var table = $"{Db.MotionEvents.Table}_{request.CameraId.ToString("N")}";

            if (await CheckTableExistsAsync(table))
            {
                var template = SqlQueryBuilder.Create()
                    .Select(Db.MotionEvents.Columns.StartTime)
                    .Select(Db.MotionEvents.Columns.EndTime)
                    .Where(@$"{Db.MotionEvents.Columns.StartTime}::DATE = :Period::DATE OR {Db.MotionEvents.Columns.EndTime}::DATE = :Period::DATE", new { request.Period })
                    .Build(QueryType.Standard, table, RowSelection.AllRows);

                events = await _dbConnection.QueryAsync<EventModel>(template.RawSql, template.Parameters);
            }

            return new Response(events.Select(e => new Response.Event(e.Name, e.StartTime, e.EndTime)));
        }

        private async Task<bool> CheckTableExistsAsync(string tableName)
        {
            var tableExists = await _dbConnection.ExecuteScalarAsync<int>(
                "SELECT COUNT(*) FROM information_schema.tables " +
                "WHERE table_schema = 'public' AND table_name = @TableName",
                new { tableName });

            return tableExists > 0;
        }
    }

    public record EventModel(DateTimeOffset StartTime, DateTimeOffset? EndTime)
    {
        public string Name { get; } = "Motion";
    }
}
