﻿using MediatR;
using Teslametrics.App.Web.Domain.AccessControl.Organizations.Events;
using Teslametrics.App.Web.Domain.AccessControl.Users;

namespace Teslametrics.App.Web.Features.EventHandlers;

public class DeleteUserFromOrganizationEventHandler : INotificationHandler<OrganizationDeletedEvent>
{
    private readonly IUserRepository _userRepository;

    public DeleteUserFromOrganizationEventHandler(IUserRepository userRepository)
    {
        _userRepository = userRepository;
    }

    public async Task Handle(OrganizationDeletedEvent notification, CancellationToken cancellationToken)
    {
        var users = await _userRepository.GetUsersByOrganizationIdAsync(notification.Id, cancellationToken);

        foreach (var user in users)
        {
            user.RemoveOrganization(notification.Id);
            if (user.Organizations.Count == 0)
            {
                await _userRepository.DeleteAsync(user.Id, cancellationToken);
            }
        }

        await _userRepository.SaveChangesAsync(cancellationToken);
    }
}