using System.Linq.Expressions;
using Microsoft.AspNetCore.Components;
using MudBlazor;
using Teslametrics.App.Web.Extensions;

namespace Teslametrics.App.Web.Features.Main.Cameras.CameraDrawer.Edit.PresetField;

public partial class PresetFieldComponent
{
	public record Preset(Guid id, string Title);

	#region [Parameters]
	[Parameter]
	[Category(CategoryTypes.FormComponent.Validation)]
	public Expression<Func<Preset>>? For { get; set; }

	[Parameter]
	public Preset? Selected { get; set; }

	[Parameter]
	public EventCallback<Preset> SelectedChanged { get; set; }

	[Parameter]
	[EditorRequired]
	public Guid OrganizationId { get; set; }
	#endregion

	private async Task<IEnumerable<Preset>> SearchAsync(string value, CancellationToken token)
	{
		GetQuotaListUseCase.Response? response = null;
		try
		{
			await SetLoadingAsync(true);
			response = await ScopeFactory.MediatorSend(new GetQuotaListUseCase.Query(OrganizationId, 0, 25, value));
		}
		catch (Exception exc)
		{
			response = null;
			Logger.LogError(exc, exc.Message);
			Snackbar.Add("Не удалось получить список пресетов камеры из-за непредвиденной ошибки.", Severity.Error);
		}

		if (response is null) return [];

		switch (response.Result)
		{
			case GetQuotaListUseCase.Result.Success:
				return response.Items.Select(item => new Preset(item.Id, item.Name));
			case GetQuotaListUseCase.Result.ValidationError:
				Snackbar.Add("Ошибка валидации при запросе квот камеры.", Severity.Error);
				break;
			case GetQuotaListUseCase.Result.Unknown:
			default:
				break;
		}

		return [];
	}

	#region
	private async Task OnSelectedValudeChanged(Preset? preset)
	{
		if (SelectedChanged.HasDelegate)
		{
			await SelectedChanged.InvokeAsync(preset);
		}
		else
		{
			Selected = preset;
		}
	}
	#endregion
}
