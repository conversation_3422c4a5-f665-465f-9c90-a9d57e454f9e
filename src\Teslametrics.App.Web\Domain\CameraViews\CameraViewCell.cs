namespace Teslametrics.App.Web.Domain.CameraViews;

public class <PERSON><PERSON>iew<PERSON>ell(Guid cameraId, short cellIndex)
{
    public Guid CameraId { get; init; } = cameraId;

    public short CellIndex { get; init; } = cellIndex;

    public override int GetHashCode()
    {
        return HashCode.Combine(CameraId, CellIndex);
    }

    public override bool Equals(object? obj)
    {
        if (obj is null) return false;
        if (ReferenceEquals(this, obj)) return true;
        return obj is CameraViewCell other &&
               CameraId == other.CameraId &&
               CellIndex == other.CellIndex;
    }

    public static bool operator ==(CameraViewCell? left, CameraViewCell? right)
    {
        if (left is null) return right is null;
        return left.Equals(right);
    }

    public static bool operator !=(CameraViewCell? left, CameraViewCell? right)
    {
        return !(left == right);
    }
}