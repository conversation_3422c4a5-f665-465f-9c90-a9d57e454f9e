.plan_fridge {
    z-index: 3;
    position: relative;
}

::deep.plan_fridge svg {
    fill: var(--mud-palette-surface);
}

.fridge-hover {
    width: 90px;
    min-height: 90px;
    background-color: #ECF4FC;
    border-radius: 8px;
    border: 2px solid #B2C2D2;
    display: flex;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transform: translate(-45px, -45px);
}

.fridge-content {
    width: 100%;
    padding: 8px;
}

.sensor-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
    width: 100%;
}

.sensor-item {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 24px;
    height: 24px;
    border-radius: 4px;
    position: relative;
    background: #F8FCFF;
}

::deep .sensor-item.no_data svg {
    color: var(--color-neutral-80);
}

::deep .sensor-item svg {
    color: var(--color-neutral-10);
}

.sensor-item.error {
    background: #FFF3F4;
}

::deep .sensor-item.error svg {
    color: var(--mud-palette-error);
}

.sensor-item::after {
    position: absolute;
    content: ' ';
    width: 2px;
    height: 2px;
    border-radius: 4px;
    top: 2px;
    right: 2px;
}

.sensor-item.no_data::after {
    background: #B8C2CC;
}

.sensor-item::after {
    background: var(--mud-palette-success);
}

::deep .sensor-icon {
    width: 16px;
    height: 16px;
}