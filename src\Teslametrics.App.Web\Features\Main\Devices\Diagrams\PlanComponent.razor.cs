using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using Teslametrics.App.Web.Extensions;
using Teslametrics.App.Web.Features.Main.Devices.Diagrams.Nodes;
using Teslametrics.App.Web.Features.Main.Devices.Diagrams.Nodes.PlanNode;
using Teslametrics.App.Web.Features.Main.Devices.Diagrams.Nodes.FridgeNode;
using Teslametrics.App.Web.Features.Main.Devices.Diagrams.Nodes.CameraNode;
using Teslametrics.App.Web.Features.Main.Devices.Diagrams.Nodes.RoomNode;

namespace Teslametrics.App.Web.Features.Main.Devices.Diagrams;

public partial class PlanComponent : IAsyncDisposable
{
    private Guid? _roomId;
    private Guid? _floorId;

    private GetFloorDiagramUseCase.Response? _response;
    private List<PlanElementBase> _elements = new();

    private string _containerId = $"plan-container-{Guid.NewGuid()}";
    private string _contentId = $"plan-content-{Guid.NewGuid()}";
    private IJSObjectReference? _jsModule;

    [Inject]
    private IJSRuntime JS { get; set; } = null!;

    #region [Parameters]
    [Parameter]
    public Guid? CityId { get; set; }

    [Parameter]
    public Guid? BuildingId { get; set; }

    [Parameter]
    public Guid? FloorId { get; set; }

    [Parameter]
    public Guid? RoomId { get; set; }

    [Parameter]
    public EventCallback<Guid?> RoomIdChanged { get; set; }
    #endregion

    public async ValueTask DisposeAsync()
    {
        if (_jsModule != null)
        {
            await _jsModule.InvokeVoidAsync("disposePanZoom", _containerId);
            await _jsModule.DisposeAsync();
        }
    }

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();

        // Load plan data if IDs are provided
        if (CityId.HasValue && BuildingId.HasValue && FloorId.HasValue && _floorId != FloorId)
        {
            _floorId = FloorId;
            await LoadPlanDataAsync();
        }
        else
        {
            _elements = [];
            _response = null;
        }
    }

    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();

        // Reload plan data if IDs have changed
        if (CityId.HasValue && BuildingId.HasValue && FloorId.HasValue)
        {
            await LoadPlanDataAsync();
        }
        else
        {
            _elements = [];
            _response = null;
        }

        if (RoomId != _roomId)
        {
            _roomId = RoomId;
            if (RoomId is not null)
            {
                var room = _elements.OfType<RoomElement>().FirstOrDefault(r => r.Id == RoomId);
                await SelectRoomAsync(room);
            }
            else
            {
                await ResetZoomAsync();
            }
        }
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            _jsModule = await JS.InvokeAsync<IJSObjectReference>("import", "./Features/Main/Devices/Diagrams/PlanComponent.razor.js");

            await InitializePanZoom();
        }
    }

    private async Task InitializePanZoom()
    {
        if (_jsModule != null)
        {
            await _jsModule.InvokeVoidAsync("initializePanZoom", _containerId, _contentId);
        }
    }

    private async Task LoadPlanDataAsync()
    {
        if (IsLoading || !CityId.HasValue || !BuildingId.HasValue || !FloorId.HasValue) return;
        await SetLoadingAsync(true);
        try
        {
            _response = await ScopeFactory.MediatorSend(new GetFloorDiagramUseCase.Query(CityId!.Value, BuildingId!.Value, FloorId!.Value));
        }
        catch (Exception ex)
        {
            _response = null;
            Logger.LogError(ex, ex.Message);
        }
        // Обработка полученных данных
        if (_response != null && _response.IsSuccess && _response.Floor != null)
        {
            _elements.Clear();

            // Добавляем изображение плана, если оно есть
            if (_response.Floor.Plan != null)
            {
                var imageElement = new ImageElement
                {
                    X = _response.Floor.Plan.Position.X,
                    Y = _response.Floor.Plan.Position.Y,
                    Width = _response.Floor.Plan.Size.Width > 0 ? _response.Floor.Plan.Size.Width : 800,
                    Height = _response.Floor.Plan.Size.Height > 0 ? _response.Floor.Plan.Size.Height : 600,
                    Id = _response.Floor.Id,
                    ImageUrl = $"/devices/floor/{_response.Floor.Id}"
                };
                _elements.Add(imageElement);
            }

            // Добавляем комнаты как области
            foreach (var room in _response.Floor.Rooms)
            {
                var areaPoints = room.ZonePoints.Select(p => new AreaPoint(p.X, p.Y)).ToList();

                // Добавляем только комнаты с валидными точками зоны
                if (areaPoints.Count >= 3)
                {
                    var areaElement = new RoomElement
                    {
                        Id = room.Id,
                        X = room.Position.X,
                        Y = room.Position.Y,
                        Title = room.Name,
                        Points = areaPoints
                    };
                    _elements.Add(areaElement);
                }

                // Добавляем холодильники как специализированные объекты
                foreach (var fridge in room.Fridges)
                {
                    var fridgeElement = new FridgeElement(fridge.Name)
                    {
                        Id = fridge.Id,
                        X = fridge.Position.X,
                        Y = fridge.Position.Y,
                    };
                    _elements.Add(fridgeElement);
                }

                // Добавляем камеры как специализированные объекты
                if (room.Camera != null)
                {
                    var cameraElement = new CameraElement(room.Camera.Name)
                    {
                        Id = room.Camera.Id,
                        X = room.Camera.Position.X,
                        Y = room.Camera.Position.Y
                    };
                    _elements.Add(cameraElement);
                }
            }
        }

        await SetLoadingAsync(false);
    }

    private async Task ResetZoomAsync()
    {
        try
        {
            // Reset zoom and unlock panning/zooming
            if (_jsModule != null)
            {
                await _jsModule.InvokeVoidAsync("resetZoom", _containerId);
                await _jsModule.InvokeVoidAsync("unlockPanZoom", _containerId);
            }
        }
        catch (JSDisconnectedException) // https://learn.microsoft.com/en-us/aspnet/core/blazor/javascript-interoperability/?view=aspnetcore-9.0
        {
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, ex.Message);
        }
    }

    private async Task SelectRoomAsync(RoomElement? room)
    {
        RoomId = room?.Id;
        if (RoomIdChanged.HasDelegate)
            await RoomIdChanged.InvokeAsync(RoomId);

        try
        {
            // Зумируем на выбранный элемент и блокируем перемещение/зум
            if (_jsModule != null)
            {
                await _jsModule.InvokeVoidAsync("zoomToElement", _containerId, RoomId);
            }
        }
        catch (JSDisconnectedException) // https://learn.microsoft.com/en-us/aspnet/core/blazor/javascript-interoperability/?view=aspnetcore-9.0
        {
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, ex.Message);
        }

    }
}
