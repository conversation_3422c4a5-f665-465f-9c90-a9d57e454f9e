using Dapper;
using FluentValidation;
using MediatR;
using System.Data;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Data.Sql;
using Teslametrics.App.Web.Services.Persistence;
using Teslametrics.App.Web.Shared;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Users.Drawer.View;

public static class GetUserUseCase
{
    public record Query(Guid Id, Guid OrganizationId) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public Guid Id { get; init; }

        public string Username { get; init; }

        public bool IsSystem { get; init; }

        public bool LockedoutEnabled { get; init; }

        public DateTimeOffset? LastLoginTime { get; init; }

        public bool IsOwner { get; init; }

        public List<Role> Roles { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(Guid id, string username, bool isSystem, bool lockedoutEnabled, DateTimeOffset? lastLoginTime, bool isOwner, List<Role> roles)
        {
            Id = id;
            Username = username;
            IsSystem = isSystem;
            LockedoutEnabled = lockedoutEnabled;
            LastLoginTime = lastLoginTime;
            IsOwner = isOwner;
            Roles = roles;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Result = result;

            Id = Guid.Empty;
            Username = string.Empty;
            IsSystem = false;
            LockedoutEnabled = false;
            LastLoginTime = null;
            Roles = [];
        }

        public record Role(Guid Id, string Name);
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        UserNotFound
    }

    public class Validator : AbstractValidator<Query>
    {
        public Validator()
        {
            RuleFor(q => q.Id).NotEmpty();
            RuleFor(q => q.OrganizationId).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Query, Response>
    {
        private readonly IValidator<Query> _validator;
        private readonly IDbConnection _dbConnection;

        public Handler(IValidator<Query> validator,
                       IDbConnection dbConnection)
        {
            _validator = validator;
            _dbConnection = dbConnection;
        }

        public async Task<Response> Handle(Query request, CancellationToken cancellationToken = default)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            var template = SqlQueryBuilder.Create()
                .Select(Db.Users.Props.Id, "UserId")
                .Select(Db.Users.Props.Name, "Username")
                .Select(Db.Users.Props.LockoutEnabled)
                .Select(Db.Users.Props.LastLogInTime)
                .Select($"{Db.Organizations.Columns.OwnerId} IS NOT NULL", "IsOwner")
                .Select(Db.Roles.Props.Id, "RoleId")
                .Select(Db.Roles.Props.Name, "RoleName")
                .LeftJoin(Db.UserRoles.Table, Db.UserRoles.Props.UserId, Db.Users.Props.Id, SqlOperator.Equals)
                .LeftJoin($"{Db.Roles.Table} ON {Db.Roles.Props.Id} = {Db.UserRoles.Props.RoleId} AND {Db.Roles.Props.OrganizationId} = :OrganizationId", new { request.OrganizationId })
                .LeftJoin($"{Db.Organizations.Table} ON {Db.Organizations.Props.OwnerId} = {Db.Users.Props.Id} AND {Db.Organizations.Props.Id} = :OrganizationId", new { request.OrganizationId })
                .Where(Db.Users.Props.Id, ":Id", SqlOperator.Equals, new { request.Id })
                .Build(QueryType.Standard, Db.Users.Table, RowSelection.AllRows);

            var users = await _dbConnection.QueryAsync<UserModel, RoleModel, UserModel>(template.RawSql, (user, role) =>
            {
                if (role is not null)
                {
                    user.Roles.Add(role);
                }
                return user;
            }, template.Parameters, splitOn: "RoleId");

            if (!users.Any())
            {
                return new Response(Result.UserNotFound);
            }

            var result = users.GroupBy(u => u.UserId).Select(u =>
            {
                var groupedUser = u.First();
                groupedUser.Roles = u.SelectMany(p => p.Roles).ToList();

                return groupedUser;
            });

            var user = result.Single();

            return new Response(user.UserId,
                                user.Username,
                                user.UserId == SystemConsts.RootUserId,
                                user.LockoutEnabled,
                                user.LastLogInTime,
                                user.IsOwner,
                                user.Roles.Select(r => new Response.Role(r.RoleId, r.RoleName)).ToList());
        }
    }

    public record UserModel(Guid UserId, string Username, bool LockoutEnabled, DateTimeOffset? LastLogInTime, bool IsOwner)
    {
        public List<RoleModel> Roles { get; set; } = [];
    }

    public record RoleModel(Guid RoleId, string RoleName);
}