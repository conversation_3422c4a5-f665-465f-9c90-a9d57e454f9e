@using Microsoft.AspNetCore.Components.Authorization
@inherits InteractiveBaseComponent
<DrawerHeader>
	<MudText Typo="Typo.h1">Создание пользователя</MudText>
</DrawerHeader>
<MudForm Model="_model"
		 Validation="_validator.ValidateValue"
		 @bind-IsValid="_isValid"
		 Class="flex-1"
		 OverrideFieldValidation="true"
		 UserAttributes="@(new Dictionary<string, object>() {{"autocomplete", "off"}, {"aria-autocomplete", "none"}, {"role", "presentation"} })"
		 Spacing="8">
	<FormSectionComponent Title="Описание пользователя"
						  Subtitle="Настройки, которые влияют только на восприятие человеком">
		<MudTextField @bind-Value="_model.Username"
					  For="@(() => _model.Username)"
					  Clearable="true"
					  InputType="InputType.Text"
					  Immediate="true"
					  Label="Логин"
					  HelperText="Текст в данном поле будет использовать пользователь для входа в систему"
					  RequiredError="Данное поле обязательно"
					  UserAttributes="@(new Dictionary<string, object>() {{"autocomplete", "username"}, {"aria-autocomplete", "none" }})"
					  Required="true" />
	</FormSectionComponent>
	<FormSectionComponent Title="Параметры пользователя"
						  Subtitle="Данные настройки важны для работы в системе">

		<MudAutocomplete T="RoleModel"
						 Value="_search"
						 ValueChanged="OnRoleSelected"
						 Label="Роли пользователя"
						 Placeholder="Введите название роли"
						 ResetValueOnEmptyText="true"
						 ToStringFunc="@(e=> e==null?null : e.Name)"
						 DebounceInterval="500"
						 SearchFunc="@FetchRolesAsync"
						 ShowProgressIndicator="true"
						 AutoFocus="false"
						 CoerceValue="false"
						 CoerceText="false"
						 UserAttributes="@(new Dictionary<string, object>() {{"autocomplete", "false"}, {"name", "roles"}, {"aria-autocomplete", "none" }})"
						 AdornmentIcon="@Icons.Material.Filled.Search"
						 AdornmentColor="Color.Primary">
			<NoItemsTemplate>
				<MudText Align="Align.Center"
						 Class="px-4 py-1">
					Не найдено ролей с данным названием
				</MudText>
			</NoItemsTemplate>
		</MudAutocomplete>
		<MudSelect T="RoleModel"
				   SelectedValues="@_model.Roles"
				   Required="true"
				   Style="display: none" /> <!-- Нужно для валидации -->

		<div class="d-flex flex-column overflow-auto mb-4 roles">
			@if (_model.Roles.Count > 0)
			{
				@foreach (var role in _model.Roles)
				{
					<MudCheckBox T="bool"
								 Value="true"
								 ValueChanged="(isChecked) => _model.Roles.Remove(role)"
								 Label="@role.Name"
								 Color="Color.Primary"
								 @key="role" />
				}
			}
			else
			{
				<MudText Class="pa-3"
						 Color="Color.Error">Нет выбранных ролей</MudText>
			}
		</div>

		<PasswordFieldComponent Value="@_model.Password"
								ValueChanged="OnPwdChange"
								For="@(() => _model.Password)"
								Label="Пароль"
								Required="true"
								RequiredError="Поле должно быть заполнено"
								HelperText="Данный пароль будет использоваться для входа"
								Immediate="true"
								UserAttributes="@(new Dictionary<string, object>() {{"autocomplete", "false"}, {"name", "password"}, {"aria-autocomplete", "none" }})"
								@ref="pwdFieldRef" />

		<PasswordFieldComponent Value="@_model.PasswordConfirm"
								ValueChanged="OnPwdConfirmChange"
								For="@(() => _model.PasswordConfirm)"
								Label="Подтверждение пароля"
								RequiredError="Поле должно быть заполнено"
								HelperText="Введите такой же пароль, как в поле выше"
								Required="true"
								Immediate="true"
								UserAttributes="@(new Dictionary<string, object>() {{"autocomplete", "false"}, {"aria-autocomplete", "none" }})"
								@ref="pwdConfirmFieldRef" />
	</FormSectionComponent>
</MudForm>
<DrawerActions>
	<MudSpacer />
	<MudButton OnClick="CancelAsync"
			   Variant="Variant.Outlined"
			   StartIcon="@Icons.Material.Outlined.Close">Закрыть</MudButton>
	<AuthorizeView Policy="@(Fqdn<AppPermissions>.GetNames(AppPermissions.Main.AccessControl.Users.Create).Last())"
				   Context="innerContext">
		<MudButton OnClick="SubmitAsync"
				   Disabled="@(!_isValid)"
				   Color="Color.Secondary"
				   Variant="Variant.Outlined">Сохранить</MudButton>
	</AuthorizeView>
</DrawerActions>