@using Teslametrics.App.Web.Features.Main.Incidents.SelectedEvent
@using Teslametrics.App.Web.Features.Main.Incidents.IncidentsList
@using Teslametrics.App.Web.Features.Main.Incidents.Filter
@page "/incidents"
@attribute [Authorize]
<MudStack Spacing="5"
		  Class="mud-height-full pl-12 pr-12 pb-12 overflow-auto layout"
		  Row="true">
	@if (_selectedIncident is not null)
	{
		<SelectedEventComponent IncidentId="_selectedIncident.Value" />
	}
	<div class="d_contents">
		<MudPaper Elevation="0"
				  Outlined="true"
				  Class="table mud-height-full mud-width-full container">
			<div class="px-3 py-2">
				<MudText Typo="Typo.subtitle1">Фильтры</MudText>
			</div>
			<FilterComponent @bind-IsResolved="IsResolved"
							 @bind-IncidentType="IncidentType"
							 @bind-CityId="@CityId"
							 @bind-BuildingId="BuildingId"
							 @bind-FloorId="FloorId"
							 @bind-RoomId="RoomId"
							 @bind-FridgeId="FridgeId"
							 @bind-DateFrom="DateFrom"
							 @bind-DateTo="DateTo" />
			<IncidentsListComponent IsResolved="IsResolved"
									IncidentType="IncidentType"
									DateFrom="DateFrom"
									DateTo="DateTo"
									CityId="@CityId"
									BuildingId="@BuildingId"
									FloorId="@FloorId"
									RoomId="@RoomId"
									FridgeId="@FridgeId"
									@bind-IncidentId="_selectedIncident" />
		</MudPaper>
	</div>
</MudStack>