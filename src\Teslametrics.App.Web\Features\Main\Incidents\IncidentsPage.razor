@using Teslametrics.App.Web.Features.Main.Incidents.SelectedEvent
@using Teslametrics.App.Web.Features.Main.Incidents.IncidentsList
@using Teslametrics.App.Web.Features.Main.Incidents.Filter
@page "/incidents"
@attribute [Authorize]
<div class="d_contents">
	<MudStack Spacing="5"
			  Class="mud-height-full overflow-auto layout"
			  Row="true">
		@if (UserDeviceService.IsMobile)
		{
			<MudDrawer Open="_selectedIncident.HasValue"
					   Fixed="true"
					   Width="100%"
					   Elevation="1"
					   Variant="@DrawerVariant.Temporary">
				<MudSwipeArea OnSwipeEnd="@OnSwipeEnd"
							  Class="mud-width-full mud-height-full overflow-hidden">
					@if (_selectedIncident is not null)
					{
						<SelectedEventComponent IncidentId="_selectedIncident.Value" />
					}
				</MudSwipeArea>
			</MudDrawer>
		}
		else
		{
			@if (_selectedIncident is not null)
			{
				<MudPaper Elevation="0"
						  Outlined="true"
						  Class="information mud-height-full"
						  Style="max-width: 444px;">
					<SelectedEventComponent IncidentId="_selectedIncident.Value" />
				</MudPaper>
			}
		}
		<MudPaper Elevation="0"
				  Outlined="true"
				  Class="table mud-height-full mud-width-full container">
			<div class="px-3 py-2">
				<MudText Typo="Typo.subtitle1">Фильтры</MudText>
			</div>
			<FilterComponent @bind-IsResolved="IsResolved"
							 @bind-IncidentType="IncidentType"
							 @bind-CityId="@CityId"
							 @bind-BuildingId="BuildingId"
							 @bind-FloorId="FloorId"
							 @bind-RoomId="RoomId"
							 @bind-FridgeId="FridgeId"
							 @bind-DateFrom="DateFrom"
							 @bind-DateTo="DateTo" />
			<IncidentsListComponent IsResolved="IsResolved"
									IncidentType="IncidentType"
									DateFrom="DateFrom"
									DateTo="DateTo"
									CityId="@CityId"
									BuildingId="@BuildingId"
									FloorId="@FloorId"
									RoomId="@RoomId"
									FridgeId="@FridgeId"
									@bind-IncidentId="_selectedIncident" />
		</MudPaper>
	</MudStack>
</div>