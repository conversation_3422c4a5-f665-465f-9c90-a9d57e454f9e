using Microsoft.AspNetCore.Components;
using MudBlazor;
using System.Linq.Expressions;

namespace Teslametrics.App.Web.Components.Form;

public partial class SelectEnumComponent<TEnum> where TEnum : IConvertible
{
	private TEnum[] _values;

	[Parameter]
	public string? Label { get; set; }
	[Parameter]
	public TEnum? SelectedValue { get; set; }

	[Parameter]
	public EventCallback<TEnum?> SelectedValueChanged { get; set; }

	[Parameter]
	public bool Clearable { get; set; }

	[Parameter]
	public bool Disabled { get; set; }

	[Parameter]
	public bool Required { get; set; }

	[Parameter]
	public bool ReadOnly { get; set; }

	[Parameter]
	public string? RequiredError { get; set; }

	[Parameter]
	public Variant Variant { get; set; } = Variant.Text;

	[Parameter]
	public Expression<Func<TEnum>>? For { get; set; }

	[Parameter]
	public string? HelperText { get; set; }

	public SelectEnumComponent()
	{
		_values = (TEnum[])Enum.GetValues(typeof(TEnum));
	}
}
