using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Domain.CameraViews;
using Teslametrics.App.Web.Domain.CameraViews.Events;
using Teslametrics.App.Web.Services.Outbox;
using Teslametrics.App.Web.Services.TransactionManager;

namespace Teslametrics.App.Web.Features.Main.CameraViews.DeleteDialog;

public static class DeleteViewUseCase
{
    public record Command(Guid Id) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(Result result)
        {
            Result = result;
        }
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError
    }

    public class Validator : AbstractValidator<Command>
    {
        public Validator()
        {
            RuleFor(c => c.Id).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Command, Response>
    {
        private readonly IValidator<Command> _validator;
        private readonly ICameraViewRepository _cameraViewRepository;
        private readonly ITransactionManager _transactionManager;
        private readonly IPublisher _publisher;
        private readonly IOutbox _outbox;

        public Handler(IValidator<Command> validator,
                       ICameraViewRepository cameraViewRepository,
                       ITransactionManager transactionManager,
                       IPublisher publisher,
                       IOutbox outbox)
        {
            _validator = validator;
            _cameraViewRepository = cameraViewRepository;
            _transactionManager = transactionManager;
            _publisher = publisher;
            _outbox = outbox;
        }

        public async Task<Response> Handle(Command request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            using var transaction = await _transactionManager.CreateTransactionAsync();

            var cameraView = await _cameraViewRepository.FindAsync(request.Id, cancellationToken);

            if (cameraView is null)
            {
                return new Response(Result.Success);
            }

            await _cameraViewRepository.DeleteAsync(request.Id, cancellationToken);
            await _cameraViewRepository.SaveChangesAsync(cancellationToken);

            List<object> events = [new CameraViewDeletedEvent(request.Id, cameraView.OrganizationId)];

            foreach (var @event in events)
            {
                await _publisher.Publish(@event, cancellationToken);
            }

            await _outbox.AddRangeAsync(events);

            await transaction.CommitAsync();

            return new Response(Result.Success);
        }
    }
}