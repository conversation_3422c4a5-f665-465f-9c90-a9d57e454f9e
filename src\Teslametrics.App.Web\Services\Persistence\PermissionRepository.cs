using Microsoft.EntityFrameworkCore;
using Teslametrics.App.Web.Domain.AccessControl;

namespace Teslametrics.App.Web.Services.Persistence;

public class PermissionRepository : IPermissionRepository
{
    private readonly CommandAppDbContext _dbContext;

    public PermissionRepository(CommandAppDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public Task<List<PermissionModel>> GetPermissions(IEnumerable<Guid> roles, CancellationToken cancellationToken) =>
         _dbContext.Database.SqlQueryRaw<PermissionModel>($"SELECT {Db.RolePermissions.Props.RoleId}, {Db.RolePermissions.Columns.OrganizationId}, {Db.RolePermissions.Columns.Permission}, {Db.RolePermissions.Columns.ResourceId} " +
                                                          $"FROM {Db.RolePermissions.Table}")
                .Where(p => roles.Contains(p.RoleId))
                .OrderBy(entity => entity.ResourceId)
                .ToListAsync(cancellationToken);
}