﻿/* CUSTOM SCROLBAR */
body {
	scrollbar-color: rgba(0, 0, 0, .2) transparent;
	scrollbar-color: var(--palette-black-alpha-20, rgba(0, 0, 0, .2)) transparent;
	scrollbar-width: thin;
}

body ::-webkit-scrollbar {
	width: 18px;
	height: 18px;
}

body ::-webkit-scrollbar-thumb {
	border: 6px solid transparent;
	/*	
		background: rgba(0, 0, 0, .2);
		background: var(--palette-black-alpha-20, rgba(0, 0, 0, .2));
	*/
	background: var(--mud-palette-primary-darken);
	border-radius: 10px;
	background-clip: padding-box;
}

body ::-webkit-scrollbar-corner {
	background: transparent;
}

body ::-webkit-scrollbar-thumb:vertical {
	min-height: 30px;
}

body ::-webkit-scrollbar-thumb:horizontal {
	min-width: 30px;
}

body ::-webkit-scrollbar-thumb:hover {
	/*
		background: rgba(0, 0, 0, .3);
		background: var(--palette-black-alpha-30, rgba(0, 0, 0, .3));
	*/
	background: var(--mud-palette-primary);
	background-clip: padding-box;
	border: 4px solid transparent;
}

/* AUTO-HIDE */
.scroll-auto-hide {
	scrollbar-color: transparent transparent;
}

.scroll-auto-hide:hover {
	scrollbar-color: rgba(0, 0, 0, .2) transparent;
	scrollbar-color: var(--palette-black-alpha-20, rgba(0, 0, 0, .2)) transparent;
}

.scroll-auto-hide::-webkit-scrollbar-thumb {
	background: transparent;
	background-clip: padding-box;
}

.scroll-auto-hide:hover::-webkit-scrollbar-thumb {
	background: rgba(0, 0, 0, .2);
	background: var(--palette-black-alpha-20, rgba(0, 0, 0, .2));
	background-clip: padding-box;
}

/* HIDDEN */
.custom-scrollbar-hidden {
	-ms-overflow-style: none;
	scrollbar-width: none;
}

.custom-scrollbar-hidden::-webkit-scrollbar {
	width: 0;
}
