﻿using FluentValidation;

namespace Teslametrics.App.Web.Abstractions;

public class BaseFluentValidator<T> : AbstractValidator<T>, IBaseFluentValidator<T>
{
    public Func<object, string, Task<IEnumerable<string>>> ValidateValue => async (model, propertyName) =>
    {
        var result = await ValidateAsync(ValidationContext<T>.CreateWithOptions((T)model, x => x.IncludeProperties(propertyName)));
        if (result.IsValid)
        {
            return Array.Empty<string>();
        }

        return result.Errors.Select(e => e.ErrorMessage);
    };

    //https://github.com/MudBlazor/MudBlazor/discussions/4210
    public async Task<IEnumerable<string>> ValidateValueAsync(object model, string propertyName)
    {
        var result = await ValidateAsync(ValidationContext<T>.CreateWithOptions((T)model, x => x.IncludeProperties(propertyName)));
        if (result.IsValid)
        {
            return Array.Empty<string>();
        }
        return result.Errors.Select(e => e.ErrorMessage);
    }
}
