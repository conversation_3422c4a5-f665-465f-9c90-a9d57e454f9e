using Microsoft.AspNetCore.SignalR;
using System.Collections.Concurrent;
using Orleans.Streams;
using Teslametrics.MediaServer.Orleans.Camera;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Hubs;

// TODO: Сделать модуль и засунуть его в Components рядом с MSEPlayer
/// <summary>
/// SignalR хаб для передачи видеосегментов
/// </summary>
public class VideoHub : Hub
{
    private static readonly ConcurrentDictionary<string, StreamSubscriptionHandle<MicroSegment>> _clientStreams = new();
    private readonly ILogger<VideoHub> _logger;
    private readonly IHubContext<VideoHub> _hubContext;
    private readonly IClusterClient _clusterClient;

    public VideoHub(ILogger<VideoHub> logger,
                    IClusterClient clusterClient,
                    IHubContext<VideoHub> hubContext)
    {
        _logger = logger;
        _clusterClient = clusterClient;
        _hubContext = hubContext;
    }

    /// <summary>
    /// Запускает поток видео с реальной камеры
    /// </summary>
    /// <param name="cameraStreamId">ID стрима камеры</param>
    /// <returns>Task</returns>
    public async Task PlayStreamAsync(Guid cameraStreamId)
    {
        string connectionId = Context.ConnectionId;
        _logger.LogInformation("Starting camera stream {CameraStreamId} for client {ConnectionId}", cameraStreamId, connectionId);

        // Останавливаем предыдущий поток для этого клиента, если он существует
        await StopClientStreamAsync(connectionId);

        var streamProvider = _clusterClient.GetStreamProvider(StreamNames.VideoLiveStream);

        var streamId = StreamId.Create(StreamNamespaces.CameraStreams, cameraStreamId);
        var stream = streamProvider.GetStream<MicroSegment>(streamId);

        var sub = await stream.SubscribeAsync(async (segment, token) =>
        {
            // Отправляем оригинальный .ts сегмент вместе с временем записи
            // Конвертируем в UTC для избежания проблем с часовыми поясами
            var utcStartTime = segment.StartTime.UtcDateTime;

            _logger.LogDebug("Отправка сегмента клиенту {ConnectionId}: размер {Size} байт, время записи {StartTime} UTC",
                connectionId, segment.Payload?.Length ?? 0, utcStartTime);

            await _hubContext.Clients.Client(connectionId).SendAsync(
                "ReceiveVideoSegment",
                segment.Payload,
                utcStartTime.ToString("yyyy-MM-ddTHH:mm:ss.fffZ")); // Явный UTC формат
        });

        _clientStreams[connectionId] = sub;
    }

    /// <summary>
    /// Останавливает поток видео для клиента
    /// </summary>
    /// <returns>Task</returns>
    public Task StopStreamAsync()
    {
        string connectionId = Context.ConnectionId;
        _logger.LogInformation("Stopping stream for client {ConnectionId}", connectionId);

        return StopClientStreamAsync(connectionId);
    }

    /// <summary>
    /// Останавливает поток для указанного клиента
    /// </summary>
    /// <param name="connectionId">ID подключения клиента</param>
    private static async Task StopClientStreamAsync(string connectionId)
    {
        if (_clientStreams.TryRemove(connectionId, out var sub))
        {
            await sub.UnsubscribeAsync();
        }
    }

    public override async Task OnConnectedAsync()
    {
        _logger.LogInformation("Client connected: {ConnectionId}", Context.ConnectionId);
        await base.OnConnectedAsync();
    }

    public override async Task OnDisconnectedAsync(Exception? exception)
    {
        string connectionId = Context.ConnectionId;
        _logger.LogInformation("Client disconnected: {ConnectionId}", connectionId);

        // Останавливаем поток для отключившегося клиента
        await StopClientStreamAsync(connectionId);

        await base.OnDisconnectedAsync(exception);
    }
}