﻿using System.Web;

namespace Teslametrics.App.Web.Extensions;

public static class QueryExtensions
{
	public static bool TryGetQueryParameter<T>(this string queryString, string key, out T? value)
	{
		var valueFromQueryString = HttpUtility.ParseQueryString(queryString)[key];

		value = default;
		if (valueFromQueryString is null)
		{
			return false;
		}

		if (typeof(T) == typeof(int) && int.TryParse(valueFromQueryString, out var valueAsInt))
		{
			value = (T)(object)valueAsInt;
			return true;
		}

		if (typeof(T) == typeof(string))
		{
			value = (T)(object)valueFromQueryString.ToString();
			return true;
		}

		if (typeof(T) == typeof(decimal) && decimal.TryParse(valueFromQueryString, out var valueAsDecimal))
		{
			value = (T)(object)valueAsDecimal;
			return true;
		}

		return false;
	}
}
