using System.Buffers;
using System.Threading.Channels;

namespace FFMpegNET;

internal static class RtspProcessorHelper
{
    public static async Task ProcessOutputStream(ChannelReader<(MemoryOutputBuffer Buffer, DateTimeOffset StartTime, double Duration)> channel,
                                                 Func<Segment, Task> onNextSegment,
                                                 CancellationToken cancellationToken)
    {
        bool readAvailable;
        do
        {
            readAvailable = await channel.WaitToReadAsync();
            if (readAvailable && channel.TryRead(out var seg))
            {
                try
                {
                    await onNextSegment(new Segment(seg.Buffer.Stream, seg.StartTime, seg.Duration));
                }
                finally
                {
                    seg.Buffer.Dispose();
                }

            }
        }
        while (!cancellationToken.IsCancellationRequested && readAvailable);
    }

    public static async Task ProcessPreviewStream(ChannelReader<byte[]> channel, Func<byte[], Task> onNextPreview, CancellationToken cancellationToken)
    {
        bool readAvailable;
        do
        {
            readAvailable = await channel.WaitToReadAsync();
            if (readAvailable && channel.TryRead(out var preview))
            {
                try
                {
                    await onNextPreview(preview.ToArray());
                }
                finally
                {
                    ArrayPool<byte>.Shared.Return(preview, true);
                }
            }
        }
        while (!cancellationToken.IsCancellationRequested && readAvailable);
    }
}