namespace Teslametrics.App.Web.Orleans.Camera;

[<PERSON><PERSON>("Teslametrics.App.Web.Orleans.ICameraGrain")]
public interface ICameraGrain : IGrainWithGuidKey
{
    [<PERSON><PERSON>("GetStatusAsync")]
    public Task<CameraStatus> GetStatusAsync();

    [<PERSON><PERSON>("ConnectRtspAsync")]
    public Task ConnectRtspAsync(ConnectRtspRequest request);

    [<PERSON><PERSON>("ConnectOnvifAsync")]
    public Task ConnectOnvifAsync(ConnectOnvifRequest request);

    [<PERSON><PERSON>("DisconnectAsync")]
    public Task DisconnectAsync();

    [<PERSON><PERSON>("GetCameraStreamIdAsync")]
    public Task<GetCameraStreamIdResponse> GetCameraStreamIdAsync(GetCameraStreamIdRequest request);

    [GenerateSerializer]
    [<PERSON><PERSON>("Teslametrics.App.Web.Orleans.ICameraGrain.ConnectRtspRequest")]
    public record ConnectRtspRequest(string ArchiveUri, string ViewUri, string PublicUri);

    [GenerateSerializer]
    [<PERSON>as("Teslametrics.App.Web.Orleans.ICameraGrain.ConnectOnvifRequest")]
    public record ConnectOnvifRequest(string Host, int Port, string Username, string Password);

    [GenerateSerializer]
    [Alias("Teslametrics.App.Web.Orleans.ICameraGrain.GetCameraStreamIdRequest")]
    public record GetCameraStreamIdRequest(StreamType StreamType);

    [GenerateSerializer]
    [Alias("Teslametrics.App.Web.Orleans.ICameraGrain.GetCameraStreamIdResponse")]
    public record GetCameraStreamIdResponse(Guid? CameraStreamId);
}