.hover-zone
{
	position: fixed;
	top: 0;
	width: 10%;
	height: 100%;
	z-index: 1000;
}

.left
{
	left: 0;
}

::deep
{
	--mud-drawer-width-right: 12vw;
	--mud-drawer-width-left: 12vw;
}

.right
{
	right: 0;
}

::deep #layout_sidebar
{
	z-index: calc(var(--mud-zindex-appbar) - 1);
}

@media (min-width: 641px)
{
	::deep #layout_sidebar
	{
		border-right: 1px solid var(--mud-palette-divider);
	}
}

::deep layout_sidebar:not(.mud-drawer-mini) .nav_menu
{
	padding: 8px;
}