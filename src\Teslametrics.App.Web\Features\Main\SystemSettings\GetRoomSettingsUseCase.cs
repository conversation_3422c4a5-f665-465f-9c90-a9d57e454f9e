using System.Data;
using System.Text.Json;
using Dapper;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.Core.Services.Persistence;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Features.Main.SystemSettings;

public static class GetRoomSettingsUseCase
{
    public record Query() : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public PageModel Page { get; init; }

        public List<FloorImage> FloorImages { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(PageModel page, List<FloorImage> floorImages)
        {
            Page = page;
            FloorImages = floorImages;

            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Result = result;
            Page = new();
            FloorImages = [];
        }

        public record FloorImage(Guid FloorId, byte[] Image, string ContentType);
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        PlanNotFound
    }

    public class Handler : IRequestHandler<Query, Response>
    {
        //private readonly IValidator<Query> _validator;
        private readonly IDbConnection _dbConnection;

        public Handler(IDbConnection dbConnection)
        {
            _dbConnection = dbConnection;
        }

        public async Task<Response> Handle(Query request, CancellationToken cancellationToken)
        {
            if (!await CheckTableExistsAsync(Db.Plans.Table))
            {
                return new Response(Result.PlanNotFound);
            }

            var template = SqlQueryBuilder.Create()
                .Select(Db.Plans.Props.Page)
                .Build(QueryType.Standard, Db.Plans.Table, RowSelection.AllRows);

            var pageJson = await _dbConnection.ExecuteScalarAsync<string?>(template.RawSql);

            if (string.IsNullOrEmpty(pageJson))
            {
                return new Response(Result.PlanNotFound);
            }

            var page = JsonSerializer.Deserialize<PageModel>(pageJson)!;

            var floorImages = new List<PlanImageModel>();

            if (await CheckTableExistsAsync(Db.PlanImages.Table))
            {
                template = SqlQueryBuilder.Create()
                    .Select(Db.PlanImages.Props.FloorId)
                    .Select(Db.PlanImages.Props.Image)
                    .Select(Db.PlanImages.Props.ContentType)
                    .Build(QueryType.Standard, Db.PlanImages.Table, RowSelection.AllRows);

                floorImages = (await _dbConnection.QueryAsync<PlanImageModel>(template.RawSql)).ToList();
            }

            return new Response(page, floorImages.Select(i => new Response.FloorImage(i.FloorId, i.Image, i.ContentType)).ToList());
        }

        private async Task<bool> CheckTableExistsAsync(string tableName)
        {
            // Check if table exists
            var tableExists = await _dbConnection.ExecuteScalarAsync<int>(
                "SELECT COUNT(*) FROM information_schema.tables " +
                "WHERE table_schema = 'public' AND table_name = @TableName",
                new { TableName = tableName });

            return tableExists > 0;
        }
    }
}

public record PlanImageModel(Guid FloorId, byte[] Image, string ContentType);