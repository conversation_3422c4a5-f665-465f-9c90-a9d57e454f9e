using Microsoft.AspNetCore.Components;
using MudBlazor;
using System.Reactive;
using System.Text;
using Teslametrics.App.Web.Components.Drawer;
using Teslametrics.App.Web.Eto.Roles;
using Teslametrics.App.Web.Eto.Users;
using Teslametrics.App.Web.Extensions;
using Teslametrics.App.Web.Shared;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Roles.Drawer.View;

public partial class RoleViewComponent
{
	[CascadingParameter(Name = DrawerConsts.InstanceName)]
	private DrawerComponent Drawer { get; set; } = null!;

	private string _contextMenuAuthPolicyString => new StringBuilder()
		.Append("," + AppPermissions.Main.AccessControl.Roles.Update.GetEnumPermissionString())
		.Append("," + AppPermissions.Main.AccessControl.Roles.Delete.GetEnumPermissionString())
		.ToString();// Есть вероятность, что спереди будет запятая.

	private DateTime _lastRefreshTime = DateTime.Now;

	private bool _subscribing;
	private Guid _roleId;
	private GetRoleUseCase.Response? _model;
	private SubscribeRoleUseCase.Response? _subscriptionResult;

	#region Parameters
	[Parameter]
	[EditorRequired]
	public Guid RoleId { get; set; }

	[Parameter]
	public Guid OrganizationId { get; set; }
	#endregion

	protected override async Task OnParametersSetAsync()
	{
		if (RoleId != _roleId)
		{
			_roleId = RoleId;
			_model = null;
			await FetchAsync();
		}

		await base.OnParametersSetAsync();
	}
	protected async Task FetchAsync()
	{
		if (IsLoading) return;
		await SetLoadingAsync(true);
		try
		{
			_model = await ScopeFactory.MediatorSend(new GetRoleUseCase.Query(RoleId));
		}
		catch (Exception ex)
		{
			_model = null;
			Snackbar.Add("Не удалось получить выбранную роль из-за непредвиденной ошибки связи с сервером. Повторите попытку", Severity.Error);
			Logger.LogError(ex, ex.Message);
		}

		await SetLoadingAsync(false);

		if (_model is null) return;
		switch (_model.Result)
		{
			case GetRoleUseCase.Result.Success:
				_lastRefreshTime = DateTime.UtcNow;
				await SubscribeAsync();
				break;
			case GetRoleUseCase.Result.ValidationError:
				Snackbar.Add("Ошибка валидации данных", Severity.Error);
				break;
			case GetRoleUseCase.Result.RoleNotFound:
				Snackbar.Add("Роль не найден", Severity.Error);
				break;
			case GetRoleUseCase.Result.Unknown:
				Logger.LogError("Unexpected result in {Component}, {UseCase}: {Result}", nameof(RoleViewComponent), nameof(GetRoleUseCase), _model.Result);
				Snackbar.Add("Не удалось получить выбранную роль из-за непредвиденной ошибки. Повторите попытку", Severity.Error);
				break;
			default:
				Logger.LogError("Unexpected result in {Component}, {UseCase}: {Result}", nameof(RoleViewComponent), nameof(GetRoleUseCase), _model.Result);
				Snackbar.Add("Не удалось получить выбранную роль из-за непредвиденной ошибки связи с сервером. Повторите попытку", Severity.Error);
				break;
		}
	}

	private async Task SubscribeAsync()
	{
		try
		{
			if (_model is null || !_model.IsSuccess) return;
			await SetSubscribingAsync(true);
			Unsubscribe();
			_subscriptionResult = await ScopeFactory.MediatorSend(new SubscribeRoleUseCase.Request(Observer.Create<object>(OnAppEventHandler, OnError), _model.Id));
			switch (_subscriptionResult.Result)
			{
				case SubscribeRoleUseCase.Result.Success:
					CompositeDisposable.Add(_subscriptionResult.Subscription!);
					break;

				case SubscribeRoleUseCase.Result.ValidationError:
					Snackbar.Add("Ошибка валидации при подписке на события", Severity.Error);
					break;

				case SubscribeRoleUseCase.Result.Unknown:
				default:
					throw new Exception($"Unexpected result in {nameof(SubscribeRoleUseCase)}: {_subscriptionResult.Result}");
			}
		}
		catch (Exception exc)
		{
			Snackbar.Add("Не удалось получить подписку на события роли. Повторите попытку", Severity.Error);
			Logger.LogError(exc, exc.Message);
		}
		finally
		{
			await SetSubscribingAsync(false);
		}
	}

	private void Unsubscribe()
	{
		if (_subscriptionResult is not null && _subscriptionResult.Subscription is not null)
		{
			CompositeDisposable.Remove(_subscriptionResult.Subscription);
			_subscriptionResult.Subscription.Dispose();
		}
	}
	protected Task SetSubscribingAsync(bool isLoading = true) => UpdateViewAsync(() =>
	{
		_subscribing = isLoading;
	});

	#region [Actions]
	private Task CancelAsync()
	{
		Unsubscribe();
		_model = null;
		return Drawer.HideAsync();
	}
	private Task RefreshAsync() => FetchAsync();
	private void Edit() => EventSystem.Publish(new RoleEditEto(OrganizationId, RoleId));
	private void Delete() => EventSystem.Publish(new RoleDeleteEto(OrganizationId, RoleId));
	private void SelectUser(Guid userId) => EventSystem.Publish(new UserSelectEto(userId));
	#endregion

	#region [Event Handlers]
	private async void OnAppEventHandler(object appEvent)
	{
		if (_model is null) return;

		switch (appEvent)
		{
			case SubscribeRoleUseCase.UpdatedEvent updatedEto:
				await FetchAsync();
				await UpdateViewAsync();
				break;

			case SubscribeRoleUseCase.DeletedEvent deletedEto:
				Snackbar.Add("Просматриваемая вами роль была удалена", Severity.Warning);
				await CancelAsync();
				break;

			default:
				break;
		}
	}

	private void OnError(Exception exc)
	{
		Logger.LogError(exc, exc.Message);
		Snackbar.Add("Ошибка валидации при подписке на события", Severity.Error);
	}
	#endregion [Event Handlers]
}
