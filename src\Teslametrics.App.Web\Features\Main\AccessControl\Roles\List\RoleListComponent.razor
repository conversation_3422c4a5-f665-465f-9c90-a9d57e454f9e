@using Microsoft.AspNetCore.Components.Authorization
@attribute [StreamRendering]
@inherits InteractiveBaseComponent<RoleListComponent>
<div class="d_contents">
	<MudDataGrid T="RoleListItemDto"
				 Items="@Items"
				 MultiSelection="true"
				 Filterable="false"
				 SortMode="SortMode.None"
				 QuickFilter="@_quickFilter"
				 Outlined="false"
				 Elevation="0"
				 Loading="IsLoading"
				 Virtualize="true"
				 FixedHeader="true"
				 Height="100%"
				 CurrentPage="CurrentPage"
				 RowsPerPage="Limit"
				 Striped="true"
				 Hover="true"
				 RowsPerPageChanged="RowsPerPageChanged"
				 RowClick="Select">
		<ToolBarContent>
			<MudText Typo="Typo.h6">Пользовательские роли</MudText>
			<MudSpacer />
			<MudStack Row="true"
					  AlignItems="AlignItems.Center"
					  Justify="Justify.Center">
				<MudTextField @bind-Value="_searchString"
							  Placeholder="Поиск"
							  Label="Поиск"
							  Adornment="Adornment.Start"
							  AdornmentIcon="@Icons.Material.Filled.Search"
							  IconSize="Size.Medium"
							  Immediate="true"
							  Class="mb-0" />
				<AuthorizeView Policy="@AppPermissions.Main.AccessControl.Roles.Create.GetEnumPermissionString()"
							   Resource="new PolicyRequirementResource(OrganizationId)"
							   Context="innerContext">
					<MudButton OnClick="CreateRole"
							   Color="Color.Primary"
							   Variant="Variant.Outlined">
						Добавить роль
					</MudButton>
				</AuthorizeView>
				<AuthorizeView Policy="@AppPermissions.Main.AccessControl.Users.Create.GetEnumPermissionString()"
							   Resource="new PolicyRequirementResource(OrganizationId)"
							   Context="innerContext">
					<MudButton OnClick="CreateUser"
							   Color="Color.Primary"
							   Variant="Variant.Outlined">
						Добавить пользователя
					</MudButton>
				</AuthorizeView>
			</MudStack>
		</ToolBarContent>
		<Columns>
			<PropertyColumn Property="x => x.Name"
							Title="Название роли" />
			<TemplateColumn CellClass="d-flex justify-end">
				<HeaderTemplate>
					<MudSpacer />
					<MudTooltip Text="@($"Время последнего обновления: {_lastRefreshTime.ToLocalTime()}")"
								Arrow="true"
								Placement="Placement.Left">
						<MudIconButton OnClick="RefreshAsync"
									   Icon="@Icons.Material.Outlined.Refresh" />
					</MudTooltip>
				</HeaderTemplate>
				<CellTemplate>
					<MudButtonGroup Color="Color.Primary"
									Variant="Variant.Outlined">
						<AuthorizeView Policy="@AppPermissions.Main.AccessControl.Roles.Update.GetEnumPermissionString()"
									   Resource="new PolicyRequirementResource(OrganizationId, context.Item.Id)"
									   Context="innerContext">
						   <Authorized>
								<MudButton OnClick="() => Edit(context.Item)">Редактировать</MudButton>
						   </Authorized>
						   <NotAuthorized>
								<MudButton OnClick="() => Select(context.Item)">Просмотр роли</MudButton>
						   </NotAuthorized>
						</AuthorizeView>
						<MudMenu Icon="@Icons.Material.Filled.ArrowDropDown"
								 Style="align-self: auto;">
							<MudMenuItem OnClick="() => Select(context.Item)">Просмотр</MudMenuItem>
							<AuthorizeView Policy="@AppPermissions.Main.AccessControl.Roles.Update.GetEnumPermissionString()"
										   Resource="new PolicyRequirementResource(OrganizationId, context.Item.Id)"
										   Context="innerContext">
								<MudMenuItem OnClick="() => Edit(context.Item)"
											 Icon="@Icons.Material.Outlined.Edit">Редактировать</MudMenuItem>
							</AuthorizeView>
							<AuthorizeView Policy="@AppPermissions.Main.AccessControl.Roles.Delete.GetEnumPermissionString()"
										   Resource="new PolicyRequirementResource(OrganizationId, context.Item.Id)"
										   Context="innerContext">
								<MudDivider />
								<MudMenuItem OnClick="() => Delete(context.Item)"
											 Icon="@Icons.Material.Outlined.Delete"
											 IconColor="Color.Warning">Удалить</MudMenuItem>
							</AuthorizeView>
						</MudMenu>
					</MudButtonGroup>
				</CellTemplate>
			</TemplateColumn>
		</Columns>
		<NoRecordsContent>
			<MudStack Class="mud-width-full"
					  AlignItems="AlignItems.Center"
					  Justify="Justify.Center">
				<NoRolesFoundComponent />
				<MudButton OnClick="RefreshAsync"
						   Variant="Variant.Filled"
						   Color="Color.Primary">Обновить</MudButton>
			</MudStack>
		</NoRecordsContent>
		<PagerContent>
			<MudDataGridPager T="RoleListItemDto"
							  InfoFormat="{first_item}-{last_item} из {all_items}"
							  RowsPerPageString="Строк на страницу:" />
		</PagerContent>
	</MudDataGrid>
</div>