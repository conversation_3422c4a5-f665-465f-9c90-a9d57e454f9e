using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using Microsoft.Extensions.Logging;
using MQTTnet;
using MQTTnet.Protocol;

namespace Teslametrics.App.Web.Services.Mqtt
{
    /// <summary>
    /// Исключение, возникающее при превышении времени ожидания ответа RPC
    /// </summary>
    public class MqttRpcTimeoutException : Exception
    {
        public MqttRpcTimeoutException() : base("RPC response timeout exceeded") { }
        public MqttRpcTimeoutException(string message) : base(message) { }
    }

    /// <summary>
    /// Исключение, возникающее при ошибке на стороне RPC сервера
    /// </summary>
    public class MqttRpcException : Exception
    {
        public string RpcMessage { get; }
        public int Code { get; }
        public new object? Data { get; }

        public MqttRpcException(string message, int code, object? data)
            : base($"{message} [Code: {code}]")
        {
            RpcMessage = message;
            Code = code;
            Data = data;
        }
    }

    /// <summary>
    /// Класс для представления асинхронного результата RPC вызова с поддержкой таймаута
    /// </summary>
    public class TaskCompletionSourceWithTimeout<T> : TaskCompletionSource<T>
    {
        private CancellationTokenSource? _cts;

        /// <summary>
        /// Идентификатор запроса
        /// </summary>
        public int RequestId { get; }

        /// <summary>
        /// Создает новый экземпляр TaskCompletionSourceWithTimeout
        /// </summary>
        /// <param name="requestId">Идентификатор запроса</param>
        public TaskCompletionSourceWithTimeout(int requestId) : base(TaskCreationOptions.RunContinuationsAsynchronously)
        {
            RequestId = requestId;
        }

        /// <summary>
        /// Устанавливает таймаут для ожидания результата
        /// </summary>
        /// <param name="timeout">Время ожидания</param>
        public void SetTimeout(TimeSpan timeout)
        {
            _cts?.Dispose();
            _cts = new CancellationTokenSource();

            _cts.CancelAfter(timeout);
            _cts.Token.Register(() =>
            {
                TrySetException(new MqttRpcTimeoutException());
                _cts.Dispose();
                _cts = null;
            }, useSynchronizationContext: false);
        }

        /// <summary>
        /// Отменяет таймаут
        /// </summary>
        public void CancelTimeout()
        {
            _cts?.Cancel();
            _cts?.Dispose();
            _cts = null;
        }

        /// <summary>
        /// Устанавливает результат и отменяет таймаут
        /// </summary>
        public new bool TrySetResult(T result)
        {
            CancelTimeout();
            return base.TrySetResult(result);
        }

        /// <summary>
        /// Устанавливает исключение и отменяет таймаут
        /// </summary>
        public new bool TrySetException(Exception exception)
        {
            CancelTimeout();
            return base.TrySetException(exception);
        }

        /// <summary>
        /// Устанавливает отмену и отменяет таймаут
        /// </summary>
        public new bool TrySetCanceled()
        {
            CancelTimeout();
            return base.TrySetCanceled();
        }
    }

    /// <summary>
    /// Модель ошибки RPC
    /// </summary>
    public class MqttRpcError
    {
        [JsonPropertyName("message")]
        public string? Message { get; set; }

        [JsonPropertyName("code")]
        public int Code { get; set; }

        [JsonPropertyName("data")]
        public JsonElement? Data { get; set; }
    }

    /// <summary>
    /// Модель запроса RPC
    /// </summary>
    public class MqttRpcRequest
    {
        [JsonPropertyName("params")]
        public object? Parameters { get; set; }

        [JsonPropertyName("id")]
        public int Id { get; set; }
    }

    /// <summary>
    /// Модель ответа RPC
    /// </summary>
    public class MqttRpcResponse
    {
        [JsonPropertyName("id")]
        public int Id { get; set; }

        [JsonPropertyName("result")]
        public JsonElement? Result { get; set; }

        [JsonPropertyName("error")]
        public MqttRpcError? Error { get; set; }

        /// <summary>
        /// Десериализует JSON в объект ответа
        /// </summary>
        public static MqttRpcResponse FromJson(string json)
        {
            var options = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            };

            return JsonSerializer.Deserialize<MqttRpcResponse>(json, options) ??
                   throw new InvalidOperationException("Failed to deserialize JSON response");
        }
    }

    /// <summary>
    /// Клиент для выполнения RPC вызовов через MQTT
    /// </summary>
    public class MqttRpcClient
    {
        private readonly IMqttClient _client;
        private int _requestCounter = 0;
        private readonly SemaphoreSlim _counterLock = new SemaphoreSlim(1, 1);
        private readonly Dictionary<(string, string, string, int), TaskCompletionSourceWithTimeout<JsonElement?>> _pendingRequests =
            new Dictionary<(string, string, string, int), TaskCompletionSourceWithTimeout<JsonElement?>>();
        private readonly HashSet<string> _subscribedTopics = new HashSet<string>();
        private readonly string _rpcClientId;
        private readonly JsonSerializerOptions _jsonOptions;
        private readonly ILogger<MqttRpcClient> _logger;

        /// <summary>
        /// Создает новый экземпляр клиента MQTT RPC
        /// </summary>
        /// <param name="client">MQTT клиент</param>
        /// <param name="logger">Логгер</param>
        public MqttRpcClient(IMqttClient client, ILogger<MqttRpcClient> logger)
        {
            _client = client ?? throw new ArgumentNullException(nameof(client));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _rpcClientId = client.Options.ClientId.Replace("/", "_");
            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                PropertyNameCaseInsensitive = true
            };

            // Подписываемся на сообщения
            _client.ApplicationMessageReceivedAsync += HandleMessageReceived;
        }

        /// <summary>
        /// Обработчик входящих MQTT сообщений
        /// </summary>
        private Task HandleMessageReceived(MqttApplicationMessageReceivedEventArgs e)
        {
            string topic = e.ApplicationMessage.Topic;

            // Проверяем, соответствует ли топик формату RPC ответа
            if (!IsRpcReplyTopic(topic))
            {
                return Task.CompletedTask;
            }

            // Разбираем топик на части
            string[] parts = topic.Split('/');
            if (parts.Length < 7) return Task.CompletedTask;

            string driverId = parts[3];
            string serviceId = parts[4];
            string methodId = parts[5];

            try
            {
                // Декодируем и парсим ответ
                string payload = Encoding.UTF8.GetString(e.ApplicationMessage.Payload);
                MqttRpcResponse response = MqttRpcResponse.FromJson(payload);

                // Находим соответствующий запрос
                var key = (driverId, serviceId, methodId, response.Id);
                lock (_pendingRequests)
                {
                    if (!_pendingRequests.TryGetValue(key, out var tcs))
                    {
                        return Task.CompletedTask;
                    }

                    // Удаляем запрос из словаря
                    _pendingRequests.Remove(key);

                    // Обрабатываем ошибку, если она есть
                    if (response.Error != null)
                    {
                        string message = response.Error.Message ?? "Unknown error";
                        int code = response.Error.Code;
                        object? data = response.Error.Data;

                        tcs.TrySetException(new MqttRpcException(message, code, data));
                        return Task.CompletedTask;
                    }

                    // Устанавливаем результат
                    tcs.TrySetResult(response.Result);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing RPC response: {Message}", ex.Message);
            }

            return Task.CompletedTask;
        }

        /// <summary>
        /// Проверяет, соответствует ли топик формату RPC ответа
        /// </summary>
        private bool IsRpcReplyTopic(string topic)
        {
            return topic.StartsWith("/rpc/v1/") && topic.EndsWith($"/{_rpcClientId}/reply");
        }

        /// <summary>
        /// Асинхронно вызывает RPC метод
        /// </summary>
        /// <typeparam name="T">Тип результата</typeparam>
        /// <param name="driver">Идентификатор драйвера</param>
        /// <param name="service">Идентификатор сервиса</param>
        /// <param name="method">Имя метода</param>
        /// <param name="parameters">Параметры вызова</param>
        /// <param name="timeout">Таймаут ожидания ответа</param>
        /// <returns>Результат вызова</returns>
        public async Task<T?> CallAsync<T>(string driver, string service, string method, object? parameters = null, TimeSpan? timeout = null)
        {
            // Получаем ID запроса и создаем TaskCompletionSource
            int requestId = await GetNextRequestIdAsync();
            var tcs = new TaskCompletionSourceWithTimeout<JsonElement?>(requestId);

            // Устанавливаем таймаут, если указан
            if (timeout.HasValue)
            {
                tcs.SetTimeout(timeout.Value);
            }

            // Формируем топик
            string requestTopic = $"/rpc/v1/{driver}/{service}/{method}/{_rpcClientId}";
            string replyTopic = $"{requestTopic}/reply";

            // Подписываемся на ответ, если еще не подписаны
            await EnsureTopicSubscriptionAsync(replyTopic);

            // Сохраняем запрос в словаре ожидающих ответа
            var requestKey = (driver, service, method, requestId);
            lock (_pendingRequests)
            {
                _pendingRequests[requestKey] = tcs;
            }

            try
            {
                // Создаем объект запроса
                var request = new MqttRpcRequest
                {
                    Parameters = parameters,
                    Id = requestId
                };

                // Сериализуем запрос
                string jsonPayload = JsonSerializer.Serialize(request, _jsonOptions);

                // Отправляем запрос
                var message = new MqttApplicationMessageBuilder()
                    .WithTopic(requestTopic)
                    .WithPayload(jsonPayload)
                    .WithQualityOfServiceLevel(MqttQualityOfServiceLevel.AtLeastOnce)
                    .WithRetainFlag(false)
                    .Build();

                await _client.PublishAsync(message);

                // Ожидаем результат
                var result = await tcs.Task;

                // Десериализуем результат, если он есть
                if (result.HasValue)
                {
                    return JsonSerializer.Deserialize<T>(result.Value.GetRawText(), _jsonOptions);
                }

                return default;
            }
            catch (MqttRpcTimeoutException)
            {
                // Удаляем запрос при таймауте
                lock (_pendingRequests)
                {
                    _pendingRequests.Remove(requestKey);
                }
                throw;
            }
            catch (Exception ex)
            {
                // Удаляем запрос при ошибке
                lock (_pendingRequests)
                {
                    _pendingRequests.Remove(requestKey);
                }

                // Если это не MqttRpcException, оборачиваем в нее
                if (ex is not MqttRpcException)
                {
                    throw new MqttRpcException($"Error executing RPC call: {ex.Message}", -1, null);
                }
                throw;
            }
        }

        /// <summary>
        /// Синхронно вызывает RPC метод
        /// </summary>
        public T? Call<T>(string driver, string service, string method, object? parameters = null, TimeSpan? timeout = null)
        {
            return CallAsync<T>(driver, service, method, parameters, timeout).GetAwaiter().GetResult();
        }

        /// <summary>
        /// Получает следующий ID запроса
        /// </summary>
        private async Task<int> GetNextRequestIdAsync()
        {
            await _counterLock.WaitAsync();
            try
            {
                _requestCounter++;
                return _requestCounter;
            }
            finally
            {
                _counterLock.Release();
            }
        }

        /// <summary>
        /// Обеспечивает подписку на указанный топик
        /// </summary>
        private async Task EnsureTopicSubscriptionAsync(string topic)
        {
            lock (_subscribedTopics)
            {
                if (_subscribedTopics.Contains(topic))
                {
                    return;
                }

                _subscribedTopics.Add(topic);
            }

            var topicFilter = new MqttTopicFilterBuilder()
                .WithTopic(topic)
                .WithQualityOfServiceLevel(MqttQualityOfServiceLevel.AtLeastOnce)
                .Build();

            await _client.SubscribeAsync(topicFilter);
        }
    }
}