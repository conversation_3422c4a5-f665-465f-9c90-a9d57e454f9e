@using Teslametrics.App.Web.Features.Main.CameraViews.ViewFrame.Camera
@using Teslametrics.Shared
@inherits InteractiveBaseComponent
<div class="cams_list_component mud-height-full">
	<MudStack Row="true"
			  Class="pb-4 mud-width-full"
			  AlignItems="AlignItems.Center">
		@if (IsLoading)
		{
			<MudSkeleton Width="65%"
						 Height="42px" />
		}
		@if (!IsLoading && _viewResponse is not null && _viewResponse.IsSuccess)
		{
			<MudText Typo="Typo.h1"
					 Class="align-content-center">@_viewResponse.Name</MudText>
			<MudSpacer />
			<div style="height: fit-content;padding-top: 6px;">
				<TimePassedComponent InputTime="@_lastRefreshTime"
									 TooltipText="@($"Время последнего обновления: {_lastRefreshTime.ToLocalTime()}")" />
				<MudIconButton OnClick="RefreshAsync"
							   Icon="@Icons.Material.Outlined.Refresh" />
			</div>
			@if (!_subscribing && (_subscriptionResult is null || !_subscriptionResult.IsSuccess))
			{
				<MudTooltip Arrow="true"
							Placement="Placement.Start"
							Text="Ошибка подписки на события">
					<MudIconButton OnClick="SubscribeAsync"
								   Icon="@Icons.Material.Filled.ErrorOutline"
								   Color="Color.Error" />
				</MudTooltip>
			}
			<MudButton OnClick="NavigateToDetails"
					   Variant="Variant.Outlined"
					   StartIcon="@Icons.Material.Filled.PlayCircle">
				Запустить просмотр
			</MudButton>
			<MudMenu Icon="@Icons.Material.Filled.MoreVert"
					 AriaLabel="Действия с камерой"
					 Variant="Variant.Outlined"
					 Color="Color.Primary">
				<MudMenuItem OnClick="NavigateToDetails"
							 Icon="@Icons.Material.Outlined.PlayCircle">Запустить просмотр
				</MudMenuItem>
				<MudMenuItem OnClick="View"
							 Icon="@Icons.Material.Outlined.PanoramaFishEye">Просмотр настроек
				</MudMenuItem>

				<AuthorizeView Policy="@AppPermissions.Main.CameraViews.Update.GetEnumPermissionString()"
							   Resource="@(new PolicyRequirementResource(OrganizationId, ViewId))"
							   Context="editContext">
					<MudMenuItem OnClick="Edit"
								 IconColor="Color.Warning"
								 Icon="@Icons.Material.Outlined.Edit">Редактирование настроек
					</MudMenuItem>
				</AuthorizeView>
				<AuthorizeView Policy="@AppPermissions.Main.CameraViews.Delete.GetEnumPermissionString()"
							   Resource="@(new PolicyRequirementResource(OrganizationId, ViewId))"
							   Context="deleteContext">
					<MudDivider />
					<MudMenuItem OnClick="Delete"
								 IconColor="Color.Error"
								 Icon="@Icons.Material.Outlined.Delete">Удаление вида
					</MudMenuItem>
				</AuthorizeView>
			</MudMenu>
		}
		@if (!IsLoading && (_viewResponse is null || !_viewResponse.IsSuccess))
		{
			<MudText Typo="Typo.h1"
					 Class="align-content-center">Ошибка загрузки вида</MudText>
			<MudSpacer />
			<div style="height: fit-content;padding-top: 6px;">
				<TimePassedComponent InputTime="@_lastRefreshTime"
									 TooltipText="@($"Время последнего обновления: {_lastRefreshTime.ToLocalTime()}")" />
				<MudIconButton OnClick="RefreshAsync"
							   Icon="@Icons.Material.Outlined.Refresh" />
			</div>
		}
	</MudStack>

	@if (_viewResponse is not null && _viewResponse.IsSuccess && _viewResponse.Cells.Count > 0)
	{
		<div class="grid_container overflow-hidden mb-n4 mud-height-full">
			<div class="@($"grid {_viewResponse.GridType.ToString()}")"
				 style="@($"--cols: {_viewResponse?.ColumnCount};--rows: {_viewResponse?.RowCount};")">
				@foreach (var cell in _cells.OrderBy(x => x.CellIndex))
				{
					<div @key="cell">
						<CameraComponent CameraId="@cell.CameraId"
										 OrganizationId="@OrganizationId" />
					</div>
				}
			</div>
		</div>

	}
	@if (_viewResponse is not null && _viewResponse.IsSuccess && _viewResponse.Cells.Count == 0)
	{
		<MudStack AlignItems="AlignItems.Center">
			<MudText Typo="Typo.subtitle1">Нет элементов</MudText>
			<MudText Typo="Typo.body1">Добавьте элементы и попробуйте снова</MudText>
			<MudText Typo="Typo.body2">Время последнего обновления: @_lastRefreshTime.ToLocalTime()</MudText>

			<MudButton OnClick="RefreshAsync"
					   Variant="Variant.Filled"
					   Color="Color.Primary"
					   StartIcon="@Icons.Material.Outlined.Refresh">Обновить</MudButton>
		</MudStack>
	}
	@if (_viewResponse is not null && _viewResponse.Result == GetViewUseCase.Result.ViewNotFound)
	{
		<MudStack AlignItems="AlignItems.Center">
			<MudText Typo="Typo.subtitle1">Вид не найден
			</MudText>
			<MudText Typo="Typo.body1">Данный вид не существует</MudText>
			<MudText Typo="Typo.body2">Время последнего обновления: @_lastRefreshTime.ToLocalTime()</MudText>

			<MudButton OnClick="RefreshAsync"
					   Variant="Variant.Filled"
					   Color="Color.Primary"
					   StartIcon="@Icons.Material.Outlined.Refresh">Обновить</MudButton>
		</MudStack>
	}
	@if (_viewResponse is not null && _viewResponse.Result != GetViewUseCase.Result.ViewNotFound &&
			!_viewResponse.IsSuccess)
	{
		<MudStack AlignItems="AlignItems.Center">
			<MudText Typo="Typo.subtitle1">Вид не найден
			</MudText>
			<MudText Typo="Typo.body1">Нет удалось получить вид из-за внутренней ошибки</MudText>
			<MudText Typo="Typo.body2">Время последнего обновления: @_lastRefreshTime.ToLocalTime()</MudText>

			<MudButton OnClick="RefreshAsync"
					   Variant="Variant.Filled"
					   Color="Color.Primary"
					   StartIcon="@Icons.Material.Outlined.Refresh">Обновить</MudButton>
		</MudStack>
	}
</div>