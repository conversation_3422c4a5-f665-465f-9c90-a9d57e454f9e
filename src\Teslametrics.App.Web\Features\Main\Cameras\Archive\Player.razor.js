﻿const CONFIG = {
	PLAYER: {
		MAX_RETRIES: 5, // максимальное количество попыток запроса каталога в случае ошибок
		RETRY_DELAY: 2000, // задержка между повторными попытками
		BACKOFF_MULTIPLIER: 2 // множитель для увеличения задержки между повторными попытками
	},

	DURATION: {
		ZOOM_IN_FACTOR: 0.75,
		ZOOM_OUT_FACTOR: 1.25,
		MIN_SCROLL_SPEED: 0.05, // At maximum zoom
		MAX_SCROLL_SPEED: 0.10, // At maximum zoom out
		MAX_VISIBLE_DURATION: 1 * 60 * 60, // 1 час в секундах
		MIN_VISIBLE_DURATION: 5 * 60, // 5 минут в секундах
		THRESHOLDS: {
			LONG: 10,    // часов
			MEDIUM: 2,   // часа
			SHORT: 0.5   // часа
		},
		STEPS: {
			THREE_HOURS: 180,    // минут
			TWO_HOURS: 120,      // минут
			ONE_HOUR: 60,        // минут
			FIFTEEN_MIN: 15,     // минут
			FIVE_MIN: 5         // минут
		}
	},

	ERRORS: {
		PLAYBACK_FAILED: 'Не удалось запустить плеер. Попробуйте снова позднее',
		NETWORK_ERROR: 'Ошибка сети. Проверьте подключение',
		CODEC_ERROR: 'Ошибка декодирования видео',
		TIMEOUT_ERROR: 'Превышено время ожидания',
		RETRY_MESSAGE: (count, max) => `Повторная попытка (${count}/${max})...`
	}
};

/**
 * Утилита для управления запросами с поддержкой отмены и троттлинга
 * @param {Function} func - Функция для выполнения
 * @param {number} limit - Минимальный интервал между вызовами в миллисекундах
 * @returns {Function} Функция с поддержкой троттлинга и отмены предыдущих запросов
 */
const createThrottledRequest = (func, limit) => {
	let inThrottle = false;
	let lastArgs = null;
	let lastThis = null;
	let lastPromise = null;
	let controller = null;

	const executeRequest = function (thisArg, args) {
		if (controller) {
			controller.abort();
		}

		controller = new AbortController();
		const signal = controller.signal;

		return func.apply(thisArg, [...args, signal])
			.catch(error => {
				if (error.name === 'AbortError') {
					return Promise.reject(error);
				}
				throw error;
			});
	};

	return function (...args) {
		if (!inThrottle) {
			inThrottle = true;
			lastPromise = executeRequest(this, args);

			setTimeout(() => {
				inThrottle = false;
				if (lastArgs) {
					const thisValue = lastThis;
					const pendingArgs = lastArgs;
					lastArgs = lastThis = null;
					lastPromise = executeRequest(thisValue, pendingArgs);
				}
			}, limit);
		} else {
			lastArgs = args;
			lastThis = this;
		}
		return lastPromise;
	};
};

const TimeUtils = {
	/**
	 * Преобразует дату в Unix timestamp (секунды)
	 * @param {Date|string} date - Дата для преобразования
	 * @returns {number} Количество секунд с начала эпохи Unix
	 */
	dateToSeconds: (date) => Math.floor(new Date(date).getTime() / 1000),

	/**
	 * Форматирует время в строку ЧЧ:ММ
	 * @param {Date} time - Дата для форматирования
	 * @returns {string} Отформатированное время
	 */
	formatTime: (time) => new Intl.DateTimeFormat('ru-RU', {
		hour: '2-digit',
		minute: '2-digit',
		hour12: false
	}).format(time),

	/**
	 * Преобразует дату в Unix timestamp (секунды)
	 * @param {Date|string} dateStringUtc - Дата для преобразования
	 * @param {number} offsetHours - Дата для преобразования
	 * @returns {string} Отформатированное время
	 */
	shiftUtcDateByOffset: (dateStringUtc, offsetHours) => {
		// Создаём объект Date (интерпретируется как UTC0, т.к. есть 'Z' на конце)
		const date = new Date(dateStringUtc);

		// Сдвигаем время на нужное количество часов
		date.setUTCHours(date.getUTCHours() + offsetHours);

		return date; // Возвращаем объект Date
	},

	isSameDay: (date1, date2) => date1.getFullYear() === date2.getFullYear() &&
		date1.getMonth() === date2.getMonth() &&
		date1.getDate() === date2.getDate()
};

const PlayerUtils = {
	/**
	 * Converts a relative or absolute URL to an absolute URL.
	 * @param {string} url - The URL to convert. Can be either relative (e.g., '/path/to/resource') or absolute (e.g., 'https://domain.com/path).
	 * @returns {URL} A URL object representing the absolute URL. If the input is already absolute, returns it as is. Otherwise, prepends the current window's origin.
	 * @example
	 * // For absolute URL
	 * getAbsoluteUrl('https://example.com/video.m3u8') // returns URL object for 'https://example.com/video.m3u8'
	 * // For relative URL
	 * getAbsoluteUrl('/streams/123/video.m3u8') // returns URL object for 'https://current-domain.com/streams/123/video.m3u8'
	 */
	getAbsoluteUrl: (url) => {
		var absoluteUriTest = new RegExp('^(?:[a-z+]+:)?//', 'i');
		let newUrl;
		if (absoluteUriTest.test(url)) {
			newUrl = new URL(url);
		} else {
			const baseUrl = `${window.location.protocol}//${window.location.host}`;
			newUrl = new URL(url, baseUrl);
		}
		return newUrl;
	}
}

/**
 * Класс для управления взаимодействием между JavaScript и Blazor компонентом
 */
class PlayerBlazorBridge {
	/**
	 * @param {DotNet.DotNetObject} objRef - Ссылка на Blazor компонент
	 */
	constructor(objRef) {
		if (!objRef) {
			throw new Error('objRef is required for Blazor interop');
		}
		this.objRef_ = objRef;
		this.methodNames_ = {
			error: 'ShowError',
			dateChanged: 'PlayerDateChangedAsync',
			timeLineChanged: 'SetCurrentTimeLineRangeAsync'
		};
	}

	/**
	 * Отправляет сообщение об ошибке в Blazor компонент
	 * @param {Error|string} error - Ошибка или текст ошибки
	 * @returns {Promise<void>}
	 */
	async sendErrorAsync(error) {
		try {
			const errorMessage = this._formatErrorMessage(error);
			await this._invokeBlazorMethodAsync(this.methodNames_.error, errorMessage);
		} catch (err) {
			console.error('Failed to send error to Blazor:', err);
		}
	}

	/**
	 * Уведомляет Blazor компонент об изменении даты воспроизведения
	 * @param {Date} currentPlaybackDate - Новая дата воспроизведения
	 * @returns {Promise<void>}
	 */
	async sendNewPlaybackDate(currentPlaybackDate) {
		try {
			if (!(currentPlaybackDate instanceof Date)) {
				throw new Error('currentPlaybackDate must be a Date object');
			}
			await this._invokeBlazorMethodAsync(this.methodNames_.dateChanged, currentPlaybackDate);
		} catch (error) {
			await this.sendErrorAsync(error);
		}
	}

	async sendNewTimeLineRangeAsync(startTime, endTime) {
		try {
			await this._invokeBlazorMethodAsync(this.methodNames_.timeLineChanged, startTime, endTime);
		} catch (error) {
			await this.sendErrorAsync(error);
		}
	}

	/**
	 * Форматирует сообщение об ошибке
	 * @private
	 * @param {Error|string} error - Ошибка или текст ошибки
	 * @returns {string} Отформатированное сообщение об ошибке
	 */
	_formatErrorMessage(error) {
		if (error instanceof Error) {
			return error.message || 'Неизвестная ошибка';
		}
		return error?.toString() || 'Неизвестная ошибка';
	}

	/**
	 * Вызывает метод Blazor компонента с обработкой ошибок
	 * @private
	 * @param {string} methodName - Имя метода
	 * @param {...any} args - Аргументы метода
	 * @returns {Promise<any>}
	 */
	async _invokeBlazorMethodAsync(methodName, ...args) {
		try {
			if (!this.objRef_) {
				throw new Error('Blazor component reference is not initialized');
			}
			return await this.objRef_.invokeMethodAsync(methodName, ...args);
		} catch (error) {
			console.error(`Failed to invoke Blazor method ${methodName}:`, error);
			throw error;
		}
	}
}

/**
 * Класс для управления временными интервалами, которые отображаются на плеере. Данные интервалы могут не соответствовать интервалам, полученным из манифеста.
 */
class IntervalManager {
	/**
	 * @typedef {Object} Interval
	 * @property {string} start - Время начала интервала
	 * @property {string} end - Время конца интервала
	 */

	/**
	 * @typedef {Object} TimePoint
	 * @property {Date} date - Дата
	 * @property {number} seconds - Время в секундах
	 */

	/**
	 * @typedef {Object} Gap
	 * @property {number} start - Начало пропуска в секундах
	 * @property {number} end - Конец пропуска в секундах
	 * @property {Date} startDate - Дата начала пропуска
	 * @property {Date} endDate - Дата конца пропуска
	 */

	/**
	 * Создает экземпляр менеджера интервалов
	 * @param {string} cameraId - ID камеры
	 */
	constructor(cameraId, utcOffset = 0) {
		/** @type {number} смещение по UTC в часах */
		this.utcOffset_ = utcOffset;
		/** @type {string} */
		this.cameraId_ = cameraId;
		/** @type {Array<Interval>} */
		this.intervals = [];
		/** @type {Array<Gap>} */
		this.gaps = []; // Добавляем хранение пропусков
		/** @type {TimePoint} */
		this.earliest = {
			date: new Date(),
			seconds: TimeUtils.dateToSeconds(new Date())
		};
		/** @type {TimePoint} */
		this.latest = {
			date: new Date(),
			seconds: TimeUtils.dateToSeconds(new Date())
		};
	}

	/**
	 * Возвращает общую длительность в секундах
	 * @returns {number} Длительность в секундах
	 */
	getTotalDurationSeconds() {
		return this.latest.seconds - this.earliest.seconds;
	}

	/**
	 * Обновляет интервалы и их границы
	 * @param {Date} starOfPeriod - Начало временного диапазона
	 * @param {Date} endOfPeriod - Конец временного диапазона
	 * @param {AbortSignal} [signal] - Сигнал для отмены запроса
	 */
	async updateIntervalsAsync(starOfPeriod, endOfPeriod, signal) {
		try {
			const response = await fetch(
				`/streams/${this.cameraId_}/gettimeline/?start=${starOfPeriod.toJSON()}&end=${endOfPeriod.toJSON()}`,
				{ signal }
			);

			if (signal?.aborted) {
				throw new DOMException('Aborted', 'AbortError');
			}

			const newIntervals = await response.json();

			if (signal?.aborted) {
				throw new DOMException('Aborted', 'AbortError');
			}

			this.intervals = newIntervals.map(interval => ({
				start: new Date(interval.startTime).getTime(),
				end: new Date(interval.endTime).getTime()
			}));

			this.earliest.date = starOfPeriod;
			this.earliest.seconds = TimeUtils.dateToSeconds(starOfPeriod);
			this.latest.date = endOfPeriod;
			this.latest.seconds = TimeUtils.dateToSeconds(endOfPeriod);

			// Если интервалов нет вообще, весь период считается пропуском
			if (this.intervals.length === 0) {
				this.gaps = [{
					start: TimeUtils.dateToSeconds(starOfPeriod),
					end: TimeUtils.dateToSeconds(endOfPeriod),
					startDate: starOfPeriod,
					endDate: endOfPeriod
				}];
				return;
			}

			// Рассчитываем пропуски с учетом всего временного периода
			const gaps = [];
			let currentTime = TimeUtils.dateToSeconds(starOfPeriod);

			// Проверяем пропуск в начале, если первый интервал начинается позже starOfPeriod
			const firstInterval = this.intervals[0];
			const firstIntervalStart = TimeUtils.dateToSeconds(new Date(firstInterval.start));
			if (currentTime < firstIntervalStart) {
				gaps.push({
					start: currentTime,
					end: firstIntervalStart,
					startDate: starOfPeriod,
					endDate: new Date(firstIntervalStart * 1000)
				});
			}

			// Проверяем пропуски между интервалами
			for (let i = 0; i < this.intervals.length - 1; i++) {
				const currentEnd = TimeUtils.dateToSeconds(new Date(this.intervals[i].end));
				const nextStart = TimeUtils.dateToSeconds(new Date(this.intervals[i + 1].start));

				if (nextStart - currentEnd > 0) {
					gaps.push({
						start: currentEnd,
						end: nextStart,
						startDate: new Date(currentEnd * 1000),
						endDate: new Date(nextStart * 1000)
					});
				}
			}

			// Проверяем пропуск в конце, если последний интервал заканчивается раньше endOfPeriod
			const lastInterval = this.intervals[this.intervals.length - 1];
			const lastIntervalEnd = TimeUtils.dateToSeconds(new Date(lastInterval.end));
			const periodEnd = TimeUtils.dateToSeconds(endOfPeriod);
			if (lastIntervalEnd < periodEnd) {
				gaps.push({
					start: lastIntervalEnd,
					end: periodEnd,
					startDate: new Date(lastIntervalEnd * 1000),
					endDate: new Date(periodEnd * 1000)
				});
			}

			this.gaps = gaps;
		} catch (error) {
			if (error.name === 'AbortError') {
				return; // ignore aborted requests
			} else {
				throw error;
			}
		}
	}
}

class PlayerTimeManager {
	constructor() {
		this.lastPlayedAbsoluteTime = null; // Для прогресс-бара надо делить на тысячу
		this.lastUpdateTime_ = Date.now();
		this.onProgressBound_ = this.onProgress.bind(this);
	}

	/**
	 * Обработчик события progress
	 * @private
	 */
	onProgress() {
		const currentTime = Date.now();
		const timeSinceLastUpdate = currentTime - this.lastUpdateTime_; // время между обновлениями в миллисекундах
		this.lastUpdateTime_ = currentTime;

		this.lastPlayedAbsoluteTime.setTime(this.lastPlayedAbsoluteTime.getTime() + timeSinceLastUpdate);
	}

	initialize(lastPlayedAbsoluteTime) {
		this.lastUpdateTime_ = Date.now();
		this.lastPlayedAbsoluteTime = lastPlayedAbsoluteTime;
	}

	setTime(time) {
		this.lastUpdateTime_ = Date.now();
		this.lastPlayedAbsoluteTime = time;
	}
}

/**
 * Класс для управления UI элементами плеера
 */
class VideoPlayerUI {
	progressBarTooltipContainerClass_ = "vjs-progress-tooltip-container";

	constructor(player, utcOffset = 0) {
		this.utcOffset_ = utcOffset;
		this.player = player;
		this.thumbnailPreview = null;
		const seekbar_ = null;
	}

	/**
	 * Создает метку времени
	 * @param {Date} time - Время для отображения
	 * @param {number} position - Позиция в процентах
	 * @returns {HTMLElement} DOM элемент метки времени
	 */
	createTimeLabel(labelText, position, isDateChange) {
		const template = document.getElementById('player_time_label_template');
		const labelElement = template.content.cloneNode(true).firstElementChild;
		if (isDateChange) {
			labelElement.className += ' date-change-label';
		}
		labelElement.textContent = labelText;
		labelElement.style.left = `${position * 100}%`;
		return labelElement;
	}

	/**
	 * Определяет шаг временных меток
	 * @returns {number} Шаг в минутах
	 */
	determineStepMinutes() {
		const totalDurationHours = this.player.intervals_.getTotalDurationSeconds() / 3600;

		if (totalDurationHours > CONFIG.DURATION.THRESHOLDS.LONG) {
			return CONFIG.DURATION.STEPS.THREE_HOURS;
		} else if (totalDurationHours > CONFIG.DURATION.THRESHOLDS.MEDIUM) {
			return CONFIG.DURATION.STEPS.TWO_HOURS;
		} else if (totalDurationHours > CONFIG.DURATION.THRESHOLDS.SHORT) {
			return CONFIG.DURATION.STEPS.ONE_HOUR;
		} else {
			return CONFIG.DURATION.STEPS.FIFTEEN_MIN;
		}
	}

	/**
	 * Генерирует временные метки с округлением до ближайшего интервала в зависимости от шага
	 * @param {Date} startTime - Начало временного диапазона
	 * @param {Date} endTime - Конец временного диапазона
	 * @returns {Array<{time: Date, label: string, isDateChange: boolean}>} Массив дат для меток
	 */
	generateTimeLabels(startTime, endTime) {
		const stepMinutes = this.determineStepMinutes();
		const labels = [];

		// Округляем начальное время в зависимости от шага
		let currentTime = new Date(startTime);
		let minutes = currentTime.getMinutes();
		let roundedMinutes;

		if (stepMinutes >= 180) { // Для шага 3 часа и более - метки каждые 2 часа в XX:00
			roundedMinutes = 0;
			currentTime.setMinutes(roundedMinutes);
			// Округляем до ближайшего четного часа
			const hours = currentTime.getHours();
			currentTime.setHours(Math.floor(hours / 2) * 2);
		} else if (stepMinutes === 120) { // Для шага 2 часа - метки только в XX:00
			roundedMinutes = 0;
			currentTime.setMinutes(roundedMinutes);
		} else if (stepMinutes === 60) { // Для часового шага - метки в 00 и 30 минут
			roundedMinutes = Math.floor(minutes / 30) * 30;
			currentTime.setMinutes(roundedMinutes);
		} else if (stepMinutes === 15) { // Для 15-минутного интервала шаг между метками 5 минут
			roundedMinutes = Math.ceil(minutes / 5) * 5;
			if (roundedMinutes === 60) {
				currentTime.setHours(currentTime.getHours() + 1);
				roundedMinutes = 0;
			}
			currentTime.setMinutes(roundedMinutes);
		} else { // Для 5-минутного интервала шаг между метками 1 минута
			roundedMinutes = Math.ceil(minutes);
			if (roundedMinutes === 60) {
				currentTime.setHours(currentTime.getHours() + 1);
				roundedMinutes = 0;
			}
			currentTime.setMinutes(roundedMinutes);
		}
		currentTime.setSeconds(0, 0);

		const startTimeOffseted = TimeUtils.shiftUtcDateByOffset(startTime, this.utcOffset_);
		const endTimeOffseted = TimeUtils.shiftUtcDateByOffset(endTime, this.utcOffset_);

		// Проверяем, есть ли переход между днями в диапазоне
		const startDay = startTimeOffseted.getDate();
		const endDay = endTimeOffseted.getDate();
		const hasDayChange = startDay !== endDay;

		// Если есть переход между днями, добавляем все метки полночи между startTime и endTime
		if (hasDayChange) {
			let nextMidnight = startTimeOffseted;
			nextMidnight.setDate(startDay + 1);
			nextMidnight.setHours(0, 0, 0, 0);

			while (nextMidnight < endTimeOffseted) {
				const prevDate = TimeUtils.shiftUtcDateByOffset(nextMidnight, this.utcOffset_);
				prevDate.setDate(prevDate.getDate() - 1);

				labels.push({
					time: TimeUtils.shiftUtcDateByOffset(nextMidnight, this.utcOffset_),
					label: `${prevDate.toLocaleDateString('ru-RU')} | ${nextMidnight.toLocaleDateString('ru-RU')}`,
					isDateChange: true
				});

				nextMidnight.setDate(nextMidnight.getDate() + 1);
			}
		}

		// Генерируем обычные метки времени с учетом шага
		while (currentTime <= endTimeOffseted) {
			const hours = currentTime.getHours();
			const minutes = currentTime.getMinutes();
			let label = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;

			// Проверяем, есть ли эта метка уже в списке переходов между днями
			const isDateChange = hours === 0 && minutes === 0;
			const timeString = currentTime.toISOString();
			const alreadyHasDateChange = labels.some(l =>
				l.isDateChange && l.time.toISOString() === timeString
			);

			if (!alreadyHasDateChange) {
				labels.push({
					time: TimeUtils.shiftUtcDateByOffset(currentTime, this.utcOffset_),
					label,
					isDateChange
				});
			}

			// Определяем шаг для следующей метки
			let nextStepMinutes;
			if (stepMinutes >= 180) {
				nextStepMinutes = 120; // Для 3-часового и более шага - интервал 2 часа
			} else if (stepMinutes === 120) {
				nextStepMinutes = 60; // Для 2-часового шага - интервал 1 час
			} else if (stepMinutes === 60) {
				nextStepMinutes = 30; // Для часового шага - интервал 30 минут
			} else if (stepMinutes === 15) {
				nextStepMinutes = 5; // Для 15-минутного интервала шаг между метками 5 минут
			} else {
				nextStepMinutes = 1; // Для 5-минутного интервала шаг между метками 1 минута
			}

			currentTime = new Date(currentTime.getTime() + nextStepMinutes * 60 * 1000);
		}

		// Сортируем метки по времени
		labels.sort((a, b) => a.time - b.time);

		// Удаляем метки, выходящие за пределы диапазона
		if (labels[0] && labels[0].time < startTime) {
			labels.shift();
		}

		if (labels[labels.length - 1] && labels[labels.length - 1].time > endTime) {
			labels.pop();
		}

		return labels;
	}

	/**
	 * Обновляет отображение временных меток
	 * @param {HTMLElement} container - Контейнер для меток
	 */
	updateTimeLabels(container) {
		// Очищаем контейнер
		container.innerHTML = '';

		// Генерируем метки времени
		const labels = this.generateTimeLabels(this.player.intervals_.earliest.date, this.player.intervals_.latest.date);

		// Создаем элементы меток времени
		labels.forEach(({ time, label, isDateChange }) => {
			// Вычисляем позицию метки
			const position = this.calculateTimePosition(time);
			const labelElement2 = this.createTimeLabel(label, position, isDateChange);
			container.appendChild(labelElement2);
		});
	}

	/**
	 * Вычисляет позицию метки времени
	 * @param {Date} time - Время для вычисления позиции
	 * @returns {number} Позиция в диапазоне [0, 1]
	 */
	calculateTimePosition(time) {
		const totalDuration = this.player.intervals_.getTotalDurationSeconds() * 1000; // общая длительность в миллисекундах
		const timeDiff = time - this.player.intervals_.earliest.date;
		return timeDiff / totalDuration;
	}

	/**
	 * Обновляет визуальное отображение временных пропусков на прогресс-баре
	 * @param {Array} gaps Массив пропусков
	 * @param {number} earliest Самое раннее время в секундах
	 * @param {number} totalDuration Общая длительность в секундах
	 */
	updateGapSegments(seekBar, gaps, earliest, totalDuration) {
		if (!seekBar) return;

		// Удаляем существующие пропуски
		const existingGaps = seekBar.querySelectorAll('.vjs-gap-segment');
		existingGaps.forEach(gap => gap.remove());

		let currentTime = new Date();
		// Добавляем новые пропуски
		gaps.forEach(gap => {
			const gapStart = gap.start - earliest;
			const gapWidth = gap.end - gap.start;

			// Конвертируем время гапа в даты для сравнения
			const gapStartDate = new Date(gap.start * 1000);
			const gapEndDate = new Date(gap.end * 1000);

			// Если гап полностью в прошлом
			if (gapEndDate <= currentTime) {
				const gapElement = document.createElement('div');
				gapElement.className = 'vjs-gap-segment';

				const leftPercent = (gapStart / totalDuration) * 100;
				const widthPercent = (gapWidth / totalDuration) * 100;

				gapElement.style.left = leftPercent + '%';
				gapElement.style.width = widthPercent + '%';

				seekBar.appendChild(gapElement);
			}
			// Если гап частично в будущем
			else if (gapStartDate < currentTime && gapEndDate > currentTime) {
				// Создаем гап для прошлой части
				const pastGapElement = document.createElement('div');
				pastGapElement.className = 'vjs-gap-segment';

				const pastGapWidth = (currentTime.getTime() / 1000 - gap.start);
				const leftPercent = (gapStart / totalDuration) * 100;
				const widthPercent = (pastGapWidth / totalDuration) * 100;

				pastGapElement.style.left = leftPercent + '%';
				pastGapElement.style.width = widthPercent + '%';

				seekBar.appendChild(pastGapElement);

				// Создаем гап для будущей части
				const futureGapElement = document.createElement('div');
				futureGapElement.className = 'vjs-gap-segment gap-future';

				const futureGapStart = currentTime.getTime() / 1000 - earliest;
				const futureGapWidth = gap.end - currentTime.getTime() / 1000;
				const futureLeftPercent = (futureGapStart / totalDuration) * 100;
				const futureWidthPercent = (futureGapWidth / totalDuration) * 100;

				futureGapElement.style.left = futureLeftPercent + '%';
				futureGapElement.style.width = futureWidthPercent + '%';

				seekBar.appendChild(futureGapElement);
			}
			// Если гап полностью в будущем
			else if (gapStartDate >= currentTime) {
				const gapElement = document.createElement('div');
				gapElement.className = 'vjs-gap-segment gap-future';

				const leftPercent = (gapStart / totalDuration) * 100;
				const widthPercent = (gapWidth / totalDuration) * 100;

				gapElement.style.left = leftPercent + '%';
				gapElement.style.width = widthPercent + '%';

				seekBar.appendChild(gapElement);
			}
		});
	}

	// Проверяет, попадает ли время в пропуск
	isTimeInGap(previewTime) {
		const absolutePreviewTime = this.player.intervals_.earliest.seconds + previewTime;
		for (let gap of this.player.intervals_.gaps) {
			const gapStartSeconds = gap.start;
			const gapEndSeconds = gap.end;

			if (absolutePreviewTime >= gapStartSeconds && absolutePreviewTime <= gapEndSeconds) {
				return true;
			}
		}
		return false;
	}

	// Проверяет, попадает ли время в просмотренный промежуток
	isTimeInVieved(previewTime) {
		const absolutePreviewTime = this.player.intervals_.earliest.seconds + previewTime;
		return absolutePreviewTime * 1000 <= this.player.timeManager_.lastPlayedAbsoluteTime.getTime();
	}

	// Проверяет, попадает ли время в будущее
	isTimeInFuture(previewTime) {
		const absolutePreviewTime = new Date((this.player.intervals_.earliest.seconds + previewTime) * 1000);
		return absolutePreviewTime >= new Date();
	}

	// Обновляет состояние и классы thumbnail на основе текущего времени
	updateThumbnailState(previewTime) {
		this.thumbnailPreview.classList.remove('in_gap', 'viewed', 'future');

		if (this.isTimeInGap(previewTime)) {
			this.thumbnailPreview.classList.add('in_gap');
		}
		if (this.isTimeInVieved(previewTime)) {
			this.thumbnailPreview.classList.add('viewed');
		}
		if (this.isTimeInFuture(previewTime)) {
			this.thumbnailPreview.classList.add('future');
		}
	}

	// Create thumbnail preview element if it doesn't exist
	addThumbnailPreview(seekBar) {
		if (this.thumbnailPreview && !seekBar.querySelector('.vjs-thumbnail-preview')) {
			this.thumbnailPreview = null;
		}

		if (!this.thumbnailPreview) {
			this.thumbnailPreview = document.getElementById('player_thumbnail_preview').content.cloneNode(true).firstElementChild;
			seekBar.appendChild(this.thumbnailPreview);
		}
	}

	addToTooltipContainer(element, index = undefined) {
		var container = this.seekbar_.querySelector('.vjs_progressbar_tooltip_container');
		if (!container || element.parentNode === container) return;

		if (index = undefined) {
			container.appendChild(element);
		}
		else {
			var index = container.children.length > index ? index : container.children.length - 1;
			container.insertBefore(element, container.children[index]);
		}
		return element;
	}

	removeFromTooltipContainer(element) {
		var tooltipContainer = this.seekbar_.querySelector('.vjs_progressbar_tooltip_container');
		if (tooltipContainer && element.parentNode === tooltipContainer)
			tooltipContainer.removeChild(element);
	}

	addEventMarkersListener() {
		// Создаем тултип
		this.tooltip_ = document.getElementById('player_event_tooltip').content.cloneNode(true).firstElementChild;
		const labelElement = this.tooltip_.querySelector('.event-name');
		const timeElement = this.tooltip_.querySelector('.time');

		// При наведении курсора
		this.seekbar_.addEventListener('mouseover', event => {
			// Получаем все элементы под курсором
			const elemsUnderPointer = document.elementsFromPoint(event.clientX, event.clientY);

			// Ищем среди них тот, у которого есть класс "player-event"
			const playerEventElem = elemsUnderPointer.find(el => el.classList?.contains('player-event'));

			if (playerEventElem) {
				// Здесь ваша логика
				var eventArrayElement = Number.parseInt(playerEventElem.dataset.index);
				if (isNaN(eventArrayElement)) {
					return;
				}
				var element = this.player.events_.events[eventArrayElement];

				// Форматируем время для тултипа
				const startTimeStr = element.startTime.toLocaleString();
				const endTimeStr = element.endTime
					? element.endTime.toLocaleString()
					: 'Продолжается';

				// Заполняем данные в тултипе
				labelElement.textContent = element.name;
				timeElement.textContent = `${startTimeStr} - ${endTimeStr}`;
				this.addToTooltipContainer(this.tooltip_, 0);
			}
		});

		// При уходе курсора
		this.seekbar_.addEventListener('mouseout', event => {
			if (event.target.classList.contains('player-event')) {
				this.removeFromTooltipContainer(this.tooltip_);
			}
		});

		// При прокрутке прогресс-бара
		this.seekbar_.addEventListener('mousemove', (e) => {
			// Получаем все элементы под курсором
			const elemsUnderPointer = document.elementsFromPoint(e.clientX, e.clientY);

			// Ищем среди них тот, у которого есть класс "player-event"
			const playerEventElem = elemsUnderPointer.find(el => el.classList?.contains('player-event'));
			if (!playerEventElem) {
				this.removeFromTooltipContainer(this.tooltip_);
			}
			else {
				// Здесь ваша логика
				var eventArrayElement = Number.parseInt(playerEventElem.dataset.index);
				if (isNaN(eventArrayElement)) {
					return;
				}
				var element = this.player.events_.events[eventArrayElement];

				// Форматируем время для тултипа
				const startTimeStr = element.startTime.toLocaleString();
				const endTimeStr = element.endTime
					? element.endTime.toLocaleString()
					: 'Продолжается';

				// Заполняем данные в тултипе
				labelElement.textContent = element.name;
				timeElement.textContent = `${startTimeStr} - ${endTimeStr}`;
				this.addToTooltipContainer(this.tooltip_, 0);
			}
			// if (!elemUnderPointer || !elemUnderPointer.classList.contains('player-event')) {
			// 	this.removeFromTooltipContainer(this.tooltip_);
			// }
		});
	}

	// Обновляет превью с временем при наведении на прогресс-бар
	addProgressBarTooltipContainer(seekBar) {
		this.seekbar_ = seekBar;

		const container = document.getElementById('progressbar_tooltip_container').content.cloneNode(true).firstElementChild;
		seekBar.appendChild(container);

		this.addThumbnailPreview(container);
		this.addEventMarkersListener();

		// Add mouse move listener for thumbnail preview
		seekBar.addEventListener('mousemove', (e) => {
			const rect = seekBar.getBoundingClientRect();

			// Update tooltip position
			const thumbnailX = e.clientX - rect.left;
			container.style.left = `${thumbnailX}px`;

			// Show thumbnail
			container.style.visibility = 'visible';
		});

		// Add mouse move listener for thumbnail preview
		seekBar.addEventListener('mousemove', (e) => {
			const rect = seekBar.getBoundingClientRect();
			const clickedPercent = (e.clientX - rect.left) / rect.width;
			let previewTime = this.player.intervals_.getTotalDurationSeconds() * clickedPercent;

			// Update preview time with date and time
			const previewDate = new Date(this.player.intervals_.earliest.date.getTime() + (previewTime * 1000));
			this.thumbnailPreview.querySelector('.vjs-preview-time').textContent = previewDate.toLocaleString();

			// Update thumbnail state based on gaps
			this.updateThumbnailState(previewTime);
		});

		// Hide thumbnail on mouse leave
		seekBar.addEventListener('mouseleave', () => {
			container.style.visibility = 'hidden';
		});
	}

	// Обновляет превью с учетом текущей позиции мыши
	updateThumbnail() {
		if (!this.thumbnailPreview || this.thumbnailPreview.style.display === 'none' || this.thumbnailPreview.style.visibility === 'hidden') {
			return; // Не обновляем если превью скрыто или не создано
		}

		const rect = this.seekbar_.getBoundingClientRect();
		const thumbnailX = parseFloat(this.seekbar_.querySelector('.vjs_progressbar_tooltip_container').style.left);
		const clickedPercent = thumbnailX / rect.width;

		let previewTime = this.player.intervals_.getTotalDurationSeconds() * clickedPercent;

		// Update preview time with date and time
		const previewDate = new Date(this.player.intervals_.earliest.date.getTime() + (previewTime * 1000));
		this.thumbnailPreview.querySelector('.vjs-preview-time').textContent = previewDate.toLocaleString();

		// Update thumbnail state based on gaps
		this.updateThumbnailState(previewTime);
	}

	addBitrateComponent() {
		if (this.player.player.getChild('BitrateComponent')) {
			return;
		}

		const bitrateComponent = this.player.player.addChild('BitrateComponent', {});
		const playerElement = this.player.player.el();

		// Добавляем компонент к главному контейнеру плеера
		playerElement.appendChild(bitrateComponent.el());
	}

	/**
	 * Обновляет отображение событий на временной линии
	 * @param {HTMLElement} container - Контейнер для меток
	 */
	updateEventMarkers() {
		const eventMarkerContainer = document.getElementById('player_event_timeline').content.cloneNode(true).firstElementChild;

		// Очистка старых меток перед добавлением новых
		const oldContainer = this.seekbar_.querySelector('.event_timeline');
		if (oldContainer) oldContainer.remove();
		this.seekbar_.appendChild(eventMarkerContainer);
		//this.seekbar_.querySelectorAll('.player-event').forEach(el => el.remove());

		const events = this.player.events_.getEventsInRange();

		for (const [idx, event] of events.entries()) {
			const startPosition = this.calculateTimePosition(event.startTime);
			const endPosition = event.endTime ?
				this.calculateTimePosition(event.endTime) :
				1; // Если нет даты окончания, растягиваем до конца


			var width = (endPosition - startPosition) * 100;
			var left = startPosition * 100;

			if (left <= 0 && width + left <= 0) {
				continue; // Элемент полностью за пределами видимой области
			}

			if (left <= 0) {
				width = width + left;
				left = 0;
			}

			if (width + left >= 100) {
				// Создаем элемент события
				const eventElement = document.getElementById('player_event_template').content.cloneNode(true).firstElementChild;

				width = 100 - left;
				eventElement.style.width = `${width}%`;
				eventElement.style.left = `${left}%`;
				eventElement.dataset.index = idx;

				eventMarkerContainer.appendChild(eventElement);
				break;
			}
			else {
				// Создаем элемент события
				const eventElement = document.getElementById('player_event_template').content.cloneNode(true).firstElementChild;

				eventElement.style.width = `${width}%`;
				eventElement.style.left = `${left}%`;
				eventElement.dataset.index = idx;

				eventMarkerContainer.appendChild(eventElement);
			}
		}
	}

	updateEventTooltip(e) {
		const elemUnderPointer = document.elementFromPoint(e.clientX, e.clientY);

		if (!elemUnderPointer || !elemUnderPointer.classList.contains('player-event')) {
			this.removeFromTooltipContainer(this.tooltip_);
		}
	}

	updateEventTooltip() {
		if (!this.tooltip_ || this.tooltip_.style.visibility === 'hidden') {
			return; // Не обновляем если подсказка скрыта или не создана
		}

		if (this.seekbar_.querySelector('.player-event')) {
			this.removeFromTooltipContainer(this.tooltip_);
		}
	}
}

/**
 * Основной класс для управления видеоплеером
 */
class VideoPlayer {
	/**
	 * @param {string} playerId - ID of the video player element
	 * @param {object} blazorPlayerBridge - Blazor player bridge
	 * @param {string} cameraId - ID of the camera
	 */
	constructor(playerId, blazorPlayerBridge, cameraId, utcOffset) {
		// Input validation
		if (!playerId || typeof playerId !== 'string') {
			throw new Error('playerId must be a non-empty string');
		}
		if (!blazorPlayerBridge) {
			throw new Error('BlazorPlayerBridge is required');
		}
		if (!cameraId || typeof cameraId !== 'string') {
			throw new Error('cameraId must be a non-empty string');
		}

		// Player-related properties
		this.playerId_ = playerId;
		this.player_ = null;
		this.blazorPlayerBridge_ = blazorPlayerBridge;

		// Camera-related properties
		this.cameraId_ = cameraId;
		this.intervals_ = new IntervalManager(cameraId, utcOffset);
		this.events_ = new EventManager(cameraId);
		this.utcOffset_ = utcOffset;

		// UI-related properties
		this.ui_ = null;

		// State properties
		this.retryCount_ = 0;

		// Range properties
		this.startRange_ = null;
		this.endRange_ = null;
		this.rangesIsLoading_ = false;

		// Time management
		this.timeManager_ = new PlayerTimeManager();

		// Bind event handlers
		this._onLoadedMetadataHandlerAsync = this._onLoadedMetadataHandlerAsync.bind(this);
		this._onTimeUpdateHandlerAsync = this._onTimeUpdateHandlerAsync.bind(this);
		this._onProgressHandlerAsync = this._onProgressHandlerAsync.bind(this);
		this._onErrorHandlerAsync = this._onErrorHandlerAsync.bind(this);
		this._onReadyHandlerAsync = this._onReadyHandlerAsync.bind(this);
		this.updateTimeLineRangesAsync = this.updateTimeLineRangesAsync.bind(this);
	}

	/**
	 * Инициализирует плеер
	 * @param {string} newSource - URL источника видео
	 * @param {date} selectedDate - день просмотра
	 */
	async initialize(newSource, selectedDate) {
		this.src_ = newSource;
		this.selectedDate_ = selectedDate;

		// Получем первый доступный интервал в выбранной дате
		let targetTime = new Date(selectedDate);
		let { startRange } = await this.fetchRangesAsync(targetTime);

		if (!startRange || !TimeUtils.isSameDay(startRange.start, targetTime)) {
			targetTime = new Date(selectedDate);
			targetTime.setHours(0, 0, 0, 0);
			({ startRange } = await this.fetchRangesAsync(targetTime));
			targetTime = new Date(startRange.start);
		}
		else {
			targetTime = new Date(startRange.start);
		}

		let endOfPeriod = new Date(targetTime);
		endOfPeriod.setSeconds(endOfPeriod.getSeconds() + CONFIG.DURATION.MAX_VISIBLE_DURATION);

		await this.intervals_.updateIntervalsAsync(targetTime, endOfPeriod);
		await this.events_.updateEventsAsync(targetTime, endOfPeriod);

		this.timeManager_.initialize(new Date(this.intervals_.intervals[0].start)); // Отслеживаем абсолютное время воспроизведения

		const existingPlayer = videojs.getPlayer(this.playerId_);
		if (existingPlayer) {
			this.player = existingPlayer;
			await this.resetPlayer(newSource, false);

			return;
		}

		await this.createPlayer(newSource, this.utcOffset_);
		await this.blazorPlayerBridge_.sendNewTimeLineRangeAsync(targetTime, endOfPeriod);
	}

	/**
	 * Создает новый экземпляр плеера
	 * @private
	 */
	async createPlayer(newSource) {
		this.player = window.videojs(this.playerId_, {
			fluid: true,
			autoplay: false,
			liveui: true, // Включает Live UI для отображения полосы прокрутки
			controls: true,
			inactivityTimeout: 0,// делаем так, чтобы прогес-бар не скрывался
			controlBar: {
				progressControl: false, // Скрываем оригинальный прогресс бар
				children: [
					'playToggle',
					{
						name: 'ToCurrentTimeComponent',
						videoPlayer: this
					},
					'volumePanel',
					{
						name: 'TimerComponent',
						timeManager: this.timeManager_
					},
					// Добавляем свой кастомный прогресс бар
					{
						name: 'MyCustomProgressBar',
						intervalsManager: this.intervals_,
						timeManager: this.timeManager_
					},
					'fullscreenToggle'
				]
			},
			html5: {
				vhs: {
					overrideNative: true,
					enableLowInitialPlaylist: true,
					useBandwidthFromLocalStorage: true
				},
				nativeAudioTracks: false,
				nativeVideoTracks: false
			},
			sources: [{
				src: newSource,
				type: 'application/x-mpegURL'
			}]
		});

		this.ui = new VideoPlayerUI(this);

		// Add thumbnail preview functionality
		this.ui.addProgressBarTooltipContainer(this.player.el().querySelector('.vjs-my-progress-bar'));

		this.updateAvaliableRangesAsync();

		this.setupPlayerHandlers.bind(this);

		this.addGaps();

		this.setupPlayerHandlers();
	}

	/**
	 * Сбрасывает состояние существующего плеера
	 * @private
	 */
	async resetPlayer(newSource) {
		this.player.reset();
		this.player.src(newSource);
		this.player.load();

		// Add thumbnail preview functionality
		this.ui.addProgressBarTooltipContainer(this.player.el().querySelector('.vjs-my-progress-bar'));

		await new Promise(resolve => {
			this.player.ready(() => {
				this.player.play().catch(console.error);
				/*if (isLive) {
					this.player.seekToLiveEdge();
				}*/
				resolve();
			});
		});

		this.ui = new VideoPlayerUI(this);
	}

	getCurrentTime() {
		return this.timeManager_.lastPlayedAbsoluteTime;
	}

	async setTimeAsync(time) {
		let targetTime = new Date(time);
		let { startRange } = await this.fetchRangesAsync(targetTime);

		if (!startRange || !TimeUtils.isSameDay(startRange.start, targetTime)) {
			targetTime = new Date(time);
			targetTime.setHours(0, 0, 0, 0);
			({ startRange } = await this.fetchRangesAsync(targetTime));
			targetTime = new Date(startRange.start);
		}
		else {
			targetTime = new Date(startRange.start);
		}

		await this.changeUrlAsync(targetTime);

		let duration = this.intervals_.getTotalDurationSeconds();
		let startTime = new Date(targetTime.getTime() - duration / 2 * 1000);
		let endTime = new Date(targetTime.getTime() + duration / 2 * 1000);

		await this.intervals_.updateIntervalsAsync(startTime, endTime);
		await this.events_.updateEventsAsync(startTime, endTime);

		// Обновляем пропуски на прогресс-баре
		const seekBar = this.player.el().querySelector('.vjs-my-progress');
		if (seekBar) {
			this.ui.updateGapSegments(
				seekBar,
				this.intervals_.gaps,
				this.intervals_.earliest.seconds,
				this.intervals_.getTotalDurationSeconds()
			);
		}

		// Обновляем прогресс-бар
		const progressBar = this.player.controlBar.MyCustomProgressBar;
		if (progressBar) {
			progressBar.update();
		}

		let timeLabelsContainer = this.player.el().querySelector('.vjs-time-labels-container');
		if (timeLabelsContainer) {
			// Обновляем метки времени
			this.ui.updateTimeLabels(timeLabelsContainer);
			this.ui.updateEventMarkers();
			this.ui.updateThumbnail();
		}
	}

	/**
	 * Настраивает обработчики событий плеера. Вызывать при установке src плееру, т.к. обработчики могут быть удалены
	 * @private
	 */
	setupPlayerHandlers() {
		clearInterval(this._timerId);

		// Снимаем обработчики, если он уже были установлены
		this.player.off('loadedmetadata', this._onLoadedMetadataHandlerAsync);
		this.player.off('timeupdate', this._onTimeUpdateHandlerAsync);
		this.player.off('progress', this._onProgressHandlerAsync);
		this.player.off('error', this._onErrorHandlerAsync);
		this.player.off('dispose', this._onDisposeHandler);

		this.player.ready(this._onReadyHandlerAsync);

		// Устанавливаем обработчики
		this.player.on('error', this._onErrorHandlerAsync);
		this.player.on('loadedmetadata', this._onLoadedMetadataHandlerAsync);
		this.player.on('timeupdate', this._onTimeUpdateHandlerAsync);
		this.player.on('progress', this._onProgressHandlerAsync);
		this.player.on('dispose', this._onDisposeHandler);
	}

	getErrorType(error) {
		switch (error.code) {
			case 1:
				return 'aborted';
			case 2:
				return 'network';
			case 3:
				return 'decode';
			case 4:
				return 'not_supported';
			default:
				return 'unknown';
		}
	}

	/**
	 * Обработчик ошибок сети
	 */
	handleNetworkError(error) {
		console.error('Network Error:', error);
		return {
			type: 'network',
			retryable: true,
			message: CONFIG.ERRORS.NETWORK_ERROR
		};
	}

	/**
	 * Обработчик ошибок декодирования
	 */
	handleDecodeError(error) {
		console.error('Decode Error:', error);
		const isFatal = error.message?.includes('fatal');
		return {
			type: 'decode',
			retryable: !isFatal,
			message: CONFIG.ERRORS.CODEC_ERROR
		};
	}

	/**
	 * Обработчик таймаутов
	 */
	handleTimeoutError() {
		return {
			type: 'timeout',
			retryable: true,
			message: CONFIG.ERRORS.TIMEOUT_ERROR
		};
	}

	handleFatalError(errorType) {
		let errorMessage;
		switch (errorType) {
			case 'network':
				errorMessage = CONFIG.ERRORS.NETWORK_ERROR;
				break;
			case 'decode':
				errorMessage = CONFIG.ERRORS.CODEC_ERROR;
				break;
			default:
				errorMessage = CONFIG.ERRORS.PLAYBACK_FAILED;
		}

		this.blazorPlayerBridge_.sendErrorAsync(errorMessage);
		if (this.player.errorDisplay) {
			this.player.errorDisplay.open();
			this.player.errorDisplay.contentEl().innerHTML = errorMessage;
		}
	}

	updateRetryUI(retryCount) {
		if (this.player.errorDisplay) {
			this.player.errorDisplay.open();
			this.player.errorDisplay.contentEl().innerHTML = CONFIG.ERRORS.RETRY_MESSAGE(retryCount, CONFIG.PLAYER.MAX_RETRIES);
		}
	}

	async retryPlayback(source) {
		return new Promise((resolve, reject) => {
			try {
				this.player.reset();
				this.player.src(source);
				this.player.load();

				const timeoutId = setTimeout(() => {
					reject(new Error(CONFIG.ERRORS.TIMEOUT_ERROR));
				}, CONFIG.PLAYER.RETRY_DELAY);

				this.player.one("loadeddata", () => {
					clearTimeout(timeoutId);
					this.player.play()
						.then(() => {
							if (this.player.IsLive) {
								this.player.seekToLiveEdge();
							}
							resolve();
						})
						.catch(reject);
				});
			} catch (error) {
				reject(error);
			}
		});
	}

	handleMaxRetriesExceeded() {
		this.blazorPlayerBridge_.sendErrorAsync(CONFIG.ERRORS.PLAYBACK_FAILED);
		if (this.player.errorDisplay) {
			this.player.errorDisplay.open();
			this.player.errorDisplay.contentEl().innerHTML = CONFIG.ERRORS.PLAYBACK_FAILED;
		}
	}

	/**
	 * Регистрирует слушатель для пропусков
	 * @private
	 */
	addGaps() {
		const seekBar = this.player.el().querySelector('.vjs-my-progress');
		if (!seekBar) return;

		// Обновляем пропуски через VideoPlayerUI
		this.ui.updateGapSegments(
			seekBar,
			this.intervals_.gaps,
			this.intervals_.earliest.seconds,
			this.intervals_.getTotalDurationSeconds()
		);

		// Удаляем предыдущий обработчик, если он есть

		const progressBar = this.player.el().querySelector('.vjs-my-progress-bar');
		if (progressBar._clickHandler) {
			progressBar.removeEventListener('click', _clickHandler);
		}
		// Создаем новый обработчик
		const _clickHandler = async (event) => {
			let rect = seekBar.getBoundingClientRect();
			let clickedPercent = (event.clientX - rect.left) / rect.width;
			let clickedTime = this.intervals_.earliest.seconds + clickedPercent * this.intervals_.getTotalDurationSeconds();

			// Проверяем, не кликнули ли мы в будущее
			var clickedDate = new Date(clickedTime * 1000);
			var currentDay = new Date();
			if (clickedDate >= currentDay) {
				return;
			}

			// Проверяем, не кликнули ли мы в пропуск
			for (let gap of this.intervals_.gaps) {
				if (clickedTime >= gap.start && clickedTime <= gap.end) {
					clickedTime = gap.end + 0.25;
					break;
				}
			}

			await this.changeUrlAsync(new Date(clickedTime * 1000));
		};
		progressBar.addEventListener('click', _clickHandler);
	}

	/**
	 * Обновляет диапазоны на временной линии.
	 * Инициализирует плеер
	 * @param {MouseEvent} event - событие мыши, нужно, чтобы обновить подсказки
	 * @param {Date} newStartTime - начало временной линии
	 * @param {Date} newEndTime - конец временной линии
	 * @param {object} signal - cancellationToken
	 */
	async updateTimeLineRangesAsync(newStartTime, newEndTime, signal) { // TODO: Перенести в VideoPlayerUI
		try {
			await this.intervals_.updateIntervalsAsync(newStartTime, newEndTime, signal);
			await this.events_.updateEventsAsync(newStartTime, newEndTime, signal);

			// Обновляем пропуски на прогресс-баре
			const seekBar = this.player.el().querySelector('.vjs-my-progress');
			if (seekBar) {
				this.ui.updateGapSegments(
					seekBar,
					this.intervals_.gaps,
					this.intervals_.earliest.seconds,
					this.intervals_.getTotalDurationSeconds()
				);
			}

			// Обновляем прогресс-бар
			const progressBar = this.player.controlBar.MyCustomProgressBar;
			if (progressBar) {
				progressBar.update();
			}

			const timeLabelsContainer = this.player.el().querySelector('.vjs-time-labels-container');

			// Обновляем метки времени
			this.ui.updateTimeLabels(timeLabelsContainer);
			this.ui.updateEventMarkers();
			this.ui.updateThumbnail();

			await this.blazorPlayerBridge_.sendNewTimeLineRangeAsync(newStartTime, newEndTime); // хз тут ли это должно быть
		} catch (error) {
			if (error.name === 'AbortError') {
				// Игнорируем ошибки отмены
				return;
			}
			console.error('Error in throttledUpdateProgressBar:', error);
			throw error;
		}
	}

	addTimeLabels() {
		// Очистка старых меток перед добавлением новых
		const oldContainer = this.player.el().querySelector('.vjs-time-labels-wrapper');
		if (oldContainer) oldContainer.remove();

		// Создаем контейнер для кнопок и меток
		const timeLabelsWrapper = document.createElement('div');
		timeLabelsWrapper.className = 'vjs-time-labels-wrapper';

		// Добавляем временные метки
		const timeLabelsContainer = document.createElement('div');
		timeLabelsContainer.className = 'vjs-time-labels-container';

		// Добавляем все элементы в wrapper
		timeLabelsWrapper.appendChild(timeLabelsContainer);

		this.player.el().querySelector('.vjs-my-progress-bar').appendChild(timeLabelsWrapper);

		//Обновляем метки времени
		this.ui.updateTimeLabels(timeLabelsContainer);
		this.ui.updateEventMarkers();
		this.ui.updateThumbnail();

		const throttledUpdateProgressBar = createThrottledRequest(this.updateTimeLineRangesAsync, 50);

		const zoomHandler = async (e) => {
			if (!e.shiftKey) {
				return;
			}

			e.preventDefault();
			e.stopPropagation();

			const zoomFactor = e.deltaY > 0 ? CONFIG.DURATION.ZOOM_IN_FACTOR : CONFIG.DURATION.ZOOM_OUT_FACTOR;

			// Получаем текущую видимую длительность
			const currentVisibleDuration = this.intervals_.getTotalDurationSeconds();

			// Проверяем, не нарушает ли новый зум ограничения
			let newVisibleDuration = currentVisibleDuration / zoomFactor;

			if (currentVisibleDuration >= CONFIG.DURATION.MAX_VISIBLE_DURATION && zoomFactor < 1) {
				return;
			}

			if (currentVisibleDuration <= CONFIG.DURATION.MIN_VISIBLE_DURATION && zoomFactor > 1) {
				return;
			}

			if (newVisibleDuration < CONFIG.DURATION.MIN_VISIBLE_DURATION) {
				newVisibleDuration = CONFIG.DURATION.MIN_VISIBLE_DURATION;
			}

			if (newVisibleDuration > CONFIG.DURATION.MAX_VISIBLE_DURATION) {
				newVisibleDuration = CONFIG.DURATION.MAX_VISIBLE_DURATION;
			}

			// Сохраняем текущую позицию воспроизведения

			// Вычисляем новые временные границы на основе масштабирования
			var removedTimeFromPeriods = Math.floor((currentVisibleDuration - (currentVisibleDuration * zoomFactor)) / 2);

			// Определяем позицию курсора относительно прогресс-бара
			const rect = e.currentTarget.getBoundingClientRect();
			const mouseX = e.clientX - rect.left;
			const mouseRatio = mouseX / rect.width;

			// Вычисляем, как распределить изменение времени между началом и концом
			const leftRatio = mouseRatio;
			const rightRatio = 1 - mouseRatio;

			const leftChange = Math.floor(removedTimeFromPeriods * leftRatio);
			const rightChange = Math.floor(removedTimeFromPeriods * rightRatio);

			let startTime = new Date((this.intervals_.earliest.seconds - leftChange) * 1000);
			let endTime = new Date((this.intervals_.latest.seconds + rightChange) * 1000);

			await throttledUpdateProgressBar(startTime, endTime);
			this.ui.updateEventTooltip(e);
		}

		const scrollHandler = async (e) => {
			if (e.shiftKey || e.deltaY === 0) {
				return;
			}

			e.preventDefault();
			e.stopPropagation();

			const currentDuration = this.intervals_.getTotalDurationSeconds();
			const zoomPercentage = (currentDuration - CONFIG.DURATION.MIN_VISIBLE_DURATION) /
				(CONFIG.DURATION.MAX_VISIBLE_DURATION - CONFIG.DURATION.MIN_VISIBLE_DURATION);

			const scrollSpeed = CONFIG.DURATION.MIN_SCROLL_SPEED +
				(CONFIG.DURATION.MAX_SCROLL_SPEED - CONFIG.DURATION.MIN_SCROLL_SPEED) * zoomPercentage;
			const scrollDirection = e.deltaY > 0 ? scrollSpeed : -scrollSpeed;
			const { startTime, endTime } = moveTime(scrollDirection);

			await throttledUpdateProgressBar(startTime, endTime);
			this.ui.updateEventTooltip(e);
		}

		const moveTime = (direction) => {
			const stepMinutes = this.ui.determineStepMinutes();
			const timeShiftMs = direction * stepMinutes * 60 * 1000;
			const newStartTime = new Date(this.intervals_.earliest.date.getTime() + timeShiftMs);
			const newEndTime = new Date(this.intervals_.latest.date.getTime() + timeShiftMs);

			return { startTime: newStartTime, endTime: newEndTime };
		}

		document.addEventListener('keydown', async (event) => {
			const currentDuration = this.intervals_.getTotalDurationSeconds();
			const zoomPercentage = (currentDuration - CONFIG.DURATION.MIN_VISIBLE_DURATION) /
				(CONFIG.DURATION.MAX_VISIBLE_DURATION - CONFIG.DURATION.MIN_VISIBLE_DURATION);

			const scrollSpeed = CONFIG.DURATION.MIN_SCROLL_SPEED +
				(CONFIG.DURATION.MAX_SCROLL_SPEED - CONFIG.DURATION.MIN_SCROLL_SPEED) * zoomPercentage;

			let startTime = null;
			let endTime = null;

			switch (event.key) {
				case 'ArrowRight':
					var scrollDirection = scrollSpeed;
					var result = moveTime(scrollDirection);
					startTime = result.startTime;
					endTime = result.endTime;
					break;
				case 'ArrowLeft':
					var scrollDirection = -scrollSpeed;
					var result = moveTime(scrollDirection);
					startTime = result.startTime;
					endTime = result.endTime;
					break;
				default:
					return;
			}

			if (startTime && endTime) {
				await throttledUpdateProgressBar(startTime, endTime);
				this.ui.updateEventTooltip(e);
			}
		});

		// Добавляем обработчик колесика мыши для масштабирования
		timeLabelsContainer.addEventListener('wheel', zoomHandler, { passive: false });

		// Добавляем обработчик колесика мыши для горизонтальной прокрутки
		timeLabelsContainer.addEventListener('wheel', scrollHandler, { passive: false });
	}

	async changeUrlAsync(startTime = null) {
		var player = this.player;

		const currentSrc = this.src_;
		let url = PlayerUtils.getAbsoluteUrl(currentSrc);
		if (startTime) {
			// Перезапрос нового m3u8
			this.timeManager_.setTime(new Date(startTime));
			await this.updateAvaliableRangesAsync(true);

			url.searchParams.set('start', this.timeManager_.lastPlayedAbsoluteTime.toJSON());
		}

		player.reset();
		player.src({ src: url.toString(), type: 'application/x-mpegURL' }); // если будут ошибки или ещё что - запрашивать новый url. Но не работало
		await player.load();

		this.setupPlayerHandlers();

		// Начинаем воспроизведение и ожидаем события
		await player.play();
	}

	async fetchRangesAsync(date = null) {
		try {
			let requestTime = !date ? new Date(this.timeManager_.lastPlayedAbsoluteTime) : new Date(date);
			const response = await fetch(`/streams/${this.cameraId_}/getplaybackrange/?fromTime=` + requestTime.toJSON());

			if (!response.ok) {
				return { startRange: null, endRange: null };
			}

			const intervals = await response.json();

			// Проверяем наличие данных в массиве
			if (!Array.isArray(intervals) || intervals.length === 0) {
				return { startRange: null, endRange: null };
			}

			// Извлекаем первый диапазон
			const startRange = intervals[0] ? {
				start: new Date(intervals[0].startTime),
				end: new Date(intervals[0].endTime)
			} : null;

			// Извлекаем второй диапазон, если он есть
			const endRange = intervals[1] ? {
				start: new Date(intervals[1].startTime),
				end: new Date(intervals[1].endTime)
			} : null;

			return { startRange, endRange };
		} catch (error) {
			console.error('Error fetching ranges:', error);
			return { startRange: null, endRange: null };
		}
	}

	async updateAvaliableRangesAsync(force = false) {
		if (this.rangesIsLoading_ && !force) return;
		this.rangesIsLoading_ = true;

		var { startRange, endRange } = await this.fetchRangesAsync();

		this.startRange_ = startRange;
		this.endRange_ = endRange;
		this.rangesIsLoading_ = false;
	}

	async gapSkipHandlerAsync() {
		if (!this.startRange_) {
			return;
		}

		let msTimeForGapCheck = this.timeManager_.lastPlayedAbsoluteTime.getTime() + 250; // 250ms - время обновления timeUpdate. Следующая проверка через этот временной лаг.
		if (msTimeForGapCheck <= this.startRange_.end) { // Проверяем, не входит ли текущее время в пропуск
			return;
		}

		if (!this.endRange_) {
			await this.updateAvaliableRangesAsync();
			return;
		}

		if (this.timeManager_.lastPlayedAbsoluteTime.getTime() >= this.endRange_.start) { // обновляем текущие проигрываемые сегменты
			await this.updateAvaliableRangesAsync();
			return;
		}

		if (this.startRange_.end.getTime() + 1000 >= this.endRange_.start.getTime()) { // Проверяем пересечение интервалов с секундной точностью
			return;
		}

		console.log(`[Gap Detected] Текущий gap ${this.startRange_.end} - ${this.endRange_.start}`);
		await this.setTimeAsync(new Date(this.endRange_.start.getTime() + 250));
	}

	// Согласно исходникам событие вызывается с периодикой 250 ms https://github.com/videojs/video.js/blob/main/src/js/tech/tech.js#L339 (trackCurrentTime)
	_onTimeUpdateHandlerAsync = async () => {
		// await this.timeManager_.onProgressBound_();
		this.timeManager_.onProgress();
		await this.gapSkipHandlerAsync();

		const customProgress = this.player.controlBar.getChild('MyCustomProgressBar');
		await customProgress.update();

		await this._checkPlaybackDateChangeAsync();
	};

	// Обновляем при загрузке метаданных, обновлении времени, буффера и т.д.
	_onLoadedMetadataHandlerAsync = async () => {
		this.addTimeLabels();

		const customProgress = this.player.controlBar.getChild('MyCustomProgressBar');
		await customProgress.update();
	}

	_onProgressHandlerAsync = async () => {
		await this.timeManager_.onProgressBound_();

		const customProgress = this.player.controlBar.getChild('MyCustomProgressBar');
		await customProgress.update();
	}

	_onReadyHandlerAsync = async () => {
		// Меняем middleware запросов.
		this._changeStartTimeMiddleware();
		this.ui.addBitrateComponent();

		this.player.currentTime(0);

		this._timerId = setInterval(async () => {
			await this._checkCurrentPlaybackIntervalAsync(); // обновляем видимые интервалы и пропуски
		}, 2000);

	}

	/**
	 * Обработчик ошибок сети
	 */
	_onErrorHandlerAsync = async () => {
		const error = this.player.error();
		const errorType = this.getErrorType(error);

		// Логируем детали ошибки
		console.error('Video.js Error:', {
			type: errorType,
			code: error.code,
			message: error.message,
			details: error,
			timestamp: new Date().toISOString()
		});

		// Определяем тип ошибки и стратегию обработки
		let errorHandler;
		switch (errorType) {
			case 'network':
				errorHandler = this.handleNetworkError(error);
				break;
			case 'decode':
				errorHandler = this.handleDecodeError(error);
				break;
			case 'timeout':
				errorHandler = this.handleTimeoutError();
				break;
			default:
				errorHandler = {
					type: 'unknown',
					retryable: true,
					message: CONFIG.ERRORS.PLAYBACK_FAILED
				};
		}

		// Если ошибка не подлежит повторной попытке
		if (!errorHandler.retryable) {
			this.handleFatalError(errorHandler.type);
			return;
		}

		const currentSrc = this.src_;
		let retryCount = 0;
		let delay = CONFIG.PLAYER.RETRY_DELAY;

		while (retryCount < CONFIG.PLAYER.MAX_RETRIES) {
			try {
				retryCount++;
				this.updateRetryUI(retryCount);

				await new Promise(resolve => setTimeout(resolve, delay));
				delay *= CONFIG.PLAYER.BACKOFF_MULTIPLIER;

				await this.retryPlayback(currentSrc);

				console.log('Playback recovered successfully');
				return;
			} catch (retryError) {
				console.error('Retry failed:', {
					attempt: retryCount,
					error: retryError,
					timestamp: new Date().toISOString()
				});
			}
		}

		this.handleMaxRetriesExceeded();
	}

	/**
	 * Изменяет middleware для времени начала
	 * @private
	 */
	_changeStartTimeMiddleware() {
		const tech = this.player.tech({ IWillNotUseThisInPlugins: false }); // Получаем доступ к технологическому слою

		if (!tech) {
			return;
		}

		const originalXhr = tech.vhs.xhr;
		tech.vhs.xhr = (options, callback) => {
			if (!options || !options.uri) { // Тут что-то не так, пусть с этим оригинал разбирается
			}

			if (!options.uri.includes(".m3u8")) {
				return originalXhr.call(this, options, callback); //  этот файл не каталог. Вызываем оригинальный XHR
			}

			let url;
			var absoluteUriTest = new RegExp('^(?:[a-z+]+:)?//', 'i');
			if (absoluteUriTest.test(options.uri)) { // Если URI содержит только путь, преобразуем его в абсолютный URL
				url = new URL(options.uri);
			} else {
				const baseUrl = `${window.location.protocol}//${window.location.host}`;
				url = new URL(options.uri, baseUrl);
			}

			let requestTime = new Date(this.timeManager_.lastPlayedAbsoluteTime.getTime() - 200);

			url.searchParams.set('start', requestTime.toJSON());
			options.uri = url.toString();

			// Вызываем оригинальный XHR c новой URL
			return originalXhr.call(this, options, callback);
		};
	}

	_getTargetDuration() {
		if (!this.player || this.player.isDisposed_)
			return null;

		const vhs = this.player.tech({ IWillNotUseThisInPlugins: true }).vhs;
		if (vhs && vhs.playlists && vhs.playlists.media()) {
			const playlist = vhs.playlists.media();
			const lastSegment = playlist.segments[playlist.segments.length - 1];

			var totalTargetDuration = 0;
			const targetDuration = playlist.targetDuration;
			totalTargetDuration += targetDuration;

			if (lastSegment) {
				totalTargetDuration += lastSegment.duration >= targetDuration ? targetDuration : lastSegment.duration;
			}

			return totalTargetDuration;
		} else {
			console.error('Технология VHS или плейлист недоступны.');
		}

		return null;
	}

	async _checkPlaybackDateChangeAsync() {
		if (!this.prevPlaybackDate_) {
			this.prevPlaybackDate_ = this.timeManager_.lastPlayedAbsoluteTime;
			return;
		}

		if (this.prevPlaybackDate_.getDate() != this.timeManager_.lastPlayedAbsoluteTime.getDate()) {
			this.prevPlaybackDate_ = this.timeManager_.lastPlayedAbsoluteTime;
			await this.blazorPlayerBridge_.sendNewPlaybackDate(this.timeManager_.lastPlayedAbsoluteTime);
		}
	}

	async _checkCurrentPlaybackIntervalAsync() {
		if (!this.timeManager_) return; // на всякий случай

		if (this.timeManager_.lastPlayedAbsoluteTime >= this.intervals_.earliest.date
			&& this.timeManager_.lastPlayedAbsoluteTime <= this.intervals_.latest.date) {
			await this.intervals_.updateIntervalsAsync(this.intervals_.earliest.date, this.intervals_.latest.date);

			if (!this.player.el || this.player.isDisposed_) { // Если плейер не существует или уничтожен - останавливаем таймер. Кто решил, что isDisposed_ - это приватное поле?
				clearInterval(this._timerId);
			}

			// Обновляем пропуски на прогресс-баре
			const seekBar = this.player.el().querySelector('.vjs-my-progress');
			if (seekBar) {
				this.ui.updateGapSegments(
					seekBar,
					this.intervals_.gaps,
					this.intervals_.earliest.seconds,
					this.intervals_.getTotalDurationSeconds()
				);
			}
		}
	}

	_onDisposeHandler() {
		clearInterval(this._timerId);
	}
}

const Component = videojs.getComponent('Component');

class MyCustomProgressBar extends Component {
	constructor(player, options) {
		super(player, options);

		this.intervalsManager_ = options.intervalsManager;
		this.timeManager_ = options.timeManager;
	}

	createEl() {
		const el = videojs.dom.createEl('div', {
			className: 'vjs-my-progress-bar'
		});

		const fullBar = videojs.dom.createEl('div', {
			className: 'vjs-my-full-bar'
		});
		const bufferBar = videojs.dom.createEl('div', {
			className: 'vjs-my-buffer-bar'
		});
		const handle = videojs.dom.createEl('div', {
			className: 'vjs-my-handle'
		});
		const progressLine = videojs.dom.createEl('div', {
			className: 'vjs-my-progress'
		});

		fullBar.appendChild(bufferBar);
		fullBar.appendChild(handle);
		fullBar.appendChild(progressLine);
		el.appendChild(fullBar);

		return el;
	}

	update() {
		const player = this.player();
		const duration = this.intervalsManager_.getTotalDurationSeconds(); //player.duration();
		if (!duration || isNaN(duration) || duration === Infinity) {
			return; // Нет корректной длительности
		}

		// Вычисляем прогресс на основе абсолютного времени
		let playedRatio = 0;
		if (this.timeManager_ && this.timeManager_.lastPlayedAbsoluteTime) {
			const currentTimeMs = this.timeManager_.lastPlayedAbsoluteTime.getTime();
			const startTimeMs = this.intervalsManager_.earliest.date.getTime();
			playedRatio = (currentTimeMs - startTimeMs) / (duration * 1000);
		}

		const bufferBar = this.el().querySelector('.vjs-my-buffer-bar');
		if (bufferBar) {
			// Рассчитываем процент буфера и текущего времени
			const buffered = player.buffered();
			let bufferedEnd = 0;
			if (buffered && buffered.length) {
				bufferedEnd = player.bufferedEnd();
			}
			const bufferRatio = bufferedEnd / duration;

			bufferBar.style.width = (bufferRatio * 100) + '%';
		}

		const handle = this.el().querySelector('.vjs-my-handle');
		if (handle) {
			if (playedRatio < 0 || playedRatio >= 1) {
				handle.style.display = 'none';
			} else {
				handle.style.display = 'block';
				handle.style.left = (playedRatio * 100) + '%';
			}
		}
	}
}

// Регистрируем компонент
videojs.registerComponent('MyCustomProgressBar', MyCustomProgressBar);

class BitrateComponent extends Component {
	constructor(player, options) {
		super(player, options);

		// Создаем DOM элемент для отображения битрейта
		this.bitrateEl_ = videojs.dom.createEl('div', {
			className: 'vjs-current-bitrate-overlay',
			innerHTML: 'Битрейт: N/A'
		});

		this.el().appendChild(this.bitrateEl_);

		// Обновляем битрейт на событии `progress` или `loadedmetadata`
		player.on('loadedmetadata', () => this.updateBitrate());
	}

	createEl() {
		return videojs.dom.createEl('div', {
			className: 'vjs-current-bitrate-container'
		});
	}

	updateBitrate() {
		const player = this.player();
		const tech = player.tech({ IWillNotUseThisInPlugins: true });

		// Проверяем наличие VHS (Video.js HTTP Streaming)
		if (tech && tech.vhs) {
			const vhs = tech.vhs;

			// Получаем текущий битрейт
			const bitrate = vhs.stats.mediaBytesTransferred / (vhs.stats.mediaRequests || 1) * 8;

			// Обновляем текст в DOM
			this.bitrateEl_.innerHTML = `Битрейт: ${Math.round(bitrate / 1000)} кбит/с`;
		} else {
			// Если данные недоступны, отображаем "N/A"
			this.bitrateEl_.innerHTML = 'Битрейт: N/A';
		}
	}
}
// Регистрируем компонент
videojs.registerComponent('BitrateComponent', BitrateComponent);

class TimerComponent extends Component {
	constructor(player, options) {
		super(player, options);

		this.timeManager_ = options.timeManager;

		this._onTimeUpdateHandlerAsync = this._onTimeUpdateHandlerAsync.bind(this);

		player.on('timeupdate', this._onTimeUpdateHandlerAsync);
		//player.off('timeupdate', this._onTimeUpdateHandlerAsync);
		//player.on('progress', this._onTimeUpdateHandlerAsync);
		//player.off('progress', this._onTimeUpdateHandlerAsync);
	}

	createEl() {
		return videojs.dom.createEl('div', {
			className: 'vjs-current-time',
			innerHTML: 'N/A'
		});
	}

	// Согласно исходникам событие вызывается с периодикой 250 ms https://github.com/videojs/video.js/blob/main/src/js/tech/tech.js#L339 (trackCurrentTime)
	_onTimeUpdateHandlerAsync = () => {
		this.updateTextContent(this.timeManager_.lastPlayedAbsoluteTime.toLocaleTimeString());
	};

	updateTextContent(text) {
		var el = this.el();
		videojs.dom.emptyEl(el);
		videojs.dom.appendContent(el, text);
	}
}

videojs.registerComponent('TimerComponent', TimerComponent);

class ToCurrentTimeComponent extends videojs.getComponent('Button') {
	constructor(player, options) {
		super(player, options);

		this.videoPlayer_ = options.videoPlayer;
	}
	createEl() {
		const template = document.getElementById('player_to_current_time');
		return template.firstChild.cloneNode(true);
	}
	async handleClick() {
		const duration = this.videoPlayer_.intervals_.getTotalDurationSeconds(); // TODO:: Пересмотреть то, что прокидывается. Нужно прокинуть отдельно intervalsManager и VideoPlayerUI
		if (!duration || isNaN(duration) || duration === Infinity) {
			return; // Нет корректной длительности
		}

		// получить дату в будущем на N секунд
		const newEndTime = new Date(this.videoPlayer_.timeManager_.lastPlayedAbsoluteTime.getTime() + duration * 1000);

		await this.videoPlayer_.updateTimeLineRangesAsync(this.videoPlayer_.timeManager_.lastPlayedAbsoluteTime, newEndTime);
	}
}
// Register as a component, so it can be added
videojs.registerComponent('ToCurrentTimeComponent', ToCurrentTimeComponent);

/**
 * Класс для управления событиями на временной шкале плеера
 */
class EventManager {
	constructor(cameraId) {
		/** @type {string} */
		this.cameraId_ = cameraId;
		/** @type {Array<Event>} */
		this.events = [];
	}

	/**
	 * Возвращает все события в заданном временном диапазоне
	 * @returns {Array<Event>} Массив событий
	 */
	getEventsInRange() {
		return this.events;
	}

	/**
	 * Обновляет список событий для заданного временного диапазона
	 * @param {Date} startOfPeriod - Начало периода
	 * @param {Date} endOfPeriod - Конец периода
	 * @param {AbortSignal} [signal] - Сигнал для отмены запроса
	 * @returns {Promise<void>}
	 */
	async updateEventsAsync(startOfPeriod, endOfPeriod, signal) {
		try {
			const url = new URL(`events/${this.cameraId_}`, window.location.origin);
			url.searchParams.append('start', startOfPeriod.toISOString());
			url.searchParams.append('end', endOfPeriod.toISOString());

			const response = await fetch(url, {
				method: 'GET',
				headers: {
					'Accept': 'application/json'
				},
				signal
			});

			if (!response.ok) {
				this.events = [];
				return;
			}

			const events = await response.json();
			this.events = events.map(event => ({
				...event,
				startTime: new Date(event.startTime),
				endTime: event.endTime ? new Date(event.endTime) : undefined
			}));
		} catch (error) {
			if (error.name === 'AbortError') {
				console.log('Запрос событий был отменен');
				return;
			}
			console.error('Ошибка при получении событий:', error);
		}
	}
}

let videoPlayer = null;


// Экспортируемые функции как публичное API
/**
 * Останавливает воспроизведение плеера
 * @param {string} playerId - Идентификатор плеера
 */
export function stopPlayer(playerId) {
	const existingPlayer = videojs.getPlayer(playerId);
	if (existingPlayer) {
		if (videoPlayer) {
			videoPlayer = null;
		}
		existingPlayer.dispose();
	}
}

/**
 * Устанавливает время проигрывания на определённое время с сохранением длины временного промежутка
 * @param {string} playerId - Идентификатор плеера
 * @param {Date} newStartTime - Новое время начала воспроизведения
 */
export async function setTimeAsync(playerId, newStartTime) {
	const existingPlayer = videojs.getPlayer(playerId);
	if (existingPlayer) {
		let startTime = new Date(newStartTime);

		let duration = videoPlayer.intervals_.getTotalDurationSeconds();
		let endTime = new Date(startTime.getTime() + duration * 1000);

		await videoPlayer.intervals_.updateIntervalsAsync(startTime, endTime);
		await videoPlayer.events_.updateEventsAsync(startTime, endTime);

		await videoPlayer.setTimeAsync(startTime);
	}
}

/**
 * Обновляет источник видео в плеере
 * @param {object} objRef - Ссылка на объект Blazor
 * @param {string} playerId - Идентификатор плеера
 * @param {string} cameraId - Идентификатор камеры
 * @param {string} source - Новый источник видео
 * @param {Date} selectedDate - Выбранная дата просмотра
 */
export async function updatePlayer(objRef, playerId, cameraId, source, selectedDate) {
	const existingPlayer = videojs.getPlayer(playerId);
	if (existingPlayer) {
		let startTime = new Date(selectedDate);

		let duration = videoPlayer.intervals_.getTotalDurationSeconds();
		let endTime = new Date(startTime.getTime() + duration * 1000);

		await videoPlayer.intervals_.updateIntervalsAsync(startTime, endTime);
		await videoPlayer.events_.updateEventsAsync(startTime, endTime);

		await videoPlayer.setTimeAsync(startTime);
	} else {
		var blazorPlayerBridge = new PlayerBlazorBridge(objRef);
		// Create new player if none exists
		videoPlayer = new VideoPlayer(playerId, blazorPlayerBridge, cameraId);
		await videoPlayer.initialize(source, selectedDate);
	}
}

/**
 * Инициализирует видеоплеер
 * @param {object} objRef - Ссылка на объект Blazor
 * @param {string} playerId - Идентификатор плеера
 * @param {string} cameraId - Идентификатор камеры
 * @param {string} source - Источник видео
 * @param {boolean} [selectedDate] - месяц, за который будет идти просмотр
 */
export async function initializePlayer(objRef, playerId, cameraId, source, selectedDate, utcOffset) {
	var blazorPlayerBridge = new PlayerBlazorBridge(objRef);
	videoPlayer = new VideoPlayer(playerId, blazorPlayerBridge, cameraId, utcOffset);
	await videoPlayer.initialize(source, selectedDate);
}