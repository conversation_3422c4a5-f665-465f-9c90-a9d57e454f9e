﻿@using Teslametrics.App.Web.Features.Main.Incidents.Filter.City
@using Teslametrics.App.Web.Features.Main.Incidents.Filter.Building
@using Teslametrics.App.Web.Features.Main.Incidents.Filter.Floor
@using Teslametrics.App.Web.Features.Main.Incidents.Filter.Room
@using Teslametrics.App.Web.Features.Main.Incidents.Filter.Device
<MudStack Spacing="4"
          Class="pa-4">
    <MudStack Spacing="2"
              Row="true"
              StretchItems="StretchItems.All">
        <MudDatePicker Label="Дата"
                       Date="@DateFrom"
                       DateChanged="OnDateFromChanged"
                       Editable="true"
                       Mask="@(new DateMask("0000-00-00"))"
                       DateFormat="yyyy-MM-dd"
                       Placeholder="ISO Date"
                       Margin="Margin.Dense"
                       Variant="Variant.Outlined" />
        <MudTimePicker Label="Время"
                       Editable="true"
                       Time="@_timeFrom"
                       Margin="Margin.Dense"
                       TimeChanged="OnTimeFromChanged"
                       Variant="Variant.Outlined" />
        <MudTimePicker Label="До"
                       Editable="true"
                       Time="@_timeTo"
                       Margin="Margin.Dense"
                       TimeChanged="OnTimeToChanged"
                       Variant="Variant.Outlined" />
    </MudStack>
    <MudStack Spacing="2"
              Row="true"
              StretchItems="StretchItems.All">
        <CitySelectorComponent City="@CityId"
                               CityChanged="OnCityIdChanged" />
        <BuildingSelectorComponent City="@CityId"
                                   BuildingId="BuildingId"
                                   BuildingIdChanged="OnBuildingIdChanged" />
        <FloorSelectorComponent CityId="@CityId"
                                BuildingId="BuildingId"
                                Floor="FloorId"
                                FloorChanged="FloorIdChanged" />
    </MudStack>
    <MudStack Spacing="2"
              Row="true"
              StretchItems="StretchItems.All">
        <RoomSelectorComponent CityId="@CityId"
                               BuildingId="BuildingId"
                               FloorId="FloorId"
                               RoomId="RoomId"
                               RoomIdChanged="OnRoomIdChanged" />
        <DeviceSelectorComponent CityId="@CityId"
                                 BuildingId="BuildingId"
                                 FloorId="FloorId"
                                 RoomId="RoomId"
                                 DeviceId="FridgeId"
                                 DeviceIdChanged="OnFridgeIdChanged" />
    </MudStack>
    <MudStack Spacing="2"
              Row="true"
              StretchItems="StretchItems.All">
        <MudSelect T="IncidentType ?"
                   Label="Тип происшествия"
                   AnchorOrigin="Origin.BottomCenter"
                   Value="IncidentType"
                   ValueChanged="IncidentTypeChanged"
                   Variant="Variant.Outlined"
                   Margin="Margin.Dense"
                   Clearable="true">
            @foreach (IncidentType selectOption in (IncidentType[])Enum.GetValues(typeof(IncidentType)))
            {
                <MudSelectItem T="IncidentType ?"
                               Value="selectOption"
                               @key="selectOption">@selectOption.GetName()</MudSelectItem>
            }
        </MudSelect>
        <MudSelect T="bool?"
                   Clearable="true"
                   Value="@IsResolved"
                   ValueChanged="IsResolvedChanged"
                   Label="Статус"
                   Margin="Margin.Dense"
                   Variant="Variant.Outlined">
            <MudSelectItem T="bool?"
                           Value="null">Все</MudSelectItem>
            <MudSelectItem T="bool?"
                           Value="true">Решено</MudSelectItem>
            <MudSelectItem T="bool?"
                           Value="false">Не решено</MudSelectItem>
        </MudSelect>
    </MudStack>
</MudStack>
@code {
    private TimeSpan _timeFrom => DateFrom.TimeOfDay;
    private TimeSpan _timeTo => DateTo.TimeOfDay;

    [Parameter]
    public DateTime DateFrom { get; set; } = DateTime.Today;
    [Parameter]
    public EventCallback<DateTime> DateFromChanged { get; set; }

    [Parameter]
    public DateTime DateTo { get; set; } = DateTime.Today.AddDays(1).AddMilliseconds(-1);
    [Parameter]
    public EventCallback<DateTime> DateToChanged { get; set; }

    [Parameter]
    public Guid? CityId { get; set; }
    [Parameter]
    public EventCallback<Guid?> CityIdChanged { get; set; }

    [Parameter]
    public Guid? BuildingId { get; set; }
    [Parameter]
    public EventCallback<Guid?> BuildingIdChanged { get; set; }

    [Parameter]
    public Guid? FloorId { get; set; }
    [Parameter]
    public EventCallback<Guid?> FloorIdChanged { get; set; }

    [Parameter]
    public Guid? RoomId { get; set; }
    [Parameter]
    public EventCallback<Guid?> RoomIdChanged { get; set; }

    [Parameter]
    public Guid? FridgeId { get; set; }
    [Parameter]
    public EventCallback<Guid?> FridgeIdChanged { get; set; }

    [Parameter]
    public IncidentType? IncidentType { get; set; }
    [Parameter]
    public EventCallback<IncidentType?> IncidentTypeChanged { get; set; }

    [Parameter]
    public bool? IsResolved { get; set; }
    [Parameter]
    public EventCallback<bool?> IsResolvedChanged { get; set; }

    private async Task UpdateParametersAsync<T>(params (T? value, EventCallback<T?> callback)[] updates)
    {
        var tasks = new List<Task>();

        foreach (var (value, callback) in updates)
        {
            if (callback.HasDelegate)
                tasks.Add(callback.InvokeAsync(value));
        }

        await Task.WhenAll(tasks);
        StateHasChanged();
    }

    private async Task OnDateFromChanged(DateTime? dateFrom)
    {
        DateFrom = dateFrom ?? DateTime.Today;
        DateTo = new DateTime(DateFrom.Year, DateFrom.Month, DateFrom.Day, DateTo.Hour, DateTo.Minute, DateTo.Second);

        await UpdateParametersAsync((DateFrom, DateFromChanged), (DateTo, DateToChanged));
    }

    private async Task OnTimeFromChanged(TimeSpan? timeFrom)
    {
        DateFrom = new DateTime(DateFrom.Year, DateFrom.Month, DateFrom.Day, timeFrom?.Hours ?? 0, timeFrom?.Minutes ?? 0, timeFrom?.Seconds ?? 0);

        await UpdateParametersAsync(
        (DateFrom, DateFromChanged)
        );
    }

    private async Task OnTimeToChanged(TimeSpan? timeTo)
    {
        DateTo = new DateTime(DateFrom.Year, DateFrom.Month, DateFrom.Day, timeTo?.Hours ?? 0, timeTo?.Minutes ?? 0, timeTo?.Seconds ?? 0);

        await UpdateParametersAsync(
        (DateTo, DateToChanged)
        );
    }

    private async Task OnCityIdChanged(Guid? cityId)
    {
        CityId = cityId;
        BuildingId = null;
        FloorId = null;
        RoomId = null;
        FridgeId = null;

        await UpdateParametersAsync(
        (CityId, CityIdChanged),
        (BuildingId, BuildingIdChanged),
        (FloorId, FloorIdChanged),
        (RoomId, RoomIdChanged),
        (FridgeId, FridgeIdChanged)
        );
    }

    private async Task OnBuildingIdChanged(Guid? buildingId)
    {
        BuildingId = buildingId;
        FloorId = null;
        RoomId = null;
        FridgeId = null;

        await UpdateParametersAsync(
        (BuildingId, BuildingIdChanged),
        (FloorId, FloorIdChanged),
        (RoomId, RoomIdChanged),
        (FridgeId, FridgeIdChanged)
        );
    }

    private async Task OnFloorIdChanged(Guid? floorId)
    {
        FloorId = floorId;
        RoomId = null;
        FridgeId = null;

        await UpdateParametersAsync(
        (FloorId, FloorIdChanged),
        (RoomId, RoomIdChanged),
        (FridgeId, FridgeIdChanged)
        );
    }

    private async Task OnRoomIdChanged(Guid? roomId)
    {
        RoomId = roomId;
        FridgeId = null;

        await UpdateParametersAsync(
        (RoomId, RoomIdChanged),
        (FridgeId, FridgeIdChanged)
        );
    }

    private async Task OnFridgeIdChanged(Guid? fridgeId)
    {
        FridgeId = fridgeId;

        await UpdateParametersAsync((FridgeId, FridgeIdChanged));
    }
}
