using System.Data;
using Dapper;
using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Data.Sql;
using Teslametrics.App.Web.Services.Persistence;

namespace Teslametrics.App.Web.Features.Main.CameraPublicAccess.Drawer.View;

public static class GetCameraPublicAccessUseCase
{
    public record Query(Guid AccessId) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public string CameraName { get; init; } // Наименование камеры

        public Guid AccessId { get; init; } // ID ссылки

        public string AccessName { get; init; } // Наименование ссылки

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(string cameraName, Guid accessId, string accessName)
        {
            CameraName = cameraName;
            AccessId = accessId;
            AccessName = accessName;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Result = result;

            CameraName = string.Empty;
            AccessId = Guid.Empty;
            AccessName = string.Empty;
        }
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        AccessNotFound
    }

    public class Validator : AbstractValidator<Query>
    {
        public Validator()
        {
            RuleFor(q => q.AccessId).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Query, Response>
    {
        private readonly IValidator<Query> _validator;
        private readonly IDbConnection _dbConnection;

        public Handler(IValidator<Query> validator,
                       IDbConnection dbConnection)
        {
            _validator = validator;
            _dbConnection = dbConnection;
        }

        public async Task<Response> Handle(Query request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            var template = SqlQueryBuilder.Create()
                .Select(Db.PublicLinks.Props.Id)
                .Select(Db.PublicLinks.Props.Name, "AccessName")
                .Select(Db.Cameras.Props.Name, "CameraName")
                .InnerJoin(Db.Cameras.Table, Db.Cameras.Props.Id, Db.PublicLinks.Props.CameraId, SqlOperator.Equals)
                .Where(Db.PublicLinks.Props.Id, ":Id", SqlOperator.Equals, new { Id = request.AccessId })
                .Build(QueryType.Standard, Db.PublicLinks.Table, RowSelection.AllRows);

            var publicLink = await _dbConnection.QuerySingleOrDefaultAsync<PublicLinkModel>(template.RawSql, template.Parameters);

            if (publicLink is null)
            {
                return new Response(Result.AccessNotFound);
            }

            return new Response(publicLink.CameraName, publicLink.Id, publicLink.AccessName);
        }
    }

    public record PublicLinkModel(Guid Id, string AccessName, string CameraName);
}