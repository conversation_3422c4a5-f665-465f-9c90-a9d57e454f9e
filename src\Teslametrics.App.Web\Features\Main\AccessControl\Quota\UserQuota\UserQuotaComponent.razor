﻿<div class="mud-table-container ">
    <MudStack Row="true"
              Class="pl-4 pr-2 py-5">
        <MudText Typo="Typo.h6">Квоты пользователей</MudText>
        <MudSpacer />
        <AuthorizeView Policy="@AppPermissions.Main.CameraPresets.Update.GetEnumPermissionString()"
                       Context="innerContext"
                       Resource="new PolicyRequirementResource(null, null)">
            <MudButton Variant="Variant.Outlined"
                       Color="Color.Secondary">
                Редактировать
            </MudButton>
        </AuthorizeView>
    </MudStack>
    <table class="mud-table-root">
        <thead class="mud-table-head">
            <tr class="mud-table-row">
                <th class="mud-table-cell"></th>
                <th class="mud-table-cell cell">Использовано</th>
                <th class="mud-table-cell cell">Осталось</th>
                <th class="mud-table-cell cell">Всего</th>
            </tr>
        </thead>
        <tbody class="mud-table-body">
            <tr class="mud-table-row">
                <td class="mud-table-cell">Пользователи</td>
                <td class="mud-table-cell cell">150</td>
                <td class="mud-table-cell cell">
                    <MudText Color="@GetQuotaColor(0, 150)">
                        0
                    </MudText>
                </td>
                <td class="mud-table-cell cell">150</td>
            </tr>
        </tbody>
    </table>
</div>