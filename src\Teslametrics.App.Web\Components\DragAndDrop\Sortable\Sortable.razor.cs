using System;
using System.Diagnostics.CodeAnalysis;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
namespace Teslametrics.App.Web.Components.DragAndDrop.Sortable;

public partial class Sortable<T>
{
	[Inject]
	private IJSRuntime JS { get; set; } = null!;

    [Parameter]
    public RenderFragment<T>? SortableItemTemplate { get; set; }

    [Parameter, AllowNull]
    public List<T> Items { get; set; }

    [Parameter]
    public EventCallback<(int oldIndex, int newIndex)> OnUpdate { get; set; }

    [Parameter]
    public EventCallback<(int oldIndex, int newIndex)> OnRemove { get; set; }

    [Parameter]
    public string Id { get; set; } = Guid.NewGuid().ToString();

    [Parameter]
    public string Group { get; set; } = Guid.NewGuid().ToString();

    [Parameter]
    public string? Pull { get; set; }

    [Parameter]
    public bool Put { get; set; } = true;

    [Parameter]
    public bool Sort { get; set; } = true;

    [Parameter]
    public string Handle { get; set; } = string.Empty;

    [Parameter]
    public string? Filter { get; set; }

    [Parameter]
    public bool ForceFallback { get; set; } = true;

    [Parameter]
    public string? Class { get; set; } = string.Empty;

    [Parameter]
    public string? Style { get; set; } = string.Empty;

    private DotNetObjectReference<Sortable<T>>? selfReference;

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        try
        {
            if (firstRender)
            {
                selfReference = DotNetObjectReference.Create(this);
                var module = await JS.InvokeAsync<IJSObjectReference>("import", "./Components/DragAndDrop/Sortable/Sortable.razor.js");
                await module.InvokeAsync<string>("loadAndInit", Id, Group, Pull, Put, Sort, Handle, Filter, selfReference, ForceFallback);
            }
        }
        catch (Exception)
        {
        }
    }

    [JSInvokable]
    public void OnUpdateJS(int oldIndex, int newIndex)
    {
        // invoke the OnUpdate event passing in the oldIndex and the newIndex
        OnUpdate.InvokeAsync((oldIndex, newIndex));
    }

    [JSInvokable]
    public void OnRemoveJS(int oldIndex, int newIndex)
    {
        // remove the item from the list
        OnRemove.InvokeAsync((oldIndex, newIndex));
    }

    public void Dispose() => selfReference?.Dispose();
}
