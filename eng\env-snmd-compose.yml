name: teslametrics
services:
  timescaledb:
    image: timescale/timescaledb-ha:pg17
    container_name: timescaledb
    environment:
      POSTGRES_DB: teslametrics
      POSTGRES_USER: admin
      POSTGRES_PASSWORD: 7rMKe1MautXSY20R
    ports:
      - 127.0.0.1:5432:5432
    volumes:
      - postgres_data:/home/<USER>/pgdata/data
    restart: always

  kafka:
    image: bitnami/kafka:latest
    container_name: kafka
    environment:
      KAFKA_CFG_NODE_ID: 0
      KAFKA_CFG_PROCESS_ROLES: controller,broker
      KAFKA_CFG_CONTROLLER_QUORUM_VOTERS: 0@kafka:9093
      KAFKA_CFG_AUTO_CREATE_TOPICS_ENABLE: true
      KAFKA_CFG_LISTENERS: PLAINTEXT://:9092,CONTROLLER://:9093,EXTERNAL://:9094
      KAFKA_CFG_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092,EXTERNAL://localhost:9094
      KAFKA_CFG_LISTENER_SECURITY_PROTOCOL_MAP: CONTROLLER:PLAINTEXT,PLAINTEXT:PLAINTEXT,EXTERNAL:PLAINTEXT
      KAFKA_CFG_CONTROLLER_LISTENER_NAMES: CONTROLLER
      KAFKA_CFG_INTER_BROKER_LISTENER_NAME: PLAINTEXT
      KAFKA_CFG_MESSAGE_MAX_BYTES: 8388608
    expose:
      - 9092
    ports:
      - 127.0.0.1:9094:9094
    volumes:
      - kafka_data:/bitnami/kafka
    restart: always

  kafka-ui:
    image: provectuslabs/kafka-ui:latest
    container_name: kafka-ui
    ports:
      - 8080:8080
    environment:
      DYNAMIC_CONFIG_ENABLED: true
      KAFKA_CLUSTERS_0_NAME: local
      KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: kafka:9092
      KAFKA_CLUSTERS_0_PROPERTIES_SECURITY_PROTOCOL: PLAINTEXT
      AUTH_TYPE: LOGIN_FORM
      SPRING_SECURITY_USER_NAME: admin
      SPRING_SECURITY_USER_PASSWORD: 8FUXMlEBhvXTuu0m
    depends_on:
      - kafka
    restart: unless-stopped

  minio:
    image: minio/minio:latest
    container_name: minio
    command: server --console-address ":9001"
    environment:
      MINIO_ROOT_USER: admin
      MINIO_ROOT_PASSWORD: mp8rd1S9ec36vJCD
      MINIO_VOLUMES: /data-{1...6}
      MINIO_NOTIFY_KAFKA_ENABLE_PRIMARY: on
      MINIO_NOTIFY_KAFKA_BROKERS_PRIMARY: kafka:9092
      MINIO_NOTIFY_KAFKA_TOPIC_PRIMARY: minio-events
      MINIO_NOTIFY_KAFKA_QUEUE_DIR_PRIMARY: /events
      MINIO_NOTIFY_KAFKA_QUEUE_LIMIT_PRIMARY: 100000
      MINIO_NOTIFY_KAFKA_PRODUCER_COMPRESSION_CODEC_PRIMARY: none
    ports:
      - 127.0.0.1:9000:9000
      - 9001:9001
    volumes:
      - minio_events:/events
      - minio_data1:/data-1
      - minio_data2:/data-2
      - minio_data3:/data-3
      - minio_data4:/data-4
      - minio_data5:/data-5
      - minio_data6:/data-6
    healthcheck:
      test: ["CMD", "mc", "ready", "local"]
      interval: 30s
      timeout: 20s
      retries: 3
    restart: always
    depends_on:
      - kafka

volumes:
  postgres_data:
  kafka_data:
  minio_events:
  minio_data1:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /mnt/drive-1
  minio_data2:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /mnt/drive-2
  minio_data3:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /mnt/drive-3
  minio_data4:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /mnt/drive-4
  minio_data5:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /mnt/drive-5
  minio_data6:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /mnt/drive-6
