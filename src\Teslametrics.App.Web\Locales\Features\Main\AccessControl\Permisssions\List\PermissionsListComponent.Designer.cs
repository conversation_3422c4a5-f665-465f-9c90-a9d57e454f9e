﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Teslametrics.App.Web.Locales.Features.Main.AccessControl.Permisssions.List {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class PermissionsListComponent {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal PermissionsListComponent() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Teslametrics.App.Web.Locales.Features.Main.AccessControl.Permisssions.List.Permis" +
                            "sionsListComponent", typeof(PermissionsListComponent).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete cameras.
        /// </summary>
        public static string _Main_Cameras_Delete {
            get {
                return ResourceManager.GetString(" Main.Cameras.Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allows the user to create organizations.  This right is inherited by viewing organizations..
        /// </summary>
        public static string Description_Main_AccessControl_Organizations_Create {
            get {
                return ResourceManager.GetString("Description:Main.AccessControl.Organizations.Create", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allows the user to delete organizations. This right is inherited by viewing organizations..
        /// </summary>
        public static string Description_Main_AccessControl_Organizations_Delete {
            get {
                return ResourceManager.GetString("Description:Main.AccessControl.Organizations.Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allows the user to view information about organizations..
        /// </summary>
        public static string Description_Main_AccessControl_Organizations_Read {
            get {
                return ResourceManager.GetString("Description:Main.AccessControl.Organizations.Read", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allows the user to edit organizations.  This right is inherited by viewing organizations..
        /// </summary>
        public static string Description_Main_AccessControl_Organizations_Update {
            get {
                return ResourceManager.GetString("Description:Main.AccessControl.Organizations.Update", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allows the user to create new roles. This right inherits the user&apos;s view right..
        /// </summary>
        public static string Description_Main_AccessControl_Roles_Create {
            get {
                return ResourceManager.GetString("Description:Main.AccessControl.Roles.Create", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allows the user to delete roles. This right inherits the user&apos;s view right..
        /// </summary>
        public static string Description_Main_AccessControl_Roles_Delete {
            get {
                return ResourceManager.GetString("Description:Main.AccessControl.Roles.Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allows the user to view role information..
        /// </summary>
        public static string Description_Main_AccessControl_Roles_Read {
            get {
                return ResourceManager.GetString("Description:Main.AccessControl.Roles.Read", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allows the user to edit roles. This right inherits the user&apos;s view right..
        /// </summary>
        public static string Description_Main_AccessControl_Roles_Update {
            get {
                return ResourceManager.GetString("Description:Main.AccessControl.Roles.Update", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allows a user to create a user. This right is inherited by viewing users..
        /// </summary>
        public static string Description_Main_AccessControl_Users_Create {
            get {
                return ResourceManager.GetString("Description:Main.AccessControl.Users.Create", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allows the user to delete users. This right is inherited by viewing users..
        /// </summary>
        public static string Description_Main_AccessControl_Users_Delete {
            get {
                return ResourceManager.GetString("Description:Main.AccessControl.Users.Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allows a user to force another user to change their password the next time they log into the application. This right inherits the user&apos;s view right.
        /// </summary>
        public static string Description_Main_AccessControl_Users_ForceChangePassword {
            get {
                return ResourceManager.GetString("Description:Main.AccessControl.Users.ForceChangePassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allows a user to block other users. This right inherits the user&apos;s view right.
        ///.
        /// </summary>
        public static string Description_Main_AccessControl_Users_Lock {
            get {
                return ResourceManager.GetString("Description:Main.AccessControl.Users.Lock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allows the user to view users..
        /// </summary>
        public static string Description_Main_AccessControl_Users_Read {
            get {
                return ResourceManager.GetString("Description:Main.AccessControl.Users.Read", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allows the user to unblock other users. This right inherits the user&apos;s view right..
        /// </summary>
        public static string Description_Main_AccessControl_Users_Unlock {
            get {
                return ResourceManager.GetString("Description:Main.AccessControl.Users.Unlock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allows the user to edit created users. This right is inherited by viewing users..
        /// </summary>
        public static string Description_Main_AccessControl_Users_Update {
            get {
                return ResourceManager.GetString("Description:Main.AccessControl.Users.Update", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allows the user to create camera presets. This right is inherited by viewing camera presets..
        /// </summary>
        public static string Description_Main_CameraPresets_Create {
            get {
                return ResourceManager.GetString("Description:Main.CameraPresets.Create", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allows the user to delete created camera presets. This right is inherited by viewing camera presets..
        /// </summary>
        public static string Description_Main_CameraPresets_Delete {
            get {
                return ResourceManager.GetString("Description:Main.CameraPresets.Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allows the user to view created camera presets, and their settings..
        /// </summary>
        public static string Description_Main_CameraPresets_Read {
            get {
                return ResourceManager.GetString("Description:Main.CameraPresets.Read", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allows the user to edit created camera presets. This right is inherited by viewing camera presets..
        /// </summary>
        public static string Description_Main_CameraPresets_Update {
            get {
                return ResourceManager.GetString("Description:Main.CameraPresets.Update", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allows the user to connect cameras. This right is inherited by viewing cameras..
        /// </summary>
        public static string Description_Main_Cameras_Connect {
            get {
                return ResourceManager.GetString("Description:Main.Cameras.Connect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Access to add new cameras to the system..
        /// </summary>
        public static string Description_Main_Cameras_Create {
            get {
                return ResourceManager.GetString("Description:Main.Cameras.Create", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allows the user to delete created cameras. This right is inherited by viewing cameras..
        /// </summary>
        public static string Description_Main_Cameras_Delete {
            get {
                return ResourceManager.GetString("Description:Main.Cameras.Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allows the user to disconnect cameras. This right is inherited by viewing cameras..
        /// </summary>
        public static string Description_Main_Cameras_Disconnect {
            get {
                return ResourceManager.GetString("Description:Main.Cameras.Disconnect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to allows the user to move cameras between directories. This right inherits the right to view cameras..
        /// </summary>
        public static string Description_Main_Cameras_Move {
            get {
                return ResourceManager.GetString("Description:Main.Cameras.Move", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allows a user with this right to view cameras.
        /// </summary>
        public static string Description_Main_Cameras_Read {
            get {
                return ResourceManager.GetString("Description:Main.Cameras.Read", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allows the user to edit created cameras. This right is inherited by viewing cameras..
        /// </summary>
        public static string Description_Main_Cameras_Update {
            get {
                return ResourceManager.GetString("Description:Main.Cameras.Update", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allows the user to create directories. This right inherits the right to view directories..
        /// </summary>
        public static string Description_Main_Folders_Create {
            get {
                return ResourceManager.GetString("Description:Main.Folders.Create", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allows the user to delete directories. This right inherits the right to view directories..
        /// </summary>
        public static string Description_Main_Folders_Delete {
            get {
                return ResourceManager.GetString("Description:Main.Folders.Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allows the user to move directories to other directories. This right inherits the right to view directories..
        /// </summary>
        public static string Description_Main_Folders_Move {
            get {
                return ResourceManager.GetString("Description:Main.Folders.Move", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allows the user to move directories to other directories. This right inherits the right to view directories..
        /// </summary>
        public static string Description_Main_Folders_Read {
            get {
                return ResourceManager.GetString("Description:Main.Folders.Read", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allows the user to edit directories. This right inherits the right to view directories..
        /// </summary>
        public static string Description_Main_Folders_Update {
            get {
                return ResourceManager.GetString("Description:Main.Folders.Update", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allows the user to create groups. This right inherits the right to view groups..
        /// </summary>
        public static string Description_Main_Groups_Create {
            get {
                return ResourceManager.GetString("Description:Main.Groups.Create", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allows the user to delete groups. This right inherits the right to view groups..
        /// </summary>
        public static string Description_Main_Groups_Delete {
            get {
                return ResourceManager.GetString("Description:Main.Groups.Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allows the user to move groups. This right inherits the right to view groups..
        /// </summary>
        public static string Description_Main_Groups_Move {
            get {
                return ResourceManager.GetString("Description:Main.Groups.Move", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allows the user to view groups..
        /// </summary>
        public static string Description_Main_Groups_Read {
            get {
                return ResourceManager.GetString("Description:Main.Groups.Read", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allows the user to edit groups. This right inherits the right to view groups..
        /// </summary>
        public static string Description_Main_Groups_Update {
            get {
                return ResourceManager.GetString("Description:Main.Groups.Update", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create organizations.
        /// </summary>
        public static string Main_AccessControl_Organizations_Create {
            get {
                return ResourceManager.GetString("Main.AccessControl.Organizations.Create", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete organizations.
        /// </summary>
        public static string Main_AccessControl_Organizations_Delete {
            get {
                return ResourceManager.GetString("Main.AccessControl.Organizations.Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Read organizations.
        /// </summary>
        public static string Main_AccessControl_Organizations_Read {
            get {
                return ResourceManager.GetString("Main.AccessControl.Organizations.Read", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit organization.
        /// </summary>
        public static string Main_AccessControl_Organizations_Update {
            get {
                return ResourceManager.GetString("Main.AccessControl.Organizations.Update", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create roles.
        /// </summary>
        public static string Main_AccessControl_Roles_Create {
            get {
                return ResourceManager.GetString("Main.AccessControl.Roles.Create", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete roles.
        /// </summary>
        public static string Main_AccessControl_Roles_Delete {
            get {
                return ResourceManager.GetString("Main.AccessControl.Roles.Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Read roles.
        /// </summary>
        public static string Main_AccessControl_Roles_Read {
            get {
                return ResourceManager.GetString("Main.AccessControl.Roles.Read", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit roles.
        /// </summary>
        public static string Main_AccessControl_Roles_Update {
            get {
                return ResourceManager.GetString("Main.AccessControl.Roles.Update", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create user.
        /// </summary>
        public static string Main_AccessControl_Users_Create {
            get {
                return ResourceManager.GetString("Main.AccessControl.Users.Create", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete users.
        /// </summary>
        public static string Main_AccessControl_Users_Delete {
            get {
                return ResourceManager.GetString("Main.AccessControl.Users.Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Force the user to change their password upon login.
        /// </summary>
        public static string Main_AccessControl_Users_ForceChangePassword {
            get {
                return ResourceManager.GetString("Main.AccessControl.Users.ForceChangePassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lock users.
        /// </summary>
        public static string Main_AccessControl_Users_Lock {
            get {
                return ResourceManager.GetString("Main.AccessControl.Users.Lock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Read users.
        /// </summary>
        public static string Main_AccessControl_Users_Read {
            get {
                return ResourceManager.GetString("Main.AccessControl.Users.Read", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unlock users.
        /// </summary>
        public static string Main_AccessControl_Users_Unlock {
            get {
                return ResourceManager.GetString("Main.AccessControl.Users.Unlock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit user.
        /// </summary>
        public static string Main_AccessControl_Users_Update {
            get {
                return ResourceManager.GetString("Main.AccessControl.Users.Update", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create camera presets.
        /// </summary>
        public static string Main_CameraPresets_Create {
            get {
                return ResourceManager.GetString("Main.CameraPresets.Create", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete camera presets.
        /// </summary>
        public static string Main_CameraPresets_Delete {
            get {
                return ResourceManager.GetString("Main.CameraPresets.Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Read cameras presets.
        /// </summary>
        public static string Main_CameraPresets_Read {
            get {
                return ResourceManager.GetString("Main.CameraPresets.Read", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit camera presets.
        /// </summary>
        public static string Main_CameraPresets_Update {
            get {
                return ResourceManager.GetString("Main.CameraPresets.Update", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Connect camera.
        /// </summary>
        public static string Main_Cameras_Connect {
            get {
                return ResourceManager.GetString("Main.Cameras.Connect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Creating cameras.
        /// </summary>
        public static string Main_Cameras_Create {
            get {
                return ResourceManager.GetString("Main.Cameras.Create", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete cameras.
        /// </summary>
        public static string Main_Cameras_Delete {
            get {
                return ResourceManager.GetString("Main.Cameras.Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Disconnect cameras.
        /// </summary>
        public static string Main_Cameras_Disconnect {
            get {
                return ResourceManager.GetString("Main.Cameras.Disconnect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Move cameras to another folder.
        /// </summary>
        public static string Main_Cameras_Move {
            get {
                return ResourceManager.GetString("Main.Cameras.Move", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Viewing cameras.
        /// </summary>
        public static string Main_Cameras_Read {
            get {
                return ResourceManager.GetString("Main.Cameras.Read", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Editing cameras.
        /// </summary>
        public static string Main_Cameras_Update {
            get {
                return ResourceManager.GetString("Main.Cameras.Update", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create folders.
        /// </summary>
        public static string Main_Folders_Create {
            get {
                return ResourceManager.GetString("Main.Folders.Create", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete folders.
        /// </summary>
        public static string Main_Folders_Delete {
            get {
                return ResourceManager.GetString("Main.Folders.Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Move folders.
        /// </summary>
        public static string Main_Folders_Move {
            get {
                return ResourceManager.GetString("Main.Folders.Move", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Read folders.
        /// </summary>
        public static string Main_Folders_Read {
            get {
                return ResourceManager.GetString("Main.Folders.Read", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit folders.
        /// </summary>
        public static string Main_Folders_Update {
            get {
                return ResourceManager.GetString("Main.Folders.Update", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Creating groups.
        /// </summary>
        public static string Main_Groups_Create {
            get {
                return ResourceManager.GetString("Main.Groups.Create", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deleting groups.
        /// </summary>
        public static string Main_Groups_Delete {
            get {
                return ResourceManager.GetString("Main.Groups.Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Moving groups.
        /// </summary>
        public static string Main_Groups_Move {
            get {
                return ResourceManager.GetString("Main.Groups.Move", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to View groups.
        /// </summary>
        public static string Main_Groups_Read {
            get {
                return ResourceManager.GetString("Main.Groups.Read", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Editing groups.
        /// </summary>
        public static string Main_Groups_Update {
            get {
                return ResourceManager.GetString("Main.Groups.Update", resourceCulture);
            }
        }
    }
}
