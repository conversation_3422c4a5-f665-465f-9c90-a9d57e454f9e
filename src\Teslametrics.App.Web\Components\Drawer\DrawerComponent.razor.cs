using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;

namespace Teslametrics.App.Web.Components.Drawer;

public partial class DrawerComponent : IAsyncDisposable
{
    private string _instanceId => InstanceId.ToString();
    private IJSObjectReference? _jsModule;
    private IJSObjectReference? _jsDraggerReference;
    private Dictionary<string, object> _drawerAttributes => new() {
        { "data-id", InstanceId },
        { "data-xs", Options.XSColumnsWidth },
        { "data-sm", Options.SMColumnsWidth },
        { "data-md", Options.MDColumnsWidth },
        { "data-lg", Options.LGColumnsWidth },
        { "data-xl", Options.XLColumnsWidth },
        { "data-xxl", Options.XXLColumnsWidth },
        { "data-anchor", Options.Anchor.ToString() }
    };

    #region [Injectables]
    [Inject]
    private ILogger<DrawerComponent> Logger { get; set; } = null!;

    [Inject]
    protected IJSRuntime JS { get; set; } = null!;
    #endregion

    #region [Parameters]
    [Parameter]
    public DrawerOptions Options { get; set; } = new();

    [Parameter]
    public bool FromRighth { get; set; } = true;

    [Parameter]
    public bool Open { get; set; } = false;

    [Parameter]
    public EventCallback<bool> OpenChanged { get; set; }

    //[Parameter]
    //public RenderFragment? Header { get; set; }

    //[Parameter]
    //public RenderFragment? HeaderContent { get; set; }

    [Parameter]
    public RenderFragment ChildContent { get; set; } = builder => { };

    [Parameter]
    public string? HeaderClass { get; set; }

    [Parameter]
    public string? BodyClass { get; set; }
    #endregion

    public Guid InstanceId { get; set; } = Guid.NewGuid();

    public async ValueTask DisposeAsync()
    {
        try
        {
            if (_jsDraggerReference is not null)
            {
                await _jsDraggerReference.InvokeVoidAsync("dispose");
                await _jsDraggerReference.DisposeAsync();
            }
            if (_jsModule is not null)
            {
                await _jsModule.DisposeAsync();
            }
        }
        catch (JSDisconnectedException) // https://learn.microsoft.com/en-us/aspnet/core/blazor/javascript-interoperability/?view=aspnetcore-9.0
        {
        }
        catch (TaskCanceledException)
        {
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, ex.Message);
        }
    }

    public Task HideAsync() => InvokeAsync(async () =>
    {
        Open = false;
        if (OpenChanged.HasDelegate)
            await OpenChanged.InvokeAsync(Open);

        StateHasChanged();
    });

    public Task ShowAsync() => InvokeAsync(async () =>
    {
        Open = true;
        if (OpenChanged.HasDelegate)
            await OpenChanged.InvokeAsync(Open);

        StateHasChanged();
    });

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            _jsModule = await JS.InvokeAsync<IJSObjectReference>("import", "./Components/Drawer/DrawerComponent.razor.js");
            _jsDraggerReference = await _jsModule.InvokeAsync<IJSObjectReference>("initializeDrawerComponent", InstanceId);

            //if (Open)
            //{
            //	return;
            //}

            //await InvokeAsync(async () =>
            //{
            //	await Task.Delay(50); // to make drawer sliding in work
            //	Open = true;
            //	StateHasChanged();
            //});
        }
    }

    //protected override async Task OnParametersSetAsync()
    //{
    //	if (_jsDraggerReference is null && _jsModule is not null)
    //	{
    //		_jsDraggerReference = await _jsModule.InvokeAsync<IJSObjectReference>("initializeDrawerComponent", _instanceId);
    //	}
    //}

    private async Task OpenToggle(bool open)
    {
        Open = open;
        if (_jsDraggerReference is not null)
        {
            if (Open)
                await _jsDraggerReference.InvokeVoidAsync("onOpen");
            else
                await _jsDraggerReference.InvokeVoidAsync("onClose");
        }
        if (OpenChanged.HasDelegate)
        {
            await OpenChanged.InvokeAsync(Open);
        }
    }
}
