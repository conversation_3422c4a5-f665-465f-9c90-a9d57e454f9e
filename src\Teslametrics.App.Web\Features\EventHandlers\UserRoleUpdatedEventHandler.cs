using Dapper;
using MediatR;
using System.Data;
using Teslametrics.Core.Domain.AccessControl.Organizations.Events;
using Teslametrics.Core.Domain.AccessControl.Users.Events;
using Teslametrics.Core.Services.Outbox;
using Teslametrics.Core.Services.Persistence;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Features.EventHandlers;

public partial class UserRoleUpdatedEventHandler : INotificationHandler<RoleUpdatedEvent>
{
    private readonly IDbConnection _dbConnection;
    private readonly IPublisher _publisher;
    private readonly IOutbox _outbox;

    public UserRoleUpdatedEventHandler(IDbConnection dbConnection,
                                       IPublisher publisher,
                                       IOutbox outbox)
    {
        _dbConnection = dbConnection;
        _publisher = publisher;
        _outbox = outbox;
    }

    public async Task Handle(RoleUpdatedEvent notification, CancellationToken cancellationToken)
    {
        var template = SqlQueryBuilder.Create()
            .Select(Db.UserRoles.Props.UserId)
            .Where(Db.UserRoles.Props.RoleId, ":RoleId", SqlOperator.Equals, new { RoleId = notification.Id })
            .Build(QueryType.Standard, Db.UserRoles.Table, RowSelection.AllRows);

        var userIds = await _dbConnection.QueryAsync<Guid>(template.RawSql, template.Parameters);

        if (userIds.Any())
        {
            var events = userIds.Select(id => new UserRoleUpdatedEvent(id)).ToList();

            foreach (var @event in events)
            {
                await _publisher.Publish(@event, cancellationToken);
            }

            await _outbox.AddRangeAsync(events);
        }
    }
}