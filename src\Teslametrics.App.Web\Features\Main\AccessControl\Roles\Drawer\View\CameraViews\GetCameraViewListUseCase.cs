using System.Data;
using Dapper;
using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Data.Sql;
using Teslametrics.App.Web.Services.Persistence;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Roles.Drawer.View.CameraViews;

public static class GetCameraViewListUseCase // Мне нужно получить список видов, для которых есть права доступа в данной роли.
{
    public record Query(Guid RoleId) : BaseRequest<Response>; // , Guid RoleId

    public record Response : BaseResponse
    {
        public List<Item> Items { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(List<Item> items)
        {
            Items = items;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Result = result;

            Items = [];
        }

        public record Item(Guid Id, string Name);
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError
    }

    public class Validator : AbstractValidator<Query>
    {
        public Validator()
        {
            RuleFor(q => q.RoleId).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Query, Response>
    {
        private readonly IValidator<Query> _validator;
        private readonly IDbConnection _dbConnection;

        public Handler(IValidator<Query> validator,
                       IDbConnection dbConnection)
        {
            _validator = validator;
            _dbConnection = dbConnection;
        }

        public async Task<Response> Handle(Query request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            var cameraViewsTemplate = SqlQueryBuilder.Create()
                .Select(Db.CameraViews.Props.Id)
                .Select(Db.CameraViews.Props.Name)
                .InnerJoin(Db.RolePermissions.Table, Db.RolePermissions.Props.ResourceId, Db.CameraViews.Props.Id, SqlOperator.Equals)
                .Where(Db.RolePermissions.Props.RoleId, ":RoleId", SqlOperator.Equals, new { request.RoleId })
                .Build(QueryType.Standard, Db.CameraViews.Table, RowSelection.UniqueRows);

            var cameraViewModels = await _dbConnection.QueryAsync<CameraViewModel>(cameraViewsTemplate.RawSql, cameraViewsTemplate.Parameters);

            return new Response(cameraViewModels.Select(c => new Response.Item(c.Id, c.Name)).ToList());
        }
    }

    public record CameraViewModel(Guid Id, string Name);
}