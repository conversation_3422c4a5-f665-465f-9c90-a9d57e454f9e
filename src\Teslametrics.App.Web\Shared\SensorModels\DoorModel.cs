namespace Teslametrics.App.Web.Shared;

[GenerateSerializer]
public class DoorModel : BaseSensorModel
{
    [Id(3)]
    public int AvailableOpeningTime { get; set; }

    // Конструктор без параметров для System.Text.Json
    public DoorModel()
        : base(Guid.Empty, string.Empty, null)
    {
        AvailableOpeningTime = 30;
    }

    public DoorModel(string name = "", string? displayName = null, int availableOpeningTime = 30)
        : this(GuidGenerator.New(), name, displayName, availableOpeningTime)
    {
    }

    public DoorModel(Guid id, string name, string? displayName, int availableOpeningTime)
        : base(id, name, displayName)
    {
        AvailableOpeningTime = availableOpeningTime;
    }
}