using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using System.Text.Json;
using Teslametrics.App.Web.Domain.AccessControl.Organizations;
using Teslametrics.App.Web.Domain.AccessControl.Users;
using Teslametrics.App.Web.Domain.CameraPresets;
using Teslametrics.App.Web.Domain.Cameras;
using Teslametrics.App.Web.Domain.CameraViews;
using Teslametrics.App.Web.Domain.Folders;
using Teslametrics.App.Web.Domain.Incidents;
using Teslametrics.App.Web.Domain.Notifications;
using Teslametrics.App.Web.Domain.PublicLinks;
using Teslametrics.App.Web.Shared;

namespace Teslametrics.App.Web.Services.Persistence;

public class CommandAppDbContext : DbContext
{
    public CommandAppDbContext(DbContextOptions<CommandAppDbContext> options)
        : base(options)
    {
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        var options = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            PropertyNameCaseInsensitive = true
        };

        modelBuilder.Entity<UserAggregate>(entity =>
        {
            entity.ToTable(Db.Users.Table);
            entity.Property(e => e.Id)
                .HasColumnName(Db.Users.Columns.Id)
                .ValueGeneratedNever()
                .IsRequired();
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Name)
                .HasColumnName(Db.Users.Columns.Name);
            entity.Property(e => e.Password)
                .HasColumnName(Db.Users.Columns.Password);
            entity.Property(e => e.LastLogInTime)
                .HasColumnName(Db.Users.Columns.LastLogInTime);
            entity.Property(e => e.ForcePasswordChange)
                .HasColumnName(Db.Users.Columns.ForcePasswordChange);
            entity.Property(e => e.LockoutEnabled)
                .HasColumnName(Db.Users.Columns.LockoutEnabled);
            entity.Property(e => e.SecretKey)
                .HasColumnName(Db.Users.Columns.SecretKey);
            entity.Property(e => e.Is2faEnabled)
                .HasColumnName(Db.Users.Columns.Is2faEnabled);
            entity.Property(e => e.Setup2FAIsCompleted)
                .HasColumnName(Db.Users.Columns.Setup2faIsCompleted);
            entity.HasMany(e => e.Organizations)
                .WithOne()
                .HasForeignKey(e => e.UserId)
                .OnDelete(DeleteBehavior.Cascade);
            entity.Navigation(e => e.Organizations).AutoInclude();
            entity.HasMany(e => e.Roles)
                .WithOne()
                .HasForeignKey(e => e.UserId)
                .OnDelete(DeleteBehavior.Cascade);
            entity.Navigation(e => e.Roles).AutoInclude();
        });

        modelBuilder.Entity<UserOrganizationEntity>(entity =>
        {
            entity.ToTable(Db.UserOrganizations.Table);
            entity.Property(e => e.UserId)
                .HasColumnName(Db.UserOrganizations.Columns.UserId);
            entity.Property(e => e.OrganizationId)
                .HasColumnName(Db.UserOrganizations.Columns.OrganizationId);
            entity.HasKey(e => new { e.UserId, e.OrganizationId });
        });

        modelBuilder.Entity<UserRoleEntity>(entity =>
        {
            entity.ToTable(Db.UserRoles.Table);
            entity.Property(e => e.UserId)
                .HasColumnName(Db.UserRoles.Columns.UserId)
                .IsRequired();
            entity.Property(e => e.RoleId)
                .HasColumnName(Db.UserRoles.Columns.RoleId)
                .IsRequired();
            entity.HasKey(e => new { e.UserId, e.RoleId });
        });

        modelBuilder.Entity<OrganizationAggregate>(entity =>
        {
            entity.ToTable(Db.Organizations.Table);
            entity.Property(e => e.Id)
                .HasColumnName(Db.Organizations.Columns.Id)
                .ValueGeneratedNever()
                .IsRequired();
            entity.HasKey(e => e.Id);
            entity.Property(e => e.OwnerId)
                .HasColumnName(Db.Organizations.Columns.OwnerId);
            entity.Property(e => e.Name)
                .HasColumnName(Db.Organizations.Columns.Name);
            entity.HasMany(e => e.Roles)
                .WithOne()
                .HasForeignKey(Db.Roles.Columns.OrganizationId)
                .OnDelete(DeleteBehavior.Cascade);
            entity.Navigation(e => e.Roles).AutoInclude();
            entity.HasMany(e => e.CameraQuotas)
                .WithOne()
                .HasForeignKey(e => e.OrganizationId)
                .OnDelete(DeleteBehavior.Cascade);
            entity.Navigation(e => e.CameraQuotas).AutoInclude();
        });

        modelBuilder.Entity<CameraQuotaEntity>(entity =>
        {
            entity.ToTable(Db.CameraQuotas.Table);
            entity.Property(e => e.Id)
                .HasColumnName(Db.Roles.Columns.Id)
                .ValueGeneratedNever()
                .IsRequired();
            entity.HasKey(e => e.Id);
            entity.Property(e => e.OrganizationId)
                .HasColumnName(Db.CameraQuotas.Columns.OrganizationId);
            entity.Property(e => e.PresetId)
                .HasColumnName(Db.CameraQuotas.Columns.PresetId);
            entity.Property(e => e.Name)
                .HasColumnName(Db.CameraQuotas.Columns.Name);
            entity.Property(e => e.Limit)
                .HasColumnName(Db.CameraQuotas.Columns.Limit);
            entity.Property(e => e.RetentionPeriodDays)
                .HasColumnName(Db.CameraQuotas.Columns.RetentionPeriodDays);
            entity.Property(e => e.StorageLimitMb)
                .HasColumnName(Db.CameraQuotas.Columns.StorageLimitMb);
        });

        modelBuilder.Entity<OrganizationRoleEntity>(entity =>
        {
            entity.ToTable(Db.Roles.Table);
            entity.Property(e => e.Id)
                .HasColumnName(Db.Roles.Columns.Id)
                .ValueGeneratedNever()
                .IsRequired();
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Name)
                .HasColumnName(Db.Roles.Columns.Name);
            entity.Property(e => e.IsAdmin)
                .HasColumnName(Db.Roles.Columns.IsAdmin);
            entity.HasMany(e => e.ResourcePermissions)
                .WithOne()
                .HasForeignKey(e => e.RoleId)
                .OnDelete(DeleteBehavior.Cascade);
            entity.Navigation(e => e.ResourcePermissions).AutoInclude();
        });

        modelBuilder.Entity<RolePermissionEntity>(entity =>
        {
            entity.ToTable(Db.RolePermissions.Table);
            entity.Property(e => e.Id)
                .HasColumnName(Db.RolePermissions.Columns.Id)
                .ValueGeneratedNever()
                .IsRequired();
            entity.HasKey(e => e.Id);
            entity.Property(e => e.OrganizationId)
                .HasColumnName(Db.RolePermissions.Columns.OrganizationId);
            entity.Property(e => e.RoleId)
                .HasColumnName(Db.RolePermissions.Columns.RoleId)
                .IsRequired();
            entity.ComplexProperty(e => e.ResourcePermission, b =>
            {
                b.ComplexProperty(p => p.ResourceId)
                    .Property(p => p.Value)
                    .HasColumnName(Db.RolePermissions.Columns.ResourceId);

                b.ComplexProperty(p => p.Permission)
                    .Property(p => p.Value)
                    .HasColumnName(Db.RolePermissions.Columns.Permission);
            });
        });

        modelBuilder.Entity<CameraAggregate>(entity =>
        {
            entity.ToTable(Db.Cameras.Table);
            entity.Property(e => e.Id)
                .HasColumnName(Db.Cameras.Columns.Id)
                .ValueGeneratedNever()
                .IsRequired();
            entity.HasKey(e => e.Id);
            entity.Property(e => e.OrganizationId)
                .HasColumnName(Db.Cameras.Columns.OrganizationId);
            entity.Property(e => e.FolderId)
                .HasColumnName(Db.Cameras.Columns.FolderId);
            entity.Property(e => e.QuotaId)
                .HasColumnName(Db.Cameras.Columns.QuotaId);
            entity.Property(e => e.Name)
                .HasColumnName(Db.Cameras.Columns.Name);
            entity.Property(e => e.TimeZone)
                .HasColumnName(Db.Cameras.Columns.TimeZone);
            entity.Property(e => e.Latitude)
                .HasColumnName(Db.Cameras.Columns.Latitude);
            entity.Property(e => e.Longitude)
                .HasColumnName(Db.Cameras.Columns.Longitude);
            entity.Property(e => e.ArchiveUri)
                .HasColumnName(Db.Cameras.Columns.ArchiveUri);
            entity.Property(e => e.ViewUri)
                .HasColumnName(Db.Cameras.Columns.ViewUri);
            entity.Property(e => e.PublicUri)
                .HasColumnName(Db.Cameras.Columns.PublicUri);
            entity.Property(e => e.AutoStart)
                .HasColumnName(Db.Cameras.Columns.AutoStart);
            entity.Property(e => e.IsBlocked)
                .HasColumnName(Db.Cameras.Columns.IsBlocked);
            entity.Property(e => e.OnvifEnabled)
                .HasColumnName(Db.Cameras.Columns.OnvifEnabled)
                .HasDefaultValue(false);
            entity.Property(e => e.OnvifSettings)
                .HasColumnName(Db.Cameras.Columns.OnvifSettings)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, JsonSerializerOptions.Default),
                    v => JsonSerializer.Deserialize<OnvifSettingsValueObject?>(v, JsonSerializerOptions.Default))
                .HasDefaultValue(null);
        });

        modelBuilder.Entity<FolderAggregate>(entity =>
        {
            entity.ToTable(Db.Folders.Table);
            entity.Property(e => e.Id)
                .HasColumnName(Db.Folders.Columns.Id)
                .ValueGeneratedNever()
                .IsRequired();
            entity.HasKey(e => e.Id);
            entity.Property(e => e.OrganizationId)
                .HasColumnName(Db.Folders.Columns.OrganizationId);
            entity.Property(e => e.ParentId)
                .HasColumnName(Db.Folders.Columns.ParentId);
            entity.Property(e => e.Name)
                .HasColumnName(Db.Folders.Columns.Name);
        });

        modelBuilder.Entity<PresetAggregate>(entity =>
        {
            entity.ToTable(Db.Presets.Table);
            entity.Property(e => e.Id)
                .HasColumnName(Db.Presets.Columns.Id)
                .ValueGeneratedNever()
                .IsRequired();
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Name)
                .HasColumnName(Db.Presets.Columns.Name);
            entity.Property(e => e.ArchiveStreamConfig)
                .HasColumnName(Db.Presets.Columns.ArchiveStreamConfig)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, JsonSerializerOptions.Default),
                    v => JsonSerializer.Deserialize<StreamConfigValueObject>(v, JsonSerializerOptions.Default));
            entity.Property(e => e.ViewStreamConfig)
                .HasColumnName(Db.Presets.Columns.ViewStreamConfig)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, JsonSerializerOptions.Default),
                    v => JsonSerializer.Deserialize<StreamConfigValueObject>(v, JsonSerializerOptions.Default));
            entity.Property(e => e.PublicStreamConfig)
                .HasColumnName(Db.Presets.Columns.PublicStreamConfig)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, JsonSerializerOptions.Default),
                    v => JsonSerializer.Deserialize<StreamConfigValueObject>(v, JsonSerializerOptions.Default));
        });

        modelBuilder.Entity<PublicLinkAggregate>(entity =>
        {
            entity.ToTable(Db.PublicLinks.Table);
            entity.Property(e => e.Id)
                .HasColumnName(Db.PublicLinks.Columns.Id)
                .ValueGeneratedNever()
                .IsRequired();
            entity.HasKey(e => e.Id);
            entity.Property(e => e.CameraId)
                .HasColumnName(Db.PublicLinks.Columns.CameraId);
            entity.Property(e => e.Name)
                .HasColumnName(Db.PublicLinks.Columns.Name);
        });

        modelBuilder.Entity<CameraViewAggregate>(entity =>
        {
            entity.ToTable(Db.CameraViews.Table);
            entity.Property(e => e.Id)
                .HasColumnName(Db.CameraViews.Columns.Id)
                .ValueGeneratedNever()
                .IsRequired();
            entity.HasKey(e => e.Id);
            entity.Property(e => e.OrganizationId)
                .HasColumnName(Db.CameraViews.Columns.OrganizationId);
            entity.Property(e => e.Name)
                .HasColumnName(Db.CameraViews.Columns.Name);
            entity.Property(e => e.ColumnCount)
                .HasColumnName(Db.CameraViews.Columns.ColumnCount);
            entity.Property(e => e.RowCount)
                .HasColumnName(Db.CameraViews.Columns.RowCount);
            entity.Property(e => e.GridType)
                .HasColumnName(Db.CameraViews.Columns.GridType)
                .HasDefaultValue(GridType.GridCustom);
            entity.Property(e => e.Cells)
                .HasColumnName(Db.CameraViews.Columns.Cells)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, JsonSerializerOptions.Default),
                    v => JsonSerializer.Deserialize<List<CameraViewCell>>(v, JsonSerializerOptions.Default) ?? new List<CameraViewCell>(),
                    new ValueComparer<IReadOnlyCollection<CameraViewCell>>((v1, v2) => v1!.SequenceEqual(v2!),
                            v => v.Aggregate(0, (a, v) => HashCode.Combine(a, v.GetHashCode())),
                            v => v.ToList())
                );
        });

        modelBuilder.Entity<IncidentAggregate>(entity =>
        {
            entity.ToTable(Db.Incidents.Table);

            entity.Property(e => e.Id)
                .HasColumnName(Db.Incidents.Columns.Id)
                .ValueGeneratedNever()
                .IsRequired();

            entity.HasKey(e => e.Id);

            entity.Property(e => e.IncidentType)
                .HasColumnName(Db.Incidents.Columns.IncidentType)
                .HasConversion<string>()
                .IsRequired();

            entity.Property(e => e.CityId)
                .HasColumnName(Db.Incidents.Columns.CityId);

            entity.Property(e => e.City)
                .HasColumnName(Db.Incidents.Columns.City)
                .IsRequired();

            entity.Property(e => e.BuildingId)
                .HasColumnName(Db.Incidents.Columns.BuildingId);

            entity.Property(e => e.Building)
                .HasColumnName(Db.Incidents.Columns.Building)
                .IsRequired();

            entity.Property(e => e.FloorId)
                .HasColumnName(Db.Incidents.Columns.FloorId);

            entity.Property(e => e.Floor)
                .HasColumnName(Db.Incidents.Columns.Floor)
                .IsRequired();

            entity.Property(e => e.RoomId)
                .HasColumnName(Db.Incidents.Columns.RoomId);

            entity.Property(e => e.Room)
                .HasColumnName(Db.Incidents.Columns.Room)
                .IsRequired();

            entity.Property(e => e.DeviceId)
                .HasColumnName(Db.Incidents.Columns.DeviceId);

            entity.Property(e => e.Device)
                .HasColumnName(Db.Incidents.Columns.Device);

            entity.Property(e => e.SensorId)
                .HasColumnName(Db.Incidents.Columns.SensorId);

            entity.Property(e => e.Topic)
                .HasColumnName(Db.Incidents.Columns.Topic)
                .IsRequired();

            entity.Property(e => e.CreatedAt)
                .HasColumnName(Db.Incidents.Columns.CreatedAt)
                .IsRequired();

            entity.Property(e => e.ResolvedAt)
                .HasColumnName(Db.Incidents.Columns.ResolvedAt);

            entity.HasIndex(e => e.IncidentType);

            entity.HasIndex(e => e.CityId);
            entity.HasIndex(e => e.City);

            entity.HasIndex(e => e.BuildingId);
            entity.HasIndex(e => e.Building);

            entity.HasIndex(e => e.FloorId);
            entity.HasIndex(e => e.Floor);

            entity.HasIndex(e => e.RoomId);
            entity.HasIndex(e => e.Room);

            entity.HasIndex(e => e.DeviceId);
            entity.HasIndex(e => e.Device);

            entity.HasIndex(e => e.SensorId);
            entity.HasIndex(e => e.Topic);

            entity.HasIndex(e => e.CreatedAt);
            entity.HasIndex(e => e.ResolvedAt);

            entity.HasIndex(e => new { e.City, e.Building, e.Floor, e.Room });
        });

        modelBuilder.Entity<IncidentNotificationAggregate>(entity =>
        {
            entity.ToTable(Db.IncidentNotifications.Table);

            entity.Property(e => e.Id)
                .HasColumnName(Db.IncidentNotifications.Columns.Id)
                .ValueGeneratedNever()
                .IsRequired();

            entity.HasKey(e => e.Id);

            entity.Property(e => e.IncidentId)
                .HasColumnName(Db.IncidentNotifications.Columns.IncidentId)
                .IsRequired();

            entity.Property(e => e.UserId)
                .HasColumnName(Db.IncidentNotifications.Columns.UserId)
                .IsRequired();

            entity.HasIndex(e => e.IncidentId);
            entity.HasIndex(e => e.UserId);
            entity.HasIndex(e => new { e.IncidentId, e.UserId }).IsUnique();
        });

        modelBuilder.ApplyConfigurationsFromAssembly(typeof(CommandAppDbContext).Assembly);
    }
}