using Microsoft.AspNetCore.Components;
using Teslametrics.App.Web.Extensions;
using Teslametrics.App.Web.Orleans.Wirenboard;

namespace Teslametrics.App.Web.Features.Main.Devices.Diagrams.Nodes.FridgeNode.Sensors;

public partial class SensorDataComponent : ISensorObserver, IAsyncDisposable
{

    private string? _topicName;
    private ISensorObserver? _observer;
    private string? _value;

    [Inject]
    private IClusterClient _clusterClient { get; set; } = null!;

    [Parameter]
    [EditorRequired]
    public string TopicName { get; set; } = null!;

    [Parameter]
    [EditorRequired]
    public string Name { get; set; } = string.Empty;

    [Parameter]
    public string? Icon { get; set; }

    [Parameter]
    public bool Error { get; set; }

    [Parameter]
    public Func<object, string> ValueProcessor { get; set; } = v => v.ToString() ?? "-";

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();

        var timer = new System.Timers.Timer(300_000); // 5 минут
        timer.Elapsed += OnTimerElapsed;
        timer.AutoReset = true;
        timer.Start();

        CompositeDisposable.Add(timer);
    }

    protected override async Task OnParametersSetAsync()
    {
        if (_topicName != TopicName)
        {
            _topicName = TopicName;
            await SubscribeAsync();
        }
        await base.OnParametersSetAsync();
    }

    public Task ReceiveData(ISensorData SensorData)
    {
        switch (SensorData)
        {
            case SensorDoubleData data:
                return InvokeAsync(() =>
                {
                    _value = ValueProcessor(data.Value);
                    StateHasChanged();
                });
            case SensorIntData data:
                return InvokeAsync(() =>
                {
                    _value = ValueProcessor(data.Value);
                    StateHasChanged();
                });
            case SensorBoolData data:
                return InvokeAsync(() =>
                {
                    _value = ValueProcessor(data.Value);
                    StateHasChanged();
                });
            case SensorStringData data:
                return InvokeAsync(() =>
                {
                    _value = ValueProcessor(data.Value);
                    StateHasChanged();
                });
        }

        return Task.CompletedTask;
    }

    private async Task SubscribeAsync()
    {
        SubscribeSensorUseCase.Response? response = null;
        try
        {
            if (_observer is not null)
                await UnsubscribeAsync();
            if (_observer is null)
            {
                _observer = _clusterClient.CreateObjectReference<ISensorObserver>(this);
            }
            response = await ScopeFactory.MediatorSend(new SubscribeSensorUseCase.Request(_observer, TopicName));
        }
        catch (Exception ex)
        {
            response = null;
            Snackbar.Add($"Не удалось подписаться на события датчика из-за ошибки сообщения с сервером. Повторите попытку позже.", MudBlazor.Severity.Error);
            Logger.LogError(ex, ex.Message);
        }

        if (response is null) return;

        switch (response.Result)
        {
            case SubscribeSensorUseCase.Result.Success:
                break;

            case SubscribeSensorUseCase.Result.ValidationError:
                Snackbar.Add($"Не удалось подписаться на события датчика из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.",
                MudBlazor.Severity.Error);
                break;
            case SubscribeSensorUseCase.Result.Unknown:
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(SensorDataComponent), nameof(SubscribeSensorUseCase));
                Snackbar.Add($"Не удалось подписаться на события датчика из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.",
                MudBlazor.Severity.Error);
                break;

            default:
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(SensorDataComponent), nameof(SubscribeSensorUseCase), response.Result);
                Snackbar.Add($"Не удалось подписаться на события датчика из-за ошибки: {response.Result}", MudBlazor.Severity.Error);
                break;
        }
    }

    private async Task UnsubscribeAsync()
    {
        if (_observer is null) return;
        UnsubscribeSensorUseCase.Response? response = null;
        try
        {
            response = await ScopeFactory.MediatorSend(new UnsubscribeSensorUseCase.Request(_observer));
            _clusterClient.DeleteObjectReference<ISensorObserver>(_observer);
        }
        catch (Exception ex)
        {
            response = null;
            Snackbar.Add($"Не удалось отменить подписку на события датчика из-за ошибки сообщения с сервером. Повторите попытку позже.", MudBlazor.Severity.Error);
            Logger.LogError(ex, ex.Message);
        }

        if (response is null) return;

        switch (response.Result)
        {
            case UnsubscribeSensorUseCase.Result.Success:
                break;

            case UnsubscribeSensorUseCase.Result.ValidationError:
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(SensorDataComponent), nameof(UnsubscribeSensorUseCase));
                Snackbar.Add($"Не удалось отменить подписку на события датчика из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.",
                MudBlazor.Severity.Error);
                break;

            default:
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(SensorDataComponent), nameof(UnsubscribeSensorUseCase), response.Result);
                Snackbar.Add($"Не удалось отменить подписку на события датчика из-за ошибки: {response.Result}", MudBlazor.Severity.Error);
                break;
        }
    }

    public async ValueTask DisposeAsync()
    {
        await UnsubscribeAsync();
    }

    private void OnTimerElapsed(object? sender, System.Timers.ElapsedEventArgs e)
    {
        InvokeAsync(async () =>
        {
            await SubscribeAsync();
            StateHasChanged();
        });
    }
}
