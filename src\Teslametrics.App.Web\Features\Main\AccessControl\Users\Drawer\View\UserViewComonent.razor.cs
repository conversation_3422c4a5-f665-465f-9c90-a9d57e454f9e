using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using MudBlazor;
using System.Reactive;
using System.Text;
using Teslametrics.App.Web.Components.Drawer;
using Teslametrics.App.Web.Eto.Users;
using Teslametrics.App.Web.Events.Users;
using Teslametrics.App.Web.Extensions;
using Teslametrics.App.Web.Shared;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Users.Drawer.View;

public partial class UserViewComonent
{
	[CascadingParameter(Name = DrawerConsts.InstanceName)]
	private DrawerComponent Drawer { get; set; } = null!;

	private string _contextMenuAuthPolicyString => new StringBuilder()
		.Append("," + AppPermissions.Main.AccessControl.Users.ForceChangePassword.GetEnumPermissionString())
		.AppendIf("," + AppPermissions.Main.AccessControl.Users.Update.GetEnumPermissionString(), !_model!.IsSystem)
		.AppendIf("," + AppPermissions.Main.AccessControl.Users.Unlock.GetEnumPermissionString(), !_model.IsSystem && _model.LockedoutEnabled)
		.AppendIf("," + AppPermissions.Main.AccessControl.Users.Lock.GetEnumPermissionString(), !_model.IsSystem && !_sameUser && !_model.LockedoutEnabled)
		.AppendIf("," + AppPermissions.Main.AccessControl.Users.Delete.GetEnumPermissionString(), !_model!.IsSystem)
		.ToString();// Есть вероятность, что спереди будет запятая.

	private bool _subscribing;
	private Guid _userId;
	private GetUserUseCase.Response? _model;
	private SubscribeUserUseCase.Response? _subscriptionResult;
	private bool _sameUser; // Пользователь открыл самого себя

	#region Parameters
	[Parameter]
	[EditorRequired]
	public Guid UserId { get; set; }

	[Parameter]
	[EditorRequired]
	public Guid OrganizationId { get; set; }
	#endregion

	protected override void OnInitialized()
	{
		AuthenticationStateProvider.AuthenticationStateChanged += OnAuthStateChanged;
		base.OnInitialized();
	}

	protected override async Task OnParametersSetAsync()
	{
		if (UserId != _userId)
		{
			_userId = UserId;
			await FetchAsync();
		}

		await base.OnParametersSetAsync();
	}

	protected async Task FetchAsync()
	{
		if (IsLoading) return;

		await SetLoadingAsync(true);
		try
		{
			_model = await ScopeFactory.MediatorSend(new GetUserUseCase.Query(UserId, OrganizationId));
		}
		catch (Exception ex)
		{
			_model = null;
			Snackbar.Add("Не удалось получить пользователя из-за ошибки сообщения с сервером. Повторите попытку и обратитесь к администратору", Severity.Error);
			Logger.LogError(ex, ex.Message);
		}

		if (_model is null) return;

		await SetLoadingAsync(false);
		switch (_model.Result)
		{
			case GetUserUseCase.Result.Success:
				await SubscribeAsync();
				break;
			case GetUserUseCase.Result.ValidationError:
				Snackbar.Add("Ошибка валидации данных", Severity.Error);
				break;
			case GetUserUseCase.Result.Unknown:
				Snackbar.Add("Не удалось получить пользователя из-за неизвестной ошибки. Повторите попытку и обратитесь к администратору", Severity.Error);
				Logger.LogError("UNKNOWN result in {Component}.{Method}, using: {UseCase}", nameof(UserViewComonent), nameof(FetchAsync), nameof(GetUserUseCase));
				break;
			default:
				Snackbar.Add("Не удалось получить пользователя из-за непредвиденной ошибки. Повторите попытку и обратитесь к администратору", Severity.Error);
				Logger.LogError("Unexpected result in {Component}.{Method}, using: {UseCase}. Received result: {Result}", nameof(UserViewComonent), nameof(FetchAsync), nameof(GetUserUseCase), _model.Result);
				break;
		}
	}

	private async Task SubscribeAsync()
	{
		try
		{
			if (_model is null || !_model.IsSuccess) return;
			await SetSubscribingAsync(true);
			Unsubscribe();
			_subscriptionResult = await ScopeFactory.MediatorSend(new SubscribeUserUseCase.Request(Observer.Create<object>(OnAppEventHandler, OnError), _model.Id));
			switch (_subscriptionResult.Result)
			{
				case SubscribeUserUseCase.Result.Success:
					CompositeDisposable.Add(_subscriptionResult.Subscription!);
					break;

				case SubscribeUserUseCase.Result.ValidationError:
					Snackbar.Add("Ошибка валидации при подписке на события", Severity.Error);
					break;

				case SubscribeUserUseCase.Result.Unknown:
				default:
					throw new Exception($"Unexpected result in {nameof(SubscribeUserUseCase)}: {_subscriptionResult.Result}");
			}
		}
		catch (Exception exc)
		{
			Snackbar.Add("Не удалось получить подписку на события пользователя. Повторите попытку", Severity.Error);
			Logger.LogError(exc, exc.Message);
		}
		finally
		{
			await SetSubscribingAsync(false);
		}
	}

	private void Unsubscribe()
	{
		if (_subscriptionResult is not null && _subscriptionResult.Subscription is not null)
		{
			CompositeDisposable.Remove(_subscriptionResult.Subscription);
			_subscriptionResult.Subscription.Dispose();
		}
	}
	protected Task SetSubscribingAsync(bool isLoading = true) => UpdateViewAsync(() =>
	{
		_subscribing = isLoading;
	});

	#region [Actions]
	private Task CancelAsync() => Drawer.HideAsync();
	private Task RefreshAsync() => FetchAsync();
	private void Edit() => EventSystem.Publish(new UserEditEto(UserId));
	private void Delete() => EventSystem.Publish(new UserDeleteEto(OrganizationId, UserId));
	protected void LoginpasswordChange() => EventSystem.Publish(new ForcePasswordChangeEto(UserId));
	private void ChangeUserPassword() => EventSystem.Publish(new ChangeUserPasswordEto(UserId));
	protected virtual async Task UnLockAsync()
	{
		if (IsLoading || _model is null) return;

		try
		{
			await SetLoadingAsync();
			var result = await ScopeFactory.MediatorSend(new UnlockUserUseCase.Command(_model.Id));
			if (result.IsSuccess)
			{
				Snackbar.Add("Пользователь разблокирован", Severity.Success);
				return;
			}

			switch (result.Result)
			{
				case UnlockUserUseCase.Result.CannotUnlockSystemUser:
					Snackbar.Add("Системного пользователя невозможно разблокировать", Severity.Error);
					break;
				case UnlockUserUseCase.Result.UserNotFound:
					Snackbar.Add("Выбранный пользователь недоступен", Severity.Error);
					break;
				case UnlockUserUseCase.Result.ValidationError:
					Snackbar.Add("Произошла ошибка валидации пользователя", Severity.Error);
					break;
				case UnlockUserUseCase.Result.Unknown:
				default:
					Snackbar.Add("Произошла неизвестная ошибка при попытке разблокировать пользователя. Код: " + result.Result.ToString(), Severity.Error);
					break;
			}
		}
		catch (Exception ex)
		{
			Snackbar.Add("Произошла ошибка при смене пароля", Severity.Error);
			Logger.LogError(ex, ex.Message);
		}
		finally
		{
			await SetLoadingAsync(false);
		}
	}

	protected virtual async Task LockAsync()
	{
		if (IsLoading || _model is null) return;

		try
		{
			await SetLoadingAsync();
			var result = await ScopeFactory.MediatorSend(new LockUserUseCase.Command(_model.Id));
			if (result.IsSuccess)
			{
				Snackbar.Add("Пользователь заблокирован", Severity.Success);
				return;
			}

			switch (result.Result)
			{
				case LockUserUseCase.Result.CannotLockSystemUser:
					Snackbar.Add("Системного пользователя невозможно заблокировать", Severity.Error);
					break;
				case LockUserUseCase.Result.UserNotFound:
					Snackbar.Add("Выбранный пользователь недоступен", Severity.Error);
					break;
				case LockUserUseCase.Result.ValidationError:
					Snackbar.Add("Произошла ошибка валидации пользователя", Severity.Error);
					break;
				case LockUserUseCase.Result.Unknown:
				default:
					Snackbar.Add("Произошла неизвестная ошибка при попытке заблокировать пользователя. Код: " + result.Result.ToString(), Severity.Error);
					break;
			}
		}
		catch (Exception ex)
		{
			Snackbar.Add("Произошла ошибка при смене пароля", Severity.Error);
			Logger.LogError(ex, ex.Message);
		}
		finally
		{
			await SetLoadingAsync(false);
		}
	}
	#endregion

	#region [Event Handlers]
	private async void OnAppEventHandler(object appEvent)
	{
		if (_model is null) return;

		switch (appEvent)
		{
			case SubscribeUserUseCase.UpdatedEvent:
				await FetchAsync();
				await UpdateViewAsync();
				break;

			case SubscribeUserUseCase.DeletedEvent:
				Snackbar.Add("Просматриваемый вами пользователь был удалён", Severity.Warning);
				await CancelAsync();
				break;

			default:
				break;
		}
	}

	private void OnError(Exception exc)
	{
		Logger.LogError(exc, exc.Message);
		Snackbar.Add("Ошибка валидации при подписке на события", Severity.Error);
	}

	private async void OnAuthStateChanged(Task<AuthenticationState> authenticationState)
	{
		if (_model is null) return;

		var authState = await authenticationState;
		if (authState == null || (!authState.User.Identity?.IsAuthenticated ?? true)) return;

		_sameUser = authState.User.GetUserId()!.Value == _model.Id;
	}
	#endregion [Event Handlers]
}
