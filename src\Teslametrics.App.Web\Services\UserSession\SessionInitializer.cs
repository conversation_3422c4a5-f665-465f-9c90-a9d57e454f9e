namespace Teslametrics.App.Web.Services.UserSession;

public class SessionInitializer : BackgroundService
{
	private readonly ISessionProvider _sessionProvider;

	public SessionInitializer(ISessionProvider sessionProvider)
	{
		_sessionProvider = sessionProvider;
	}

	protected override Task ExecuteAsync(CancellationToken stoppingToken) // TODO:: CancellationToken
	{
		return _sessionProvider.InitializeAsync();
	}
}