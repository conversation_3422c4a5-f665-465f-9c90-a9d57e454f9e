using System.Reflection;

namespace Teslametrics.App.Web.Shared;

[AttributeUsage(AttributeTargets.Class)]
public class FqdnRootAttribute : Attribute;

public static class Fqdn<T>
{
    private static readonly Dictionary<string, string> _namesCache;
    private static readonly Dictionary<string, MemberInfo> _membersCache;
    private static readonly Dictionary<string, Type> _typesCache;

    static Fqdn()
    {
        _namesCache = [];
        _membersCache = [];
        _typesCache = [];

        InitializeNamesCache();
    }

    public static IEnumerable<string> GetNames(Func<Type, MemberInfo, bool>? predicate = null)
    {
        return predicate is not null
            ? _namesCache.Values.Where(v => predicate(_typesCache[v], _membersCache[v]))
            : _namesCache.Values;
    }

    public static string GetName<TEnum>(TEnum permissions)
        where TEnum : Enum
    {
        return GetNames(permissions, (_, member) => member.Name == permissions.ToString()).Single();
    }

    public static IEnumerable<string> GetNames<TEnum>(TEnum permissions, Func<Type, MemberInfo, bool>? predicate = null)
        where TEnum : Enum
    {
        var enumType = typeof(TEnum);

        if (Attribute.IsDefined(enumType, typeof(FlagsAttribute)))
        {
            foreach (var flag in Enum.GetValues(enumType))
            {
                var flagValue = (TEnum)flag;

                if (Convert.ToUInt64(flagValue) != 0 && permissions.HasFlag(flagValue))
                {
                    var key = $"{enumType.FullName}.{flagValue}";
                    if (_namesCache.TryGetValue(key, out var name))
                    {
                        if (predicate is null || predicate(_typesCache[name], _membersCache[name]))
                        {
                            yield return name;
                        }
                    }
                }
            }
        }
        else
        {
            var key = $"{enumType.FullName}.{permissions}";
            if (_namesCache.TryGetValue(key, out var name))
            {
                if (predicate is null || predicate(_typesCache[name], _membersCache[name]))
                {
                    yield return name;
                }
            }
        }
    }

    private static void InitializeNamesCache()
    {
        CacheEnumValues(typeof(T));
    }

    private static void CacheEnumValues(Type type)
    {
        if (type.IsEnum)
        {
            var enumValues = GetEnumValues(type);

            foreach (var enumValue in enumValues)
            {
                var key = $"{type.FullName}.{enumValue.Value}";
                var name = $"{GetRootNamespace(type)}{enumValue.Value}";

                _namesCache[key] = name;
                _membersCache[name] = enumValue.MemberInfo;
                _typesCache[name] = type;
            }
        }

        var nestedTypes = type.GetNestedTypes(BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Static);

        foreach (var nestedType in nestedTypes)
        {
            CacheEnumValues(nestedType);
        }
    }

    private static IEnumerable<(MemberInfo MemberInfo, string Value)> GetEnumValues(Type type)
    {
        foreach (var value in Enum.GetValues(type))
        {
            if (Convert.ToUInt64(value) == 0)
            {
                continue;
            }

            var memberInfo = type.GetMember(value.ToString()!).First();

            yield return (memberInfo, value!.ToString()!);
        }
    }

    public static string GetRootNamespace(Type type)
    {
        var namespaceParts = new Stack<string>();

        while (type != null)
        {
            namespaceParts.Push(type.Name);

            if (type.GetCustomAttribute<FqdnRootAttribute>() != null)
            {
                break;
            }
            type = type.DeclaringType!;
        }

        return string.Join(".", namespaceParts) + ".";
    }

    public static Type? GetValueType(string memberName)
    {
        return _typesCache.GetValueOrDefault(memberName);
	}
}