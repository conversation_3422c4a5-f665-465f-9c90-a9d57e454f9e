using Orleans.Streams;
using System.Collections.Concurrent;
using Teslametrics.MediaServer.Orleans.Camera.Events;
using Teslametrics.Shared;
using static Teslametrics.MediaServer.Orleans.Camera.IMediaServerGrain;

namespace Teslametrics.MediaServer.Orleans.Camera;

[KeepAlive]
public class MediaServerGrain : Grain, IMediaServerGrain
{
    private readonly ILogger<MediaServerGrain> _logger;
    private readonly HashSet<Guid> _cameras;
    private StreamSubscriptionHandle<CameraStatusChangedEvent>? _subscriptionHandle;

    private readonly ConcurrentDictionary<Guid, CameraStatus> _statuses;

    public MediaServerGrain(ILogger<MediaServerGrain> logger)
    {
        _cameras = [];
        _statuses = [];
        _logger = logger;
    }

    public override async Task OnActivateAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("MediaServerGrain activated with ID: {GrainId}", this.GetPrimaryKeyString());

        var provider = this.GetStreamProvider(StreamNames.CameraEventLiveStream);

        _subscriptionHandle = await provider.GetStream<CameraStatusChangedEvent>(StreamId.Create(StreamNamespaces.CameraStreams, Guid.Empty))
            .SubscribeAsync((statusChangedEvent, token) =>
            {
                _statuses[statusChangedEvent.Id] = statusChangedEvent.Status;
                return Task.CompletedTask;
            });

        // Регистрируем напоминание для предотвращения деактивации зерна
        try
        {
            _logger.LogInformation("Registered keep-alive reminder for MqttGrain with ID: {GrainId}",
                this.GetPrimaryKeyString());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to register keep-alive reminder for MqttGrain with ID: {GrainId}",
                this.GetPrimaryKeyString());
        }

        await base.OnActivateAsync(cancellationToken);
    }

    public override async Task OnDeactivateAsync(DeactivationReason reason, CancellationToken cancellationToken)
    {
        if (_subscriptionHandle is not null)
        {
            await _subscriptionHandle.UnsubscribeAsync();
        }

        _logger.LogInformation("MediaServerGrain deactivating with ID: {GrainId}, reason: {Reason}",
            this.GetPrimaryKeyString(), reason);

        _logger.LogInformation("MediaServerGrain resources cleaned up for ID: {GrainId}",
            this.GetPrimaryKeyString());

        await base.OnDeactivateAsync(reason, cancellationToken);
    }

    public async Task ConnectRtspAsync(CameraConnectRtspRequest request)
    {
        if (!_cameras.Contains(request.CameraId))
        {
            var grain = GrainFactory.GetGrain<ICameraGrain>(request.CameraId);
            await grain.ConnectRtspAsync(new ICameraGrain.ConnectRtspRequest(request.ArchiveUri, request.ViewUri, request.PublicUri));

            _cameras.Add(request.CameraId);
        }
    }

    public async Task ConnectOnvifAsync(CameraConnectOnvifRequest request)
    {
        if (!_cameras.Contains(request.CameraId))
        {
            var grain = GrainFactory.GetGrain<ICameraGrain>(request.CameraId);
            await grain.ConnectOnvifAsync(new ICameraGrain.ConnectOnvifRequest(request.Host, request.Port, request.Username, request.Password));

            _cameras.Add(request.CameraId);
        }
    }

    public async Task DisconnectAsync(CameraDisconnectRequest request)
    {
        if (_cameras.Contains(request.CameraId))
        {
            var grain = GrainFactory.GetGrain<ICameraGrain>(request.CameraId);
            await grain.DisconnectAsync();

            _cameras.Remove(request.CameraId);
        }
    }

    public async Task StopAllAsync()
    {
        foreach (var cameraId in _cameras)
        {
            var grain = GrainFactory.GetGrain<ICameraGrain>(cameraId);
            await grain.DisconnectAsync();
        }

        _cameras.Clear();
    }

    public Task<CameraStatus> GetStatusAsync(GetCameraStatusRequest request)
    {
        if (_statuses.TryGetValue(request.CameraId, out var status))
        {
            return Task.FromResult(status);
        }
        else
        {
            return Task.FromResult(CameraStatus.Stopped);
        }
    }
}