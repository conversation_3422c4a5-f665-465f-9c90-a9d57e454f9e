let divider = null;
let container = null;
let firstPanel = null;
let storageId = null;
let isReadOnly = false;
let orientation = "horizontal";

let isResizing = false;

function onMouseDown(e) {
  if (isReadOnly) return;

  isResizing = true;
  document.body.style.cursor =
    orientation === "horizontal" ? "col-resize" : "row-resize";
}

function onMouseMove(e) {
  if (!isResizing || isReadOnly) return;

  if (orientation === "horizontal") {
    handleHorizontalResize(e);
  } else {
    handleVerticalResize(e);
  }
}

function handleHorizontalResize(e) {
  let offset = 0;
  if (container) {
    offset =
      container.getBoundingClientRect().x +
      parseInt(getComputedStyle(container).gap, 10);
  }

  const newWidth = e.clientX - offset;
  const minWidth = parseInt(getComputedStyle(firstPanel).minWidth, 10);
  const maxWidth = window.innerWidth * 0.7; // 70% of the viewport width

  if (newWidth >= minWidth && newWidth <= maxWidth) {
    firstPanel.style.width = `${newWidth}px`;
    localStorage.setItem("resizerWidth-" + storageId, newWidth);
  }
}

function handleVerticalResize(e) {
  let offset = 0;
  if (container) {
    offset =
      container.getBoundingClientRect().y +
      parseInt(getComputedStyle(container).gap, 10);
  }

  const newHeight = e.clientY - offset;
  const minHeight = parseInt(getComputedStyle(firstPanel).minHeight, 10);
  const maxHeight = window.innerHeight * 0.7; // 70% of the viewport height

  if (newHeight >= minHeight && newHeight <= maxHeight) {
    firstPanel.style.height = `${newHeight}px`;
    localStorage.setItem("resizerHeight-" + storageId, newHeight);
  }
}

function onMouseUp(e) {
  if (isResizing) {
    isResizing = false;
    document.body.style.cursor = "default";
  }
}
function dispose() {
  // Безопасно удаляем обработчики событий
  try {
    document.removeEventListener("mouseup", onMouseUp);
    document.removeEventListener("mousemove", onMouseMove);

    if (divider) {
      divider.removeEventListener("mousedown", onMouseDown);
    }
  } catch (error) {
    console.warn(
      "ResizerComponent: Ошибка при удалении обработчиков событий:",
      error
    );
  }

  // Очищаем inline стили при смене ориентации
  if (firstPanel) {
    try {
      firstPanel.style.width = "";
      firstPanel.style.height = "";
    } catch (error) {
      console.warn("ResizerComponent: Ошибка при очистке стилей:", error);
    }
  }

  // Сбрасываем переменные
  divider = null;
  container = null;
  firstPanel = null;
  storageId = null;
  isReadOnly = false;
  orientation = "horizontal";
  isResizing = false;
}

function initialize(draggerId, localStorageId, readOnly, orientationValue) {
  try {
    storageId = localStorageId;
    isReadOnly = readOnly;
    orientation = orientationValue;

    // Ждем, пока DOM будет готов
    if (document.readyState === "loading") {
      document.addEventListener("DOMContentLoaded", () => {
        initializeInternal(draggerId);
      });
    } else {
      initializeInternal(draggerId);
    }
  } catch (error) {
    console.error("ResizerComponent: Ошибка при инициализации:", error);
  }
}

function initializeInternal(draggerId) {
  try {
    divider = document.getElementById(draggerId);
    if (!divider) {
      console.error(
        "ResizerComponent: Не удалось найти разделитель с ID:",
        draggerId
      );
      return;
    }

    container = divider.parentNode;
    if (!container) {
      console.error(
        "ResizerComponent: Не удалось найти контейнер для разделителя"
      );
      return;
    }

    // Выбираем первую панель в зависимости от ориентации
    if (orientation === "horizontal") {
      firstPanel = container.querySelector(".left_panel");
    } else {
      firstPanel = container.querySelector(".top_panel");
    }

    if (!firstPanel) {
      console.error(
        "ResizerComponent: Не удалось найти панель для ориентации:",
        orientation
      );
      return;
    }

    // Очищаем предыдущие inline стили (только после проверки на существование)
    try {
      firstPanel.style.width = "";
      firstPanel.style.height = "";
    } catch (error) {
      console.warn(
        "ResizerComponent: Ошибка при очистке стилей панели:",
        error
      );
      return;
    }

    // Восстанавливаем сохраненный размер для текущей ориентации
    try {
      if (orientation === "horizontal") {
        const savedWidth = localStorage.getItem("resizerWidth-" + storageId);
        if (savedWidth && firstPanel) {
          firstPanel.style.width = `${savedWidth}px`;
        }
      } else {
        const savedHeight = localStorage.getItem("resizerHeight-" + storageId);
        if (savedHeight && firstPanel) {
          firstPanel.style.height = `${savedHeight}px`;
        }
      }
    } catch (error) {
      console.warn(
        "ResizerComponent: Ошибка при восстановлении размеров:",
        error
      );
    }

    // Добавляем обработчики событий только если не в режиме только для чтения
    if (!isReadOnly && divider) {
      try {
        document.addEventListener("mousemove", onMouseMove, true);
        divider.addEventListener("mousedown", onMouseDown, true);
        document.addEventListener("mouseup", onMouseUp, true);
      } catch (error) {
        console.warn(
          "ResizerComponent: Ошибка при добавлении обработчиков событий:",
          error
        );
      }
    }
  } catch (error) {
    console.error(
      "ResizerComponent: Ошибка при внутренней инициализации:",
      error
    );
  }
}

export { dispose, initialize };
