let divider = null;
let container = null;
let leftPanel = null;
let storageId = null;

let isResizing = false;

function onMouseDown(e) {
	isResizing = true;
	document.body.style.cursor = 'col-resize';
}

function onMouseMove(e) {
	if (!isResizing) return;

	let offset = 0;
	if (container) {
		offset = container.getBoundingClientRect().x + parseInt(getComputedStyle(container).gap, 10);
	}

	const newWidth = e.clientX - offset;
	const minWidth = parseInt(getComputedStyle(leftPanel).minWidth, 10);
	const maxWidth = window.innerWidth * 0.7; // 70% of the viewport width

	if (newWidth >= minWidth && newWidth <= maxWidth) {
		leftPanel.style.width = `${newWidth}px`;
		localStorage.setItem('resizerWidth-' + storageId, newWidth);
	}
}

function onMouseUp(e) {
	if (isResizing) {
		isResizing = false;
		document.body.style.cursor = 'default';
	}
}
function dispose() {
	document.removeEventListener("mouseup", onMouseUp);
	container.removeEventListener("mousedown", onMouseDown);
	document.removeEventListener("mousemove", onMouseMove);

	container = null;
	container = null;
	leftPanel = null;
}

function initialize(draggerId, localStorageId) {
	storageId = localStorageId;
	divider = document.getElementById(draggerId);
	container = divider.parentNode;
	leftPanel = container.querySelector('.left_panel');

	const savedWidth = localStorage.getItem('resizerWidth-' + storageId);
	if (savedWidth) {
		leftPanel.style.width = `${savedWidth}px`;
	}

	document.addEventListener('mousemove', onMouseMove, true);
	divider.addEventListener('mousedown', onMouseDown, true);
	document.addEventListener("mouseup", onMouseUp, true);
}

export {
	dispose,
	initialize
};