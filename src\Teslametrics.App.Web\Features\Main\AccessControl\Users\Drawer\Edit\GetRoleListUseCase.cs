﻿using Dapper;
using FluentValidation;
using MediatR;
using System.Data;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Data.Sql;
using Teslametrics.App.Web.Services.Persistence;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Users.Drawer.Edit;

public static class GetRoleListUseCase
{
    public record Query(Guid OrganizationId, string Filter) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public List<Item> Items { get; init; }


        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(List<Item> items)
        {
            Items = items;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Result = result;

            Items = [];
        }

        public record Item(Guid Id, string Name);
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError
    }
    public class Validator : AbstractValidator<Query>
    {
        public Validator()
        {
            RuleFor(q => q.OrganizationId).NotEmpty();
            RuleFor(q => q.Filter).MaximumLength(60);
        }
    }

    public class Handler : IRequestHandler<Query, Response>
    {
        private readonly IValidator<Query> _validator;
        private readonly IDbConnection _dbConnection;

        public Handler(IValidator<Query> validator,
                       IDbConnection dbConnection)
        {
            _validator = validator;
            _dbConnection = dbConnection;
        }

        public async Task<Response> Handle(Query request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            var template = SqlQueryBuilder.Create()
                .Select(Db.Roles.Props.Id)
                .Select(Db.Roles.Props.Name)
                .Where(Db.Roles.Props.OrganizationId, ":OrganizationId", SqlOperator.Equals, new { request.OrganizationId })
                .WhereIf(!string.IsNullOrEmpty(request.Filter), Db.Roles.Props.Name, "CONCAT('%', :Filter, '%')", SqlOperator.Like, new { request.Filter })
                .Build(QueryType.Standard, Db.Roles.Table, RowSelection.AllRows);

            var roles = await _dbConnection.QueryAsync<RoleModel>(template.RawSql, template.Parameters);

            return new Response(roles.Select(r => new Response.Item(r.Id, r.Name)).ToList());
        }
    }

    public record RoleModel(Guid Id, string Name);
}