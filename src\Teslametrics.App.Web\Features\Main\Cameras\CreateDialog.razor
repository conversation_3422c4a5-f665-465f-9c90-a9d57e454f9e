@inherits InteractiveBaseComponent
<MudDialog ActionsClass="mx-2"
           @bind-Visible="@_isVisible"
           Options="_dialogOptions">
    <TitleContent>
        <MudStack Row=true>
            <MudIcon Icon="@Icons.Material.Filled.Add"
                     Class="mt-1"
                     Color="Color.Secondary" />
            <MudStack Spacing="0">
                <MudText Typo="Typo.h6">Создание</MudText>
            </MudStack>
        </MudStack>
    </TitleContent>
    <DialogContent>
        <MudGrid>
            <MudItem xs="12"
                     md="6">
                <MudButton OnClick="CreateGroup"
                           Variant="Variant.Outlined"
                           FullWidth="true"
                           Class="py-15"
                           Style="border-color: var(--mud-palette-lines-default);">
                    <MudStack AlignItems="AlignItems.Center"
                              Justify="Justify.Center">
                        <MudBadge Icon="@Icons.Material.Filled.Add"
                                  Color="Color.Success"
                                  Overlap="true"
                                  Bordered="true"
                                  Class="mx-6 my-4"
                                  Origin="Origin.BottomLeft"
                                  BadgeClass="mt-n3">
                            <MudIcon Icon="@Icons.Material.Outlined.Folder"
                                     Size="Size.Large" />
                        </MudBadge>
                        <MudText Typo="Typo.body1">Создание группы устройств</MudText>
                    </MudStack>
                </MudButton>
            </MudItem>

            <MudItem xs="12"
                     md="6">
                <MudButton OnClick="CreateCamera"
                           Variant="Variant.Outlined"
                           FullWidth="true"
                           Class="py-15"
                           Style="border-color: var(--mud-palette-lines-default);">
                    <MudStack AlignItems="AlignItems.Center"
                              Justify="Justify.Center">
                        <MudBadge Icon="@Icons.Material.Filled.Add"
                                  Color="Color.Success"
                                  Overlap="true"
                                  Bordered="true"
                                  Class="mx-6 my-4"
                                  Origin="Origin.BottomLeft"
                                  BadgeClass="mt-n3">
                            <MudIcon Icon="@Icons.Material.Outlined.Camera"
                                     Size="Size.Large" />
                        </MudBadge>
                        <MudText Typo="Typo.body1">Создание камер</MudText>
                    </MudStack>
                </MudButton>
            </MudItem>
        </MudGrid>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="Cancel">Отменить</MudButton>
    </DialogActions>
</MudDialog>