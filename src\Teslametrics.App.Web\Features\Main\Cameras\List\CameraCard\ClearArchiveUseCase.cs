using System.Data;
using System.Reflection;
using Dapper;
using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Domain.Cameras;
using Teslametrics.App.Web.Orleans.Camera;
using Teslametrics.App.Web.Services.FileStorage;
using Teslametrics.App.Web.Services.Persistence;

namespace Teslametrics.App.Web.Features.Main.Cameras.List.CameraCard;

public static class ClearArchiveUseCase
{
    public record Command(Guid CameraId) : BaseRequest<Response>;


    public record Response : BaseResponse
    {
        public Guid Id { get; private set; }

        public Result Result { get; private set; }

        public bool IsSuccess => Result == Result.Success;

        public Response(Guid id)
        {
            Id = id;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Id = Guid.Empty;
            Result = result;
        }
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        CameraNotFound
    }

    public class Validator : AbstractValidator<Command>
    {
        public Validator()
        {
            RuleFor(c => c.CameraId).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Command, Response>
    {
        private readonly IValidator<Command> _validator;
        private readonly ICameraRepository _cameraRepository;
        private readonly IClusterClient _clusterClient;
        private readonly IFileStorage _fileStorage;

        public Handler(IValidator<Command> validator,
                       ICameraRepository cameraRepository,
                       IClusterClient clusterClient,
                       IFileStorage fileStorage)
        {
            _validator = validator;
            _cameraRepository = cameraRepository;
            _clusterClient = clusterClient;
            _fileStorage = fileStorage;
        }

        public async Task<Response> Handle(Command request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            var camera = await _cameraRepository.FindAsync(request.CameraId, cancellationToken);
            if (camera is null)
            {
                return new Response(Result.CameraNotFound);
            }

            var grain = _clusterClient.GetGrain<IMediaServerGrain>(request.CameraId);
            await grain.DisconnectAsync(new IMediaServerGrain.CameraDisconnectRequest(request.CameraId));

            await _fileStorage.ClearDirectoryAsync(request.CameraId.ToString("N"));

            return new Response(camera.Id);
        }
    }
}