.stream_content
{
	aspect-ratio: 16/9;
	display: flex;
	align-items: center;
	align-content: center;
	justify-content: center;
}

.empty_cell
{
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	color: var(--mud-palette-text-secondary);
	gap: 8px;
	height: 100%;
}

::deep .camera_cell
{
	position: relative;
	width: 100%;
	overflow: hidden;
	height: 100%;
	display: flex;
	justify-content: center;
}

.empty_cell ::deep .mud-icon-root
{
	font-size: 2rem;
}

::deep .camera_name
{
	background: var(--mud-palette-background-gray);
	position: absolute;
	padding: 4px 8px;
	border-radius: 4px;
	font-size: 0.875rem;
	z-index: 1;
	top: 8px;
	left: 8px;
}