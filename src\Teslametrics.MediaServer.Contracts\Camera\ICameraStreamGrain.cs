using Orleans.Concurrency;

namespace Teslametrics.MediaServer.Orleans.Camera;
public interface ICameraStreamGrain : IGrainWithGuidKey
{
    public Task<CameraStreamState> GetStatusAsync();

    [OneWay]
    public Task ConnectAsync(ConnectRequest request);

    [OneWay]
    public Task DisconnectAsync();

    [GenerateSerializer]
    public record ConnectRequest(Guid CameraId, string Uri, StreamType StreamType);
}
