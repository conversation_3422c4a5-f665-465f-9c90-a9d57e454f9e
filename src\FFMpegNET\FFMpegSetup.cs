using System.Reflection;
using System.Runtime.InteropServices;
using FFmpeg.AutoGen;
using Microsoft.Extensions.Logging;

namespace FFMpegNET;

public static class FFMpegSetup
{
    public static void InitializeFFMpeg(ILogger? logger = null, string? ffmpegPath = null)
    {
        RegisterFFmpegBinaries(logger, ffmpegPath);
        DynamicallyLoadedBindings.Initialize();
        ffmpeg.avdevice_register_all();
        ffmpeg.avformat_network_init();
    }

    public static void FinalizeFFMpeg()
    {
        ffmpeg.avformat_network_deinit();
    }

    private static void RegisterFFmpegBinaries(ILogger? logger, string? ffmpegPath)
    {
        var current = Path.GetDirectoryName(Assembly.GetEntryAssembly()!.Location)!;
        string? probe;

        if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
        {
            probe = ffmpegPath ?? Path.Combine("libraries", "win-x64");
        }
        else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
        {
            probe = ffmpegPath ?? Path.Combine("libraries", "linux-x64");
        }
        else
        {
            throw new PlatformNotSupportedException("Only Windows and Linux are supported.");
        }

        while (current != null)
        {
            var ffmpegBinaryPath = Path.Combine(current, probe);

            if (Directory.Exists(ffmpegBinaryPath))
            {
                logger?.LogInformation("FFmpeg binaries found in: {FfmpegBinaryPath}", ffmpegBinaryPath);
                ffmpeg.RootPath = ffmpegBinaryPath;
                return;
            }

            current = Directory.GetParent(current)?.FullName;
        }

        if (string.IsNullOrEmpty(ffmpeg.RootPath))
        {
            throw new Exception("FFmpeg binaries not found.");
        }
    }
}