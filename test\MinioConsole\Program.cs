using System.Globalization;
using Minio;
using Minio.DataModel.Args;
using Minio.DataModel.ILM;
using Minio.DataModel.Tags;
using Minio.Exceptions;

public class Program
{
    private static async Task Main(string[] args)
    {
        var endpoint  = "localhost:9000";
        var accessKey = "ilZSyeoDP3DA8DySbKWA";
        var secretKey = "fe9nKbvl9XO6z849VpVAqrRYLp5FNW1P5hA0YbhE";
        try
        {
            var minio = new MinioClient()
                                .WithEndpoint(endpoint)
                                .WithCredentials(accessKey, secretKey)
                                .Build();
            //await Run(minio);
            // //await GetObjectAsync(minio, "testupload", "AD-Zielonka25.zip", "test.zip");

            await ListBucketsAsync(minio);

            // var rules = new List<LifecycleRule>();

            // // Создаем правило с удалением через 1 день после создания объекта
            // var expiration = new Expiration { Days = 1 }; // Установка дней через свойство

            // // Создаем правило с удалением через 1 день после создания объекта
            // var rule1 = new LifecycleRule(
            //     abortIncompleteMultipartUpload: null,
            //     id: "delete-after-1-day",
            //     expiration: expiration, // 1 день после создания объекта
            //     noncurrentVersionExpiration: null,
            //     filter: new RuleFilter(null, null, null),
            //     transition: null,
            //     noncurrentVersionTransition: null,
            //     status: LifecycleRule.LifecycleRuleStatusEnabled
            // );

            // rules.Add(rule1);
            // var lfc = new LifecycleConfiguration(rules);

            // await SetLifecycleAsync(minio, "testupload", lfc);

            // await GetLifecycleAsync(minio, "testupload");
        }
        catch (Exception ex)
        {
            Console.WriteLine(ex.Message);
        }
        Console.ReadLine();
    }

    // File uploader task.
    private async static Task Run(IMinioClient minio)
    {
        var bucketName = "testupload";
        var objectName = "AD-Zielonka-2.0.1.zip";
        var filePath = "C:\\Users\\<USER>\\Downloads\\AD-Zielonka-2.0.1.zip";
        var contentType = "application/zip";

        try
        {
            // Make a bucket on the server, if not already present.
            var beArgs = new BucketExistsArgs()
                .WithBucket(bucketName);
            bool found = await minio.BucketExistsAsync(beArgs).ConfigureAwait(false);
            if (!found)
            {
                var mbArgs = new MakeBucketArgs()
                    .WithBucket(bucketName);
                await minio.MakeBucketAsync(mbArgs).ConfigureAwait(false);
            }
            // Upload a file to bucket.
            var putObjectArgs = new PutObjectArgs()
                .WithBucket(bucketName)
                .WithObject(objectName)
                .WithFileName(filePath)
                .WithContentType(contentType)
                .WithTagging(Tagging.GetObjectTags(new Dictionary<string, string>() { { "start", "1" }, { "end", "2" } }));
            var response =await minio.PutObjectAsync(putObjectArgs).ConfigureAwait(false);
            Console.WriteLine("Successfully uploaded " + objectName );


        }
        catch (MinioException e)
        {
            Console.WriteLine("File Upload Error: {0}", e.Message);
        }
    }

    public static async Task GetObjectAsync(IMinioClient minio,
                                            string bucketName = "my-bucket-name",
                                            string objectName = "my-object-name",
                                            string fileName = "my-file-name")
    {
        try
        {
            Console.WriteLine("Running example for API: GetObjectAsync");
            var args = new GetObjectArgs()
                .WithBucket(bucketName)
                .WithObject(objectName)
                .WithFile(fileName);
                // .WithCallbackStream(async (stream, cancellationToken) =>
                // {
                //     var fileStream = File.Create(fileName);
                //     await stream.CopyToAsync(fileStream, cancellationToken).ConfigureAwait(false);
                //     await fileStream.DisposeAsync().ConfigureAwait(false);
                //     var writtenInfo = new FileInfo(fileName);
                //     var file_read_size = writtenInfo.Length;
                //     // Uncomment to print the file on output console
                //     // stream.CopyTo(Console.OpenStandardOutput());
                //     Console.WriteLine(
                //         $"Successfully downloaded object with requested offset and length {writtenInfo.Length} into file");
                //     stream.Dispose();
                // });
            var stat = await minio.GetObjectAsync(args).ConfigureAwait(false);

            Console.WriteLine($"Downloaded the file {fileName} in bucket {bucketName}");
            Console.WriteLine($"Stat details of object {objectName} in bucket {bucketName}\n" + stat);
            Console.WriteLine();
        }
        catch (Exception e)
        {
            Console.WriteLine($"[Bucket]  Exception: {e}");
        }
    }

    public static async Task ListBucketsAsync(IMinioClient minio)
    {
        try
        {
            Console.WriteLine("Running example for API: ListBucketsAsync");
            var list = await minio.ListBucketsAsync().ConfigureAwait(false);
            foreach (var bucket in list.Buckets)
            {

                Console.WriteLine($"{bucket.Name} {bucket.CreationDateDateTime}");

                await GetLifecycleAsync(minio, bucket.Name);
                Console.WriteLine("Running example for API: ListObjectsAsync");
                var listArgs = new ListObjectsArgs()
                    .WithBucket(bucket.Name);
                    // .WithRecursive(recursive)
                    // .WithVersions(versions);
                await foreach (var item in minio.ListObjectsEnumAsync(listArgs).ConfigureAwait(false))
                {
                    Console.WriteLine($"Object: {item.Key}");
                    var tags = await minio.GetObjectTagsAsync(
                        new GetObjectTagsArgs()
                            .WithBucket(bucket.Name)
                            .WithObject(item.Key));

                    if (tags is not null && tags.Tags is not null)
                    {
                        foreach (var tag in tags.Tags)
                        {
                            Console.WriteLine(tag.Key + " : " + tag.Value);
                        }
                    }
                }
                Console.WriteLine($"Listed all objects in bucket {bucket.Name}\n");
            }
            Console.WriteLine();
        }
        catch (Exception e)
        {
            Console.WriteLine($"[Bucket]  Exception: {e}");
        }
    }

        // Set Lifecycle configuration to the bucket
    public static async Task SetLifecycleAsync(IMinioClient minio,
        string bucketName = "my-bucket-name",
        LifecycleConfiguration lfc = null)
    {
        if (minio is null) throw new ArgumentNullException(nameof(minio));

        try
        {
            Console.WriteLine("Running example for API: SetBucketLifecycle");
            await minio.SetBucketLifecycleAsync(
                new SetBucketLifecycleArgs()
                    .WithBucket(bucketName)
                    .WithLifecycleConfiguration(lfc)
            ).ConfigureAwait(false);
            Console.WriteLine($"Bucket Lifecycle set for bucket {bucketName}.");
            Console.WriteLine();
        }
        catch (Exception e)
        {
            Console.WriteLine($"[Bucket]  Exception: {e}");
        }
    }

    public static async Task GetLifecycleAsync(IMinioClient minio, string bucketName = "my-bucket-name")
    {
        ArgumentNullException.ThrowIfNull(minio);

        try
        {
            Console.WriteLine("Running example for API: GetBucketLifecycle");
            var lifecycle = await minio.GetBucketLifecycleAsync(
                new GetBucketLifecycleArgs()
                    .WithBucket(bucketName)
            ).ConfigureAwait(false);

            if (lifecycle?.Rules != null)
            {
                Console.WriteLine($"\nПравила жизненного цикла для бакета {bucketName}:");
                foreach (var rule in lifecycle.Rules)
                {
                    Console.WriteLine($"\nПравило ID: {rule.ID}");
                    Console.WriteLine($"Статус: {rule.Status}");

                    if (rule.Expiration is not null)
                    {
                        if (rule.Expiration.Days.HasValue)
                        {
                            Console.WriteLine($"Удаление через дней: {rule.Expiration.Days}");
                        }
                        else
                        {
                            Console.WriteLine($"Удаление в дату: {rule.Expiration.ExpiryDate}");
                        }
                    }

                    if (rule.Filter != null)
                    {
                        if (!string.IsNullOrEmpty(rule.Filter.Prefix))
                            Console.WriteLine($"Префикс: {rule.Filter.Prefix}");
                        if (rule.Filter.Tag != null && rule.Filter.Tag.Tags is not null)
                        {
                            foreach (var tag in rule.Filter.Tag.Tags)
                            {
                                Console.WriteLine($"Тег: {tag.Key}={tag.Value}");
                            }
                        }
                    }
                }
            }
            else
            {
                Console.WriteLine($"\nДля бакета {bucketName} правила жизненного цикла не установлены");
            }
            Console.WriteLine();
        }
        catch (Exception e)
        {
            Console.WriteLine($"[Bucket]  Exception: {e}");
        }
    }
}