using MediatR;
using Microsoft.AspNetCore.Components;
using MudBlazor;

namespace Teslametrics.App.Web.Features.DatabaseMigrations;

public partial class DatabaseMigrationsPage
{
    public bool _checked { get; set; } = true;
    private class Element(string dbContextName, string migration, bool applied)
    {
        public string DbContextName { get; init; } = dbContextName;

        public string Migration { get; init; } = migration;

        public bool Applied { get; init; } = applied;
    }

    private bool _isLoading;
    private List<Element> _appliedMigrations = [];
    private List<Element> _pendingMigrations = [];

    [Inject]
    protected IServiceScopeFactory ScopeFactory { get; set; } = null!;

    [Inject]
    protected ISnackbar Snackbar { get; set; } = null!;

    protected override async Task OnInitializedAsync()
    {
        await FetchAsync();
        await base.OnInitializedAsync();
    }

    private async Task FetchAsync()
    {
        if (_isLoading) return;

        try
        {
            _isLoading = true;
            var result = await ScopeFactory.CreateScope().ServiceProvider.GetRequiredService<IMediator>().Send(new GetDatabaseMigrationsUseCase.Query());
            if (result.IsSuccess)
            {
                foreach (var item in result.AppliedMigrations ?? [])
                {
                    _appliedMigrations.Add(new Element("AuthDbContext", item, true));
                }
                foreach (var item in result.PendingMigrations ?? [])
                {
                    _appliedMigrations.Add(new Element("AppDbContext", item, false));
                }
                return;
            }

            switch (result.Result)
            {
                case GetDatabaseMigrationsUseCase.Result.Failure:
                    Snackbar.Add("Не удалось получить список миграций", Severity.Error);
                    break;
                case GetDatabaseMigrationsUseCase.Result.Unknown:
                default:
                    Snackbar.Add("Не удалось получить список миграции из-за непредвиденной ошибки:" + result.Result.ToString(), Severity.Error);
                    break;
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add("Не удалось получить миграции. Повторите попытку.", Severity.Error);
            Logger.LogError(ex, ex.Message);
        }
        finally
        {
            _isLoading = false;
        }
    }

    private async Task ApplyMigrationsAsync()
    {
        if (_isLoading) return;

        _isLoading = true;
        try
        {
            var result = await ScopeFactory.CreateScope().ServiceProvider.GetRequiredService<IMediator>().Send(new ApplyDatabaseMigrationsUseCase.Command());
            if (result.IsSuccess)
            {
                Snackbar.Add("Миграции успешно применены", Severity.Success);
                _isLoading = false;
                await FetchAsync();
                return;
            }

            switch (result.Result)
            {
                case ApplyDatabaseMigrationsUseCase.Result.Failure:
                    Snackbar.Add("Ошибка при применении миграций", Severity.Error);
                    break;
                case ApplyDatabaseMigrationsUseCase.Result.Unknown:
                default:
                    Snackbar.Add("Не удалось применить миграции из-за непредвиденной ошибки:" + result.Result.ToString(), Severity.Error);
                    break;
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add("Не удалось применить миграции из-за непредвиденной ошибки. Повторите попытку.", Severity.Error);
            Logger.LogError(ex, ex.Message);
        }
        finally
        {
            _isLoading = false;
        }
    }
}
