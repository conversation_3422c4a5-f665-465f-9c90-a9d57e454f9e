﻿@using CsvHelper.Configuration
@using MudExtensions
@using MudExtensions.Utilities
@using Teslametrics.App.Web.Events.Cameras

@inherits InteractiveBaseComponent

<MudDialog @bind-Visible="_isVisible"
           ActionsClass="mx-2"
           ContentClass="px-4 mx-0"
           Options="_dialogOptions">
    <DialogContent>
        <MudTabs Elevation="0"
                 ApplyEffectsToContainer="true"
                 PanelClass="pt-6"
                 KeepPanelsAlive="true"
                 @bind-ActivePanelIndex="ActivePanelIndex">
            <MudTabPanel Text="Выберите поля экспорта">
                <MudStack Class="ml-n3 my-2">
                    <MudCheckBox Value="@_exportFields.Contains(nameof(GetCameraListUseCase.Response.Item.Name))"
                                 ValueChanged="(bool isChecked) => CheckedChanged(nameof(GetCameraListUseCase.Response.Item.Name))"
                                 Label="Название" />
                    <MudCheckBox Value="@_exportFields.Contains(nameof(GetCameraListUseCase.Response.Item.FolderName))"
                                 ValueChanged="(bool isChecked) => CheckedChanged(nameof(GetCameraListUseCase.Response.Item.FolderName))"
                                 Label="Директория" />
                    <MudCheckBox Value="@_exportFields.Contains(nameof(GetCameraListUseCase.Response.Item.Coordinates))"
                                 ValueChanged="(bool isChecked) => CheckedChanged(nameof(GetCameraListUseCase.Response.Item.Coordinates))"
                                 Label="Координаты" />
                    <MudCheckBox Value="@_exportFields.Contains(nameof(GetCameraListUseCase.Response.Item.TimeZone))"
                                 ValueChanged="(bool isChecked) => CheckedChanged(nameof(GetCameraListUseCase.Response.Item.TimeZone))"
                                 Label="Часовой пояс" />
                    <MudCheckBox Value="@_exportFields.Contains(nameof(GetCameraListUseCase.Response.Item.QuotaName))"
                                 ValueChanged="(bool isChecked) => CheckedChanged(nameof(GetCameraListUseCase.Response.Item.QuotaName))"
                                 Label="Квота" />
                    <MudCheckBox Value="@_exportFields.Contains(nameof(GetCameraListUseCase.Response.Item.ViewUri))"
                                 ValueChanged="(bool isChecked) => CheckedChanged(nameof(GetCameraListUseCase.Response.Item.ViewUri))"
                                 Label="Поток для видов" />
                    <MudCheckBox Value="@_exportFields.Contains(nameof(GetCameraListUseCase.Response.Item.PublicUri))"
                                 ValueChanged="(bool isChecked) => CheckedChanged(nameof(GetCameraListUseCase.Response.Item.PublicUri))"
                                 Label="Поток публичного доступа" />
                    <MudCheckBox Value="@_exportFields.Contains(nameof(GetCameraListUseCase.Response.Item.ArchiveUri))"
                                 ValueChanged="(bool isChecked) => CheckedChanged(nameof(GetCameraListUseCase.Response.Item.ArchiveUri))"
                                 Label="Поток архива" />
                    <MudCheckBox Value="@_exportFields.Contains(nameof(GetCameraListUseCase.Response.Item.AutoStart))"
                                 ValueChanged="(bool isChecked) => CheckedChanged(nameof(GetCameraListUseCase.Response.Item.AutoStart))"
                                 Label="Автозапуск при перезапуске системы" />
                </MudStack>
                <MudGrid Justify="Justify.FlexEnd">
                    <MudItem>
                        <MudButton OnClick="ConfirmFields">Подтвердить заголовки</MudButton>
                    </MudItem>
                </MudGrid>
            </MudTabPanel>
            <MudTabPanel Text="Cписок сущностей"
                         Disabled="_response is null">
                <MudDataGrid T="GetCameraListUseCase.Response.Item"
                             Items="@(_response?.Items ?? [])"
                             SortMode="SortMode.None"
                             QuickFilter="@_quickFilter"
                             Hideable="true"
                             ColumnResizeMode="ResizeMode.Column">
                    <ToolBarContent>
                        <MudText Typo="Typo.h6">Сущности к экспорту</MudText>
                        <MudSpacer />
                        <MudTextField @bind-Value="_searchString"
                                      Placeholder="Поиск"
                                      Adornment="Adornment.Start"
                                      Immediate="true"
                                      AdornmentIcon="@Icons.Material.Filled.Search"
                                      IconSize="Size.Medium" />
                    </ToolBarContent>
                    <Columns>
                        <PropertyColumn Property="x => x.FolderName"
                                        Title="Директория"
                                        Hidden="!_exportFields.Contains(nameof(GetCameraListUseCase.Response.Item.FolderName))" />
                        <PropertyColumn Property="x => x.Name"
                                        Title="Название"
                                        Hidden="!_exportFields.Contains(nameof(GetCameraListUseCase.Response.Item.Name))" />

                        <PropertyColumn Property="@(x => $"UTC{(x.TimeZone >= TimeSpan.Zero ? "+" : "-")}{x.TimeZone:hh\\:mm}")"
                                        Title="Часловой пояс"
                                        Hidden="!_exportFields.Contains(nameof(GetCameraListUseCase.Response.Item.TimeZone))" />

                        <PropertyColumn Property="@(x => x.Coordinates.HasValue ? $"{x.Coordinates.Value.Latitude}, {x.Coordinates.Value.Longitude}" : string.Empty)"
                                        Title="Координаты"
                                        Hidden="!_exportFields.Contains(nameof(GetCameraListUseCase.Response.Item.Coordinates))" />

                        <PropertyColumn Property="x => x.QuotaName"
                                        Title="Квота"
                                        Hidden="!_exportFields.Contains(nameof(GetCameraListUseCase.Response.Item.QuotaName))" />
                        <PropertyColumn Property="x => x.PublicUri"
                                        Title="Ссылка на поток для публичного просмотра"
                                        Hidden="!_exportFields.Contains(nameof(GetCameraListUseCase.Response.Item.PublicUri))" />
                        <PropertyColumn Property="x => x.ViewUri"
                                        Title="Ссылка на поток для видов"
                                        Hidden="!_exportFields.Contains(nameof(GetCameraListUseCase.Response.Item.ViewUri))" />
                        <PropertyColumn Property="x => x.ArchiveUri"
                                        Title="Ссылка на поток для архива"
                                        Hidden="!_exportFields.Contains(nameof(GetCameraListUseCase.Response.Item.ArchiveUri))" />
                        <PropertyColumn Property="x => x.AutoStart"
                                        Title="Автозапуск при перезапуске системы"
                                        Hidden="!_exportFields.Contains(nameof(GetCameraListUseCase.Response.Item.AutoStart))">
                            <EditTemplate>
                                <MudCheckBox T="bool"
                                             Value="context.Item.AutoStart" />
                            </EditTemplate>
                        </PropertyColumn>
                    </Columns>
                    <PagerContent>
                        <MudDataGridPager T="GetCameraListUseCase.Response.Item" />
                    </PagerContent>
                </MudDataGrid>
            </MudTabPanel>
        </MudTabs>
    </DialogContent>
    <DialogActions>
        <DSFabComponent OnClick="SubmitAsync"
                        Disabled="!_exportFields.Any() || (!_response?.Items?.Any() ?? true)"
                        StartIcon="@Icons.Material.Filled.Download"
                        Label="Экспорт"
                        Color="Color.Primary" />
    </DialogActions>
</MudDialog>