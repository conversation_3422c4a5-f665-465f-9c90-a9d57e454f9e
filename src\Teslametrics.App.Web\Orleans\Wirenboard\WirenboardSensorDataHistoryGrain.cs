using Dapper;
using System.Collections.Concurrent;
using System.Data;
using System.Text.Json;
using Teslametrics.App.Web.Services.Mqtt;
using Teslametrics.App.Web.Services.Persistence;
using Teslametrics.App.Web.Shared;

namespace Teslametrics.App.Web.Orleans.Wirenboard;

public class WirenboardSensorDataHistoryGrain : Grain, IWirenboardSensorDataHistoryGrain
{
    private readonly ILogger<WirenboardSensorDataHistoryGrain> _logger;
    private readonly MqttClientService _mqttService;
    private readonly IServiceScopeFactory _serviceScopeFactory;
    private readonly ILoggerFactory _loggerFactory;
    private readonly CancellationTokenSource _cts;
    private readonly ConcurrentQueue<Signal> _externalQueue;
    private MqttRpcClient? _rpcClient;
    private Task? _processHistoryTask;
    private Task? _processTask;
    private State _state = State.Disconnected;
    private string? _brokerAddress;
    private int? _port;
    private HistoryTopicInfo? _topicInfo;
    private string? _tableName;
    private DateTime? _lastRecordTime;
    private readonly TimeSpan _updateInterval = TimeSpan.FromSeconds(1);

    public WirenboardSensorDataHistoryGrain(ILoggerFactory loggerFactory,
                                            MqttClientService mqttClientService,
                                            IServiceScopeFactory serviceScopeFactory)
    {
        _loggerFactory = loggerFactory;
        _logger = _loggerFactory.CreateLogger<WirenboardSensorDataHistoryGrain>();
        _mqttService = mqttClientService;
        _serviceScopeFactory = serviceScopeFactory;
        _cts = new CancellationTokenSource();

        _externalQueue = new ConcurrentQueue<Signal>();
    }

    public override Task OnActivateAsync(CancellationToken cancellationToken)
    {
        _processTask = Task.Run(() => ProcessAsync(_cts.Token), cancellationToken);

        return base.OnActivateAsync(cancellationToken);
    }

    public override async Task OnDeactivateAsync(DeactivationReason reason, CancellationToken cancellationToken)
    {
        try
        {
            // Отправляем сигнал на отключение
            _externalQueue.Enqueue(Signal.Disconnect);

            // Ждем некоторое время, чтобы сигнал успел обработаться
            await Task.Delay(TimeSpan.FromSeconds(2), cancellationToken);

            // Отменяем токен для завершения основной задачи обработки
            _cts.Cancel();

            // Ждем завершения основной задачи с таймаутом
            var timeoutTask = Task.Delay(TimeSpan.FromSeconds(5), cancellationToken);
            var completedTask = await Task.WhenAny(_processTask!, timeoutTask);

            if (completedTask == timeoutTask)
            {
                _logger.LogWarning("Timeout waiting for process task to complete for grain {GrainId}", this.GetPrimaryKeyString());
            }
        }
        catch (OperationCanceledException)
        {
            _logger.LogWarning("Wirenboard sensor data history grain {GrainId} deactivation process was canceled", this.GetPrimaryKey());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to deactivate Wirenboard sensor data history grain {GrainId}", this.GetPrimaryKey());
        }

        await base.OnDeactivateAsync(reason, cancellationToken);
    }

    public async Task StartAsync(IWirenboardSensorDataHistoryGrain.StartRequest request)
    {
        _brokerAddress = request.BrokerAddress;
        _port = request.Port;
        _topicInfo = request.TopicInfo;

        _tableName = GetTableName();
        await EnsureTableExistsAsync(_tableName, _topicInfo.ValueType);

        // Получаем и сохраняем время последней записи
        _lastRecordTime = await GetLastRecordTimeAsync(_tableName);

        _externalQueue.Enqueue(Signal.Connect);
    }

    public Task StopAsync()
    {
        _externalQueue.Enqueue(Signal.Disconnect);

        return Task.CompletedTask;
    }

    private async Task ProcessAsync(CancellationToken cancellationToken)
    {
        while (!cancellationToken.IsCancellationRequested)
        {
            if (!_externalQueue.TryDequeue(out var signal))
            {
                await Task.Delay(100, cancellationToken);
                continue;
            }

            switch (signal)
            {
                case Signal.Connect:
                    {
                        switch (_state)
                        {
                            case State.Disconnected:
                                {
                                    ChangeState(State.Connecting);
                                    await ConnectAsync();
                                    ChangeState(State.Running);
                                    break;
                                }
                        }

                        break;
                    }
                case Signal.Disconnect:
                    {
                        switch (_state)
                        {
                            case State.Connecting:
                            case State.Running:
                                {
                                    _logger.LogDebug("Received disconnect signal for grain {GrainId}", this.GetPrimaryKeyString());
                                    ChangeState(State.Disconnecting);

                                    try
                                    {
                                        await DisconnectAsync();
                                    }
                                    catch (Exception ex)
                                    {
                                        _logger.LogWarning(ex, "Error during disconnect for grain {GrainId}", this.GetPrimaryKeyString());
                                    }

                                    ChangeState(State.Disconnected);
                                    _logger.LogDebug("Grain {GrainId} disconnected successfully", this.GetPrimaryKeyString());
                                    break;
                                }
                        }
                        break;
                    }
            }
        }
    }

    private async Task ConnectAsync()
    {
        try
        {
            await _mqttService.ConnectAsync(_brokerAddress!, _port!.Value, $"ClientId-{this.GetPrimaryKey()}");
            _rpcClient = new MqttRpcClient(_mqttService.MqttClient, _loggerFactory.CreateLogger<MqttRpcClient>());

            _processHistoryTask = Task.Run(ProcessHistoryAsync);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to connect to MQTT broker");
        }
    }

    private async Task DisconnectAsync()
    {
        try
        {
            // Если задача обработки истории запущена, дожидаемся её завершения
            if (_processHistoryTask != null)
            {
                // Ждем завершения задачи с таймаутом
                var timeoutTask = Task.Delay(TimeSpan.FromSeconds(5));
                var completedTask = await Task.WhenAny(_processHistoryTask, timeoutTask);

                if (completedTask == timeoutTask)
                {
                    _logger.LogWarning("Timeout waiting for history processing task to complete for grain {GrainId}", this.GetPrimaryKeyString());
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error while waiting for history processing task to complete for grain {GrainId}", this.GetPrimaryKeyString());
        }

        try
        {
            // Отключаемся от MQTT сервиса
            await _mqttService.DisconnectAsync();
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error disconnecting from MQTT service for grain {GrainId}", this.GetPrimaryKeyString());
        }
    }

    private void ChangeState(State state)
    {
        if (_state != state)
        {
            _state = state;
        }
    }

    /// <summary>
    /// Метод для поддержания активности грейна, предотвращающий его деактивацию
    /// </summary>
    /// <returns>Задача, представляющая асинхронную операцию</returns>
    public Task PingAsync()
    {
        _logger.LogDebug("Ping received for WirenboardSensorDataHistoryGrain with ID: {GrainId}", this.GetPrimaryKeyString());
        return Task.CompletedTask;
    }

    private async Task ProcessHistoryAsync()
    {
        var currentTime = DateTime.UtcNow;

        var topic = _topicInfo!.Topic;

        // Парсим топик формата "/devices/wb-m1w2_30/controls/External Sensor 1"
        // Нам нужны только части "wb-m1w2_30" и "External Sensor 1"
        var parts = topic.Split('/');
        string[][] channels;

        if (parts.Length == 5 && parts[1] == "devices" && parts[3] == "controls")
        {
            // Извлекаем имя устройства и имя контрола и создаем массив из одного элемента,
            // который сам является массивом из двух элементов [device, control]
            channels = [[ parts[2], parts[4] ]];
            _logger.LogDebug("Extracted channels from topic: device={Device}, control={Control}", parts[2], parts[4]);
        }
        else
        {
            // Если формат не соответствует ожидаемому, пытаемся создать массив с топиком в качестве идентификатора устройства
            // и пустой строкой в качестве контрола, так как API требует массивы размера 2
            channels = [[ topic, "" ]];
            _logger.LogWarning("Topic format is not as expected, using full topic as device ID: {Topic}", topic);
        }

        _logger.LogDebug("Starting history processing for topic {Topic} in grain {GrainId}", topic, this.GetPrimaryKeyString());

        while (_state is State.Running)
        {
            try
            {
                if (_mqttService.IsConnected)
                {
                    try
                    {
                        // Создаем параметры запроса
                        var requestParams = new
                        {
                            channels,
                            timestamp = new
                            {
                                gt = new DateTimeOffset(_lastRecordTime!.Value).ToLocalTime().ToUnixTimeSeconds(),
                            },
                            ver = 1,
                            limit = 100,
                            with_milliseconds = true
                        };

                        // Логируем параметры запроса для отладки
                        _logger.LogDebug("Sending RPC request with channels: {Channels}",
                            JsonSerializer.Serialize(channels));

                        using var result = await _rpcClient!.CallAsync<JsonDocument>(
                            "db_logger",
                            "history",
                            "get_values",
                            requestParams);

                        if (result != null)
                        {
                            var values = result.RootElement.GetProperty("values").EnumerateArray();
                            if (values.Any())
                            {
                                using var scope = _serviceScopeFactory.CreateScope();
                                using var dbConnection = scope.ServiceProvider.GetRequiredService<IDbConnection>();

                                foreach (var item in values)
                                {
                                    try
                                    {
                                        var time = DateTimeOffset.FromUnixTimeMilliseconds((long)(item.GetProperty("t").GetDouble() * 1000)).UtcDateTime;
                                        if (time <= _lastRecordTime)
                                        {
                                            continue;
                                        }

                                        var value = item.TryGetProperty("max", out var maxVal) ? maxVal.ToString() : item.GetProperty("v").ToString();

                                        await SaveRecordAsync(dbConnection, time, value);
                                        _lastRecordTime = time;
                                    }
                                    catch (Exception ex)
                                    {
                                        _logger.LogError(ex, "Error processing record: {Message}", ex.Message);
                                        _logger.LogError("Record data: {Item}", item);
                                    }
                                }
                            }
                        }
                    }
                    catch (MqttRpcException ex)
                    {
                        // Если состояние уже Disconnecting или Disconnected, то это ожидаемая ошибка при отключении
                        if (_state is State.Disconnecting or State.Disconnected)
                        {
                            _logger.LogDebug("MQTT RPC call interrupted during disconnection for grain {GrainId}", this.GetPrimaryKeyString());
                            break;
                        }

                        _logger.LogWarning(ex, "MQTT RPC error in grain {GrainId}: {Message}", this.GetPrimaryKeyString(), ex.Message);

                        // Добавляем небольшую задержку перед следующей попыткой
                        await Task.Delay(TimeSpan.FromSeconds(2));
                    }

                    var deltaTime = DateTime.UtcNow - currentTime;

                    var remainingTime = _updateInterval - deltaTime;

                    if (remainingTime > TimeSpan.Zero)
                    {
                        await Task.Delay(remainingTime);
                    }

                    currentTime = DateTime.UtcNow;
                }
                else
                {
                    _logger.LogWarning("MQTT client disconnected for grain {GrainId}, waiting before retry", this.GetPrimaryKeyString());
                    await Task.Delay(TimeSpan.FromSeconds(5));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error in ProcessHistoryAsync for grain {GrainId}", this.GetPrimaryKeyString());
                await Task.Delay(TimeSpan.FromSeconds(5));
            }
        }

        _logger.LogDebug("History processing loop exited for grain {GrainId} with state {State}", this.GetPrimaryKeyString(), _state);
    }

    /// <summary>
    /// Сохраняет запись в TimescaleDB
    /// </summary>
    /// <param name="dbConnection">Соединение с базой данных</param>
    /// <param name="timestamp">Время измерения значения</param>
    /// <param name="value">Значение в строковом формате</param>
    /// <returns>true, если запись успешно сохранена; false, если произошла ошибка</returns>
    private async Task<bool> SaveRecordAsync(IDbConnection dbConnection, DateTimeOffset timestamp, string value)
    {
        try
        {
            // Преобразуем строковое значение в типизированное в соответствии с типом данных
            object typedValue = ConvertValue(value, _topicInfo!.ValueType);

            // Сохраняем запись в таблицу
            string sql = $@"INSERT INTO {_tableName} ({Db.SensorHistory.Columns.Timestamp}, {Db.SensorHistory.Columns.Value}) VALUES (:Timestamp, :Value)";

            await dbConnection.ExecuteAsync(sql, new
            {
                timestamp,
                Value = typedValue
            });

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving MQTT message to TimescaleDB: {Message}", ex.Message);
            return false;
        }
    }

    /// <summary>
    /// Создает таблицу TimescaleDB для хранения временных рядов, если она не существует, и настраивает индексы
    /// </summary>
    /// <param name="tableName">Имя таблицы в базе данных PostgreSQL</param>
    /// <param name="valueType">Тип данных из перечисления SensorValueType, определяющий тип столбца value</param>
    private async Task EnsureTableExistsAsync(string tableName, SensorValueType valueType)
    {
        // TODO: В будущем название таблицы должно включать идентификатор Wirenboard для лучшей организации данных

        // Определяем тип данных для PostgreSQL в зависимости от типа полезной нагрузки
        string postgresType = GetPostgresType(valueType);

        // Создаем таблицу для временного ряда (используем IF NOT EXISTS)
        string createTableSql = $@"
            CREATE TABLE IF NOT EXISTS {tableName} (
                {Db.SensorHistory.Columns.Timestamp} TIMESTAMPTZ NOT NULL,
                {Db.SensorHistory.Columns.Value} {postgresType} NOT NULL
            );

            -- Создаем гипертаблицу TimescaleDB
            SELECT create_hypertable('{tableName}', '{Db.SensorHistory.Columns.Timestamp}', if_not_exists => TRUE);

            -- Создаем индекс для ускорения запросов
            CREATE INDEX IF NOT EXISTS idx_{tableName}_{Db.SensorHistory.Columns.Timestamp} ON {tableName} ({Db.SensorHistory.Columns.Timestamp} DESC);";

        using var scope = _serviceScopeFactory.CreateScope();
        using var dbConnection = scope.ServiceProvider.GetRequiredService<IDbConnection>();

        await dbConnection.ExecuteAsync(createTableSql);
        _logger.LogDebug("TimescaleDB table checked/created: {TableName} with payload type: {PayloadType}", tableName, valueType);
    }

    /// <summary>
    /// Преобразует тип данных приложения в соответствующий тип данных PostgreSQL
    /// </summary>
    /// <param name="valueType">Тип данных из перечисления ValueType</param>
    /// <returns>Строковое представление типа данных PostgreSQL (TEXT, BOOLEAN, DOUBLE PRECISION или INT)</returns>
    private static string GetPostgresType(SensorValueType valueType)
    {
        return valueType switch
        {
            SensorValueType.String => "TEXT",
            SensorValueType.Bool => "BOOLEAN",
            SensorValueType.Double => "DOUBLE PRECISION",
            SensorValueType.Integer => "INT",
            _ => throw new ArgumentOutOfRangeException(nameof(valueType), valueType, "Unsupported value type")
        };
    }

    /// <summary>
    /// Преобразует строковое представление данных в соответствующий типизированный объект
    /// </summary>
    /// <param name="value">Строковое представление данных для преобразования</param>
    /// <param name="valueType">Целевой тип данных для преобразования</param>
    /// <returns>Преобразованное значение соответствующего типа (string, bool, double или int)</returns>
    private static object ConvertValue(string value, SensorValueType valueType)
    {
        return valueType switch
        {
            SensorValueType.String => value,
            SensorValueType.Bool => ParseBooleanValue(value),
            SensorValueType.Double => double.Parse(value, System.Globalization.CultureInfo.InvariantCulture),
            SensorValueType.Integer => int.Parse(value, System.Globalization.CultureInfo.InvariantCulture),
            _ => throw new ArgumentOutOfRangeException(nameof(valueType), valueType, "Unsupported value type")
        };
    }

    /// <summary>
    /// Получает время последней записи в таблице
    /// </summary>
    /// <param name="tableName">Имя таблицы</param>
    /// <returns>Время последней записи в UTC или текущее время UTC, если записей нет</returns>
    private async Task<DateTime> GetLastRecordTimeAsync(string tableName)
    {
        try
        {
            string sql = $"SELECT {Db.SensorHistory.Columns.Timestamp} FROM {tableName} ORDER BY {Db.SensorHistory.Columns.Timestamp} DESC LIMIT 1";

            using var scope = _serviceScopeFactory.CreateScope();
            using var dbConnection = scope.ServiceProvider.GetRequiredService<IDbConnection>();

            var result = await dbConnection.QueryFirstOrDefaultAsync<DateTime?>(sql);

            if (result.HasValue)
            {
                // Если время есть, добавляем текущую временную зону хоста
                _logger.LogInformation("Last record in table {tableName} has time {lastRecordTime}", tableName, result.Value);
                return result.Value;
            }
            else
            {
                var currentTime = DateTime.UtcNow;
                // Если записей нет, используем текущее время
                _logger.LogInformation("No records in table {tableName}, using current time {currentTime}", tableName, currentTime);
                return currentTime;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting last record time from table {tableName}", tableName);
            throw;
        }
    }

    /// <summary>
    /// Преобразует строковое значение в логическое с поддержкой различных форматов
    /// </summary>
    /// <param name="value">Строковое представление логического значения</param>
    /// <returns>Логическое значение</returns>
    private static bool ParseBooleanValue(string value)
    {
        // Приводим к нижнему регистру для сравнения без учета регистра
        value = value.Trim().ToLowerInvariant();

        // Проверяем различные форматы представления "истины"
        return value switch
        {
            "true" or "1" => true,
            "false" or "0" => false,
            _ => throw new FormatException($"String '{value}' was not recognized as a valid Boolean.")
        };
    }

    /// <summary>
    /// Формирует имя таблицы PostgreSQL на основе идентификатора грейна
    /// </summary>
    /// <returns>Имя таблицы для хранения истории данных сенсора</returns>
    private string GetTableName()
    {
        return $"{Db.SensorHistory.Table}_{this.GetPrimaryKeyString()}";
    }

    public enum State
    {
        Disconnected,
        Connecting,
        Running,
        Disconnecting
    }

    public enum Signal
    {
        Connect,
        Disconnect
    }
}