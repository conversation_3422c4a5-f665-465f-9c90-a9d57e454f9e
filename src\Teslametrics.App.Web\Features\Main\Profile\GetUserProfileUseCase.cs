using Dapper;
using FluentValidation;
using MediatR;
using System.Data;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Data.Sql;
using Teslametrics.App.Web.Services.Persistence;
using Teslametrics.App.Web.Shared;

namespace Teslametrics.App.Web.Features.Main.Profile;

public static class GetUserProfileUseCase
{
    public record Query(Guid Id) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public Guid Id { get; init; }

        public string Username { get; init; }

        public bool IsSystem { get; init; }

        public List<Role> Roles { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(Guid id, string username, bool isSystem, List<Role> roles)
        {
            Id = id;
            Username = username;
            Roles = roles;
            Result = Result.Success;
            IsSystem = isSystem;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Result = result;

            Id = Guid.Empty;
            Username = string.Empty;
            IsSystem = false;
            Roles = [];
        }

        public record Role(Guid Id, string Organization, string Name);
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        UserNotFound
    }

    public class Validator : AbstractValidator<Query>
    {
        public Validator()
        {
            RuleFor(q => q.Id).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Query, Response>
    {
        private readonly IValidator<Query> _validator;
        private readonly IDbConnection _dbConnection;

        public Handler(IValidator<Query> validator,
                       IDbConnection dbConnection)
        {
            _validator = validator;
            _dbConnection = dbConnection;
        }

        public async Task<Response> Handle(Query request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            var template = SqlQueryBuilder.Create()
                .Select(Db.Users.Props.Id, "UserId")
                .Select(Db.Users.Props.Name, "Username")
                .Select(Db.Roles.Props.Id, "RoleId")
                .Select(Db.Roles.Props.Name, "RoleName")
                .Select(Db.Organizations.Props.Name, "Organization")
                .LeftJoin(Db.UserRoles.Table, Db.UserRoles.Props.UserId, Db.Users.Props.Id, SqlOperator.Equals)
                .LeftJoin(Db.Roles.Table, Db.Roles.Props.Id, Db.UserRoles.Props.RoleId, SqlOperator.Equals)
                .LeftJoin(Db.Organizations.Table, Db.Organizations.Props.Id, Db.Roles.Props.OrganizationId, SqlOperator.Equals)
                .Where(Db.Users.Props.Id, "@Id", SqlOperator.Equals, new { request.Id })
                .Build(QueryType.Standard, Db.Users.Table, RowSelection.AllRows);

            var users = await _dbConnection.QueryAsync<UserModel, RoleModel, UserModel>(template.RawSql, (user, role) =>
            {
                if (role is not null)
                {
                    user.Roles.Add(role);
                }

                return user;
            }, template.Parameters, splitOn: "RoleId");

            if (!users.Any())
            {
                return new Response(Result.UserNotFound);
            }

            var result = users.GroupBy(p => p.UserId).Select(u =>
            {
                var groupedUser = u.First();
                groupedUser.Roles = u.SelectMany(p => p.Roles).ToList();

                return groupedUser;
            });

            var user = users.Single();

            return new Response(user.UserId, user.Username, user.UserId == SystemConsts.RootUserId, user.Roles.Select(r => new Response.Role(r.RoleId, r.RoleName, r.Organization)).ToList());
        }
    }

    public record UserModel(Guid UserId, string Username)
    {
        public List<RoleModel> Roles { get; set; } = [];
    }

    public record RoleModel(Guid RoleId, string RoleName, string Organization);
}