using Dapper;
using FluentValidation;
using MediatR;
using System.Data;
using System.Reactive.Linq;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.Shared;
using Teslametrics.App.Web.Domain.AccessControl.Organizations.Events;
using Teslametrics.App.Web.Exceptions;
using Teslametrics.App.Web.Services.DomainEventBus;
using Teslametrics.App.Web.Services.Persistence;

namespace Teslametrics.App.Web.Features.Main.AccessControl;

public static class SubscribeOrganizationUseCase
{
	public record Request(IObserver<object> Observer, Guid OrganizationId) : BaseRequest<Response>;

	public record Response : BaseResponse
	{
		public IDisposable? Subscription { get; init; }

		public Result Result { get; init; }

		public bool IsSuccess => Result == Result.Success;

		public Response(IDisposable subscription)
		{
			Subscription = subscription;
			Result = Result.Success;
		}

		public Response(Result result)
		{
			if (result == Result.Success)
			{
				throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
			}

			Subscription = null;
			Result = result;
		}
	}

	public record DeletedEvent(Guid Id);

	public enum Result
	{
		Unknown = 0,
		Success,
		ValidationError,
		OrganizationNotFound
	}

	public class Validator : AbstractValidator<Request>
	{
		public Validator()
		{
			RuleFor(r => r.Observer).NotEmpty();
			RuleFor(r => r.OrganizationId).NotEmpty();
		}
	}

	public class Handler : IRequestHandler<Request, Response>
	{
		private readonly IDomainEventBus _domainEventBus;
		private readonly IValidator<Request> _validator;
		private readonly IDbConnection _dbConnection;

		public Handler(IDomainEventBus domainEventBus,
					   IValidator<Request> validator,
					   IDbConnection dbConnection)
		{
			_domainEventBus = domainEventBus;
			_validator = validator;
			_dbConnection = dbConnection;
		}

		public async Task<Response> Handle(Request request, CancellationToken cancellationToken)
		{
			if (!_validator.Validate(request).IsValid)
			{
				return new Response(Result.ValidationError);
			}

			var template = SqlQueryBuilder.Create()
				.Select(Db.Organizations.Props.Id)
				.Where(Db.Organizations.Props.Id, ":Id", SqlOperator.Equals, new { Id = request.OrganizationId })
				.Build(QueryType.Standard, Db.Organizations.Table, RowSelection.AllRows);

			var organization = await _dbConnection.QuerySingleOrDefaultAsync<OrganizationModel>(template.RawSql, template.Parameters);
			if (organization is null)
			{
				return new Response(Result.OrganizationNotFound);
			}

			var eventStream = await _domainEventBus.GetEventStreamAsync();

			var subscription = eventStream
				.Where(e => e switch
				{
					OrganizationDeletedEvent @event => @event.Id == request.OrganizationId,
					_ => false
				})
				.Select<object, object>(e => e switch
				{
					OrganizationDeletedEvent @event => new DeletedEvent(@event.Id),
					_ => throw new AppException("Invalid event type")
				})
				.Subscribe(request.Observer);

			return new Response(subscription);
		}
	}

	public record OrganizationModel(Guid Id);
}