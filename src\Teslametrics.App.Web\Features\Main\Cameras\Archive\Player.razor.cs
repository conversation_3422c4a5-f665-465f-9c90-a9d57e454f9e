using System.Formats.Asn1;
using System.Text;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using MudBlazor;

namespace Teslametrics.App.Web.Features.Main.Cameras.Archive;

public partial class Player : IAsyncDisposable
{
	private Guid _cameraId;

	private bool _shouldRender = true;
	private DateTime? _selectedDate;

	private IJSObjectReference? _playerJsModule;
	private DotNetObjectReference<Player>? _objRef;
	private string _playerId => new StringBuilder("archive_player").Append("_").Append(CameraId).ToString();

	#region [Injects]
	[Inject]
	private ILogger<Player> _logger { get; set; } = null!;

	[Inject]
	public IJSRuntime Js { get; set; } = null!;

	[Inject]
	public ISnackbar Snackbar { get; set; } = null!;
	#endregion

	#region [Parameter]
	[Parameter]
	[EditorRequired]
	public Guid CameraId { get; set; }

	[Parameter]
	[EditorRequired]
	public DateTime? SelectedDate { get; set; }

	[Parameter]
	public EventCallback<DateTime?> SelectedDateChanged { get; set; }

	[Parameter]
	public EventCallback<(DateTime Start, DateTime End)> TimeLineChanged { get; set; }

	[Parameter]
	[EditorRequired]
	public TimeSpan UTCOffset { get; set; }
	#endregion

	// TODO: Сделано нахрапом и некрасиво. Изменить в будущем
	[JSInvokable]
	public async Task SetCurrentTimeLineRangeAsync(DateTime start, DateTime end)
	{
		if (!TimeLineChanged.HasDelegate) return;

		await TimeLineChanged.InvokeAsync((start, end));
	}

	[JSInvokable]
	public void ShowError(string message)
	{
		Snackbar.Add(message, MudBlazor.Severity.Error);
	}

	[JSInvokable]
	public Task PlayerDateChangedAsync(DateTime? selectedDate)
	{
		if (!SelectedDateChanged.HasDelegate) return Task.CompletedTask;

		_selectedDate = selectedDate;
		return SelectedDateChanged.InvokeAsync(selectedDate);
	}

	public async ValueTask DisposeAsync()
	{
		try
		{
			if (_playerJsModule is not null)
			{
				await _playerJsModule.InvokeVoidAsync($"stopPlayer", _playerId);
				await _playerJsModule.DisposeAsync();
			}
		}
		catch (JSDisconnectedException) // А всё. Соединения нету. https://learn.microsoft.com/en-us/aspnet/core/blazor/javascript-interoperability/?view=aspnetcore-9.0
		{
		}
	}

	public async Task SetTimeAsync(DateTime startTime)
	{
		if (_playerJsModule is not null)
		{
			_selectedDate = startTime;
			await _playerJsModule.InvokeVoidAsync($"setTimeAsync", _playerId, startTime);
		}
	}

	protected override void OnInitialized()
	{
		_objRef = DotNetObjectReference.Create(this);
	}

	protected override async Task OnAfterRenderAsync(bool firstRender)
	{
		await base.OnAfterRenderAsync(firstRender);
		if (firstRender)
		{
			try
			{
				_playerJsModule = await Js.InvokeAsync<IJSObjectReference>("import", "./Features/Main/Cameras/Archive/Player.razor.js");
				if (_playerJsModule is not null)
				{
					await _playerJsModule.InvokeVoidAsync($"initializePlayer", _objRef, _playerId, CameraId, $"/streams/{CameraId}/stream.m3u8", SelectedDate, UTCOffset.TotalHours);
				}
			}
			catch (Exception exc)
			{
				_logger.LogError(exc, exc.Message);
				Snackbar.Add("Не удалось загрузить необходимые файлы скриптов, плеер будет недоступен!", MudBlazor.Severity.Error);
			}
		}
	}

	protected override async Task OnParametersSetAsync()
	{
		if (_cameraId != CameraId || _selectedDate != SelectedDate)
		{
			if (_playerJsModule is not null)
			{
				await _playerJsModule.InvokeVoidAsync($"updatePlayer", _objRef, _playerId, CameraId, $"/streams/{CameraId}/stream.m3u8", SelectedDate);
			}
			_selectedDate = SelectedDate;
			_cameraId = CameraId;
			_shouldRender = true;
		}
		else
		{
			_shouldRender = false;
		}

		await base.OnParametersSetAsync();
	}

	protected override bool ShouldRender() => _shouldRender;
}
