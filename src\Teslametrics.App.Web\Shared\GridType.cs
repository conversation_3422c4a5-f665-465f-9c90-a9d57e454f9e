namespace Teslametrics.App.Web.Shared;

public enum GridType
{
	// Ячеек: от 2 до 64
	/// <summary>
	///  1  2  3
	///  4  5  6
	///  7  8  9
	///  .  .  .
	/// </summary>
	Grid<PERSON>ustom,

	// Ячеек: 6
	/// <summary>
	///  1  1  2
	///  1  1  3
	///  4  5  6
	/// </summary>
	Grid1Plus5,

	// Ячеек: 8
	/// <summary>
	///  1  1  1  2
	///  1  1  1  3
	///  1  1  1  4
	///  5  6  7  8
	/// </summary>
	Grid1Plus7,

	// Ячеек: 13
	/// <summary>
	///  1  1  2  3
	///  1  1  4  5
	///  6  7  8  9
	/// 10 11 12 13
	/// </summary>
	Grid1Plus12,

	// Ячеек: 10
	/// <summary>
	///  1  1  2  2
	///  1  1  2  2
	///  3  4  5  6
	///  7  8  9 10
	/// </summary>
	Grid2Plus8,

	// Ячеек: 7
	/// <summary>
	///  1  1  2  2
	///  1  1  2  2
	///  3  3  4  5
	///  3  3  6  7
	/// </summary>
	Grid3Plus4
}
