@inherits InteractiveBaseComponent
<DrawerComponent Open="IsOpened"
				 OpenChanged="OnOpenChanged"
				 Options="_options">
	<CascadingValue IsFixed="true"
					Value="this">
		<div class="mud-height-full px-4 py-4">
			@switch (_mode)
			{
				case DrawerMode.View:
					@if (_resourceId.HasValue)
					{
						<AuthorizeView Policy="@(Fqdn<AppPermissions>.GetNames(AppPermissions.Main.AccessControl.Roles.Read).Last())"
									   Resource="new PolicyRequirementResource(_organizationId, _resourceId.Value)">
							<Authorized>
								<Teslametrics.App.Web.Features.Main.AccessControl.Roles.Drawer.View.RoleViewComponent OrganizationId="@_organizationId"
																													  RoleId="_resourceId.Value" />
							</Authorized>
							<NotAuthorized>
								@{
									_ = InvokeAsync(async () => await CloseAsync());
								}
							</NotAuthorized>
						</AuthorizeView>
					}
					break;

				case DrawerMode.Edit:
					@if (_resourceId.HasValue)
					{
						<AuthorizeView Policy="@(Fqdn<AppPermissions>.GetNames(AppPermissions.Main.AccessControl.Roles.Update).Last())"
									   Resource="new PolicyRequirementResource(_organizationId, _resourceId.Value)"
									   Context="innerContext">
							<Authorized>
								<Teslametrics.App.Web.Features.Main.AccessControl.Roles.Drawer.Edit.RoleEditComponent OrganizationId="@_organizationId"
																													  RoleId="_resourceId.Value" />
							</Authorized>
							<NotAuthorized>
								@{
									_ = InvokeAsync(async () => await ShowViewAsync(_organizationId, _resourceId!.Value));
								}
							</NotAuthorized>
						</AuthorizeView>
					}
					break;

				case DrawerMode.Create:
					<AuthorizeView Policy="@(Fqdn<AppPermissions>.GetNames(AppPermissions.Main.AccessControl.Roles.Create).Last())"
								   Resource="new PolicyRequirementResource(_organizationId)"
								   Context="innerContext">
						<Authorized>
							<Teslametrics.App.Web.Features.Main.AccessControl.Roles.Drawer.Create.RoleCreateComponent
																													  OrganizationId="@_organizationId" />
						</Authorized>
						<NotAuthorized>
							@{
								_ = InvokeAsync(async () => await CloseAsync());
							}
						</NotAuthorized>
					</AuthorizeView>
					break;
			}
		</div>
	</CascadingValue>
</DrawerComponent>