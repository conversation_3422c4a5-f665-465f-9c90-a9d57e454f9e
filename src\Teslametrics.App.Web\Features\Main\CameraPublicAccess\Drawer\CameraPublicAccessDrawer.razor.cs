using Teslametrics.App.Web.Events;
using Teslametrics.App.Web.Events.CameraPublicAccess;

namespace Teslametrics.App.Web.Features.Main.CameraPublicAccess.Drawer;

public partial class CameraPublicAccessDrawer
{
    private Guid? _organizationId;
    private Guid? _accessId;
    private Guid? _cameraId;
    private DrawerMode _mode;

    public bool IsOpened => _mode != DrawerMode.Hidden;

    public enum DrawerMode
    {
        Hidden,
        Create,
        Edit,
        View
    }

    protected override void OnInitialized()
    {
        CompositeDisposable.Add(EventSystem.Subscribe<CameraPublicAccessCreateEto>(OnEventHandler));
        CompositeDisposable.Add(EventSystem.Subscribe<CameraPublicAccessSelectEto>(OnEventHandler));
        CompositeDisposable.Add(EventSystem.Subscribe<CameraPublicAccessEditEto>(OnEventHandler));
        base.OnInitialized();
    }

    public void ShowCreate(Guid organizationId, Guid cameraId)
    {
        _organizationId = organizationId;
        _cameraId = cameraId;
        _mode = DrawerMode.Create;
        StateHasChanged();
    }

    public void ShowEdit(Guid organizationId, Guid accessId, Guid cameraId)
    {
        _organizationId = organizationId;
        _accessId = accessId;
        _cameraId = cameraId;
        _mode = DrawerMode.Edit;
        StateHasChanged();
    }
    public void ShowView(Guid organizationId, Guid accessId, Guid cameraId)
    {
        _organizationId = organizationId;
        _accessId = accessId;
        _cameraId = cameraId;
        _mode = DrawerMode.View;
        StateHasChanged();
    }

    public void Show(DrawerMode mode, Guid userId)
    {
        if (mode == DrawerMode.Hidden || mode == DrawerMode.Create)
        {
            throw new ArgumentException($"DrawerMode Edit or View expected {mode.ToString()} provided");
        }

        _mode = mode;
        _cameraId = userId;
    }

    public void Close()
    {
        _mode = DrawerMode.Hidden;
        _cameraId = null;
        StateHasChanged();
    }

    private void Show()
    {
        _mode = DrawerMode.Create;
        StateHasChanged();
    }

    #region [Event Handlers]
    private void OnEventHandler(BaseEto eto)
    {
        switch (eto)
        {
            case CameraPublicAccessCreateEto createEto:
                ShowCreate(createEto.OrganizationId, createEto.CameraId);
                break;
            case CameraPublicAccessEditEto editEto:
                ShowEdit(editEto.OrganizationId, editEto.AccessId, editEto.CameraId);
                break;
            case CameraPublicAccessSelectEto selectEto:
                ShowView(selectEto.OrganizationId, selectEto.AccessId, selectEto.CameraId);
                break;
            default:
                throw new ArgumentException("Unknown event type", nameof(eto));
        }
    }

    private void OnOpenChanged(bool opened)
    {
        if (opened)
        {
            if (_mode != DrawerMode.Hidden && _cameraId.HasValue)
            {
                Show(_mode, _cameraId.Value);
            }
            else
            {
                Show();
            }
        }
        else
        {
            Close();
        }
    }
    #endregion [Event Handlers]
}
