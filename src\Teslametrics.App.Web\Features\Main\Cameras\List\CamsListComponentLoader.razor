﻿<div class="list">
	@for (var item = 0; item < _rnd.Next(8, 20); item++)
	{
		<MudCard Class="mud-height-full mud-width-full ">
			<MudCardHeader>
				<div class="chips">
					<MudSkeleton Width="100px" />
				</div>
				<MudSpacer />
				<MudSkeleton Width="30px"
							Height="30px" />
			</MudCardHeader>
			<div class="d-flex mud-width-full flex-column justify-center align-center mud-height-full image_container">
				<MudSkeleton SkeletonType="SkeletonType.Rectangle"
							Width="100%"
							Height="100%"
							Class="img" />
			</div>
			<MudCardContent>
				<MudSkeleton />
			</MudCardContent>
		</MudCard>
	}
</div>