using System.Data;
using System.Text.Json;
using Dapper;
using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Data.Sql;
using Teslametrics.App.Web.Services.Persistence;
using Teslametrics.App.Web.Shared;

namespace Teslametrics.App.Web.Features.Main.CameraViews.TreeView;

public static class GetTreeUseCase
{
    public record Query(Guid UserId, string OrderBy, OrderDirection OrderDirection) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public List<Item> Items { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(List<Item> items)
        {
            Items = items;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Result = result;

            Items = [];
        }

        public record Item(Guid Id, string Name, List<View> Views, int ViewCount, int CameraCount); // CameraCount это количество камер в "виде"
        public record View(Guid Id, string Name, GridType GridType, int CameraCount);
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError
    }

    public class Validator : AbstractValidator<Query>
    {
        public Validator()
        {
            RuleFor(q => q.UserId).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Query, Response>
    {
        private readonly IValidator<Query> _validator;
        private readonly IDbConnection _dbConnection;

        public Handler(IValidator<Query> validator,
                       IDbConnection dbConnection)
        {
            _validator = validator;
            _dbConnection = dbConnection;
        }

        public async Task<Response> Handle(Query request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            var allowedResources = request.UserId != SystemConsts.RootUserId
                ? await GetAllowedResourcesAsync(request.UserId)
                : [];

            var cameraViewsBuilder = SqlQueryBuilder.Create()
                .Select(Db.Organizations.Props.Id)
                .Select(Db.Organizations.Props.Name)
                .Select(Db.CameraViews.Props.Id, "view_id")
                .Select(Db.CameraViews.Props.Name)
                .Select(Db.CameraViews.Props.GridType)
                .Select(Db.CameraViews.Props.Cells)
                .LeftJoin(Db.CameraViews.Table, Db.CameraViews.Props.OrganizationId, Db.Organizations.Props.Id, SqlOperator.Equals)
                .WhereIf(request.UserId != SystemConsts.RootUserId, $"({Db.Organizations.Props.OwnerId} = :UserId OR :OrganizationWildcard is TRUE OR {Db.Organizations.Props.Id} = ANY(:AllowedOrganizationIds) OR {Db.CameraViews.Props.Id} = ANY(:ResourceIds))", new
                {
                    request.UserId,
                    OrganizationWildcard = allowedResources.Any(r => r.OrganizationId == SystemConsts.RootOrganizationId),
                    AllowedOrganizationIds = allowedResources.Where(r => r.ResourceId == SystemConsts.ResourceWildcardId).Select(r => r.OrganizationId).ToList(),
                    ResourceIds = allowedResources.Select(r => r.ResourceId).ToList()
                })
                .OrderByIf(!string.IsNullOrWhiteSpace(request.OrderBy), ($"{Db.Organizations.Table}.{request.OrderBy}", request.OrderDirection), ($"{Db.CameraViews.Table}.{request.OrderBy}", request.OrderDirection))
                .Build(QueryType.Standard, Db.Organizations.Table, RowSelection.AllRows);

            var organizationModels = await _dbConnection.QueryAsync<OrganizationModel, CameraViewModel, OrganizationModel>(cameraViewsBuilder.RawSql, (organization, cameraView) =>
            {
                if (cameraView is not null)
                {
                    organization.CameraViews.Add(cameraView);
                }

                return organization;
            },
            cameraViewsBuilder.Parameters,
            splitOn: "view_id");

            if (!organizationModels.Any())
            {
                return new Response([]);
            }

            var result = organizationModels.GroupBy(r => r.Id)
                .Select(g =>
                {
                    var groupedOrganization = g.First();
                    groupedOrganization.CameraViews = g.SelectMany(r => r.CameraViews).Distinct().ToList();
                    return groupedOrganization;
                });

            var organizations = result.Select(o =>
            {
                var cameraViews = o.CameraViews
                    .Select(v =>
                    {
                        var cells = JsonSerializer.Deserialize<List<CellModel>>(v.Cells) ?? [];
                        return new Response.View(v.ViewId, v.Name, v.GridType, cells.Count);
                    }).ToList();

                return new Response.Item(o.Id, o.Name, cameraViews, cameraViews.Count, cameraViews.Sum(v => v.CameraCount));
            }).ToList();

            return new Response(organizations);
        }

        private async Task<IEnumerable<PermissionModel>> GetAllowedResourcesAsync(Guid userId)
        {
            var template = SqlQueryBuilder.Create()
                .Select(Db.RolePermissions.Props.OrganizationId)
                .Select(Db.RolePermissions.Props.ResourceId)
                .InnerJoin(Db.UserRoles.Table, Db.UserRoles.Props.RoleId, Db.RolePermissions.Props.RoleId, SqlOperator.Equals)
                .Where($"({Db.RolePermissions.Props.Permission} = :ViewPermission)", new
                {
                    ViewPermission = Fqdn<AppPermissions>.GetName(AppPermissions.Main.CameraViews.Read),
                })
                .Where(Db.UserRoles.Props.UserId, ":UserId", SqlOperator.Equals, new { userId })
                .Build(QueryType.Standard, Db.RolePermissions.Table, RowSelection.AllRows);

            return await _dbConnection.QueryAsync<PermissionModel>(template.RawSql, template.Parameters);
        }
    }

    public record OrganizationModel(Guid Id, string Name)
    {
        public List<CameraViewModel> CameraViews { get; set; } = [];
    }

    public record CameraViewModel(Guid ViewId, string Name, GridType GridType, string Cells);

    public record CellModel(Guid CameraId, uint CellIndex);

    public record PermissionModel(Guid OrganizationId, Guid ResourceId);
}
