::deep .container {
    display: grid;
    grid-template-rows: auto 1fr;
    overflow: hidden;
}

/* list rows */
::deep .notif-item {
    border-bottom: 1px solid #eef3f8;
}

::deep .notif-item:hover {
    background: var(--mud-palette-background);
}

::deep .notif-item:last-child {
    border-bottom: none;
}

/* unread orange dot */
::deep .dotted::after {
    content: " ";
    display: inline-flex;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--mud-palette-primary);
    margin-left: 12px;
}

/* misc tweaks */
::deep .fw-500 {
    font-weight: 500;
}

::deep .lh-1 {
    line-height: 1.2;
}

::deep .show_details {
    color: var(--color-neutral-70);
    border: 1px solid var(--color-neutral-90);
    border-radius: 8px;
}

::deep .show_details {
    background-color: (--mud-palette-surface);
}

::deep .icon {
    fill: var(--color-neutral-40);
}

::deep .no_items_icon {
    width: 48px;
    height: 48px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: var(--color-bg-blue);
    border-radius: 12px;
}

::deep .no_items_subtitle {
    color: var(--color-stroke-blue);
}