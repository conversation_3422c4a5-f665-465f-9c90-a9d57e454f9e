using System.Reactive;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using Teslametrics.App.Web.Extensions;
using Teslametrics.App.Web.Services.UserDevice;

namespace Teslametrics.App.Web.Features.Main.AccessControl;

public partial class AccessControlPage
{
    private bool _drawerOpen = true;
    private static class Tabs
    {
        public const string Organizations = nameof(Organizations);
        public const string Users = nameof(Users);
        public const string Roles = nameof(Roles);
        public const string Permissions = nameof(Permissions);
    }
    private int _panelIndex => View switch
    {
        Tabs.Organizations => 0,
        Tabs.Users => 1,
        Tabs.Roles => 2,
        Tabs.Permissions => 3,
        _ => 0
    };

    private bool _subscribing;
    private SubscribeOrganizationUseCase.Response? _subscriptionResult;

    #region
    private Guid? _organizationId;
    #endregion

    #region [Injectables]
    [Inject]
    private IUserDeviceService _userDeviceService { get; set; } = null!;

    [Inject]
    protected NavigationManager NavigationManager { get; set; } = null!;
    #endregion

    #region [Parameters]
    [SupplyParameterFromQuery(Name = "organization")]
    public string? OrganizationQuery { get; set; }

    [Parameter]
    [SupplyParameterFromQuery(Name = "view")]
    public string? View { get; set; }
    #endregion

    protected override void OnInitialized()
    {
        if (_userDeviceService.IsMobile)
        {
            _drawerOpen = true;
        }

        base.OnInitialized();
    }

    protected override void OnParametersSet()
    {
        bool queryParamsWasInvalid = false;
        // Validate and update organization ID
        if (!string.IsNullOrWhiteSpace(OrganizationQuery) && Guid.TryParse(OrganizationQuery, out var parsedGuid))
        {
            _organizationId = parsedGuid;
        }
        else
        {
            queryParamsWasInvalid = true;
            _organizationId = null;
        }

        // Validate View parameter
        if (!string.IsNullOrWhiteSpace(View) && !new[] { Tabs.Organizations, Tabs.Users, Tabs.Roles, Tabs.Permissions }.Contains(View))
        {
            queryParamsWasInvalid = true;
            View = null;
        }

        if (queryParamsWasInvalid)
        {
            var uri = NavigationManager.GetUriWithQueryParameters(new Dictionary<string, object?>
            {
                ["organization"] = _organizationId,
                ["view"] = View
            });
            try
            {
                NavigationManager.NavigateTo(uri, replace: true);
            }
            catch (JSDisconnectedException)
            {
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, ex.Message);
            }
        }
    }

    private async Task SubscribeAsync()
    {
        if (!_organizationId.HasValue) return;
        await SetSubscribingAsync(true);
        Unsubscribe();
        try
        {
            _subscriptionResult = await ScopeFactory.MediatorSend(new SubscribeOrganizationUseCase.Request(Observer.Create<object>(OnAppEventHandler, OnError), _organizationId.Value));
        }
        catch (Exception exc)
        {
            _subscriptionResult = null;
            Snackbar.Add("Не удалось получить подписку на события организации из-за непредвиденной ошибки сервера. Повторите попытку", MudBlazor.Severity.Error);
            Logger.LogError(exc, exc.Message);
        }

        await SetSubscribingAsync(false);
        if (_subscriptionResult is null) return;

        switch (_subscriptionResult.Result)
        {
            case SubscribeOrganizationUseCase.Result.Success:
                CompositeDisposable.Add(_subscriptionResult.Subscription!);
                break;

            case SubscribeOrganizationUseCase.Result.OrganizationNotFound:
                await OnOrganizationChanged(null);
                break;

            case SubscribeOrganizationUseCase.Result.ValidationError:
                Snackbar.Add("Ошибка валидации при подписке на события выбранной организации.", MudBlazor.Severity.Error);
                break;

            case SubscribeOrganizationUseCase.Result.Unknown:
                Logger.LogError("Unknown error while subscribing to organization events in Component: {Component}, UseCase: {UseCase}", nameof(AccessControlPage), nameof(SubscribeOrganizationUseCase));
                Snackbar.Add("Неизвестная ошибка при подписке на события выбранной организации. Повторите попытку и обратитесь к администратору.", MudBlazor.Severity.Error);
                break;
            default:
                Logger.LogError("Unknown error while subscribing to organization events in Component: {Component}, UseCase: {UseCase}, Result: {Result}", nameof(AccessControlPage), nameof(SubscribeOrganizationUseCase), _subscriptionResult.Result);
                Snackbar.Add($"Неизвестная ошибка при подписке на события выбранной организации. Повторите попытку и обратитесь к администратору. Код ошибки: {_subscriptionResult.Result}", MudBlazor.Severity.Error);
                break;
        }
    }

    private void Unsubscribe()
    {
        if (_subscriptionResult is not null && _subscriptionResult.Subscription is not null)
        {
            CompositeDisposable.Remove(_subscriptionResult.Subscription);
            _subscriptionResult.Subscription.Dispose();
        }
    }
    protected Task SetSubscribingAsync(bool isLoading = true) => UpdateViewAsync(() =>
    {
        _subscribing = isLoading;
    });

    #region [EventHandlers]
    private async Task OnOrganizationChanged(Guid? organizationId)
    {
        _organizationId = organizationId;
        var uri = NavigationManager.GetUriWithQueryParameters(new Dictionary<string, object?>
        {
            ["organization"] = organizationId,
            ["view"] = View
        });
        if (_organizationId is null)
        {
            Unsubscribe();
        }
        else
        {
            await SubscribeAsync();
        }
        _drawerOpen = false;
        NavigationManager.NavigateTo(uri, replace: true);
    }

    private async void OnAppEventHandler(object appEvent)
    {
        if (_organizationId is null)
        {
            Unsubscribe();
            return;
        }

        switch (appEvent)
        {
            case SubscribeOrganizationUseCase.DeletedEvent deletedEto:
                Snackbar.Add("Просматриваемая вами организация была удалена", MudBlazor.Severity.Error);
                await OnOrganizationChanged(null);
                await UpdateViewAsync();
                break;

            default:
                break;
        }
    }

    private void OnError(Exception exc)
    {
        Logger.LogError(exc, exc.Message);
        Snackbar.Add("Ошибка валидации при подписке на события", MudBlazor.Severity.Error);
    }

    private void OnSwipeEnd(MudBlazor.SwipeEventArgs e)
    {
        if (!_userDeviceService.IsMobile) return;

        if (e.SwipeDirection == MudBlazor.SwipeDirection.LeftToRight && !_drawerOpen)
        {
            _drawerOpen = true;
            StateHasChanged();
        }
        else if (e.SwipeDirection == MudBlazor.SwipeDirection.RightToLeft && _drawerOpen)
        {
            _drawerOpen = false;
            StateHasChanged();
        }
    }
    #endregion
}
