@using Teslametrics.Shared
@inherits InteractiveBaseComponent
<DrawerHeader>
    <MudStack Spacing="0">
        <MudText Typo="Typo.h3">Редактирование организации</MudText>
        @if (IsLoading)
        {
            <MudSkeleton Width="60%"
                         Height="calc(var(--mud-typography-subtitle1-lineheight) * var(--mud-typography-subtitle1-size))" />
        }
        @if (!IsLoading && _response is not null && _response.IsSuccess)
        {
            <MudText Typo="Typo.subtitle1">@_response.Name</MudText>
        }
        @if (!IsLoading && (_response is null || !_response.IsSuccess))
        {
            <MudText Typo="Typo.subtitle1">Не удалось получить параметры организации</MudText>
        }
    </MudStack>
    <MudSpacer />
    @if (!_subscribing && (_subscriptionResult is null || !_subscriptionResult.IsSuccess))
    {
        <MudTooltip Arrow="true"
                    Placement="Placement.Start"
                    Text="Ошибка подписки на события">
            <MudIconButton OnClick="SubscribeAsync"
                           Icon="@Icons.Material.Filled.ErrorOutline"
                           Color="Color.Error" />
        </MudTooltip>
    }
    <MudTooltip Text="@($"Время последнего обновления: {_lastRefreshTime.ToLocalTime()}")"
                Arrow="true"
                Placement="Placement.Start">
        <MudIconButton OnClick="RefreshAsync"
                       Icon="@Icons.Material.Filled.Refresh"
                       Color="Color.Primary" />
    </MudTooltip>
    @if (!IsLoading && _response is not null && _response.IsSuccess)
    {
        <MudMenu Icon="@Icons.Material.Filled.MoreVert"
                 AriaLabel="Действия с выбранной организацией"
                 Color="Color.Primary"
                 Variant="Variant.Outlined">
            <MudMenuItem OnClick="View"
                         Icon="@Icons.Material.Outlined.PanoramaFishEye">Просмотр</MudMenuItem>
            <AuthorizeView Policy="@AppPermissions.Main.AccessControl.Organizations.Delete.GetEnumPermissionString()"
                           Resource="new PolicyRequirementResource(OrganizationId, OrganizationId)"
                           Context="innerContext">
                <MudDivider Class="my-4" />
                <MudMenuItem OnClick="Delete"
                             Icon="@Icons.Material.Outlined.Delete"
                             IconColor="Color.Warning">Удалить</MudMenuItem>
            </AuthorizeView>
        </MudMenu>
    }
</DrawerHeader>
<MudForm Model="_model"
         Validation="_validator.ValidateValue"
         @bind-IsValid="_isValid"
         Class="flex-1"
         OverrideFieldValidation="true"
         UserAttributes="@(new Dictionary<string, object>() {{"autocomplete", "off"}, {"aria-autocomplete", "none" }})"
         Spacing="8">

    @if (!IsLoading && _model is not null && _response is not null && _response.IsSuccess)
    {
        <FormSectionComponent Title="Описание организации">
            <MudTextField @bind-Value="_model.Name"
                          For="@(() => _model.Name)"
                          Clearable="true"
                          InputType="InputType.Text"
                          Immediate="true"
                          Label="Наименование"
                          HelperText="Укажите наименование организации"
                          RequiredError="Данное поле обязательно"
                          Required="true" />

            <MudAutocomplete T="UserModel"
                             @bind-Value="_model.Owner"
                             For="() => _model.Owner"
                             Label="Владелец организации"
                             Clearable="true"
                             Placeholder="Введите название пользователя"
                             ResetValueOnEmptyText="true"
                             ToStringFunc="@(e=> e==null?null : e.Name)"
                             SearchFunc="@SearchOwnerAsync"
                             DebounceInterval="200"
                             ShowProgressIndicator="true"
                             AutoFocus="false"
                             CoerceValue="false"
                             CoerceText="false"
                             UserAttributes="@(new Dictionary<string, object>() {{"autocomplete", "off"}, {"name", "roles"}, {"aria-autocomplete", "none" }})"
                             AdornmentIcon="@Icons.Material.Filled.Search"
                             AdornmentColor="Color.Primary">
                <NoItemsTemplate>
                    <MudText Align="Align.Center"
                             Class="px-4 py-1">
                        Не найдено пользователей с данным именем
                    </MudText>
                </NoItemsTemplate>
            </MudAutocomplete>
        </FormSectionComponent>
    }
    <FormLoadingComponent IsLoading="IsLoading && (_response is null || !_response.IsSuccess)" />
    <NoItemsFoundComponent HasItems="_response is not null && _response.IsSuccess"
                           LastRefreshTime="_lastRefreshTime"
                           RefreshAsync="RefreshAsync" />
</MudForm>
<DrawerActions>
    <MudSpacer />
    <MudButton OnClick="CancelAsync"
               Variant="Variant.Outlined"
               StartIcon="@Icons.Material.Outlined.Close">Закрыть</MudButton>
    @if (IsLoading)
    {
        <MudSkeleton Width="150px"
                     Height="36.5px" />
    }
    else
    {
        <MudButton OnClick="SubmitAsync"
                   Disabled="@(!_isValid)"
                   Color="Color.Secondary"
                   Variant="Variant.Outlined">Сохранить</MudButton>
    }
</DrawerActions>