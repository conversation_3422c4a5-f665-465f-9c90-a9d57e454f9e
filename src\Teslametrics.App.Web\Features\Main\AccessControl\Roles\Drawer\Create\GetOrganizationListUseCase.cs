using Dapper;
using FluentValidation;
using MediatR;
using System.Data;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.Core.Services.Persistence;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Roles.Drawer.Create;

public static class GetOrganizationListUseCase
{
    public record Query(Guid UserId, int Offset, int Limit, string Filter) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public List<Item> Items { get; init; }

        public int TotalCount { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(List<Item> items, int totalCount)
        {
            Items = items;
            TotalCount = totalCount;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Result = result;

            Items = [];
            TotalCount = 0;
        }

        public record Item(Guid Id, string Name);
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError
    }

    public class Validator : AbstractValidator<Query>
    {
        public Validator()
        {
            RuleFor(q => q.UserId).NotEmpty();
            RuleFor(q => q.Offset).GreaterThanOrEqualTo(0);
            RuleFor(q => q.Limit).GreaterThan(0);
            RuleFor(q => q.Filter).MaximumLength(60);
        }
    }

    public class Handler : IRequestHandler<Query, Response>
    {
        private readonly IValidator<Query> _validator;
        private readonly IDbConnection _dbConnection;

        public Handler(IValidator<Query> validator,
                       IDbConnection dbConnection)
        {
            _validator = validator;
            _dbConnection = dbConnection;
        }

        public async Task<Response> Handle(Query request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            var allowedResources = request.UserId != SystemConsts.RootUserId
                ? await GetAllowedResourcesAsync(request.UserId)
                : [];

            var builder = SqlQueryBuilder.Create()
                .WhereIf(request.UserId != SystemConsts.RootUserId,
                         $"({Db.Organizations.Props.OwnerId} = :UserId OR :Wildcard = ANY(:ResourceIds) OR {Db.Organizations.Props.Id} = ANY(:ResourceIds))",
                         new { request.UserId, Wildcard = SystemConsts.ResourceWildcardId, ResourceIds = allowedResources })
                .WhereIf(!string.IsNullOrEmpty(request.Filter), $"{Db.Organizations.Props.Name} LIKE CONCAT('%', :Filter, '%')",
                         new { request.Filter });

            var countTemplate = builder.Build(QueryType.Standard, Db.Organizations.Table, RowSelection.AllRows, ["COUNT(*)"]);
            var totalCount = await _dbConnection.ExecuteScalarAsync<int>(countTemplate.RawSql, countTemplate.Parameters);

            var selectTemplate = builder.Build(QueryType.Paginated,
                                               Db.Organizations.Table,
                                               RowSelection.AllRows,
                                               [
                                                   Db.Organizations.Props.Id,
                                                   Db.Organizations.Props.Name
                                               ],
                                               new { request.Limit, request.Offset });
            var organizations = await _dbConnection.QueryAsync<OrganizationModel>(selectTemplate.RawSql, selectTemplate.Parameters);

            return new Response(organizations.Select(o => new Response.Item(o.Id, o.Name)).ToList(), totalCount);
        }

        private async Task<IEnumerable<Guid>> GetAllowedResourcesAsync(Guid userId)
        {
            var template = SqlQueryBuilder.Create()
                .Select(Db.RolePermissions.Props.ResourceId)
                .InnerJoin(Db.UserRoles.Table, Db.UserRoles.Props.RoleId, Db.RolePermissions.Props.RoleId, SqlOperator.Equals)
                .Where(Db.RolePermissions.Props.Permission, ":Permission", SqlOperator.Equals, new { Permission = Fqdn<AppPermissions>.GetName(AppPermissions.Main.AccessControl.Organizations.Read) })
                .Where(Db.UserRoles.Props.UserId, ":UserId", SqlOperator.Equals, new { userId })
                .Build(QueryType.Standard, Db.RolePermissions.Table, RowSelection.AllRows);;

            return await _dbConnection.QueryAsync<Guid>(template.RawSql, template.Parameters);
        }
    }

    public record OrganizationModel(Guid Id, string Name);
}