@using Teslametrics.App.Web.Features.Main.Devices.RoomContent.Fridge
@inherits InteractiveBaseComponent
<div class="d_contents">
	@if (_response is not null)
	{
		<MudGrid Spacing="2">
			<MudItem xs="12">
				<MudPaper Elevation="0" Outlined="true" Class="@(_response.ActiveIncidentCount == 0 ? "incidents pa-4" : "incidents pa-4 error")">
					<InfoCardComponent Icon="@(_response.ActiveIncidentCount == 0 ? TeslaIcons.State.Success : TeslaIcons.State.Warning)"
						Title="@_response.ActiveIncidentCount.ToString()" Error="_response.ActiveIncidentCount != 0" Subtitle="Происшествия" />
				</MudPaper>
			</MudItem>
			<MudItem xs="12" md="6">
				<MudPaper Elevation="0" Outlined="true" Class="pa-4">
					<InfoCardComponent Icon="@TeslaIcons.Devices.Fridge" Title="@_response.Fridges.Count.ToString()" Subtitle="Холодильники" />
				</MudPaper>
			</MudItem>
			<MudItem xs="12" md="6">
				<MudPaper Elevation="0" Outlined="true" Class="pa-4">
					<InfoCardComponent Icon="@TeslaIcons.Devices.Camera" Title="@_response.Cameras.Count.ToString()" Subtitle="Камеры" />
				</MudPaper>
			</MudItem>
		</MudGrid>
		<MudExpansionPanels Dense="true" Class="list" Elevation="0" MultiExpansion="true" Outlined="true" Gutters="false">
			@foreach (var fridge in _response.Fridges)
			{
				<FridgeComponent Fridge="@fridge" @key="@fridge" />
			}
		</MudExpansionPanels>
	}
</div>