using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using MudBlazor;

namespace Teslametrics.App.Web.Features.Main.Cameras.CameraDrawer.View;

public partial class IFrameCopyComponent
{
	private Guid _id;
	private string _iframe = string.Empty;

	[Inject]
	public IJSRuntime JS { get; set; } = null!;

	[Inject]
	public ISnackbar Snackbar { get; set; } = null!;

	[Parameter]
	public Guid CameraId { get; set; }

	protected override async Task OnInitializedAsync()
	{
		await base.OnInitializedAsync();

		try
		{
			var host = await JS.InvokeAsync<string>("eval", "(() => window.location.hostname)()"); // В теории eval плохо, но, т.к. serverside blazor, то можно
			var protocol = await JS.InvokeAsync<string>("eval", "(() => window.location.protocol)()");
			_iframe = $"<iframe src=\"{protocol}//{host}/Cameras/View/{CameraId}\" width=\"100%\" height=\"100%\" frameborder=\"0\" allowfullscreen></iframe>";
		}
		catch (Exception)
		{
		}
	}

	private void OnOpenChanged(bool open)
	{
		if(open)
		{
			_id = Guid.NewGuid();
		}
	}

	private async Task CopyLinkToClipboardAsync()
	{
		try
		{
			//var host = await JS.InvokeAsync<string>("eval", "(() => window.location.hostname)()"); // В теории eval плохо, но, т.к. serverside blazor, то можно
			await JS.InvokeVoidAsync("navigator.clipboard.writeText", _iframe);
			Snackbar.Add("Код для сайта скопирован!", MudBlazor.Severity.Success);
		}
		catch (Exception)
		{
			Snackbar.Add("Не удалось скопировать код для сайта", MudBlazor.Severity.Error);
		}
	}
}
