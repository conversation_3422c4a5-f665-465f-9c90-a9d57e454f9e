﻿class DrawerComponent {
	constructor(_dataId) {
		this.initialize(_dataId);
		this.isResizing = false;
	}

	initialize(_dataId) {
		this.container = document.querySelector(`.drawer[data-id="${_dataId}"]`);
		this.dragger = document.querySelector(`.drawer[data-id="${_dataId}"] .dragger`);

		document.addEventListener("mouseup", this.onMouseUp.bind(this), true);
		this.dragger.addEventListener("mousedown", this.onMouseDown.bind(this), true);
	}

	onMouseUp(e) {
		this.isResizing = false;
		document.removeEventListener("mousemove", this.onMouseMove);
	}

	onMouseMove(e) {
		if (!this.isResizing) {
			return;
		}

		e.stopPropagation();
		e.preventDefault();

		if (document.selection) {
			document.selection.empty()
		} else {
			window.getSelection().removeAllRanges()
		}

		switch (this.container.dataset.anchor) {
			case "Left":
				this.container.style.width = e.clientX + "px";
				break;
			case "Right":
				var offsetRight = this.container.clientWidth - (e.clientX - this.container.offsetLeft);

				if (this.container.dataset.minimalWidth != undefined && offsetRight <= this.container.dataset.minimalWidth) {
					this.container.style.width = container.dataset.minimalWidth;
					break;
				}

				var space = document.getElementById('layout_sidebar').getBoundingClientRect().width;
				if (window.innerWidth - space < offsetRight) {
					offsetRight = window.innerWidth - space;
				}
				this.container.style.width = offsetRight + "px";
				break;
			default:
				break;
		}
		this.container.dataset.LastWith = this.container.style.width;
	}

	onMouseDown(e) {
		this.isResizing = true;
		document.addEventListener("mousemove", this.onMouseMove.bind(this), true);
	}

	onOpen() {
		this.container.style.width = this.container.dataset.LastWith;
	}

	onClose() {
		this.container.style.width = null;
	}

	dispose() {
		document.removeEventListener("mouseup", this.onMouseUp);
		this.dragger.removeEventListener("mousedown", this.onMouseDown);
		document.removeEventListener("mousemove", this.onMouseMove);

		this.container = null;
		this.dragger = null;
	}
}

window.DrawerComponent = DrawerComponent;

export function initializeDrawerComponent(options) {
	return new DrawerComponent(options);
}