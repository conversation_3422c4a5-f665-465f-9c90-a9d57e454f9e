﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Teslametrics.App.Web.Locales.Features.Main.AccessControl.Roles.Drawer.View.CameraViews {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class CameraViewsConcretePermissionsComponent {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal CameraViewsConcretePermissionsComponent() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Teslametrics.App.Web.Locales.Features.Main.AccessControl.Roles.Drawer.View.Camera" +
                            "Views.CameraViewsConcretePermissionsComponent", typeof(CameraViewsConcretePermissionsComponent).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete.
        /// </summary>
        public static string Main_CameraViews_Delete {
            get {
                return ResourceManager.GetString("Main.CameraViews.Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Read.
        /// </summary>
        public static string Main_CameraViews_Read {
            get {
                return ResourceManager.GetString("Main.CameraViews.Read", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Update.
        /// </summary>
        public static string Main_CameraViews_Update {
            get {
                return ResourceManager.GetString("Main.CameraViews.Update", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nothing found.
        /// </summary>
        public static string NothingFound {
            get {
                return ResourceManager.GetString("NothingFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Try again later.
        /// </summary>
        public static string TryAgainLater {
            get {
                return ResourceManager.GetString("TryAgainLater", resourceCulture);
            }
        }
    }
}
