@using Teslametrics.App.Web.Events.Cameras
@using Teslametrics.App.Web.Extensions
@using Teslametrics.App.Web.Components.DragAndDrop
@using Teslametrics.MediaServer.Orleans.Camera
@using Teslametrics.Shared
@inherits InteractiveBaseComponent
<MudTreeViewItem @bind-Expanded="@Item.Expanded"
                 CanExpand="@Item.Expandable"
                 Items="@Item.Children"
                 Value="@Item.Value"
                 ReadOnly="true"
                 OnClick="SelectHandler">
    <Content>
        <MudTreeViewItemToggleButton @bind-Expanded="@Item.Expanded"
                                     Visible="@Item.HasChildren" />
        <div class="d-flex flex-row align-center mud-width-full">
            <DragAndDropWrapper Value="Item">
                <div class="py-2 px-1 d-flex flex-row align-center overflow-auto">
                    <MudTooltip Text="@_cameraStatusTooltipText"
                                Arrow="true"
                                Placement="Placement.Top">
                        <div class="icon_container">
                            <MudIcon Icon="@Item.Icon"
                                     Class="ml-0 mr-2"
                                     Color="@IconColor" />
                            @if (_presenter.Status == CameraStatus.Connecting)
                            {
                                <MudProgressCircular Indeterminate="true"
                                                     Size="Size.Small"
                                                     Color="Color.Info"
                                                     Class="loader" />
                            }
                        </div>
                    </MudTooltip>
                    <MudText>@Item.Text</MudText>
                </div>
            </DragAndDropWrapper>
        </div>
        <MudMenu Icon="@Icons.Material.Filled.MoreVert"
                 Variant="Variant.Outlined"
                 Color="Color.Secondary"
                 Class="ml-4">
            @if (_presenter.Status == CameraStatus.Running)
            {
                <MudMenuItem OnClick="ShowPlayer"
                             Icon="@Icons.Material.Outlined.PanoramaFishEye">Просмотр</MudMenuItem>
            }
            <MudMenuItem OnClick="ShowArchive"
                         Icon="@Icons.Material.Filled.FolderZip">Архив</MudMenuItem>
            <MudMenuItem OnClick="Select"
                         Icon="@Icons.Material.Outlined.PanoramaFishEye">Показать настройки
            </MudMenuItem>
            <AuthorizeView Policy="@AppPermissions.Main.Cameras.Disconnect.GetEnumPermissionString()"
                           Resource="new PolicyRequirementResource(OrganizationId, Item.Value)"
                           Context="innerContext">
                @if (_presenter.Status == CameraStatus.Running || _presenter.Status == CameraStatus.Connecting || _presenter.Status == CameraStatus.Problem)
                {
                    <MudMenuItem OnClick="DisconnectAsync"
                                 Icon="@Icons.Material.Filled.VisibilityOff">Отключить</MudMenuItem>
                }
            </AuthorizeView>
            <AuthorizeView Policy="@AppPermissions.Main.Cameras.Connect.GetEnumPermissionString()"
                           Resource="new PolicyRequirementResource(OrganizationId, Item.Value)"
                           Context="innerContext">
                @if (_presenter.Status == CameraStatus.Stopped)
                {
                    <MudMenuItem OnClick="ConnectAsync"
                                 Icon="@Icons.Material.Filled.Visibility">Подключить</MudMenuItem>
                }
            </AuthorizeView>
            <AuthorizeView Policy="@AppPermissions.Main.Cameras.Update.GetEnumPermissionString()"
                           Resource="new PolicyRequirementResource(OrganizationId, Item.Value)"
                           Context="innerContext">
                <MudMenuItem OnClick="Edit"
                             Icon="@Icons.Material.Outlined.Edit">Редактировать настройки
                </MudMenuItem>
            </AuthorizeView>
            <AuthorizeView Policy="@AppPermissions.Main.Cameras.Delete.GetEnumPermissionString()"
                           Resource="new PolicyRequirementResource(OrganizationId, Item.Value)"
                           Context="innerContext">
                <MudDivider Class="my-3" />

                @* <MudMenuItem OnClick="ClearArchive"
							 Icon="@Icons.Material.Outlined.Delete"
							 IconColor="Color.Warning">
					Очистить архив</MudMenuItem> *@

                <MudMenuItem OnClick="Delete"
                             Icon="@Icons.Material.Outlined.Delete"
                             IconColor="Color.Warning">
                    Удалить</MudMenuItem>
            </AuthorizeView>
        </MudMenu>
    </Content>
</MudTreeViewItem>