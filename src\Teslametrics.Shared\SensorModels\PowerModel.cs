using Orleans;

namespace Teslametrics.Shared;

[GenerateSerializer]
public class PowerModel : BaseSensorModel
{
    // Конструктор без параметров для System.Text.Json
    public PowerModel()
        : base(Guid.Empty, string.Empty, null)
    {
    }

    public PowerModel(string name = "", string? displayName = null)
        : this(GuidGenerator.New(), name, displayName)
    {
    }

    public PowerModel(Guid id, string name, string? displayName)
        : base(id, name, displayName)
    {
    }
}
