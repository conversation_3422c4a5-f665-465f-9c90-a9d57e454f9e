using Dapper;
using FluentValidation;
using MediatR;
using System.Data;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.Shared;
using Teslametrics.MediaServer.Orleans.Camera;
using Teslametrics.App.Web.Services.Persistence;

namespace Teslametrics.App.Web.Features.Main.Cameras.IFrame;

public static class GetCameraUseCase
{
    public record Query(Guid PublicAccessId) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public Guid Id { get; init; }
        public CameraStatus CameraStatus { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(Guid id, CameraStatus cameraStatus)
        {
            Id = id;
            CameraStatus = cameraStatus;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Result = result;

            Id = Guid.Empty;
        }
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        CameraNotFound
    }

    public class Validator : AbstractValidator<Query>
    {
        public Validator()
        {
            RuleFor(q => q.PublicAccessId).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Query, Response>
    {
        private readonly IValidator<Query> _validator;
        private readonly IDbConnection _dbConnection;
        private readonly IClusterClient _clusterClient;

        public Handler(IValidator<Query> validator,
                       IDbConnection dbConnection,
                       IClusterClient clusterClient)
        {
            _validator = validator;
            _dbConnection = dbConnection;
            _clusterClient = clusterClient;
        }

        public async Task<Response> Handle(Query request, CancellationToken cancellationToken = default)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            var template = SqlQueryBuilder.Create()
                .Select(Db.PublicLinks.Props.CameraId)
                .Where(Db.PublicLinks.Props.Id, ":Id", SqlOperator.Equals, new { Id = request.PublicAccessId })
                .Build(QueryType.Standard, Db.PublicLinks.Table, RowSelection.AllRows);

            var cameraId = await _dbConnection.QuerySingleAsync<Guid>(template.RawSql, template.Parameters);

            if (cameraId == Guid.Empty)
            {
                return new Response(Result.CameraNotFound);
            }

            var grain = _clusterClient.GetGrain<ICameraGrain>(cameraId);
            var status = await grain.GetStatusAsync();

            return new Response(cameraId,
                                status);
        }
    }
}