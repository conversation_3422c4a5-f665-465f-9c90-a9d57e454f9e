﻿@if (IsLoading)
{
    <MudStack Spacing="8">
        <FormSectionComponent title="Описания вида">
            <MudSkeleton Width="60%"
                         Height="52px" />
        </FormSectionComponent>
        <FormSectionComponent title="Настройки отображения">
            <MudGrid>
                <MudItem xs="6">
                    <MudSkeleton Width="60%"
                                 Height="52px" />
                </MudItem>

                <MudItem xs="6">
                    <MudSkeleton Width="60%"
                                 Height="52px" />
                </MudItem>
            </MudGrid>
        </FormSectionComponent>
        <div class="grid"
             style="@($"grid-template-columns: repeat({_columns}, 1fr);grid-template-rows: repeat({_rows}, 1fr);")">
            @for (var i = 0; i < _elements; i++)
            {
                <MudSkeleton SkeletonType="SkeletonType.Rectangle"
                             Class="skeleton-camera"
                             @key="i" />
            }
        </div>
    </MudStack>
}