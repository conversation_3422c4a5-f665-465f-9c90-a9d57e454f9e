// Components/MSEPlayer/core/SignalRFeed.js
// signalR и mitt доступны глобально через CDN

/**
 * Тонкий слой поверх @microsoft/signalr:
 *  • открывает HubConnection
 *  • пересылает бинарные сегменты наружу
 *  • держит собственное состояние (connecting / ready / paused / closed)
 *
 * PlayerCore остаётся «агностиком транспорта» – он просто подписывается
 *   feed.on('chunk',  (u8, absDate) => pipeline.push(u8, absDate));
 *   feed.on('error',  err          => bus.emit('error', err));
 *   feed.on('state',  newState     => bus.emit('state', newState));
 */
export default class SignalRFeed {
  /** @typedef {'idle'|'connecting'|'ready'|'paused'|'stopped'} FeedState */

  /**
   * @param {string} cameraId  – GUID камеры
   * @param {string} wsPath    – URL, например "/videoHub"
   * @param {boolean} autoReconnect – true ➜ .withAutomaticReconnect()
   * @param {object} [opts]
   * @param {number} [opts.backOffMs=2000] – задержка перед повторным .start() если изначально не поднялось
   * @param {AbortSignal} [opts.abortSignal] – внешний AbortController для полного выключения
   */
  constructor(cameraId, wsPath = "/videoHub", autoReconnect = true, opts = {}) {
    this.id = cameraId;
    this.wsPath = wsPath;
    this.autoReconnect = autoReconnect;
    this.backOffMs = opts.backOffMs ?? 2_000;
    this.state /** @type {FeedState} */ = "idle";

    /** mitt: chunk / state / error */
    this.ev = mitt();

    /** @type {signalR.HubConnection} */
    this.hub = new signalR.HubConnectionBuilder()
      .withUrl(this.wsPath)
      .configureLogging(signalR.LogLevel.Information)
      .withHubProtocol(new signalR.protocols.msgpack.MessagePackHubProtocol())
      .withAutomaticReconnect(autoReconnect ? {} : null)
      .build();

    /* сообщение с сервера: сырые байты + абсолютное время записи */
    this.hub.on("ReceiveVideoSegment", (arrayBuffer, isoTimestamp) => {
      console.info(
        `[SignalRFeed] SignalR получил сегмент: ${
          arrayBuffer?.byteLength || 0
        } байт, время: ${isoTimestamp}`
      );
      if (arrayBuffer?.byteLength) {
        const u8 = new Uint8Array(arrayBuffer);

        // Более точный парсинг времени с проверкой валидности
        let ts = undefined;
        if (isoTimestamp) {
          try {
            // Парсим как UTC время
            ts = new Date(isoTimestamp);

            // Проверяем валидность времени
            if (isNaN(ts.getTime())) {
              console.warn(
                `[SignalRFeed] Невалидное время записи: ${isoTimestamp}`
              );
              ts = undefined;
            } else {
              console.debug(
                `[SignalRFeed] Время записи сегмента: ${ts.toISOString()}`
              );
            }
          } catch (error) {
            console.error(
              `[SignalRFeed] Ошибка парсинга времени записи: ${isoTimestamp}`,
              error
            );
            ts = undefined;
          }
        }

        console.info(
          `[SignalRFeed] Передаем сегмент в pipeline: ${
            u8.byteLength
          } байт, время: ${ts?.toISOString() || "не определено"}`
        );
        this.ev.emit("chunk", { data: u8, ts }); // передаём один объект
      } else {
        console.warn(
          "[SignalRFeed] Получен пустой или некорректный сегмент от сервера"
        );
      }
    });

    /* встроенные коллбеки HubConnection */
    this.hub.onreconnecting((err) => {
      this._setState("connecting");
      if (err) this.ev.emit("error", err);
    });

    this.hub.onreconnected(() => {
      this._setState("ready");
      // сервер не знает, что надо возобновить поток – просим заново
      void this._invokeSafe("PlayStreamAsync", this.id);
    });

    this.hub.onclose((err) => {
      this._setState("stopped");
      if (err) this.ev.emit("error", err);
    });

    /* внешняя отмена */
    opts.abortSignal?.addEventListener("abort", () => this.dispose(), {
      once: true,
    });
  }

  /* ---------- публичный API ---------- */

  /** Promise<void> – устанавливает соединение и запрашивает поток */
  async start() {
    if (this.state === "ready") {
      console.info("SignalR уже подключен");
      return;
    }
    console.info("[SignalRFeed] Начинаем подключение SignalR...");
    this._setState("connecting");

    try {
      await this.hub.start(); // может выбросить
      console.info(
        "[SignalRFeed] SignalR подключен, запрашиваем поток камеры:",
        this.id
      );
      await this.hub.invoke("PlayStreamAsync", this.id);
      console.info("[SignalRFeed] Поток камеры запущен");
      this._setState("ready");
    } catch (e) {
      console.error("Ошибка подключения SignalR:", e);
      this.ev.emit("error", e);
      // План «B»: ждём back-off → пытаемся ещё
      setTimeout(() => {
        if (this.state !== "stopped") void this.start();
      }, this.backOffMs);
    }
  }

  /** Пауза без разрыва соединения – просим сервер прекратить посылать данные */
  pause() {
    if (this.state !== "ready") return;
    void this._invokeSafe("StopStreamAsync"); //, this.id
    this._setState("paused");
  }

  /** Возобновить после pause() */
  resume() {
    if (this.state !== "paused") return;
    void this._invokeSafe("PlayStreamAsync", this.id);
    this._setState("ready");
  }

  /** Полное отключение и освобождение ресурсов */
  async dispose() {
    if (this.state === "stopped") return;
    try {
      await this._invokeSafe("StopStreamAsync"); // , this.id
    } catch {}
    try {
      await this.hub.stop();
    } catch {}
    this._setState("stopped");
    this.ev.all.clear();
  }

  /* ---------- события ---------- */

  /**
   * Подписка: feed.on('chunk', handler) и т.д.
   * @template K
   * @param {K} type
   * @param {(data: any) => void} handler
   */
  on(type, handler) {
    this.ev.on(type, handler);
  }

  /** Убрать подписку */
  off(type, handler) {
    this.ev.off(type, handler);
  }

  onChunk(handler) {
    this.ev.on("chunck", handler);
  }
  onError(handler) {
    this.ev.on("error", handler);
  }
  onState(handler) {
    this.ev.on("state", handler);
  }

  /* ---------- внутренние ---------- */

  _setState(s) {
    this.state = s;
    this.ev.emit("state", s);
  }

  /** invoke() с подавлением ошибки «The connection was closed». */
  async _invokeSafe(method, ...args) {
    try {
      await this.hub.invoke(method, ...args);
    } catch (e) {
      this.ev.emit("error", e);
    }
  }
}
