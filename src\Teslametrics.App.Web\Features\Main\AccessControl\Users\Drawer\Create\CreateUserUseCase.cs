using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Domain;
using Teslametrics.App.Web.Domain.AccessControl.Organizations;
using Teslametrics.App.Web.Domain.AccessControl.Users;
using Teslametrics.App.Web.Services;
using Teslametrics.App.Web.Services.Outbox;
using Teslametrics.App.Web.Services.TransactionManager;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Users.Drawer.Create;

public static partial class CreateUserUseCase
{
    public record Command(string Username, string Password, Guid OrganizationId, List<Guid> RoleIds) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public Guid Id { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(Guid id)
        {
            Id = id;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Id = Guid.Empty;
            Result = result;
        }
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        UsernameAlreadyExists,
        RoleNotFound,
        OrganizationNotFound
    }

    public class Validator : AbstractValidator<Command>
    {
        public Validator()
        {
            RuleFor(c => c.Username).Length(3, 60);
            RuleFor(c => c.Password).NotEmpty();
            RuleFor(c => c.OrganizationId).NotEmpty();
            RuleFor(c => c.RoleIds).NotEmpty();
            //RuleFor(c => c.ResourcePermissions).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Command, Response>
    {
        private readonly IValidator<Command> _validator;
        private readonly IUserRepository _userRepository;
        private readonly IOrganizationRepository _organizationRepository;
        private readonly ITransactionManager _transactionManager;
        private readonly IPublisher _publisher;
        private readonly IOutbox _outbox;

        public Handler(IValidator<Command> validator,
                       IUserRepository userRepository,
                       IOrganizationRepository organizationRepository,
                       ITransactionManager transactionManager,
                       IPublisher publisher,
                       IOutbox outbox)
        {
            _validator = validator;
            _userRepository = userRepository;
            _organizationRepository = organizationRepository;
            _transactionManager = transactionManager;
            _publisher = publisher;
            _outbox = outbox;
        }

        public async Task<Response> Handle(Command request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            using var transaction = await _transactionManager.CreateTransactionAsync();

            if (await _userRepository.IsUsernameExists(request.Username, cancellationToken))
            {
                return new Response(Result.UsernameAlreadyExists);
            }

            var organization = await _organizationRepository.FindAsync(request.OrganizationId, cancellationToken);
            if (organization is null)
            {
                return new Response(Result.OrganizationNotFound);
            }

            if (request.RoleIds.Except(organization.Roles.Select(r => r.Id)).Any())
            {
                return new Response(Result.RoleNotFound);
            }

            var hashedPassword = PasswordHasher.HashPassword(request.Password);

            var (user, events) = UserAggregate.Create(GuidGenerator.New(), request.Username, hashedPassword, request.OrganizationId, request.RoleIds);

            await _userRepository.AddAsync(user, cancellationToken);

            await _userRepository.SaveChangesAsync(cancellationToken);

            foreach (var @event in events)
            {
                await _publisher.Publish(@event, cancellationToken);
            }

            await _outbox.AddRangeAsync(events);

            await transaction.CommitAsync();
            return new Response(user.Id);
        }
    }
}