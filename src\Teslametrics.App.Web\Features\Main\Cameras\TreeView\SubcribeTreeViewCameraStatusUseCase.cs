using System.Reactive.Linq;
using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Exceptions;
using Teslametrics.MediaServer.Orleans.Camera;
using Teslametrics.MediaServer.Orleans.Camera.Events;
using Teslametrics.App.Web.Services.DomainEventBus;

namespace Teslametrics.App.Web.Features.Main.Cameras.TreeView;

//public static class SubcribeTreeViewCameraStatusUseCase
//{
//	public record Request(IObserver<CameraStatusUpdatedEvent> Observer, List<Guid> OrganizationIds) : BaseRequest<Response>;

//	public record Response : BaseResponse
//	{
//		public IDisposable? Subscription { get; init; }

//		public Result Result { get; init; }

//		public bool IsSuccess => Result == Result.Success;

//		public Response(IDisposable subscription)
//		{
//			Subscription = subscription;
//			Result = Result.Success;
//		}

//		public Response(Result result)
//		{
//			if (result == Result.Success)
//			{
//				throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
//			}

//			Subscription = null;
//			Result = result;
//		}
//	}

//	public record CameraStatusUpdatedEvent(IReadOnlyDictionary<Guid, CameraStatus> Statuses);

//	public enum Result
//	{
//		Unknown = 0,
//		Success,
//		ValidationError
//	}

//	public class Validator : AbstractValidator<Request>
//	{
//		public Validator()
//		{
//			RuleFor(r => r.Observer).NotEmpty();
//			RuleFor(r => r.OrganizationIds).NotEmpty();
//		}
//	}

//	public class Handler : IRequestHandler<Request, Response>
//	{
//		private readonly IValidator<Request> _validator;
//		private readonly IDomainEventBus _domainEventBus;

//		public Handler(IValidator<Request> validator,
//					   IDomainEventBus domainEventBus)
//		{
//			_validator = validator;
//			_domainEventBus = domainEventBus;
//		}

//		public async Task<Response> Handle(Request request, CancellationToken cancellationToken)
//		{
//			if (!_validator.Validate(request).IsValid)
//			{
//				return new Response(Result.ValidationError);
//			}

//			var eventStream = await _domainEventBus.GetEventStreamAsync();

//			var subscription = eventStream
//				.Where(e => e switch
//				{
//					CameraRunningEvent => true,
//					CameraStoppedEvent => true,
//					CameraStartingEvent => true,
//					CameraProblemEvent => true,
//					_ => false
//				})
//				.Buffer(TimeSpan.FromSeconds(1))
//				.Select(e =>
//				{
//					var cameraStatuses = e.Select(x => x switch
//					{
//						CameraRunningEvent e => (e.Id, State: CameraStatus.Running),
//						CameraStoppedEvent e => (e.Id, State: CameraStatus.Stopped),
//						CameraStartingEvent e => (e.Id, State: CameraStatus.Starting),
//						CameraProblemEvent e => (e.Id, State: CameraStatus.Problem),
//						_ => throw new AppException("Invalid event type")
//					});

//					var lastStatuses = new Dictionary<Guid, CameraStatus>();
//					foreach (var status in cameraStatuses)
//					{
//						lastStatuses[status.Id] = status.State;
//					}

//					return new CameraStatusUpdatedEvent(lastStatuses);
//				})
//				.Subscribe(request.Observer);

//			return new Response(subscription);
//		}
//	}
//}