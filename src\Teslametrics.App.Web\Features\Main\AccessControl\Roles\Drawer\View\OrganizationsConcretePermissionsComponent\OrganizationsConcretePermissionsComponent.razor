@using Organizations = Teslametrics.App.Web.Shared.AppPermissions.Main.AccessControl.Organizations
@inherits InteractiveBaseComponent
<MudDataGrid T="GetOrganizationListUseCase.Response.Item"
			 Items="_response?.Items ?? []"
			 Elevation="0"
			 Virtualize="true"
			 FixedHeader="true"
			 Hover="true"
			 Striped="true"
			 Style="min-height: fit-content;max-height: 400px;"
			 ItemSize="52.68f"
			 SortMode="SortMode.None">
	<ToolBarContent>
		<MudText Typo="Typo.h6">Организации</MudText>
		<MudSpacer />
	</ToolBarContent>
	<Columns>
		<PropertyColumn Property="x => x.Name"
						Title="Наименование"
						HeaderClass="mud-width-full" />
		<TemplateColumn Title="Просмотр"
						Hideable="true"
						Hidden="ContainsPermission(_wildcardPermissions, Organizations.Read)">
			<CellTemplate>
				<MudCheckBox T="bool"
							 Value="ContainsPermission(Selected, Organizations.Read, context.Item.Id)"
							 ReadOnly="true"
							 Color="Color.Primary" />
			</CellTemplate>
		</TemplateColumn>
		<TemplateColumn Title="Редактирование"
						Hideable="true"
						Hidden="ContainsPermission(_wildcardPermissions, Organizations.Update)">
			<CellTemplate>
				<MudCheckBox T="bool"
							 Value="ContainsPermission(Selected, Organizations.Update, context.Item.Id)"
							 ReadOnly="true"
							 Color="Color.Primary" />
			</CellTemplate>
		</TemplateColumn>
		<TemplateColumn Title="Удаление"
						Hideable="true"
						Hidden="ContainsPermission(_wildcardPermissions, Organizations.Delete)">
			<CellTemplate>
				<MudCheckBox T="bool"
							 Value="ContainsPermission(Selected, Organizations.Delete, context.Item.Id)"
							 ReadOnly="true"
							 Color="Color.Primary" />
			</CellTemplate>
		</TemplateColumn>
	</Columns>
	<NoRecordsContent>
		<MudStack AlignItems="AlignItems.Center"
				  Justify="Justify.Center"
				  Class="mud-height-full">
			<MudIcon Icon="@Icons.Material.Filled.WarningAmber"
					 Style="font-size: 3rem;" />
			<MudText Typo="Typo.body1">Ничего не найдено</MudText>
			<MudText Typo="Typo.subtitle1">Попробуйте снова позднее</MudText>
		</MudStack>
	</NoRecordsContent>
	<RowLoadingContent>
		<tr class="mud-table-row">
			<td class="mud-table-cell"
				colspan="1000">
				<MudSkeleton Width="60%"
							 Height="52.68f" />
			</td>
			<td class="mud-table-cell"
				colspan="1000">
				<MudSkeleton SkeletonType="SkeletonType.Circle"
							 Animation="Animation.Wave"
							 Height="30px"
							 Width="30px"
							 Class="ml-2 mb-2" />
			</td>
			<td class="mud-table-cell"
				colspan="1000">
				<MudSkeleton SkeletonType="SkeletonType.Circle"
							 Animation="Animation.Wave"
							 Height="30px"
							 Width="30px"
							 Class="ml-2 mb-2" />
			</td>
			<td class="mud-table-cell"
				colspan="1000">
				<MudSkeleton SkeletonType="SkeletonType.Circle"
							 Animation="Animation.Wave"
							 Height="30px"
							 Width="30px"
							 Class="ml-2 mb-2" />
			</td>
		</tr>
	</RowLoadingContent>
</MudDataGrid>