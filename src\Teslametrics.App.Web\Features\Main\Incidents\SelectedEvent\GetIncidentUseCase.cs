using System.Data;
using System.Text.Json;
using Dapper;
using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Data.Sql;
using Teslametrics.App.Web.Orleans.Camera;
using Teslametrics.App.Web.Services.Persistence;
using Teslametrics.App.Web.Shared;

namespace Teslametrics.App.Web.Features.Main.Incidents.SelectedEvent;

public static class GetIncidentUseCase
{
    public record Query(Guid IncidentId) : BaseRequest<Response>;

    // В Figma у нас есть запись вида "Время с открытой дверью: 44с". Но я хз можешь ли ты мне передать эту инфу, а если можешь - то как.
    public record Response : BaseResponse
    {
        public record IncidentModel(string DeviceName, IncidentType? IncidentType, DateTimeOffset FiredAt, bool IResolved);

        public FridgeModel? Fridge { get; init; }

        public IncidentModel? Incident { get; init; }

        public List<CameraModel> Cameras { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(IncidentModel incident, FridgeModel fridge, List<CameraModel> cameras)
        {
            Incident = incident;
            Fridge = fridge;
            Cameras = cameras;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }
            Incident = null;
            Fridge = null;
            Cameras = [];
            Result = result;
        }

        public record FridgeModel(Guid Id, string Name, List<ISensorModel> Sensors);

        public record CameraModel(Guid Id, Guid? CameraStreamId);

        public interface ISensorModel
        {
            public Guid Id { get; }
            public string Name { get; }
            public string? DisplayName { get; }
        }

        public record TemperatureModel(Guid Id, string Name, string? DisplayName) : ISensorModel;

        public record DoorModel(Guid Id, string Name, string? DisplayName) : ISensorModel;

        public record HumidityModel(Guid Id, string Name, string? DisplayName) : ISensorModel;

        public record LeakModel(Guid Id, string Name, string? DisplayName) : ISensorModel;

        public record PowerModel(Guid Id, string Name, string? DisplayName) : ISensorModel;
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        IncidentNotFound,
        PlanNotFound,
        FridgeNotFound,
        RoomNotFound
    }

    public class Validator : AbstractValidator<Query>
    {
        public Validator()
        {
            RuleFor(q => q.IncidentId).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Query, Response>
    {
        private readonly IValidator<Query> _validator;
        private readonly IDbConnection _dbConnection;
        private readonly IClusterClient _clusterClient;

        public Handler(IValidator<Query> validator,
                       IDbConnection dbConnection,
                       IClusterClient clusterClient)
        {
            _validator = validator;
            _dbConnection = dbConnection;
            _clusterClient = clusterClient;
        }

        public async Task<Response> Handle(Query request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            var template = SqlQueryBuilder.Create()
                .Select(Db.Incidents.Props.Id)
                .Select(Db.Incidents.Props.DeviceId)
                .Select(Db.Incidents.Props.Device)
                .Select(Db.Incidents.Props.IncidentType)
                .Select(Db.Incidents.Props.CreatedAt)
                .Select(Db.Incidents.Props.ResolvedAt)
                .Where(Db.Incidents.Props.Id, ":IncidentId", SqlOperator.Equals, new { request.IncidentId })
                .Build(QueryType.Standard, Db.Incidents.Table, RowSelection.AllRows);

            var incidentModel = await _dbConnection.QueryFirstOrDefaultAsync<IncidentModel>(template.RawSql, template.Parameters);
            if (incidentModel is null)
            {
                return new Response(Result.IncidentNotFound);
            }

            if (!await CheckTableExistsAsync())
            {
                return new Response(Result.PlanNotFound);
            }

            template = SqlQueryBuilder.Create()
                .Select(Db.Plans.Props.Page)
                .Build(QueryType.Standard, Db.Plans.Table, RowSelection.AllRows);

            var pageJson = await _dbConnection.ExecuteScalarAsync<string?>(template.RawSql);

            if (string.IsNullOrEmpty(pageJson))
            {
                return new Response(Result.PlanNotFound);
            }

            var page = JsonSerializer.Deserialize<PageModel>(pageJson)!;

            var fridge = page.GetFridge(incidentModel.DeviceId);

            if (fridge is null)
            {
                return new Response(Result.FridgeNotFound);
            }

            var room = page.GetRoomByFridgeId(fridge.Id);

            if (room is null)
            {
                return new Response(Result.RoomNotFound);
            }

            var cameras = new List<Response.CameraModel>();

            if (room.Camera is not null)
            {
                var response = await _clusterClient.GetGrain<ICameraGrain>(room.Camera.Id).GetCameraStreamIdAsync(new ICameraGrain.GetCameraStreamIdRequest(StreamType.Archive));
                cameras.Add(new Response.CameraModel(room.Camera.Id, response.CameraStreamId));
            }

            return new Response(new Response.IncidentModel(incidentModel.Device,
                                                           incidentModel.IncidentType,
                                                           incidentModel.CreatedAt,
                                                           incidentModel.ResolvedAt.HasValue),
                                new Response.FridgeModel(fridge.Id,
                                                         fridge.Name,
                                                         fridge.Sensors.Select(GetSensorModel).ToList()),
                                                         cameras);
        }

        private async Task<bool> CheckTableExistsAsync()
        {
            // Check if table exists
            var tableExists = await _dbConnection.ExecuteScalarAsync<int>(
                "SELECT COUNT(*) FROM information_schema.tables " +
                "WHERE table_schema = 'public' AND table_name = @TableName",
                new { TableName = Db.Plans.Table });

            return tableExists > 0;
        }

        private static Response.ISensorModel GetSensorModel(ISensorModel sensor)
        {
            var model = sensor switch
            {
                TemperatureModel => (Response.ISensorModel)new Response.TemperatureModel(sensor.Id, sensor.Name, sensor.DisplayName),
                DoorModel => new Response.DoorModel(sensor.Id, sensor.Name, sensor.DisplayName),
                HumidityModel => new Response.HumidityModel(sensor.Id, sensor.Name, sensor.DisplayName),
                LeakModel => new Response.LeakModel(sensor.Id, sensor.Name, sensor.DisplayName),
                PowerModel => new Response.PowerModel(sensor.Id, sensor.Name, sensor.DisplayName),
                _ => throw new ArgumentException($"Unknown sensor type: {sensor.GetType()}")
            };

            return model;
        }
    }

    public record IncidentModel(Guid Id, Guid DeviceId, string Device, IncidentType IncidentType, DateTimeOffset CreatedAt, DateTimeOffset? ResolvedAt);
}