using Microsoft.AspNetCore.Components;
using MudBlazor;
using MudExtensions;
using System.Reactive;
using System.Reactive.Disposables;
using Teslametrics.App.Web.Events.Cameras;
using Teslametrics.App.Web.Extensions;
using Teslametrics.App.Web.Services.UserDevice;

namespace Teslametrics.App.Web.Features.Main.Cameras.PlayerDialog;

public partial class PlayerDialog
{
    private GetCameraUseCase.Response? _cameraResponse;
    private SubscribeCameraUseCase.Response? _subscriptionResult;
    private bool _isSubscribing;

    private Guid _cameraId;
    private Guid _guid = Guid.NewGuid();
    //private string _playerId = string.Empty;
    private DialogOptions _dialogOptions = new() { CloseOnEscapeKey = true, FullWidth = true, MaxWidth = MaxWidth.ExtraExtraLarge, NoHeader = true }; // https://github.com/MudBlazor/MudBlazor/issues/1043
    private bool _isVisible;

    [Inject]
    private IUserDeviceService _userDeviceService { get; set; } = null!;

    protected override void OnInitialized()
    {
        CompositeDisposable.Add(EventSystem.Subscribe<ShowCameraStreamEto>(ShowStreamAsync));

        _dialogOptions = new()
        {
            FullScreen = _userDeviceService.IsMobile,
            CloseOnEscapeKey = true,
            FullWidth = true,
            MaxWidth = MaxWidth.ExtraExtraLarge,
            NoHeader = true
        };
    }

    private void VisibilityChanged(bool isVisible)
    {
        _isVisible = isVisible;
        if (!isVisible)
        {
            Unsubscribe();
        }
    }

    private Task CancelAsync() => UpdateViewAsync(() =>
    {
        _isVisible = false;
    });

    private async Task ShowStreamAsync(ShowCameraStreamEto eto)
    {
        _cameraId = eto.CameraId;
        //_playerId = new StringBuilder(nameof(PlayerDialog)).Append("-").Append(eto.EventId).ToString();
        await UpdateViewAsync(() =>
        {
            _isVisible = true;
        });

        var tasks = new List<Task>() {
            SubscribeAsync(),
            FetchAsync()
        };

        await Task.WhenAll(tasks);
    }

    private async Task SubscribeAsync()
    {
        try
        {
            await SetSubscribingAsync(true);
            Unsubscribe();
            _subscriptionResult = await ScopeFactory.MediatorSend(new SubscribeCameraUseCase.Request(Observer.Create<object>(OnAppEventHandler, OnError), _cameraId));
            await SetSubscribingAsync(false);
        }
        catch (Exception exc)
        {
            _subscriptionResult = null;
            await SetSubscribingAsync(false);
            Logger.LogError(exc, exc.Message);
        }

        if (_subscriptionResult is null) return;

        switch (_subscriptionResult.Result)
        {
            case SubscribeCameraUseCase.Result.Success:
                CompositeDisposable.Add(_subscriptionResult.Subscription!);
                break;
            case SubscribeCameraUseCase.Result.ValidationError:
                Snackbar.Add("Ошибка валидации при подписке на события", Severity.Error);
                break;
            case SubscribeCameraUseCase.Result.Unknown:
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(PlayerDialog), nameof(SubscribeCameraUseCase));
                Snackbar.Add($"Не удалось получить подписку на события камеры из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
                break;
            case SubscribeCameraUseCase.Result.CameraNotFound:
                Snackbar.Add("Не удалось получить подписку на события камеры. Возможно камера уже удалена.", Severity.Warning);
                await CancelAsync();
                break;
        }
    }

    private async Task FetchAsync()
    {
        try
        {
            await SetLoadingAsync(true);
            _cameraResponse = await ScopeFactory.MediatorSend(new GetCameraUseCase.Query(_cameraId));
            await SetLoadingAsync(false);
        }
        catch (Exception exc)
        {
            _cameraResponse = null;
            Logger.LogError(exc, exc.Message);
        }
    }

    private void Unsubscribe()
    {
        if (_subscriptionResult?.Subscription is not null)
        {
            CompositeDisposable.Remove(_subscriptionResult.Subscription);
            _subscriptionResult.Subscription.Dispose();
        }
    }

    private async Task SetSubscribingAsync(bool isLoading = true) => await UpdateViewAsync(() =>
    {
        _isSubscribing = isLoading;
    });

    private async void OnAppEventHandler(object appEvent)
    {
        switch (appEvent)
        {
            case SubscribeCameraUseCase.UpdatedEvent updatedEto:
                await FetchAsync();
                await UpdateViewAsync();
                break;

            case SubscribeCameraUseCase.DeletedEvent deletedEto:
                Snackbar.Add("Просматриваемая вами камера была удалена", Severity.Warning);
                await CancelAsync();
                break;

            default:
                await FetchAsync();
                break;
        }
        await UpdateViewAsync();
    }

    private void OnError(Exception exc)
    {
        Snackbar.Add("Ошибка при подписке на события", Severity.Error);
        Logger.LogError(exc, exc.Message);
    }
}
