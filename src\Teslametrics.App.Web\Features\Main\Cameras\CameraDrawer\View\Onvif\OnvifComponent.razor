﻿<FormSectionComponent Title="Подключение к ONVIF">
	<MudTextField T="string"
				  Value="Onvif.Username"
				  ReadOnly="true"
				  Label="Логин" />
	<PasswordFieldComponent Value="Onvif.Password"
							ReadOnly="true"
							Label="Пароль" />

	<MudStack Row="true">
		<MudTextField Value="Onvif.Host"
					  ReadOnly="true"
					  Label="Адрес ONVIF" />
		<div style="max-width: 100px">
			<MudNumericField T="int"
							 Value="Onvif.Port"
							 Label="Порт"
							 ReadOnly="true" />
		</div>
	</MudStack>
	@* <MudButton>Подключить</MudButton> *@
</FormSectionComponent>

@* <FormSectionComponent Title="Характеристики ONVIF"> *@
@* 	<MudCheckBox T="bool" *@
@* 				 Label="PTZ" *@
@* 				 ReadOnly="true" /> *@
@* 	<MudCheckBox T="bool" *@
@* 				 Label="Получать события" *@
@* 				 ReadOnly="true" /> *@
@* </FormSectionComponent> *@

@* <FormSectionComponent Title="Информация об устройстве"> *@
@* </FormSectionComponent> *@

@* <FormSectionComponent Title="Настройка выходных потоков"> *@
@* 	<MudStack> *@
@* 		<MudText Typo="Typo.subtitle2">Main stream</MudText> *@
@* 		<MudSelect T="string" *@
@* 				   Label="Профиль H.264"> *@
@* 			<MudSelectItem T="string">Main</MudSelectItem> *@
@* 		</MudSelect> *@
@* 		<SelectEnumComponent TEnum="Resolution" *@
@* 							 Label="Разрешение" *@
@* 							 ReadOnly="true" /> *@

@* 		<MudNumericField T="int" *@
@* 						 Label="Битрейт, кбит/с" /> *@

@* 		<SelectEnumComponent TEnum="FrameRate" *@
@* 							 Label="Частота кадров" *@
@* 							 ReadOnly="true" /> *@

@* 		<MudNumericField T="int" *@
@* 						 Label="Качество" *@
@* 						 ReadOnly="true" /> *@
@* 	</MudStack> *@
@* </FormSectionComponent> *@

@* <FormSectionComponent Title="Сеть"> *@
@* 	<MudCheckBox T="bool" *@
@* 				 Label="DHCP" *@
@* 				 ReadOnly="true" /> *@
@* </FormSectionComponent> *@

@* <FormSectionComponent Title="Настройки изображения"> *@
@* </FormSectionComponent> *@

@code
{
	[Parameter]
	[EditorRequired]
	public GetCameraUseCase.Response.OnvifSettings Onvif { get; set; } = null!;
}