@using System.Collections.ObjectModel

@implements IDisposable

<MudList T="ISensorModel" SelectionMode="SelectionMode.SingleSelection">
	@foreach (var sensor in Sensors)
	{
		<MudListItem Text="@sensor.DisplayName" SecondaryText="@sensor.Name" Icon="@Icons.Material.Filled.Aod" @key="@sensor">
			<ChildContent>
				<div class="d-flex flex-row align-center gap-3 ">
					<div class="d-flex flex-column sensor_title px-6 py-2">
						<MudText Typo="Typo.body1">
							Наименование: @sensor.DisplayName
						</MudText>
						<MudText Class="mud-list-item-secondary-text" Typo="Typo.subtitle2">
							Имя топика: @sensor.Name
						</MudText>
					</div>
					<div>
						<HumiditySensorContentComponent SensorModel="sensor" />
						<TempSensorContentComponent SensorModel="sensor" />
						<DoorSensorContentComponent SensorModel="sensor" />
					</div>
					<MudSpacer />
					<MudIconButton Icon="@Icons.Material.Outlined.Delete" Color="Color.Warning" OnClick="() => RemoveSensor(sensor)" />
				</div>
			</ChildContent>
		</MudListItem>
	}
</MudList>

@code {
	[Parameter]
	[EditorRequired]
	public ObservableCollection<ISensorModel> Sensors { get; set; } = null!;

	public void Dispose()
	{
		Sensors.CollectionChanged -= OnCollectionChanged;
	}

	protected override void OnInitialized()
	{
		base.OnInitialized();

		Sensors.CollectionChanged += OnCollectionChanged;
	}

	private void RemoveSensor(ISensorModel sensor)
	{
		var sensorToRemove = Sensors.Remove(sensor);
	}

	private void OnCollectionChanged(object? sender, System.Collections.Specialized.NotifyCollectionChangedEventArgs e) => StateHasChanged();
}