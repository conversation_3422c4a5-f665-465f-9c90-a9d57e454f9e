﻿using Microsoft.AspNetCore.Components;
using Teslametrics.App.Web.Services.BlzEventSystem;
using Teslametrics.App.Web.Shared;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Permissions;

public partial class PermissionsListComponent
{
	private bool _isLoading = false;
	private DateTime _lastRefreshTime = DateTime.Now;
	private string _searchString = string.Empty;

	public IEnumerable<string> Items => AppPermissions.GetAll();

	[Inject]
	protected IBlzEventSystem EventSystem { get; set; } = default!;

	#region Parameters
	[Parameter]
	public int Offset { get; set; } = 0;
	[Parameter]
	public EventCallback<int> OffsetChanged { get; set; }

	[Parameter]
	public int Limit { get; set; } = 25;
	[Parameter]
	public EventCallback<int> LimitChanged { get; set; }
	#endregion

	protected int CurrentPage => Offset / Limit;

	private Func<string, bool> _quickFilter => x =>
	{
		if (string.IsNullOrWhiteSpace(_searchString))
			return true;

		if (Localizer[x].Value.Contains(_searchString, StringComparison.OrdinalIgnoreCase))
			return true;

		if (Localizer["Desciption:" + x].Value.Contains(_searchString, StringComparison.OrdinalIgnoreCase))
			return true;

		return false;
	};

	private Task RowsPerPageChanged(int limit)
	{
		Limit = limit;
		if (LimitChanged.HasDelegate)
		{
			return LimitChanged.InvokeAsync(limit);
		}
		return Task.CompletedTask;
	}
}
