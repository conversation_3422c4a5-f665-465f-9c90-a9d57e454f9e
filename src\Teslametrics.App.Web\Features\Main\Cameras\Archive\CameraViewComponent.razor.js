﻿export function ClearVisibleRanges() {
	document.querySelectorAll(".sidebar .mud-picker-calendar .start,.sidebar .mud-picker-calendar .end").forEach(el => {
		el.classList.remove("start", "end");
	});	
}

export function SetVisibleRanges(start, end) {
	ClearVisibleRanges();

	var dateStart = new Date(start);
	var dateEnd = new Date(end);

	const startNode = document.evaluate(`//button //p[text()='${dateStart.getDay() - 1}']`, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
	startNode.parentNode.classList.add("start");

	const endNode = document.evaluate(`//button //p[text()='${dateEnd.getDay() - 1}']`, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
	endNode.parentNode.classList.add("end");
}