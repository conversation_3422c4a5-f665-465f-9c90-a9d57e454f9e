﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Teslametrics.DbMigrator.Migrations
{
    /// <inheritdoc />
    public partial class OnvifSettingsAdded : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "onvif_enabled",
                table: "cameras",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "onvif_settings",
                table: "cameras",
                type: "text",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "onvif_enabled",
                table: "cameras");

            migrationBuilder.DropColumn(
                name: "onvif_settings",
                table: "cameras");
        }
    }
}
