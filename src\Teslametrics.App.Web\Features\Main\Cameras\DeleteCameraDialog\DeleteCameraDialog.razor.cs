using MudBlazor;
using System.Reactive;
using Teslametrics.App.Web.Events.Cameras;
using Teslametrics.App.Web.Extensions;
using Teslametrics.App.Web.Features.Main.Cameras.DeleteFolderDialog;

namespace Teslametrics.App.Web.Features.Main.Cameras.DeleteCameraDialog;

public partial class DeleteCameraDialog
{
	private bool _subscribing;
	private DialogOptions _dialogOptions = new() { CloseOnEscapeKey = true, FullWidth = true, MaxWidth = MaxWidth.Medium, NoHeader = true };
	private bool _isVisible;
	private Guid _id;

	private SubscribeFolderUseCase.Response? _subscriptionResult;
	private GetCameraUseCase.Response? _camera;

	protected override void OnInitialized()
	{
		base.OnInitialized();

		CompositeDisposable.Add(EventSystem.Subscribe<CameraDeleteEto>(OnDeleteHandler));
	}

	private async Task FetchAsync()
	{
		try
		{
			if (_camera is null) await SetLoadingAsync(true);
			_camera = await <PERSON><PERSON>Factory.MediatorSend(new GetCameraUseCase.Query(_id));
			await SetLoadingAsync(false);
			switch (_camera.Result)
			{
				case GetCameraUseCase.Result.Success:
					await SubscribeAsync();
					break;
				case GetCameraUseCase.Result.CameraNotFound:
					Snackbar.Add("Не удалось получить удаляемую камеру. Возможно камера уже удалена.", Severity.Warning);
					Cancel();
					break;
				case GetCameraUseCase.Result.ValidationError:
					Snackbar.Add("Не удалось получить удаляемую камеру из-за ошибки валидации.", Severity.Error);
					Cancel();
					break;
				case GetCameraUseCase.Result.Unknown:
				default:
					throw new Exception($"Unexpected result in {nameof(GetCameraUseCase)}: {_camera.Result}");
			}
		}
		catch (Exception ex)
		{
			Logger.LogError(ex, ex.Message);
			Snackbar.Add("Не удалось получить удаляемую камеру.");
		}
		finally
		{
			await SetLoadingAsync(false);
		}
	}
	private async Task SubscribeAsync()
	{
		try
		{
			Unsubscribe();

			await SetSubscribingAsync(true);
			_subscriptionResult = await ScopeFactory.MediatorSend(new SubscribeFolderUseCase.Request(Observer.Create<object>(OnAppEventHandler, OnError), _id));
			await SetSubscribingAsync(false);
			switch (_subscriptionResult.Result)
			{
				case SubscribeFolderUseCase.Result.Success:
					CompositeDisposable.Add(_subscriptionResult.Subscription!);
					StateHasChanged();
					break;
				case SubscribeFolderUseCase.Result.ValidationError:
					Snackbar.Add("Ошибка валидации при подписке на события", Severity.Error);
					break;
				case SubscribeFolderUseCase.Result.Unknown:
				default:
					throw new Exception($"Unexpected result in {nameof(SubscribeFolderUseCase)}: {_subscriptionResult.Result}");
			}
		}
		catch (Exception ex)
		{
			await SetSubscribingAsync(false);
			Snackbar.Add("Не удалось получить подписку на события камеры из-за непредвиденной ошибки. Повторите попытку", Severity.Error);
			Logger.LogError(ex, ex.Message);
		}
	}
	private void Unsubscribe()
	{
		if (_subscriptionResult?.Subscription is not null)
		{
			CompositeDisposable.Remove(_subscriptionResult.Subscription);
			_subscriptionResult.Subscription.Dispose();
		}
	}
	protected Task SetSubscribingAsync(bool isLoading = true) => UpdateViewAsync(() =>
	{
		_subscribing = isLoading;
	});

	#region [Actions]
	private void Cancel()
	{
		_isVisible = false;
		Unsubscribe();
	}
	private Task RefreshAsync() => FetchAsync();
	private async Task SubmitAsync()
	{
		try
		{
			if (_camera is null) return;
			Unsubscribe();
			var response = await ScopeFactory.MediatorSend(new DeleteCameraUseCase.Command(_id));
			switch (response.Result)
			{
				case DeleteCameraUseCase.Result.Success:
					Snackbar.Add("Камера успешно удалена", Severity.Success);
					Cancel();
					break;
				case DeleteCameraUseCase.Result.ValidationError:
					await SubscribeAsync();
					Snackbar.Add("Не удалось удалить камеру из-за ошибки валидации", Severity.Error);
					break;
				case DeleteCameraUseCase.Result.Unknown:
				default:
					await SubscribeAsync();
					throw new Exception($"Unexpected result in {nameof(DeleteCameraUseCase)}: {response.Result}");
			}
		}
		catch (Exception ex)
		{
			Logger.LogError(ex, ex.Message);
			Snackbar.Add("Не удалось удалить группу.");
		}
	}
	#endregion


	#region [Event Handlers]
	private async void OnDeleteHandler(CameraDeleteEto eto)
	{
		_id = eto.CameraId;
		_isVisible = true;
		await FetchAsync();
		await SubscribeAsync();
		StateHasChanged();
	}

	private async void OnAppEventHandler(object appEvent)
	{
		switch (appEvent)
		{
			case SubscribeFolderUseCase.UpdatedEvent updatedEto:
				await FetchAsync();
				await UpdateViewAsync();
				break;

			case SubscribeFolderUseCase.DeletedEvent deletedEto:
				Snackbar.Add("Камера была удалена", Severity.Warning);
				Cancel();
				break;

			default:
				Snackbar.Add("Было получено непредвиденное событие.", Severity.Warning);
				await FetchAsync();
				await UpdateViewAsync();
				Logger.LogWarning("Unexpected event in {UseCase}: {Event}", nameof(SubscribeFolderUseCase), nameof(appEvent));
				break;
		}
	}

	private void OnError(Exception exc)
	{
		Snackbar.Add("Ошибка при подписке на события", Severity.Error);
		Logger.LogError(exc, exc.Message);
	}
	#endregion [Event Handlers]
}