@inherits InteractiveBaseComponent
<FormSectionComponent Title="@Title"
					  Subtitle="@Subtitle">
	<SelectEnumComponent TEnum="Resolution"
						 Label="Разрешение"
						 For="@(() => PresetConfig.Resolution)"
						 SelectedValue="PresetConfig.Resolution"
						 SelectedValueChanged="SelectedResolutionChanged"
						 Required="true"
						 RequiredError="Данное поле обязательно" />

	<SelectEnumComponent TEnum="VideoCodec"
						 Label="Кодек видео"
						 For="@(() => PresetConfig.VideoCodec)"
						 SelectedValue="PresetConfig.VideoCodec"
						 SelectedValueChanged="SelectedCodecChanged"
						 Required="true"
						 RequiredError="Данное поле обязательно" />

	<SelectEnumComponent TEnum="AudioCodec"
						 Label="Кодек аудио"
						 For="@(() => PresetConfig.AudioCodec)"
						 SelectedValue="PresetConfig.AudioCodec"
						 SelectedValueChanged="SelectedAudioCodecChanged"
						 Required="true"
						 RequiredError="Данное поле обязательно" />

	<SelectEnumComponent TEnum="FrameRate"
						 Label="Частота кадров"
						 For="@(() => PresetConfig.FrameRate)"
						 SelectedValue="PresetConfig.FrameRate"
						 SelectedValueChanged="SelectedFrameRateChanged"
						 Required="true"
						 RequiredError="Данное поле обязательно" />

	<SelectEnumComponent TEnum="SceneDynamic"
						 Label="Ожидаемая динамика сцены"
						 For="@(() => PresetConfig.SceneDynamic)"
						 SelectedValue="PresetConfig.SceneDynamic"
						 SelectedValueChanged="SelectedSceneDynamicChanged"
						 HelperText="В зависимости от реальной динамики сцены может измениться битрейт видео"
						 Required="true"
						 RequiredError="Данное поле обязательно" />
	@if (_isCalculating)
	{
		<MudSkeleton Width="100%"
					 Height="73px;" />
	}
	else
	{
		<MudField Label="Расчётный битрейт, кбит/с"
				  Variant="Variant.Text"
				  HelperText="Данное значение расчётное и может отличаться">@_bitrate</MudField>
	}
</FormSectionComponent>

@code
{
	private bool _isCalculating;
	private double _bitrate;

	public class Config
	{
		public Resolution Resolution { get; set; }
		public VideoCodec VideoCodec { get; set; }
		public FrameRate FrameRate { get; set; }
		public SceneDynamic SceneDynamic { get; set; }
		public AudioCodec AudioCodec { get; set; }

		public Config()
		{
			Resolution = Resolution.R1920x1080;
			VideoCodec = VideoCodec.H264Baseline;
			FrameRate = FrameRate.Fps30;
			SceneDynamic = SceneDynamic.Medium;
			AudioCodec = AudioCodec.AAC;
		}

		public Config(Resolution resolution, VideoCodec videoCodec, FrameRate frameRate, SceneDynamic sceneDynamic, AudioCodec audioCodec)
		{
			Resolution = resolution;
			VideoCodec = videoCodec;
			FrameRate = frameRate;
			SceneDynamic = sceneDynamic;
			AudioCodec = audioCodec;
		}
	}

	[Parameter]
	public string Title { get; set; } = string.Empty;

	[Parameter]
	public string Subtitle { get; set; } = string.Empty;

	[Parameter]
	public Config PresetConfig { get; set; } = new();

	protected override async Task OnInitializedAsync()
	{
		await CalculateBitrateAsync();
		await base.OnInitializedAsync();
	}

	private async Task CalculateBitrateAsync()
	{
		GetCalculatedBitrateUseCase.Response? response = null;
		try
		{
			await InvokeAsync(() =>
			{
				_isCalculating = true;
				StateHasChanged();
			});
			response = await ScopeFactory.MediatorSend(new GetCalculatedBitrateUseCase.Query(PresetConfig.Resolution, PresetConfig.VideoCodec, PresetConfig.FrameRate,
			PresetConfig.SceneDynamic, PresetConfig.AudioCodec));
		}
		catch (Exception exc)
		{
			response = null;
			Logger.LogError(exc, exc.Message);
			Snackbar.Add("Не удалось расчитать битрейт из-за непредвиденной ошибки.", MudBlazor.Severity.Error);
		}
		await InvokeAsync(() =>
		{
			_isCalculating = false;
			StateHasChanged();
		});

		if (response is null) return;
		switch (response.Result)
		{
			case GetCalculatedBitrateUseCase.Result.Success:
				_bitrate = response.Bitrate;
				break;
			case GetCalculatedBitrateUseCase.Result.ValidationError:
				Snackbar.Add("Ошибка валидации, проверьте правильность заполнения полей", MudBlazor.Severity.Error);
				break;
			case GetCalculatedBitrateUseCase.Result.Unknown:
				Logger.LogError("Unexpected error in {Component}, {UseCase}.", nameof(PresetFormComponent), nameof(GetCalculatedBitrateUseCase));
				Snackbar.Add("Неизвестная ошибка при расчёте битрейта. Повторите попытку.", MudBlazor.Severity.Error);
				break;
			default:
				Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(PresetFormComponent), nameof(GetCalculatedBitrateUseCase),
				response.Result);
				Snackbar.Add("Неизвестная ошибка при расчёте битрейта. Повторите попытку.", MudBlazor.Severity.Error);
				break;
		}
	}

	#region [Event Handlers]
	private Task SelectedResolutionChanged(Resolution resolution)
	{
		PresetConfig.Resolution = resolution;
		return CalculateBitrateAsync();
	}

	private Task SelectedCodecChanged(VideoCodec codec)
	{
		PresetConfig.VideoCodec = codec;
		return CalculateBitrateAsync();
	}

	private Task SelectedFrameRateChanged(FrameRate frameRate)
	{
		PresetConfig.FrameRate = frameRate;
		return CalculateBitrateAsync();
	}

	public Task SelectedSceneDynamicChanged(SceneDynamic sceneDynamic)
	{
		PresetConfig.SceneDynamic = sceneDynamic;
		return CalculateBitrateAsync();
	}

	public Task SelectedAudioCodecChanged(AudioCodec audioCodec)
	{
		PresetConfig.AudioCodec = audioCodec;
		return CalculateBitrateAsync();
	}
	#endregion
}