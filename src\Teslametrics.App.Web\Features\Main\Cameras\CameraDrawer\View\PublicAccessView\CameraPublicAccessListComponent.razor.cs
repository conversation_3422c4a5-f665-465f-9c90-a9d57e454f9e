using System.Reactive;
using Microsoft.AspNetCore.Components;
using Teslametrics.App.Web.Extensions;

namespace Teslametrics.App.Web.Features.Main.Cameras.CameraDrawer.View.PublicAccessView;

public partial class CameraPublicAccessListComponent
{
    private Guid _cameraId;
    private DateTime _lastRefreshTime = DateTime.Now;
    private GetCameraPublicAccessListUseCase.Response? _response;

    private bool _subscribing;
    private CameraPublicAccessSubscribeUseCase.Response? _subscriptionResult;

    [Parameter]
    public Guid CameraId { get; set; }

    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();

        if (_cameraId != CameraId)
        {
            _cameraId = CameraId;

            await FetchAsync();
            await SubscribeAsync();
        }
    }

    protected async Task FetchAsync()
    {
        try
        {
            await SetLoadingAsync(true);
            _response = await ScopeFactory.MediatorSend(new GetCameraPublicAccessListUseCase.Query(CameraId));
            await SetLoadingAsync(false);
            switch (_response.Result)
            {
                case GetCameraPublicAccessListUseCase.Result.Success:
                    _lastRefreshTime = DateTime.Now;
                    break;
                case GetCameraPublicAccessListUseCase.Result.ValidationError:
                    Snackbar.Add("Ошибка валидации данных", MudBlazor.Severity.Error);
                    break;
                case GetCameraPublicAccessListUseCase.Result.AccessNotFound:
                case GetCameraPublicAccessListUseCase.Result.Unknown:
                default:
                    throw new Exception($"Unexpected result in {nameof(GetCameraUseCase)}: {_response.Result}");
            }
        }
        catch (Exception ex)
        {
            await SetLoadingAsync(false);
            Snackbar.Add("Не удалось получить список публичных доступов камеры из-за непредвиденной ошибки. Повторите попытку", MudBlazor.Severity.Error);
            Logger.LogError(ex, ex.Message);
        }
    }

    private async Task SubscribeAsync()
    {
        try
        {
            Unsubscribe();

            await SetSubscribingAsync(true);
            _subscriptionResult = await ScopeFactory.MediatorSend(new CameraPublicAccessSubscribeUseCase.Request(Observer.Create<object>(OnAppEventHandler, OnError), CameraId));
            await SetSubscribingAsync(false);
            switch (_subscriptionResult.Result)
            {
                case CameraPublicAccessSubscribeUseCase.Result.Success:
                    CompositeDisposable.Add(_subscriptionResult.Subscription!);
                    break;
                case CameraPublicAccessSubscribeUseCase.Result.ValidationError:
                    Snackbar.Add("Ошибка валидации при подписке на события", MudBlazor.Severity.Error);
                    break;
                case CameraPublicAccessSubscribeUseCase.Result.Unknown:
                default:
                    throw new Exception($"Unexpected result in {nameof(CameraPublicAccessSubscribeUseCase)}: {_subscriptionResult.Result}");
            }
        }
        catch (Exception ex)
        {
            await SetSubscribingAsync(false);
            Snackbar.Add("Не удалось получить подписку на события списка публичных доступов камеры из-за непредвиденной ошибки. Повторите попытку", MudBlazor.Severity.Error);
            Logger.LogError(ex, ex.Message);
        }
    }

    private void Unsubscribe()
    {
        if (_subscriptionResult?.Subscription is not null)
        {
            CompositeDisposable.Remove(_subscriptionResult.Subscription);
            _subscriptionResult.Subscription.Dispose();
        }
    }

    protected Task SetSubscribingAsync(bool isLoading = true) => UpdateViewAsync(() =>
    {
        _subscribing = isLoading;
    });

    #region [Actions]
    private Task RefreshAsync() => FetchAsync();
    #endregion

    #region [Event Handlers]
    private async void OnAppEventHandler(object appEvent)
    {
        await FetchAsync();
        await UpdateViewAsync();
    }

    private void OnError(Exception exc)
    {
        Snackbar.Add("Ошибка при подписке на события", MudBlazor.Severity.Error);
        Logger.LogError(exc, exc.Message);
    }
    #endregion [Event Handlers]
}
