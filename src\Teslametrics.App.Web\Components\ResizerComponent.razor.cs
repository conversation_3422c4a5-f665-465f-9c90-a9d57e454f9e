using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;

namespace Teslametrics.App.Web.Components;

public partial class ResizerComponent : IAsyncDisposable
{
	private Guid _conteinerId = Guid.NewGuid();
	private Guid _resizerId = Guid.NewGuid();
	private Guid _leftPanelId = Guid.NewGuid();
	private Guid _rightPanelId = Guid.NewGuid();

	private IJSObjectReference? _jsModule;

	#region [Injectables]
	[Inject]
	protected IJSRuntime JS { get; set; } = null!;
	#endregion

	[Parameter]
	public RenderFragment? LeftPanel { get; set; }

	[Parameter]
	public RenderFragment? RightPanel { get; set; }

	[Parameter]
	public string? Class { get; set; }

	/// <summary>
	/// По данному ключу будет сохраняться состояние в браузере пользователя
	/// </summary>
	[Parameter]
	public string? StorageId { get; set; }

	[Parameter]
	public string MinWidth { get; set; } = "420px";

	public async ValueTask DisposeAsync()
	{
		try
		{
			if (_jsModule is not null)
			{
				await _jsModule.InvokeVoidAsync("dispose");
				await _jsModule.DisposeAsync();
			}
		}
		catch (JSDisconnectedException) // https://learn.microsoft.com/en-us/aspnet/core/blazor/javascript-interoperability/?view=aspnetcore-9.0
		{
		}
	}

	protected override async Task OnAfterRenderAsync(bool firstRender)
	{
		if (firstRender)
		{
			_jsModule = await JS.InvokeAsync<IJSObjectReference>("import", "./Components/ResizerComponent.razor.js");
			await _jsModule.InvokeVoidAsync("initialize", _resizerId, StorageId);
		}
	}
}
