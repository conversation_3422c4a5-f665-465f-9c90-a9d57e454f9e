using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using System.Collections.Generic;
using MudBlazor;

namespace Teslametrics.App.Web.Components;

public enum ResizerOrientation
{
    Horizontal,
    Vertical
}

public partial class ResizerComponent : IAsyncDisposable
{
    private Guid _conteinerId = Guid.NewGuid();
    private Guid _resizerId = Guid.NewGuid();
    private Guid _leftPanelId = Guid.NewGuid();
    private Guid _rightPanelId = Guid.NewGuid();

    private IJSObjectReference? _jsModule;
    private bool _isReadOnlyPrevious;
    private ResizerOrientation _orientationPrevious;

    #region [Injectables]
    [Inject]
    protected IJSRuntime JS { get; set; } = null!;
    #endregion

    [Parameter]
    public RenderFragment? LeftPanel { get; set; }

    [Parameter]
    public RenderFragment? RightPanel { get; set; }

    [Parameter]
    public string? Class { get; set; }

    /// <summary>
    /// По данному ключу будет сохраняться состояние в браузере пользователя
    /// </summary>
    [Parameter]
    public string? StorageId { get; set; }

    [Parameter]
    public string MinWidth { get; set; } = "420px";

    /// <summary>
    /// Отключает функциональность изменения размера (режим только для чтения)
    /// </summary>
    [Parameter]
    public bool IsReadOnly { get; set; } = false;

    /// <summary>
    /// Ориентация разделителя: горизонтальная (изменение ширины) или вертикальная (изменение высоты)
    /// </summary>
    [Parameter]
    public ResizerOrientation Orientation { get; set; } = ResizerOrientation.Horizontal;

    public async ValueTask DisposeAsync()
    {
        try
        {
            if (_jsModule is not null)
            {
                await _jsModule.InvokeVoidAsync("dispose");
                await _jsModule.DisposeAsync();
            }
        }
        catch (JSDisconnectedException) // https://learn.microsoft.com/en-us/aspnet/core/blazor/javascript-interoperability/?view=aspnetcore-9.0
        {
        }
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            _jsModule = await JS.InvokeAsync<IJSObjectReference>("import", "./Components/ResizerComponent.razor.js");
            await InitializeJavaScript();
            _isReadOnlyPrevious = IsReadOnly;
            _orientationPrevious = Orientation;
        }
        else if (_jsModule != null && (IsReadOnly != _isReadOnlyPrevious || Orientation != _orientationPrevious))
        {
            // Параметры изменились, нужно переинициализировать JavaScript
            try
            {
                await _jsModule.InvokeVoidAsync("dispose");
                await InitializeJavaScript();
                _isReadOnlyPrevious = IsReadOnly;
                _orientationPrevious = Orientation;
            }
            catch (JSDisconnectedException)
            {
                // Игнорируем ошибки отключения
            }
            catch (Exception ex)
            {
                // Логируем другие ошибки, но не прерываем работу
                Console.WriteLine($"ResizerComponent: Ошибка при переинициализации JavaScript: {ex.Message}");
            }
        }
    }

    private async Task InitializeJavaScript()
    {
        if (_jsModule != null)
        {
            try
            {
                await _jsModule.InvokeVoidAsync("initialize", _resizerId, StorageId, IsReadOnly, Orientation.ToString().ToLower());
            }
            catch (JSDisconnectedException)
            {
                // Игнорируем ошибки отключения
            }
            catch (Exception ex)
            {
                // Логируем другие ошибки, но не прерываем работу
                Console.WriteLine($"ResizerComponent: Ошибка при инициализации JavaScript: {ex.Message}");
            }
        }
    }

    private string GetContainerClasses()
    {
        var classes = new List<string> { "resizer_container" };

        if (Orientation == ResizerOrientation.Vertical)
            classes.Add("vertical");

        if (IsReadOnly)
            classes.Add("readonly");

        if (!string.IsNullOrEmpty(Class))
            classes.Add(Class);

        return string.Join(" ", classes);
    }

    private string GetFirstPanelClasses()
    {
        return Orientation == ResizerOrientation.Horizontal ? "left_panel" : "top_panel";
    }

    private string GetSecondPanelClasses()
    {
        return Orientation == ResizerOrientation.Horizontal ? "right_panel" : "bottom_panel";
    }

    private string GetFirstPanelStyle()
    {
        return Orientation == ResizerOrientation.Horizontal ? $"min-width: {MinWidth};" : $"min-height: {MinWidth};";
    }

    private string GetDividerClasses()
    {
        var classes = new List<string> { "mud-divider", "divider" };

        if (Orientation == ResizerOrientation.Horizontal)
            classes.Add("mud-divider-vertical");
        else
            classes.Add("mud-divider-horizontal");

        if (IsReadOnly)
            classes.Add("readonly");

        return string.Join(" ", classes);
    }

    private string GetDividerIcon()
    {
        return Orientation == ResizerOrientation.Horizontal
            ? Icons.Material.Filled.Height
            : Icons.Material.Filled.SwapVert;
    }
}
