﻿@using Teslametrics.App.Web.Features.Main.AccessControl.Quota.Drawer.View
@using Teslametrics.App.Web.Features.Main.AccessControl.Quota.Drawer.Edit
@using Teslametrics.App.Web.Features.Main.AccessControl.Quota.Drawer.Create
@inherits InteractiveBaseComponent
<DrawerComponent Open="IsOpened"
				 OpenChanged="OnOpenChanged">
	@switch (_mode)
	{
		case DrawerMode.View:
			@if (_resourceId.HasValue)
			{
				<AuthorizeView Policy="@(Fqdn<AppPermissions>.GetNames(AppPermissions.Main.CameraQuotas.Read).Last())"
							   Resource="new PolicyRequirementResource(_organizationId, _resourceId.Value)">
					<Authorized>
						<QuotaViewComponent OrganizationId="_organizationId"
											QuotaId="_resourceId.Value" />
					</Authorized>
					<NotAuthorized>
						@{
							_ = InvokeAsync(async () => await CloseAsync());
						}
					</NotAuthorized>
				</AuthorizeView>
			}
			break;

		case DrawerMode.Edit:
			@if (_resourceId.HasValue)
			{
				<AuthorizeView Policy="@(Fqdn<AppPermissions>.GetNames(AppPermissions.Main.CameraQuotas.Update).Last())"
							   Resource="new PolicyRequirementResource(_organizationId, _resourceId.Value)"
							   Context="innerContext">
					<Authorized>
						<QuotaEditComponent OrganizationId="_organizationId"
											QuotaId="_resourceId.Value" />
					</Authorized>
					<NotAuthorized>
						@{
							_ = InvokeAsync(async () => await ShowViewAsync(_organizationId, _resourceId!.Value));
						}
					</NotAuthorized>
				</AuthorizeView>
			}
			break;

		case DrawerMode.Create:
			<AuthorizeView Policy="@(Fqdn<AppPermissions>.GetNames(AppPermissions.Main.CameraQuotas.Create).Last())"
						   Resource="new PolicyRequirementResource(_organizationId)"
						   Context="innerContext">
				<Authorized>
					<PresetAddComponent OrganizationId="_organizationId" />
				</Authorized>
				<NotAuthorized>
					@{
						_ = InvokeAsync(async () => await CloseAsync());
					}
				</NotAuthorized>
			</AuthorizeView>
			break;
	}
</DrawerComponent>