using System.Security.Cryptography;
using System.Text;

namespace Teslametrics.App.Web.Shared;

/// <summary>
/// Утилита для создания детерминированных GUID на основе входных данных
/// </summary>
public static class GuidUtility
{
    /// <summary>
    /// Создает детерминированный GUID на основе пространства имен и имени
    /// </summary>
    /// <param name="namespaceId">GUID пространства имен</param>
    /// <param name="name">Имя, для которого нужно создать GUID</param>
    /// <returns>Детерминированный GUID</returns>
    public static Guid Create(Guid namespaceId, string name)
    {
        // Конвертируем пространство имен в байты
        byte[] namespaceBytes = namespaceId.ToByteArray();

        // Конвертируем имя в байты UTF-8
        byte[] nameBytes = Encoding.UTF8.GetBytes(name);

        // Объединяем байты
        byte[] combinedBytes = new byte[namespaceBytes.Length + nameBytes.Length];
        Buffer.BlockCopy(namespaceBytes, 0, combinedBytes, 0, namespaceBytes.Length);
        Buffer.BlockCopy(nameBytes, 0, combinedBytes, namespaceBytes.Length, nameBytes.Length);

        // Вычисляем хеш
        byte[] hashBytes;
        using (var algorithm = SHA1.Create())
        {
            hashBytes = algorithm.ComputeHash(combinedBytes);
        }

        // Устанавливаем биты версии (версия 5 - SHA1)
        hashBytes[6] = (byte)(hashBytes[6] & 0x0F | 0x50);
        hashBytes[8] = (byte)(hashBytes[8] & 0x3F | 0x80);

        // Создаем GUID из хеша
        return new Guid(hashBytes.Take(16).ToArray());
    }
}
