﻿@inherits InteractiveBaseComponent
<MudAutocomplete T="GetBuildingListUseCase.Response.Item"
                 SearchFunc="@SearchCityAsync"
                 Margin="Margin.Dense"
                 ToStringFunc="@(e => e == null ? null : e.Name)"
                 Value="@_selected"
                 ValueChanged="@CityValueChanged"
                 Label="Здание"
                 Clearable="true"
                 ResetValueOnEmptyText="true"
                 Variant="Variant.Outlined"
                 Disabled="City is null" />

@code {
    private GetBuildingListUseCase.Response.Item? _selected;

    [Parameter]
    public Guid? City { get; set; }

    [Parameter]
    public Guid? BuildingId { get; set; }

    [Parameter]
    public EventCallback<Guid?> BuildingIdChanged { get; set; }

    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();
        if (_selected?.Id != BuildingId)
        {
            _selected = null;
        }
    }

    private async Task CityValueChanged(GetBuildingListUseCase.Response.Item? item)
    {
        _selected = item;
        await BuildingIdChanged.InvokeAsync(item?.Id);
    }

    private async Task<IEnumerable<GetBuildingListUseCase.Response.Item>> SearchCityAsync(string value, CancellationToken token)
    {
        if (City is null) return [];

        GetBuildingListUseCase.Response? response = null;
        try
        {
            response = await ScopeFactory.MediatorSend(new GetBuildingListUseCase.Query(City.Value, value), token);
        }
        catch (TaskCanceledException)
        {
            // Search was canceled, just return empty result
            return Enumerable.Empty<GetBuildingListUseCase.Response.Item>();
        }
        catch (Exception exc)
        {
            Logger.LogError(exc, "Error searching buildings");
            Snackbar.Add("Не удалось получить список адресов из-за непредвиденной ошибки.", MudBlazor.Severity.Error);
            return Enumerable.Empty<GetBuildingListUseCase.Response.Item>();
        }

        if (response.Result == GetBuildingListUseCase.Result.Success)
            return response.Items;

        if (response.Result == GetBuildingListUseCase.Result.ValidationError)
            Snackbar.Add("Ошибка валидации при получении списка адресов", MudBlazor.Severity.Error);
        else
            Snackbar.Add("Не удалось получить список адресов из-за непредвиденной ошибки.", MudBlazor.Severity.Error);

        return Enumerable.Empty<GetBuildingListUseCase.Response.Item>();
    }
}