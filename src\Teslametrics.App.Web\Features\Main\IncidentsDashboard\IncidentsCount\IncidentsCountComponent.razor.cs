using System.Globalization;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using Teslametrics.App.Web.Extensions;

namespace Teslametrics.App.Web.Features.Main.IncidentsDashboard.IncidentsCount;

public partial class IncidentsCountComponent
{
    public class SeriesData
    {
        public string Name { get; set; } = string.Empty;
        public int[] Data { get; set; } = Array.Empty<int>();
    }
    public class IncidentsChartData
    {
        public string[] Categories { get; set; } = Array.Empty<string>();
        public SeriesData[] Series { get; set; } = Array.Empty<SeriesData>();
    }

    private IJSObjectReference? _jsModule;

    private GetIncidentsCountUseCase.Response? _response = null;

    [Inject]
    private IJSRuntime JSRuntime { get; set; } = null!;
    [Parameter]
    public DateTime DateFrom { get; set; } = DateTime.Today;

    [Parameter]
    public DateTime DateTo { get; set; } = DateTime.Today.AddDays(1).AddMilliseconds(-1);
    [Parameter]
    public Guid? CityId { get; set; }
    [Parameter]
    public Guid? BuildingId { get; set; }
    [Parameter]
    public Guid? FloorId { get; set; }
    [Parameter]
    public Guid? RoomId { get; set; }
    [Parameter]
    public Guid? FridgeId { get; set; }

    protected override async Task OnParametersSetAsync()
    {
        await LoadDataAsync();
        await BuildChartsAsync();
        await base.OnParametersSetAsync();
    }

    protected override async Task OnInitializedAsync()
    {
        await LoadDataAsync();
        await base.OnInitializedAsync();
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        await base.OnAfterRenderAsync(firstRender);

        if (firstRender)
        {
            try
            {
                _jsModule = await JSRuntime.InvokeAsync<IJSObjectReference>("import", "./Features/Main/IncidentsDashboard/IncidentsCount/IncidentsCountComponent.razor.js");
                await BuildChartsAsync();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, ex.Message);
                Snackbar.Add("Не удалось загрузить скрипт для построения графика", MudBlazor.Severity.Error);
            }
        }
    }

    private async Task BuildChartsAsync()
    {
        if (_jsModule == null) return;

        // Fetch data from the server
        try
        {
            var incidentsData = ToChartSequentialRange(_response, DateFrom, DateTo, new CultureInfo("ru-RU"));
            // Initialize charts with data
            await _jsModule.InvokeVoidAsync("initIncidentsChart", incidentsData);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, ex.Message);
            Snackbar.Add("Не удалось построить график на полученных данных", MudBlazor.Severity.Error);
        }
    }

    // Mock data methods - these would be replaced with actual API calls
    private async Task LoadDataAsync()
    {
        await SetLoadingAsync(true);
        try
        {
            _response = await ScopeFactory.MediatorSend(new GetIncidentsCountUseCase.Query(new DateTimeOffset(DateFrom.ToUniversalTime()), new DateTimeOffset(DateTo.ToUniversalTime()), CityId, BuildingId, FloorId, RoomId, FridgeId));
        }
        catch (Exception ex)
        {
            _response = null;
            Snackbar.Add("Ошибка при получении количества происшествий во время запроса к серверу. Обратитесь к администратору.", MudBlazor.Severity.Error);
            Logger.LogError(ex, ex.Message);
        }

        await SetLoadingAsync(false);
        if (_response is null) return;

        switch (_response.Result)
        {
            case GetIncidentsCountUseCase.Result.Success:
                break;
            case GetIncidentsCountUseCase.Result.ValidationError:
                Snackbar.Add("Ошибка валидации при получении количества происшествий", MudBlazor.Severity.Error);
                break;
            case GetIncidentsCountUseCase.Result.Unknown:
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(IncidentsCountComponent), nameof(GetIncidentsCountUseCase));
                Snackbar.Add($"Не удалось получить количество происшествий из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
                break;
            default:
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(IncidentsCountComponent), nameof(GetIncidentsCountUseCase), _response.Result);
                Snackbar.Add($"Не удалось получить количество происшествий из-за ошибки: {_response.Result}", MudBlazor.Severity.Error);
                break;
        }
    }

    /// <summary>
    /// Формирует модель графика за период [start; end] включительно.
    /// Если данных нет, возвращает "пустой" график с нулями.
    /// </summary>
    /// <param name="response">Ответ с инцидентами (может быть null).</param>
    /// <param name="start">Начало периода (дата без времени).</param>
    /// <param name="end">Конец периода (дата без времени, ≥ start).</param>
    /// <param name="culture">Локаль (null → CultureInfo.CurrentCulture).</param>
    public static IncidentsChartData ToChartSequentialRange(GetIncidentsCountUseCase.Response? response, DateTime start, DateTime end, CultureInfo? culture = null)
    {
        if (end < start)
            throw new ArgumentException("end must be >= start", nameof(end));

        culture ??= CultureInfo.CurrentCulture;

        // ----- 1. Готовим словарь <дата, сумма Count> -----
        var byDate = new Dictionary<DateTime, int>();

        if (response is { IsSuccess: true } &&
            response.Incidents is { Count: > 0 })
        {
            byDate = response.Incidents
                .GroupBy(i => i.Date.Date)
                .ToDictionary(g => g.Key, g => g.Sum(x => x.Count));
        }

        // ----- 2. Заполняем диапазон -----
        var spanDays = (end.Date - start.Date).Days + 1;

        var categories = new string[spanDays];
        var data = new int[spanDays];

        for (int i = 0; i < spanDays; i++)
        {
            var date = start.Date.AddDays(i);

            categories[i] = culture.DateTimeFormat
                                   .AbbreviatedDayNames[(int)date.DayOfWeek];

            data[i] = byDate.TryGetValue(date, out var v) ? v : 0;
        }

        return new IncidentsChartData
        {
            Categories = categories,
            Series = [
                new SeriesData
                {
                    Name = "Incidents",
                    Data = data
                }
            ]
        };
    }
}