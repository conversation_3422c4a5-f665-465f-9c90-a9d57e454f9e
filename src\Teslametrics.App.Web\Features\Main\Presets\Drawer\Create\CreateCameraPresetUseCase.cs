using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.Core.Domain.CameraPresets;
using Teslametrics.Core.Services.Outbox;
using Teslametrics.Core.Services.TransactionManager;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Features.Main.Presets.Drawer.Create;

public static class CreateCameraPresetUseCase
{
    public record Command(string Name, Command.StreamConfig ArchiveStreamConfig, Command.StreamConfig ViewStreamConfig, Command.StreamConfig PublicStreamConfig) : BaseRequest<Response>
    {
        public record StreamConfig(Resolution Resolution, VideoCodec VideoCodec, FrameRate FrameRate, SceneDynamic SceneDynamic, AudioCodec AudioCodec);
    }

    public record Response : BaseResponse
    {
        public Guid Id { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(Guid id)
        {
            Id = id;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Id = Guid.Empty;
            Result = result;
        }
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        CameraPresetNameAlreadyExists
    }

    public class Validator : AbstractValidator<Command>
    {
        public Validator()
        {
            RuleFor(c => c.Name).Length(3, 60);
        }
    }

    public class Handler : IRequestHandler<Command, Response>
    {
        private readonly IValidator<Command> _validator;
        private readonly IPresetRepository _presetRepository;
        private readonly ITransactionManager _transactionManager;
        private readonly IPublisher _publisher;
        private readonly IOutbox _outbox;

        public Handler(IValidator<Command> validator,
                       IPresetRepository presetRepository,
                       ITransactionManager transactionManager,
                       IPublisher publisher,
                       IOutbox outbox)
        {
            _validator = validator;
            _presetRepository = presetRepository;
            _transactionManager = transactionManager;
            _publisher = publisher;
            _outbox = outbox;
        }

        public async Task<Response> Handle(Command request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            using var transaction = await _transactionManager.CreateTransactionAsync();

            if (await _presetRepository.IsCameraPresetNameExistsAsync(request.Name, cancellationToken))
            {
                return new Response(Result.CameraPresetNameAlreadyExists);
            }

            var (preset, events) = PresetAggregate.Create(Guid.NewGuid(),
                                                          request.Name,
                                                          ToStreamConfigValueObject(request.ArchiveStreamConfig),
                                                          ToStreamConfigValueObject(request.ViewStreamConfig),
                                                          ToStreamConfigValueObject(request.PublicStreamConfig));

            await _presetRepository.AddAsync(preset, cancellationToken);
            await _presetRepository.SaveChangesAsync(cancellationToken);

            foreach (var @event in events)
            {
                await _publisher.Publish(@event, cancellationToken);
            }

            await _outbox.AddRangeAsync(events);

            await transaction.CommitAsync();

            return new Response(preset.Id);
        }

        private static StreamConfigValueObject ToStreamConfigValueObject(Command.StreamConfig streamConfig)
        {
            return new StreamConfigValueObject(
                streamConfig.Resolution,
                streamConfig.VideoCodec,
                streamConfig.FrameRate,
                streamConfig.SceneDynamic,
                streamConfig.AudioCodec);
        }
    }
}