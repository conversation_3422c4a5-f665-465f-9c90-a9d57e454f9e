﻿::deep .drawer.mud-drawer-persistent
{
	top: var(--mud-appbar-height);
}

::deep .drawer_header
{
	min-height: fit-content;
	grid-area: drawer_header_content;
	display: grid;
	grid-template-columns: auto 1fr auto;
	align-items: center;
	gap: 8px;
}

::deep .h-100
{
	height: 100%;
}

.content
{
	padding-bottom: 16px;
}

::deep .header_content
{
	width: 100%;
}

::deep .flex-row-reverse .close_button
{
	transform: rotate(180deg);
}

::deep .flex-row-reverse .header_content
{
	padding-left: 16px;
}

::deep .drawer .dragger
{
	width: 4px;
	background: var(--mud-palette-primary);
	cursor: col-resize;
	transition: 0.1s;
}

::deep .drawer .dragger:hover
{
	background: var(--mud-palette-secondary);
}

::deep .drawer_content
{
	overflow-y: auto;
	overflow-x: hidden;
	display: grid;
	grid-template:
		"drawer_header_content" auto
		"drawer_body_content" minmax(auto, 1fr) "drawer_actions_content" auto;
	height: max-content;
	max-height: 100%;
	min-height: 100px;
}

::deep .drawer .dragger
{
	width: 4px;
	background: var(--mud-palette-primary);
	cursor: col-resize;
	transition: 0.1s;
}

::deep .drawer .dragger:hover
{
	background: var(--mud-palette-secondary);
}

::deep .drawer
{
	--columns: 6;
}

/* Small to large phone */
@media only screen and (max-width: 559px)
{
	::deep .drawer[data-sm="1"]
	{
		--columns: 1;
	}

	::deep .drawer[data-xs="2"]
	{
		--columns: 2;
	}

	::deep .drawer[data-xs="3"]
	{
		--columns: 3;
	}

	::deep .drawer[data-xs="4"]
	{
		--columns: 4;
	}

	::deep .drawer[data-xs="5"]
	{
		--columns: 5;
	}

	::deep .drawer[data-xs="6"]
	{
		--columns: 6;
	}

	::deep .drawer[data-xs="7"]
	{
		--columns: 7;
	}

	::deep .drawer[data-xs="8"]
	{
		--columns: 8;
	}

	::deep .drawer[data-xs="9"]
	{
		--columns: 9;
	}

	::deep .drawer[data-xs="10"]
	{
		--columns: 10;
	}

	::deep .drawer[data-xs="11"]
	{
		--columns: 11;
	}

	::deep .drawer[data-xs="12"]
	{
		--columns: 12;
	}
}

/* Small to medium tablet */
@media only screen and (min-width: 600px) and (max-width: 959px)
{
	::deep .drawer[data-sm="1"]
	{
		--columns: 1;
	}

	::deep .drawer[data-sm="2"]
	{
		--columns: 2;
	}

	::deep .drawer[data-sm="3"]
	{
		--columns: 3;
	}

	::deep .drawer[data-sm="4"]
	{
		--columns: 4;
	}

	::deep .drawer[data-sm="5"]
	{
		--columns: 5;
	}

	::deep .drawer[data-sm="6"]
	{
		--columns: 6;
	}

	::deep .drawer[data-sm="7"]
	{
		--columns: 7;
	}

	::deep .drawer[data-sm="8"]
	{
		--columns: 8;
	}

	::deep .drawer[data-sm="9"]
	{
		--columns: 9;
	}

	::deep .drawer[data-sm="10"]
	{
		--columns: 10;
	}

	::deep .drawer[data-sm="11"]
	{
		--columns: 11;
	}

	::deep .drawer[data-sm="12"]
	{
		--columns: 12;
	}
}

/* Large tablet to laptop */
@media only screen and (min-width: 960px) and (max-width: 1279px)
{
	::deep .drawer[data-md="1"]
	{
		--columns: 1;
	}

	::deep .drawer[data-md="2"]
	{
		--columns: 2;
	}

	::deep .drawer[data-md="3"]
	{
		--columns: 3;
	}

	::deep .drawer[data-md="4"]
	{
		--columns: 4;
	}

	::deep .drawer[data-md="5"]
	{
		--columns: 5;
	}

	::deep .drawer[data-md="6"]
	{
		--columns: 6;
	}

	::deep .drawer[data-md="7"]
	{
		--columns: 7;
	}

	::deep .drawer[data-md="8"]
	{
		--columns: 8;
	}

	::deep .drawer[data-md="9"]
	{
		--columns: 9;
	}

	::deep .drawer[data-md="10"]
	{
		--columns: 10;
	}

	::deep .drawer[data-md="11"]
	{
		--columns: 11;
	}

	::deep .drawer[data-md="12"]
	{
		--columns: 12;
	}
}

/* Desktop */
@media only screen and (min-width: 1280px) and (max-width: 1919px)
{
	::deep .drawer[data-lg="1"]
	{
		--columns: 1;
	}

	::deep .drawer[data-lg="2"]
	{
		--columns: 2;
	}

	::deep .drawer[data-lg="3"]
	{
		--columns: 3;
	}

	::deep .drawer[data-lg="4"]
	{
		--columns: 4;
	}

	::deep .drawer[data-lg="5"]
	{
		--columns: 5;
	}

	::deep .drawer[data-lg="6"]
	{
		--columns: 6;
	}

	::deep .drawer[data-lg="7"]
	{
		--columns: 7;
	}

	::deep .drawer[data-lg="8"]
	{
		--columns: 8;
	}

	::deep .drawer[data-lg="9"]
	{
		--columns: 9;
	}

	::deep .drawer[data-lg="10"]
	{
		--columns: 10;
	}

	::deep .drawer[data-lg="11"]
	{
		--columns: 11;
	}

	::deep .drawer[data-lg="12"]
	{
		--columns: 12;
	}
}

/* HD and 4k  */
@media only screen and (min-width: 1920px) and (max-width: 2559px)
{
	::deep .drawer[data-xl="1"]
	{
		--columns: 1;
	}

	::deep .drawer[data-xl="2"]
	{
		--columns: 2;
	}

	::deep .drawer[data-xl="3"]
	{
		--columns: 3;
	}

	::deep .drawer[data-xl="4"]
	{
		--columns: 4;
	}

	::deep .drawer[data-xl="5"]
	{
		--columns: 5;
	}

	::deep .drawer[data-xl="6"]
	{
		--columns: 6;
	}

	::deep .drawer[data-xl="7"]
	{
		--columns: 7;
	}

	::deep .drawer[data-xl="8"]
	{
		--columns: 8;
	}

	::deep .drawer[data-xl="9"]
	{
		--columns: 9;
	}

	::deep .drawer[data-xl="10"]
	{
		--columns: 10;
	}

	::deep .drawer[data-xl="11"]
	{
		--columns: 11;
	}

	::deep .drawer[data-xl="12"]
	{
		--columns: 12;
	}
}

/* 4k+ and ultra-wide */
@media only screen and (min-width: 2560px)
{
	::deep .drawer[data-xxl="1"]
	{
		--columns: 1;
	}

	::deep .drawer[data-xxl="2"]
	{
		--columns: 2;
	}

	::deep .drawer[data-xxl="3"]
	{
		--columns: 3;
	}

	::deep .drawer[data-xxl="4"]
	{
		--columns: 4;
	}

	::deep .drawer[data-xxl="5"]
	{
		--columns: 5;
	}

	::deep .drawer[data-xxl="6"]
	{
		--columns: 6;
	}

	::deep .drawer[data-xxl="7"]
	{
		--columns: 7;
	}

	::deep .drawer[data-xxl="8"]
	{
		--columns: 8;
	}

	::deep .drawer[data-xxl="9"]
	{
		--columns: 9;
	}

	::deep .drawer[data-xxl="10"]
	{
		--columns: 10;
	}

	::deep .drawer[data-xxl="11"]
	{
		--columns: 11;
	}

	::deep .drawer[data-xxl="12"]
	{
		--columns: 12;
	}
}

::deep .drawer
{
	--actions-drawer-width: calc(var(--columns) / 12 * 100%);
}