@using Teslametrics.App.Web.Extensions
@using Teslametrics.Shared
@inherits InteractiveBaseComponent
<DrawerHeader>
    <MudStack Spacing="0">
        <MudText Typo="Typo.h3">Просмотр пресета</MudText>
        @if (IsLoading)
        {
            <MudSkeleton Width="60%"
                         Height="calc(var(--mud-typography-subtitle1-lineheight) * var(--mud-typography-subtitle1-size))" />
        }
        @if (!IsLoading && _model is not null && _model.IsSuccess)
        {
            <MudText Typo="Typo.subtitle1">@_model.Name</MudText>
        }
        @if (!IsLoading && (_model is null || !_model.IsSuccess))
        {
            <MudText Typo="Typo.subtitle1">Не удалось получить пресет</MudText>
        }
    </MudStack>
    <MudSpacer />
    @if (!_subscribing && (_subscriptionResult is null || !_subscriptionResult.IsSuccess))
    {
        <MudTooltip Arrow="true"
                    Placement="Placement.Start"
                    Text="Ошибка подписки на события">
            <MudIconButton OnClick="SubscribeAsync"
                           Icon="@Icons.Material.Filled.ErrorOutline"
                           Color="Color.Error" />
        </MudTooltip>
    }
    <MudTooltip Text="@($"Время последнего обновления: {_lastUpdateTime.ToLocalTime()}")"
                Arrow="true"
                Placement="Placement.Start">
        <MudIconButton OnClick="RefreshAsync"
                       Icon="@Icons.Material.Filled.Refresh"
                       Color="Color.Primary" />
    </MudTooltip>
    @if (!IsLoading && _model is not null && _model.IsSuccess)
    {
        <AuthorizeView Policy="@_contextMenuAuthPolicyString"
                       Resource="new PolicyRequirementResource(null, PresetId)"
                       Context="menuContext">
            <MudMenu Icon="@Icons.Material.Filled.MoreVert"
                     Color="Color.Primary"
                     Variant="Variant.Outlined">
                <AuthorizeView Policy="@AppPermissions.Main.CameraPresets.Update.GetEnumPermissionString()"
                               Resource="new PolicyRequirementResource(null, PresetId)"
                               Context="editContext">
                    <MudMenuItem OnClick="Edit"
                                 Icon="@Icons.Material.Outlined.Edit">Редактировать</MudMenuItem>
                </AuthorizeView>
                <AuthorizeView Policy="@AppPermissions.Main.CameraPresets.Delete.GetEnumPermissionString()"
                               Resource="new PolicyRequirementResource(null, PresetId)"
                               Context="deleteContext">
                    <MudDivider Class="my-4" />
                    <MudMenuItem OnClick="Delete"
                                 Icon="@Icons.Material.Outlined.Delete"
                                 IconColor="Color.Warning">Удалить</MudMenuItem>
                </AuthorizeView>
            </MudMenu>
        </AuthorizeView>
    }
</DrawerHeader>
@if (IsLoading)
{
    <MudProgressLinear Indeterminate="true" />
}
else
{
    <div style="height: 4px;"></div>
}
<div class="px-4 py-4">
    <MudStack Spacing="8">
        @if (_model is not null && _model.IsSuccess)
        {
            <FormSectionComponent Title="Описание пресета"
                                  Subtitle="Настройки, которые влияют только на восприятие человеком">
                <MudTextField Value="_model.Name"
                              Label="Наименование"
                              InputType="InputType.Text"
                              ReadOnly="true" />
            </FormSectionComponent>
            <PresetFormComponent Title="Настройки потока архива"
                                 Subtitle="Настройки, которые влияют на параметры хранения записей"
                                 PresetConfig="_model.ArchiveStreamConfig" />
            <PresetFormComponent Title="Настройки потока видов"
                                 Subtitle="Настройки, которые влияют на восприятие человеком"
                                 PresetConfig="_model.ViewStreamConfig" />
            <PresetFormComponent Title="Настройки потока публичного доступа"
                                 Subtitle="Настройки, которые влияют на восприятие человеком"
                                 PresetConfig="_model.PublicStreamConfig" />
        }
        <FormLoadingComponent IsLoading="IsLoading && (_model is null || !_model.IsSuccess)" />
        <NoItemsFoundComponent HasItems="_model is not null && _model.IsSuccess"
                               LastRefreshTime="_lastUpdateTime"
                               RefreshAsync="RefreshAsync" />
    </MudStack>
    <DrawerActions>
        <MudSpacer />
        <MudButton OnClick="CancelAsync"
                   Variant="Variant.Outlined"
                   StartIcon="@Icons.Material.Outlined.Close">Закрыть</MudButton>
        @if (IsLoading)
        {
            <MudSkeleton Width="150px"
                         Height="36.5px" />
        }
        else
        {
            <AuthorizeView Policy="@AppPermissions.Main.CameraPresets.Update.GetEnumPermissionString()"
                           Resource="new PolicyRequirementResource(null, PresetId)"
                           Context="editContext">
                <MudButton OnClick="Edit"
                           Color="Color.Secondary"
                           Variant="Variant.Outlined">Редактировать</MudButton>
            </AuthorizeView>
        }
    </DrawerActions>
</div>