using Orleans.Streams;

namespace Teslametrics.App.Web.Shared;

public class DisposableSubscription<T> : IAsyncDisposable
{
    private readonly StreamSubscriptionHandle<T> _subscription;
    public DisposableSubscription(StreamSubscriptionHandle<T> subscription)
    {
        _subscription = subscription;
    }

    public async ValueTask DisposeAsync()
    {
        await _subscription.UnsubscribeAsync();
    }
}