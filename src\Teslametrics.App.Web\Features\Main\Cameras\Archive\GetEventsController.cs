using System.Data;
using Dapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Teslametrics.App.Web.Extensions;
using Teslametrics.App.Web.Services.Authorization;
using Teslametrics.Core.Services.Persistence;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Features.Main.Cameras.Archive;

[ApiController]
[Route("events")]
public class GetEventsController : ControllerBase
{
    public record Event(DateTimeOffset StartTime, DateTimeOffset? EndTime) // Наименование события, дата начала и дата окончания
    {
        public string Name { get; } = "Motion";
    }

    private readonly ILogger<GetEventsController> _logger;
    private readonly IDbConnection _dbConnection;
    private readonly IAuthorizationService _authorizationService;

    public GetEventsController(IAuthorizationService authorizationService,
                          ILogger<GetEventsController> logger,
                          IDbConnection dbConnection)
    {
        _authorizationService = authorizationService;
        _logger = logger;
        _dbConnection = dbConnection;
    }

    // TODO: Передаю ID камеры, время начала таймлайна и время конца. Нужно получить: список событий в указанном временном промежутке.
    [Route("{cameraId}")]
    [HttpGet]
    [Authorize]
    public async Task<IActionResult> GetEventsAsync(Guid cameraId, [FromQuery] DateTimeOffset start, [FromQuery] DateTimeOffset end) //  В теории запрос могут отменить. CancellationToken?
    {
        IEnumerable<Event> events = [];

        try
        {
            var organizationId = GetOrganizationId(cameraId);

            var authResult = await _authorizationService.AuthorizeAsync(HttpContext.User, new PolicyRequirementResource(organizationId, cameraId), AppPermissions.Main.Cameras.Read.GetEnumPermissionString());
            if (!authResult.Succeeded)
            {
                return Unauthorized();
            }

            var table = $"{Db.MotionEvents.Table}_{cameraId.ToString("N")}";

            var template = SqlQueryBuilder.Create()
                .Select(Db.MotionEvents.Columns.StartTime)
                .Select(Db.MotionEvents.Columns.EndTime)
                .Where(Db.MotionEvents.Columns.StartTime, ":End", SqlOperator.LessThan, new { end })
                .Where(Db.MotionEvents.Columns.EndTime, ":Start", SqlOperator.GreaterThan, new { start })
                .OrderBy(Db.MotionEvents.Columns.StartTime, OrderDirection.Ascending)
                .Build(QueryType.Standard, table, RowSelection.AllRows);

            events = _dbConnection.Query<Event>(template.RawSql, template.Parameters);

            return Ok(events);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, ex.Message);
            return Ok(events);
        }
    }

    private Guid GetOrganizationId(Guid cameraId)
    {
        var template = SqlQueryBuilder.Create()
            .Select(Db.Cameras.Props.OrganizationId)
            .Where(Db.Cameras.Columns.Id, ":CameraId", SqlOperator.Equals, new { cameraId })
            .Build(QueryType.Standard, Db.Cameras.Table, RowSelection.AllRows);

        var organizationId = _dbConnection.QuerySingle<Guid>(template.RawSql, template.Parameters);
        return organizationId;
    }
}