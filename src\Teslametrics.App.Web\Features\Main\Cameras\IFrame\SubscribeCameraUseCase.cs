using Dapper;
using FluentValidation;
using MediatR;
using System.Data;
using System.Reactive.Linq;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.Core.Domain.Cameras.Events;
using Teslametrics.App.Web.Exceptions;
using Teslametrics.Core.Services.DomainEventBus;
using Teslametrics.Core.Services.Persistence;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Features.Main.Cameras.IFrame;

public static class SubscribeCameraUseCase
{
    public record Request(IObserver<object> Observer, Guid PublicAccessId) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public IDisposable? Subscription { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(IDisposable subscription)
        {
            Subscription = subscription;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Subscription = null;
            Result = result;
        }
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        PublicAccessNotFound
    }

    public record DeletedEvent(Guid Id);

    public class Validator : AbstractValidator<Request>
    {
        public Validator()
        {
            RuleFor(r => r.Observer).NotEmpty();
            RuleFor(r => r.PublicAccessId).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Request, Response>
    {
        private readonly IValidator<Request> _validator;
        private readonly IDomainEventBus _domainEventBus;
        private readonly IDbConnection _dbConnection;

        public Handler(IValidator<Request> validator,
                       IDomainEventBus domainEventBus,
                       IDbConnection dbConnection)
        {
            _validator = validator;
            _domainEventBus = domainEventBus;
            _dbConnection = dbConnection;
        }

        public async Task<Response> Handle(Request request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            var template = SqlQueryBuilder.Create()
                .Select(Db.PublicLinks.Props.CameraId, "CameraId")
                .Where(Db.PublicLinks.Props.Id, ":Id", SqlOperator.Equals, new { Id = request.PublicAccessId })
                .Build(QueryType.Standard, Db.PublicLinks.Table, RowSelection.AllRows);

            var cameraId = await _dbConnection.QuerySingleAsync<Guid>(template.RawSql, template.Parameters);
            if (cameraId == Guid.Empty)
            {
                return new Response(Result.PublicAccessNotFound);
            }

            var eventStream = await _domainEventBus.GetEventStreamAsync();

            var subscription = eventStream
                .Where(e => e switch
                {
                    CameraDeletedEvent @event => @event.Id == cameraId,
                    _ => false
                })
                .Select<object, object>(e => e switch
                {
                    CameraDeletedEvent @event => new DeletedEvent(@event.Id),
                    _ => throw new AppException("Invalid event type")
                })
                .Subscribe(request.Observer);

            return new Response(subscription);
        }
    }
}