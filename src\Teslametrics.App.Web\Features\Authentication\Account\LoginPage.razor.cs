using FluentValidation;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using MudBlazor;
using System.ComponentModel.DataAnnotations;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Components.Form;
using Teslametrics.App.Web.Events.Users;
using Teslametrics.App.Web.Extensions;
using Teslametrics.App.Web.Services.Authentication;
using Teslametrics.App.Web.Services.Cookies;

namespace Teslametrics.App.Web.Features.Authentication.Account;

public partial class LoginPage
{
	private enum AuthStep
	{
		Login,
		TwoFa,
		TwoFaSetup,
		ForcedPasswordChange
	}

	public class SignInModel
	{
		[Required]
		[MinLength(3)]
		public string UserName { get; set; } = string.Empty;

		[Required]
		[MinLength(3)]
		[DataType(DataType.Password)]
		public string Password { get; set; } = string.Empty;
	}

	private class SignInValidator : BaseFluentValidator<SignInModel>
	{
		public SignInValidator()
		{
			RuleFor(model => model.UserName)
				.NotEmpty().WithMessage("Поле должно быть заполнено")
				.Length(3, 60).WithMessage("Логин должен быть длиной от 3 до 60 символов");

			RuleFor(model => model.Password)
				.NotEmpty().WithMessage("Поле должно быть заполнено")
				.Length(3, 60).WithMessage("Пароль должен быть длиной от 3 до 60 символов");
		}
	}

	private UserLoginUseCase.Response? _authResponse = null;

	private AuthStep _authStep = AuthStep.Login;

	private bool _signInIsValid;
	private SignInValidator _signInValidator = new();
	private MudTextField<string>? _loginFormLoginRef;
	private PasswordFieldComponent? _loginFormPasswordRef;

	#region Injectables

	[Inject]
	protected CookieStorageAccessor _cookieStorageAccessor { get; set; } = null!;

	[Inject]
	protected IHostEnvironmentAuthenticationStateProvider _authStateProvider { get; set; } = null!;

	[Inject]
	protected IHttpContextAccessor ContextAccessor { get; set; } = null!;

	[Inject]
	protected AuthenticationStateProvider AuthStateProvider { get; set; } = null!;

	[Inject]
	protected NavigationManager NavigationManager { get; set; } = null!;
	#endregion

	#region [Forms]
	public SignInModel Model { get; set; } = new();
	#endregion [Forms]

	[SupplyParameterFromQuery(Name = "returnUrl")]
	public string? ReturnUrl { get; set; }

	protected override async Task OnInitializedAsync()
	{
		if (HttpMethods.IsGet(ContextAccessor.HttpContext!.Request.Method))
		{
			var state = await AuthStateProvider.GetAuthenticationStateAsync();
			if ((state.User.Identity?.IsAuthenticated ?? false) || state.User.Claims.Any())
			{
				await ScopeFactory.MediatorSend(new UserLogOutUseCase.Command());
				ContextAccessor.HttpContext!.Response.Redirect(RouteConstants.LogInPage);
			}
		}
		CompositeDisposable.Add(EventSystem.Subscribe<Login2FaSetupRequiredEto>(OnTwoFaSetupRequiredHandler));
		CompositeDisposable.Add(EventSystem.Subscribe<LoginForceChangePasswordEto>(OnUserForcePasswordChange));
		CompositeDisposable.Add(EventSystem.Subscribe<Login2FaRequiredEto>(OnTwoFaRequiredHandler));
		CompositeDisposable.Add(EventSystem.Subscribe<ShowLoginFormEto>(OnLoginFormShowHandler));
	}

	private async Task Submit()
	{
		try
		{
			_authResponse = await ScopeFactory.MediatorSend(new UserLoginUseCase.Command(Model.UserName, Model.Password));
		}
		catch (Exception exc)
		{
			_authResponse = null;
			Logger.LogError(exc, exc.Message);
			Snackbar.Add("Не удалось выполнить вход, попробуйте ещё раз.", MudBlazor.Severity.Error);
		}

		if (_authResponse is null) return;

		switch (_authResponse.Result)
		{
			case UserLoginUseCase.Result.Success:
				await _cookieStorageAccessor.SetValueAsync(AuthenticationStorageNames.SessionId, _authResponse.SessionId!); ;
				_authStateProvider.SetAuthenticationState(Task.FromResult(new AuthenticationState(_authResponse.ClaimsPrincipal!)));
				NavigationManager.NavigateTo(ReturnUrl ?? "/");
				break;

			case UserLoginUseCase.Result.SuccessRequared2FA:
				_authStep = AuthStep.TwoFa;
				EventSystem.Publish(new Login2FaRequiredEto(Model.UserName, Model.Password));
				break;

			case UserLoginUseCase.Result.SuccessRequared2FASetup:
				_authStep = AuthStep.TwoFaSetup;
				EventSystem.Publish(new Login2FaSetupRequiredEto(Model.UserName, Model.Password));
				break;

			case UserLoginUseCase.Result.FailedToSubscribeOnAuthenticationState:
			case UserLoginUseCase.Result.FailedToCreateSession:
				Snackbar.Add("Не удалось получить сессию. Попробуйте снова. При повторении ошибки входа - свяжитесь с администратором.", MudBlazor.Severity.Error);
				await _loginFormPasswordRef.SetErrorAsync("Проверьте правильность ввода данных");
				await _loginFormLoginRef.SetErrorAsync("Проверьте правильность ввода данных");
				break;

			case UserLoginUseCase.Result.UserLockedout:
				EventSystem.Publish(new UserBannedEto());
				break;

			case UserLoginUseCase.Result.ForceChangePassword:
				_authStep = AuthStep.ForcedPasswordChange;
				EventSystem.Publish(new LoginForceChangePasswordEto(Model.UserName, Model.Password));
				break;

			case UserLoginUseCase.Result.WrongPassword:
				Snackbar.Add("Неверный пароль", MudBlazor.Severity.Error);
				await _loginFormPasswordRef.SetErrorAsync("Неверный пароль");
				break;

			case UserLoginUseCase.Result.ValidationError:
				await _loginFormLoginRef.SetErrorAsync("Проверьте правильность ввода данных");
				await _loginFormPasswordRef.SetErrorAsync("Проверьте правильность ввода данных");
				break;

			case UserLoginUseCase.Result.UserNotFound:
				Snackbar.Add("Пользователь не найден", MudBlazor.Severity.Error);
				await _loginFormLoginRef.SetErrorAsync("Пользователь не найден");
				break;

			case UserLoginUseCase.Result.Unknown:
				Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(LoginPage), nameof(UserLoginUseCase));
				Snackbar.Add($"Не удалось выполнить вход из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
				break;
			default:
				Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(LoginPage), nameof(UserLoginUseCase), _authResponse.Result);
				Snackbar.Add($"Не удалось выполнить вход из-за ошибки: {_authResponse.Result}", MudBlazor.Severity.Error);
				break;
		}
	}

	#region [Event Handlers]
	private void OnTwoFaSetupRequiredHandler(Login2FaSetupRequiredEto eto)
	{
		_authStep = AuthStep.TwoFaSetup;
		StateHasChanged();
	}

	private void OnUserForcePasswordChange(LoginForceChangePasswordEto eto)
	{
		_authStep = AuthStep.ForcedPasswordChange;
		StateHasChanged();
	}

	private void OnTwoFaRequiredHandler(Login2FaRequiredEto eto)
	{
		_authStep = AuthStep.TwoFa;
		StateHasChanged();
	}

	private void OnLoginFormShowHandler(ShowLoginFormEto eto)
	{
		_authStep = AuthStep.Login;
		StateHasChanged();
	}
	#endregion
}
