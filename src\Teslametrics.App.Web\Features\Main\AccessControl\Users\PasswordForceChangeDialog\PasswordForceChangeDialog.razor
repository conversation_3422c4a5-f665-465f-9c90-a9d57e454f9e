﻿@using Microsoft.AspNetCore.Components.Authorization
@inherits InteractiveBaseComponent
<MudDialog ActionsClass="mx-2"
		   ContentClass="pt-8 pb-12"
		   Visible="_isVisible"
		   VisibleChanged="VisibilityChanged"
		   Options="_dialogOptions">
	<TitleContent>
		<MudStack Row=true>
			<MudIcon Icon="@Icons.Material.Filled.Key"
					 Class="mt-1" />
			<MudStack Spacing="0">
				<MudText Typo="Typo.h6">Смена пароля пользователю</MudText>
				<MudText Typo="Typo.subtitle2">@_user?.Username</MudText>
			</MudStack>
		</MudStack>
	</TitleContent>
	<DialogContent>
		<MudStack AlignItems="AlignItems.Center"
				  Justify="Justify.Center"
				  Spacing="0"
				  Class="mud-height-full">
			<MudIcon Icon="@Icons.Material.Outlined.WarningAmber"
					 Color="Color.Warning"
					 Style="font-size: 8rem;"
					 Class="mb-2" />
			<MudText Typo="Typo.subtitle1"
					 Color="Color.Warning">Смена пароля пользователю!</MudText>
			<MudText Typo="Typo.body1">Вы уверены, что вы хотите заставить пользователя <b>@_user?.Username</b> Сменить
				пароль при входе?</MudText>
			<MudText Typo="Typo.body2">Данное изменение необратимо.</MudText>
		</MudStack>
	</DialogContent>
	<DialogActions>
		<MudSpacer />
		<MudButton OnClick="CancelAsync">Отменить</MudButton>
		<MudButton OnClick="SubmitAsync"
				   Color="Color.Warning">Подвердить</MudButton>
	</DialogActions>
</MudDialog>