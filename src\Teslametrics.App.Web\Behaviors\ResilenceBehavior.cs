using System.Data;
using MediatR;
using Polly;
using Polly.Retry;

namespace Teslametrics.App.Web.Behaviors;

public class ResilenceBehavior<TRequest, TResponse> : IPipelineBehavior<TRequest, TResponse>
    where TRequest : IRequest<TResponse>
{
    private readonly ILogger _logger;
    private readonly AsyncRetryPolicy<TResponse> _retryPolicy;

    public ResilenceBehavior(ILogger<ResilenceBehavior<TRequest, TResponse>> logger)
    {
        _logger = logger;
        _retryPolicy = Policy<TResponse>
            .Handle<DBConcurrencyException>()
            .WaitAndRetryAsync(3,
                retryAttempt => TimeSpan.FromMilliseconds(Math.Pow(2, retryAttempt) * 100),
                onRetry: (outcome, timeSpan, retryCount, context) =>
                {
                    _logger.LogWarning(
                        outcome.Exception,
                        "Retry attempt {RetryCount} for {RequestName} after concurrency exception",
                        retryCount,
                        typeof(TRequest).FullName);
                });
    }

    public async Task<TResponse> Handle(TRequest request, RequestHandlerDelegate<TResponse> next, CancellationToken cancellationToken)
    {
        return await _retryPolicy.ExecuteAsync(async () => await next());
    }
}