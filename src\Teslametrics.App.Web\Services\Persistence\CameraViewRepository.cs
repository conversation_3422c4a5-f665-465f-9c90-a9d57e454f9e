using Microsoft.EntityFrameworkCore;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Domain.CameraViews;

namespace Teslametrics.App.Web.Services.Persistence;

public class CameraViewRepository : BaseRepository<CameraViewAggregate>, ICameraViewRepository
{
    public CameraViewRepository(CommandAppDbContext dbContext)
        : base(dbContext)
    {
    }

    public Task<bool> IsNameExistsAsync(string name,
                                        Guid organizationId,
                                        CancellationToken cancellationToken = default) =>
        DbContext.Set<CameraViewAggregate>()
            .AsNoTracking()
            .AnyAsync(entity => entity.Name == name && entity.OrganizationId == organizationId, cancellationToken);
}