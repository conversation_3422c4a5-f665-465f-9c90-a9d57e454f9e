using Microsoft.AspNetCore.Components;
using MudBlazor;
using System.Reactive;
using System.Text;
using Teslametrics.App.Web.Components.Drawer;
using Teslametrics.App.Web.Events.Organization;
using Teslametrics.App.Web.Extensions;
using Teslametrics.App.Web.Shared;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Organizations.Drawer.View;

public partial class OrganizationViewComponent
{
	private class UserModel(Guid id, string name)
	{
		public Guid Id { get; set; } = id;
		public string Name { get; set; } = name;
	}
	private class OrganizationModel(Guid id, string name, UserModel? owner)
	{
		public Guid Id { get; set; } = id;
		public string Name { get; set; } = name;
		public UserModel? Owner { get; set; } = owner;
	}

	[CascadingParameter(Name = DrawerConsts.InstanceName)]
	private DrawerComponent Drawer { get; set; } = null!;

	private string _contextMenuAuthPolicyString => new StringBuilder()
		.Append("," + AppPermissions.Main.AccessControl.Organizations.Update.GetEnumPermissionString())
		.Append("," + AppPermissions.Main.AccessControl.Organizations.Delete.GetEnumPermissionString())
		.ToString();// Есть вероятность, что спереди будет запятая.

	private DateTime _lastRefreshTime = DateTime.Now;

	private bool _subscribing;
	private Guid _organizationId;
	private OrganizationModel? _model;
	private GetOrganizationUseCase.Response? _response;
	private SubscribeOrganizationUseCase.Response? _subscriptionResult;

	#region Parameters
	[Parameter]
	[EditorRequired]
	public Guid OrganizationId { get; set; }
	#endregion

	protected override async Task OnParametersSetAsync()
	{
		if (OrganizationId != _organizationId)
		{
			_organizationId = OrganizationId;
			_model = null;
			await FetchAsync();
		}

		await base.OnParametersSetAsync();
	}

	protected async Task FetchAsync()
	{
		if (IsLoading) return;
		await SetLoadingAsync(true);
		try
		{
			_response = await ScopeFactory.MediatorSend(new GetOrganizationUseCase.Query(OrganizationId));
		}
		catch (Exception ex)
		{
			Snackbar.Add("Не удалось получить организацию из-за непредвиденной ошибки сервера. Повторите попытку", Severity.Error);
			Logger.LogError(ex, ex.Message);
		}

		await SetLoadingAsync(false);
		if (_response is null) return;

		switch (_response.Result)
		{
			case GetOrganizationUseCase.Result.Success:
				_lastRefreshTime = DateTime.Now;
				_model = new(_response.Id, _response.Name, new UserModel(_response.OwnerId, _response.Owner));
				await SubscribeAsync();
				break;
			case GetOrganizationUseCase.Result.ValidationError:
				Snackbar.Add("Ошибка валидации данных", Severity.Error);
				break;
			case GetOrganizationUseCase.Result.OrganizationNotFound:
				Snackbar.Add("Организация не найдена", Severity.Error);
				break;
			case GetOrganizationUseCase.Result.Unknown:
				Logger.LogError("Unexpected result in {Component}, {UseCase}: {Result}", nameof(OrganizationViewComponent), nameof(GetOrganizationUseCase), _response.Result);
				Snackbar.Add("Не удалось получить организацию из-за непредвиденной ошибки. Повторите попытку", Severity.Error);
				break;
			default:
				Logger.LogError("Unexpected result in {Component}, {UseCase}: {Result}", nameof(OrganizationViewComponent), nameof(GetOrganizationUseCase), _response.Result);
				Snackbar.Add($"Не удалось получить организацию из-за непредвиденной ошибки: {_response.Result}. Повторите попытку", Severity.Error);
				break;
		}
	}

	private async Task SubscribeAsync()
	{
		try
		{
			if (_response is null || !_response.IsSuccess) return;
			await SetSubscribingAsync(true);
			Unsubscribe();
			_subscriptionResult = await ScopeFactory.MediatorSend(new SubscribeOrganizationUseCase.Request(Observer.Create<object>(OnAppEventHandler, OnError), _response.Id));
			switch (_subscriptionResult.Result)
			{
				case SubscribeOrganizationUseCase.Result.Success:
					CompositeDisposable.Add(_subscriptionResult.Subscription!);
					break;

				case SubscribeOrganizationUseCase.Result.ValidationError:
					Snackbar.Add("Ошибка валидации при подписке на события", Severity.Error);
					break;

				case SubscribeOrganizationUseCase.Result.Unknown:
				default:
					throw new Exception($"Unexpected result in {nameof(SubscribeOrganizationUseCase)}: {_subscriptionResult.Result}");
			}
		}
		catch (Exception exc)
		{
			Snackbar.Add("Не удалось получить подписку на события организации. Повторите попытку", Severity.Error);
			Logger.LogError(exc, exc.Message);
		}
		finally
		{
			await SetSubscribingAsync(false);
		}
	}

	private void Unsubscribe()
	{
		if (_subscriptionResult is not null && _subscriptionResult.Subscription is not null)
		{
			CompositeDisposable.Remove(_subscriptionResult.Subscription);
			_subscriptionResult.Subscription.Dispose();
		}
	}
	protected Task SetSubscribingAsync(bool isLoading = true) => UpdateViewAsync(() =>
	{
		_subscribing = isLoading;
	});

	#region [Actions]
	private Task CancelAsync()
	{
		Unsubscribe();
		_model = null;
		return Drawer.HideAsync();
	}
	private void Edit() => EventSystem.Publish(new OrganizationEditEto(OrganizationId));
	private void Delete() => EventSystem.Publish(new OrganizationDeleteEto(OrganizationId));

	private Task RefreshAsync() => FetchAsync();
	#endregion

	#region [Event Handlers]
	private async void OnAppEventHandler(object appEvent)
	{
		if (_model is null)
		{
			Unsubscribe();
			return;
		}

		switch (appEvent)
		{
			case SubscribeOrganizationUseCase.UpdatedEvent updatedEto:
				await FetchAsync();
				await UpdateViewAsync();
				break;

			case SubscribeOrganizationUseCase.DeletedEvent deletedEto:
				Unsubscribe();
				_model = null;
				Snackbar.Add("Просматриваемая вами организация была удалена", Severity.Error);
				await Drawer.HideAsync();
				break;

			default:
				break;
		}
	}

	private void OnError(Exception exc)
	{
		Logger.LogError(exc, exc.Message);
		Snackbar.Add("Ошибка валидации при подписке на события", Severity.Error);
	}
	#endregion [Event Handlers]
}
