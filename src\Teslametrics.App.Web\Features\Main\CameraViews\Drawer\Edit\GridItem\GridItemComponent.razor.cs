using System.Reactive;
using Microsoft.AspNetCore.Components;
using Teslametrics.App.Web.Exceptions;
using Teslametrics.App.Web.Extensions;

namespace Teslametrics.App.Web.Features.Main.CameraViews.Drawer.Edit.GridItem;

public partial class GridItemComponent
{
	public record CameraViewCell(Guid CameraId, string Name);
	private MudBlazor.MudAutocomplete<CameraViewCell>? _ref;

	[Parameter]
	public CameraViewCell? Camera { get; set; }

	[Parameter]
	public EventCallback<CameraViewCell?> CameraChanged { get; set; }

	[Parameter]
	[EditorRequired]
	public Guid OrganizationId { get; set; }

	private bool _subscribing;

	private SubscribeCameraListUseCase.Response? _subscriptionResult;
	private GetCameraListUseCase.Response? _listResponse;

	private async Task SubscribeAsync()
	{
		if (_subscribing) return;
		await SetSubscribingAsync(true);
		try
		{
			Unsubscribe();

			_subscriptionResult = await ScopeFactory.MediatorSend(new SubscribeCameraListUseCase.Request(Observer.Create<object>(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OnError), OrganizationId));
		}
		catch (Exception ex)
		{
			_subscriptionResult = null;
			Snackbar.Add("Не удалось получить подписку на события списка камер из-за непредвиденной ошибки. Повторите попытку", MudBlazor.Severity.Error);
			Logger.LogError(ex, ex.Message);
		}

		await SetSubscribingAsync(false);

		if (_subscriptionResult is null) return;

		switch (_subscriptionResult.Result)
		{
			case SubscribeCameraListUseCase.Result.Success:
				CompositeDisposable.Add(_subscriptionResult.Subscription!);
				break;
			case SubscribeCameraListUseCase.Result.ValidationError:
				Snackbar.Add("Ошибка валидации при подписке на события", MudBlazor.Severity.Error);
				break;
			case SubscribeCameraListUseCase.Result.Unknown:
				Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(GridItemComponent), nameof(SubscribeCameraListUseCase));
				Snackbar.Add($"Не удалось получить подписку на события списка камер из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
				break;
			default:
				Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(GridItemComponent), nameof(SubscribeCameraListUseCase), _subscriptionResult.Result);
				Snackbar.Add($"Не удалось получить подписку на события списка камер из-за ошибки: {_subscriptionResult.Result}", MudBlazor.Severity.Error);
				break;
		}
	}

	private void Unsubscribe()
	{
		if (_subscriptionResult?.Subscription is not null)
		{
			CompositeDisposable.Remove(_subscriptionResult.Subscription);
			_subscriptionResult.Subscription.Dispose();
		}
	}

	protected Task SetSubscribingAsync(bool isLoading = true) => UpdateViewAsync(() =>
	{
		_subscribing = isLoading;
	});

	private async Task<IEnumerable<CameraViewCell>> SearchAsync(string value, CancellationToken token)
	{
		await SetLoadingAsync(true);
		try
		{
			var userId = await GetCurrentUserIdAsync() ?? throw new NotAuthorizedException();
			_listResponse = await ScopeFactory.MediatorSend(new GetCameraListUseCase.Query(OrganizationId, userId, value), cancellationToken: token);
		}
		catch (Exception exc)
		{
			_listResponse = null;
			Logger.LogError(exc, exc.Message);
			Snackbar.Add("Не удалось получить список камер из-за непредвиденной ошибки.", MudBlazor.Severity.Error);
		}
		await SetLoadingAsync(false);

		if (_listResponse is null) return [];

		switch (_listResponse.Result)
		{
			case GetCameraListUseCase.Result.Success:
				return _listResponse.Items.Select(x => new CameraViewCell(x.Id, x.Name));
			case GetCameraListUseCase.Result.Unknown:
				Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(GridItemComponent), nameof(GetCameraListUseCase));
				Snackbar.Add($"Не удалось получить список камер из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
				return [];
			default:
				Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(GridItemComponent), nameof(GetCameraListUseCase), _listResponse.Result);
				Snackbar.Add($"Не удалось получить список камер из-за ошибки: {_listResponse.Result}", MudBlazor.Severity.Error);
				return [];
		}
	}

	#region [Event Handlers]
	private async Task OnSelectedValudeChanged(CameraViewCell? value)
	{
		Camera = value;

		await CameraChanged.InvokeAsync(Camera);

		if (value is null)
		{
			Unsubscribe();
		}
	}

	private async void OnAppEventHandler(object appEvent)
	{
		if (_ref is null) return;

		switch (appEvent)
		{
			case SubscribeCameraListUseCase.UpdatedEvent updatedEto:
				if (_ref.Open)
					await _ref.ForceUpdate();
				break;
			case SubscribeCameraListUseCase.DeletedEvent deleteEto:
				if (deleteEto.CameraId == Camera?.CameraId)
				{
					Camera = null;
					await CameraChanged.InvokeAsync(Camera);
				}
				if (_ref.Open)
					await _ref.ForceUpdate();
				break;
			default:
				break;
		}

		await _ref.ForceUpdate();
	}

	private void OnError(Exception exc)
	{
		Snackbar.Add("Ошибка при подписке на события", MudBlazor.Severity.Error);
		Logger.LogError(exc, exc.Message);
	}

	private async Task OnOpenChangedAsync(bool value)
	{
		if (value)
		{
			await SubscribeAsync();
		}

		if (!value && Camera is null)
		{
			Unsubscribe();
		}
	}
	#endregion [Event Handlers]
}
