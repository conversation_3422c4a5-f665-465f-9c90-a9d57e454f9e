using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using MudBlazor;
using System.Reactive;
using System.Text;
using Teslametrics.App.Web.Eto.Users;
using Teslametrics.App.Web.Extensions;
using Teslametrics.App.Web.Services.Authorization;
using Teslametrics.App.Web.Shared;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Users.DeleteDialog;

public partial class DeleteDialog
{
	private bool _disposedValue;

	private Guid _id;
	private Guid _organizationId;

	private bool _subscribing;
	private bool _isVisible;
	private DialogOptions _deleteDialogOptions = new() { CloseOnEscapeKey = true, FullWidth = true, MaxWidth = MaxWidth.Medium, CloseButton = true, NoHeader = true };
	private StringBuilder _confirmationMessage => new StringBuilder("Удалить ").AppendIf(_model?.Username ?? string.Empty, _model is not null);
	private string _input = string.Empty;
	private GetUserUseCase.Response? _model;
	private SubscribeUserUseCase.Response? _subscriptionResult;

	protected override void Dispose(bool disposing)
	{
		if (!_disposedValue)
		{
			if (disposing)
			{
				AuthenticationStateProvider.AuthenticationStateChanged -= OnAuthenticationStateChanged;
			}

			_disposedValue = true;
		}

		base.Dispose(disposing);
	}

	protected override void OnInitialized()
	{
		CompositeDisposable.Add(EventSystem.Subscribe<UserDeleteEto>(OnDeleteHandler));

		base.OnInitialized();
	}

	private async Task FetchAsync()
	{
		if (IsLoading) return;

		try
		{
			await SetLoadingAsync();
			_model = await ScopeFactory.MediatorSend(new GetUserUseCase.Query(_id));
			switch (_model.Result)
			{
				case GetUserUseCase.Result.Success:
					await SubscribeAsync();
					break;
				case GetUserUseCase.Result.ValidationError:
					Snackbar.Add("Ошибка валидации данных", Severity.Error);
					break;
				case GetUserUseCase.Result.UserNotFound:
					Snackbar.Add("Пользователь не найдён, вероятно, он был удалён", Severity.Error);
					break;
				case GetUserUseCase.Result.Unknown:
				default:
					throw new Exception($"Unexpected result in {nameof(GetUserUseCase)}: {_model.Result}");
			}
		}
		catch (Exception ex)
		{
			Snackbar.Add("Не удалось получить пользователя из-за непредвиденной ошибки. Повторите попытку", Severity.Error);
			Logger.LogError(ex, ex.Message);
		}
		finally
		{
			await SetLoadingAsync(false);
		}
	}
	private Task RefreshAsync() => FetchAsync();

	private Task CancelAsync() => UpdateViewAsync(() =>
	{
		Unsubscribe();
		_isVisible = false;
		_model = null;

		AuthenticationStateProvider.AuthenticationStateChanged -= OnAuthenticationStateChanged;
	});

	private async Task SubmitAsync()
	{
		if (IsLoading || _model is null || !_model.IsSuccess) return;
		try
		{
			await SetLoadingAsync();
			Unsubscribe();
			var result = await ScopeFactory.MediatorSend(new DeleteUserUseCase.Command(_model.Id, _organizationId));
			switch (result.Result)
			{
				case DeleteUserUseCase.Result.Success:
					Snackbar.Add("Пользователь успешно удалён");
					await CancelAsync();
					break;

				case DeleteUserUseCase.Result.ValidationError:
					Snackbar.Add("Ошибка валидации при подписке на события", Severity.Error);
					await SubscribeAsync();
					break;

				case DeleteUserUseCase.Result.CannotDeleteOwner:
					Snackbar.Add("Нульзя удалить пользователя, назначенного на организацию.", Severity.Error);
					await SubscribeAsync();
					break;

				case DeleteUserUseCase.Result.Unknown:
				default:
					await SubscribeAsync();
					throw new Exception($"Unexpected result in {nameof(DeleteUserUseCase)}: {_model.Result}");
			}
		}
		catch (Exception ex)
		{
			Snackbar.Add("Произошла ошибка при удалении пользователя", Severity.Error);
			Logger.LogError(ex, ex.Message);
		}
		finally
		{
			await SetLoadingAsync(false);
		}
	}

	private async Task SubscribeAsync()
	{
		try
		{
			if (_model is null) return;
			await SetSubscribingAsync(true);
			Unsubscribe();
			_subscriptionResult = await ScopeFactory.MediatorSend(new SubscribeUserUseCase.Request(Observer.Create<object>(OnAppEventHandler, OnError), _model!.Id));
			switch (_subscriptionResult.Result)
			{
				case SubscribeUserUseCase.Result.Success:
					CompositeDisposable.Add(_subscriptionResult.Subscription!);
					break;

				case SubscribeUserUseCase.Result.ValidationError:
					Snackbar.Add("Ошибка валидации при подписке на события", Severity.Error);
					break;

				case SubscribeUserUseCase.Result.Unknown:
				default:
					throw new Exception($"Unexpected result in {nameof(SubscribeUserUseCase)}: {_subscriptionResult.Result}");
			}
		}
		catch (Exception exc)
		{
			Snackbar.Add("Не удалось получить подписку на события пользователя. Повторите попытку", Severity.Error);
			Logger.LogError(exc, exc.Message);
		}
		finally
		{
			await SetSubscribingAsync(false);
		}
	}

	private void Unsubscribe()
	{
		if (_subscriptionResult is not null && _subscriptionResult.Subscription is not null)
		{
			CompositeDisposable.Remove(_subscriptionResult.Subscription);
			_subscriptionResult.Subscription.Dispose();
		}
	}
	protected Task SetSubscribingAsync(bool isLoading = true) => UpdateViewAsync(() =>
	{
		_subscribing = isLoading;
	});

	private void VisibilityChanged(bool isVisible)
	{
		_isVisible = isVisible;
		if (!isVisible)
		{
			Unsubscribe();
			_model = null;
		}
	}

	#region Event handlers
	private async void OnAppEventHandler(object appEvent)
	{
		switch (appEvent)
		{
			case SubscribeUserUseCase.UpdatedEvent updatedEto:
				await FetchAsync();
				await UpdateViewAsync();
				break;

			case SubscribeUserUseCase.DeletedEvent deleteEto:
				Snackbar.Add("Пользователь был удалён.", Severity.Error);
				await CancelAsync();
				break;

			default:
				break;
		}
	}

	private void OnError(Exception exc)
	{
		Logger.LogError(exc, exc.Message);
		Snackbar.Add("Ошибка валидации при подписке на события", Severity.Error);
	}

	private async Task OnDeleteHandler(UserDeleteEto eto)
	{

		var userAuthState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
		if (userAuthState is null) return;

		var policyRequirementResource = new PolicyRequirementResource(eto.OrganizationId, eto.UserId);
		var authorizationResult = await AuthorizationService.AuthorizeAsync(
			userAuthState.User,  // Current user from AuthenticationStateProvider
			policyRequirementResource, // The resource being authorized
			AppPermissions.Main.AccessControl.Users.Delete.GetEnumPermissionString() // The policy name
		);

		if (authorizationResult.Succeeded)
		{
			_organizationId = eto.OrganizationId;
			_id = eto.UserId;
			_isVisible = true;
			_input = string.Empty;

			await FetchAsync();
			await SubscribeAsync();

			AuthenticationStateProvider.AuthenticationStateChanged += OnAuthenticationStateChanged;

			StateHasChanged();
		}
		else
		{
			Snackbar.Add("Недостаточно прав для удаления пользователя", MudBlazor.Severity.Warning);
			await CancelAsync();
		}
	}

	private async void OnAuthenticationStateChanged(Task<AuthenticationState> authenticationState)
	{
		var userAuthState = await authenticationState;
		if (userAuthState.User is null || userAuthState.User.Identity is null || !userAuthState.User.Identity.IsAuthenticated)
		{
			await CancelAsync();
			return;
		}

		var policyRequirementResource = new PolicyRequirementResource(_organizationId, _id);
		var authorizationResult = await AuthorizationService.AuthorizeAsync(
			userAuthState.User,  // Current user from AuthenticationStateProvider
			policyRequirementResource, // The resource being authorized
			AppPermissions.Main.AccessControl.Users.Delete.GetEnumPermissionString() // The policy name
		);

		if (!authorizationResult.Succeeded)
		{
			Snackbar.Add("Недостаточно прав для удаления пользователя", MudBlazor.Severity.Warning);
			await CancelAsync();
		}
	}
	#endregion
}
