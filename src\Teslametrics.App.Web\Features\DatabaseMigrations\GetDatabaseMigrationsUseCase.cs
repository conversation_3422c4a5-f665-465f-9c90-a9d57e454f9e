﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Services.Persistence;

namespace Teslametrics.App.Web.Features.DatabaseMigrations;

public static class GetDatabaseMigrationsUseCase
{
    public record Query : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public List<string> AppliedMigrations { get; private set; }

        public List<string> PendingMigrations { get; private set; }

        public Result Result { get; private set; }

        public bool IsSuccess => Result == Result.Success;

        public Response(List<string> appliedMigrations, List<string> pendingMigrations)
        {
            Result = Result.Success;
            AppliedMigrations = appliedMigrations;
            PendingMigrations = pendingMigrations;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Result = result;

            AppliedMigrations = [];
            PendingMigrations = [];
        }
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        Failure
    }

    public class Handler : IRequestHandler<Query, Response>
    {
        private readonly CommandAppDbContext _appDbContext;

        public Handler(CommandAppDbContext appDbContext)
        {
            _appDbContext = appDbContext;
        }

        public async Task<Response> Handle(Query request, CancellationToken cancellationToken)
        {
            try
            {
                var appAppliedMigrations = (await _appDbContext.Database.GetAppliedMigrationsAsync()).ToList();
                var appPendingMigrations = (await _appDbContext.Database.GetPendingMigrationsAsync()).ToList();

                return new Response(appAppliedMigrations, appPendingMigrations);
            }
            catch (Exception)
            {
                return new Response(Result.Failure);
            }
        }
    }
}