﻿@inherits InteractiveBaseComponent
<MudDatePicker PickerVariant="PickerVariant.Static"
			   @bind-Date="@SelectedDate"
			   IsDateDisabledFunc="IsDateDisabledFunc"
			   PickerMonthChanged="FetchMonthAvaliableDates" />
@code
{
	private GetAvailableArchivesUseCase.Response? _archive;

	[Parameter]
	public Guid CameraId { get; set; }

	[Parameter]
	public DateTime? SelectedDate { get; set; }

	[Parameter]
	public EventCallback<DateTime?> SelectedDateChanged { get; set; }

	protected override async Task OnParametersSetAsync()
	{
		await FetchMonthAvaliableDates(DateTime.UtcNow);
		if (_archive is not null && _archive.AvailableDates.Contains(DateTime.UtcNow))
		{
			SelectedDate = DateTime.UtcNow;
		}
	}

	private async Task FetchMonthAvaliableDates(DateTime? dateTime)
	{
		try
		{
			if (dateTime is null) return;
			_archive = await ScopeFactory.MediatorSend(new GetAvailableArchivesUseCase.Query(CameraId, dateTime.Value));
			await SetLoadingAsync(false);
			switch (_archive.Result)
			{
				case GetAvailableArchivesUseCase.Result.Success:
					break;
				case GetAvailableArchivesUseCase.Result.ValidationError:
					Snackbar.Add("Не удалось получить список дат с архивами.", Severity.Error);
					break;
				case GetAvailableArchivesUseCase.Result.Unknown:
				default:
					throw new Exception($"Unexpected result in {nameof(GetAvailableArchivesUseCase)}: {_archive.Result}");
			}
		}
		catch (Exception ex)
		{
			Logger.LogError(ex, ex.Message);
			Snackbar.Add("Не удалось получить архвив.");
		}
		finally
		{
			await SetLoadingAsync(false);
		}
	}

	private bool IsDateDisabledFunc(DateTime dt)
	{
		if (dt > DateTime.UtcNow) return true;

		if (_archive is null) return true;

		if (!_archive.AvailableDates.Contains(dt)) return true;

		return false;
	}
}