using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using MudBlazor;
using Teslametrics.App.Web.Extensions;
using Teslametrics.App.Web.Services.BlzEventSystem;

namespace Teslametrics.App.Web.Components;

public abstract class InteractiveBaseComponent : BaseComponent
{
    protected bool IsLoading { get; set; }

    #region Injectables
    [Inject]
    protected AuthenticationStateProvider AuthenticationStateProvider { get; set; } = null!;

    [Inject]
    protected IAuthorizationService AuthorizationService { get; set; } = null!;

    [Inject]
    protected IServiceScopeFactory ScopeFactory { get; set; } = null!;

    [Inject]
    protected ISnackbar Snackbar { get; set; } = null!;

    [Inject]
    protected IBlzEventSystem EventSystem { get; set; } = null!;
    #endregion

    protected Task SetLoadingAsync(bool isLoading = true)
    {
        if (IsLoading != isLoading)
        {
            return UpdateViewAsync(() =>
            {
                IsLoading = isLoading;
            });
        }

        return Task.CompletedTask;
    }

    protected async Task<Guid?> GetCurrentUserIdAsync()
    {
        var state = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        if (state is null)
        {
            return null;
        }

        return state.User.GetUserId();
    }
}

public abstract class InteractiveBaseComponent<T> : BaseComponent<T>
{
    protected bool IsLoading { get; set; }

    #region Injectables
    [Inject]
    protected IServiceScopeFactory ScopeFactory { get; set; } = null!;

    [Inject]
    protected ISnackbar Snackbar { get; set; } = null!;

    [Inject]
    public IBlzEventSystem EventSystem { get; set; } = null!;

    [Inject]
    protected AuthenticationStateProvider AuthenticationStateProvider { get; set; } = null!;
    #endregion

    protected Task SetLoadingAsync(bool isLoading = true) => UpdateViewAsync(() =>
    {
        IsLoading = isLoading;
    });

    protected async Task<Guid?> GetCurrentUserIdAsync()
    {
        var state = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        if (state.User.Identity?.IsAuthenticated ?? false)
        {
            return state.User.GetUserId();
        }

        return null;
    }
}