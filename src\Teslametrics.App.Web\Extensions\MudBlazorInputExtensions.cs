using Microsoft.AspNetCore.Components;
using MudBlazor;

namespace Teslametrics.App.Web.Extensions;

public static class MudBlazorInputExtensions
{
	public static async Task<bool> SetErrorAsync<T>(this MudBaseInput<T>? inputRef, string? errorText = null, bool? hasError = null)
	{
		if(inputRef is not null)
		{
			var parameters = new Dictionary<string, object?>
						{
							{ "ErrorText", errorText },
							{ "Error", hasError ?? !string.IsNullOrEmpty(errorText) }
						};
			await inputRef.SetParametersAsync(ParameterView.FromDictionary(parameters));
			return true;
		}

		return false;
	}
}
