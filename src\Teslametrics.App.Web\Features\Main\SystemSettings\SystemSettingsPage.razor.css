﻿::deep {
	--roomZIndex: 6;
	--deviceZIndex: 7;
	--imgZIndex: 5;
}

::deep .sensor_form_container>* {
	max-width: 300px;
}

::deep .sensor_form_container {
	align-content: center;
	align-items: center;
	display: grid !important;
	grid-auto-flow: column;
	grid-template-columns: repeat(auto-fill, minmax(auto, 300px));
}

::deep .sensor_form_container .add_button {
	justify-self: end;
}

::deep .tabs_content .mud-tabs-panels {
	display: grid;
	grid-template-rows: auto auto 1fr auto;
	overflow: hidden;
}

::deep .diagram_buttons {
	position: absolute;
	z-index: 60;
}

::deep .mud-input-control::after {
	content: none;
}

::deep .diagram-node:has(> .base_polygon_node) {
	z-index: var(--roomZIndex);
}

::deep .diagram-node:has(> .room_img_node) {
	z-index: var(--imgZIndex);
}

::deep .diagram-node:has(> .device_node) {
	z-index: var(--deviceZIndex);
}