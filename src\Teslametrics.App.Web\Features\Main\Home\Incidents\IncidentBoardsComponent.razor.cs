using System.Reactive;
using Microsoft.AspNetCore.Components;
using Teslametrics.App.Web.Exceptions;
using Teslametrics.App.Web.Extensions;

namespace Teslametrics.App.Web.Features.Main.Home.Incidents;

public partial class IncidentBoardsComponent
{
    private bool _subscribing;
    private SubscribeIncidentsCountUseCase.Response? _subscribeResponse;
    private GetIncidentsBoardsUseCase.Response? _response;

    [Parameter]
    [EditorRequired]
    public IEnumerable<Guid> Buildings { get; set; } = [];

    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();
        await LoadDataAsync();
    }

    private async Task LoadDataAsync()
    {
        await SetLoadingAsync(true);
        try
        {
            _response = await ScopeFactory.MediatorSend(new GetIncidentsBoardsUseCase.Query(Buildings));
        }
        catch (Exception ex)
        {
            _response = null;
            Logger.LogError(ex, ex.Message);
            Snackbar.Add("Произошла ошибка при загрузке информации по просишествиям", MudBlazor.Severity.Error);
        }

        await SetLoadingAsync(false);
        if (_response is null) return;
        switch (_response.Result)
        {
            case GetIncidentsBoardsUseCase.Result.Success:
                await SubscribeAsync();
                break;
            case GetIncidentsBoardsUseCase.Result.PlanNotFound:
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(IncidentBoardsComponent), nameof(GetIncidentsBoardsUseCase), _response.Result);
                Snackbar.Add($"Не удалось получить информацию по просишествиям из-за ошибки: {_response.Result}", MudBlazor.Severity.Error);
                break;
            case GetIncidentsBoardsUseCase.Result.Unknown:
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(IncidentBoardsComponent), nameof(GetIncidentsBoardsUseCase));
                Snackbar.Add($"Не удалось получить информацию по просишествиям из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.",
                MudBlazor.Severity.Error);
                break;
            default:
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(IncidentBoardsComponent), nameof(GetIncidentsBoardsUseCase), _response.Result);
                Snackbar.Add($"Не удалось получить информацию по просишествиям из-за ошибки: {_response.Result}", MudBlazor.Severity.Error);
                break;
        }
    }

    private async Task SubscribeAsync()
    {
        if (_response is null || !_response.IsSuccess || _subscribing) return;
        try
        {
            Unsubscribe();
            await SetSubscribingAsync(true);
            _subscribeResponse = await ScopeFactory.MediatorSend(new SubscribeIncidentsCountUseCase.Request(Observer.Create<object>(OnAppEventHandler, OnError), Buildings, _response.CameraIds));
        }
        catch (Exception ex)
        {
            _subscribeResponse = null;
            Snackbar.Add($"Не удалось подписаться на события количества просишествий из-за ошибки сообщения с сервером. Повторите попытку позже.", MudBlazor.Severity.Error);
            Logger.LogError(ex, ex.Message);
        }
        await SetSubscribingAsync(false);
        if (_subscribeResponse is null) return;

        switch (_subscribeResponse.Result)
        {
            case SubscribeIncidentsCountUseCase.Result.Success:
                CompositeDisposable.Add(_subscribeResponse.Subscription!);
                break;
            case SubscribeIncidentsCountUseCase.Result.ValidationError:
                Snackbar.Add("Ошибка валидации при подписке на события количества просишествий", MudBlazor.Severity.Error);
                break;
            case SubscribeIncidentsCountUseCase.Result.Unknown:
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(IncidentBoardsComponent), nameof(SubscribeIncidentsCountUseCase));
                Snackbar.Add($"Не удалось подписаться на события количества просишествий из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
                break;
            default:
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(IncidentBoardsComponent), nameof(SubscribeIncidentsCountUseCase), _subscribeResponse.Result);
                Snackbar.Add($"Не удалось подписаться на события количества просишествий из-за ошибки: {_subscribeResponse.Result}", MudBlazor.Severity.Error);
                break;
        }
    }
    private void Unsubscribe()
    {
        if (_subscribeResponse?.Subscription is not null)
        {
            CompositeDisposable.Remove(_subscribeResponse.Subscription);
            _subscribeResponse.Subscription.Dispose();
        }
    }

    protected Task SetSubscribingAsync(bool isLoading = true) => UpdateViewAsync(() =>
    {
        _subscribing = isLoading;
    });

    #region [Event Handlers]
    private async void OnAppEventHandler(object appEvent)
    {
        await LoadDataAsync();
        await UpdateViewAsync();
    }

    private void OnError(Exception exc)
    {
        Snackbar.Add("Ошибка при подписке на события просишествий", MudBlazor.Severity.Error);
        Logger.LogError(exc, exc.Message);
    }
    #endregion [Event Handlers]
}
