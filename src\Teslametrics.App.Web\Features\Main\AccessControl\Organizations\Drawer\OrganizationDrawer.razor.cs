using Teslametrics.App.Web.Events;
using Teslametrics.App.Web.Events.Organization;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Organizations.Drawer;

public partial class OrganizationDrawer
{
	private Guid? _organizationId;
	private DrawerMode _mode = DrawerMode.Hidden;

	public bool IsOpened => _mode != DrawerMode.Hidden;
	public enum DrawerMode
	{
		Hidden,
		Create,
		Edit,
		View,
	}

	protected override void OnInitialized()
	{
		CompositeDisposable.Add(EventSystem.Subscribe<OrganizationCreateEto>(OnEventHandler));
		CompositeDisposable.Add(EventSystem.Subscribe<OrganizationSelectEto>(OnEventHandler));
		CompositeDisposable.Add(EventSystem.Subscribe<OrganizationEditEto>(OnEventHandler));

		base.OnInitialized();
	}

	public Task ShowCreateAsync() => InvokeAsync(() =>
	{
		_organizationId = null;
		_mode = DrawerMode.Create;
		StateHasChanged();
	});

	public Task ShowEditAsync(Guid organizationId) => InvokeAsync(() =>
	{
		_organizationId = organizationId;
		_mode = DrawerMode.Edit;
		StateHasChanged();
	});

	public Task ShowViewAsync(Guid organizationId) => InvokeAsync(() =>
	{
		_organizationId = organizationId;
		_mode = DrawerMode.View;
		StateHasChanged();
	});

	public Task CloseAsync() => InvokeAsync(() =>
	{
		try
		{
			_mode = DrawerMode.Hidden;
			StateHasChanged();
		}
		catch (Exception ex)
		{
			Console.WriteLine($"Error in CloseAsync: {ex.Message}");
			throw;
		}
	});

	#region [Event Handlers]
	private async void OnEventHandler(BaseEto eto)
	{
		switch (eto)
		{
			case OrganizationCreateEto:
				await ShowCreateAsync();
				break;

			case OrganizationSelectEto selectEto:
				await ShowViewAsync(selectEto.OrganizationId);
				StateHasChanged();
				break;

			case OrganizationEditEto editEto:
				await ShowEditAsync(editEto.OrganizationId);
				break;

			default:
				return;
		}
	}

	private Task OnOpenChanged(bool opened) => InvokeAsync(() =>
	{
		_mode = DrawerMode.Hidden;
		StateHasChanged();
	});
	#endregion [Event Handlers]
}
