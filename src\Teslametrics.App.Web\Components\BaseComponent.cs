using System.Reactive.Disposables;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;

namespace Teslametrics.App.Web.Components;

public abstract class BaseComponent<TResource> : BaseComponent
{
    protected IStringLocalizer<TResource> L => Localizer;

    [Inject]
    protected IStringLocalizer<TResource> Localizer { get; set; } = default!;
}

public abstract class BaseComponent : ComponentBase, IDisposable
{
    private bool _disposedValue;

    [Inject]
    protected ILogger<BaseComponent> Logger { get; set; } = null!;

    protected CompositeDisposable CompositeDisposable = [];


    protected Task UpdateViewAsync(Action? action = null) => InvokeAsync(() =>
    {
        action?.Invoke();
        StateHasChanged();
    });

    protected void UpdateView()
    {
        StateHasChanged();
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposedValue)
        {
            if (disposing)
            {
                CompositeDisposable.Dispose();
            }

            _disposedValue = true;
        }
    }

    void IDisposable.Dispose()
    {
        Dispose(disposing: true);
        GC.SuppressFinalize(this);
    }
}