using Orleans;
using Teslametrics.Shared;

namespace Teslametrics.MediaServer.Orleans.Wirenboard;

/// <summary>
/// Интерфейс для управления историей данных с Wirenboard
/// </summary>
public interface IWirenboardHistoryManagerGrain : IGrainWithGuidKey
{
    /// <summary>
    /// Настраивает сбор истории для списка топиков
    /// При первом вызове происходит запуск сервиса
    /// </summary>
    /// <param name="request">Запрос с информацией о топиках</param>
    Task ConfigureHistoryCollectionAsync(ConfigureHistoryCollectionRequest request);

    [GenerateSerializer]
    public record ConfigureHistoryCollectionRequest(List<HistoryTopicInfo> TopicInfos);
}

/// <summary>
/// Информация о топике для сбора истории
/// </summary>
[GenerateSerializer]
public record HistoryTopicInfo(string Topic, SensorValueType ValueType);