using Orleans.Concurrency;

namespace Teslametrics.MediaServer.Orleans.Camera;

public interface ICameraStreamRecorderGrain : IGrainWithGuidKey
{
    [OneWay]
    public Task StartAsync(StartRequest request);

    [OneWay]
    public Task StopAsync();

    [OneWay]
    public Task SendSegment(SendSegmentRequest request);

    [GenerateSerializer]
    public record StartRequest(Guid CameraId);

    [GenerateSerializer]
    public record SendSegmentRequest(MicroSegment MicroSegment);
}