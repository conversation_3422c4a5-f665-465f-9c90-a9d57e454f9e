using System.Collections.Concurrent;
using Microsoft.Extensions.Options;
using Teslametrics.App.Web.Shared;

namespace Teslametrics.App.Web.Orleans.Wirenboard;

/// <summary>
/// Грейн для управления историей данных с Wirenboard
/// Запускается через BackgroundService и обновляет запущенные Grain при изменении данных топиков
/// Остановка происходит автоматически при деактивации грейна при остановке хоста
/// </summary>
public class WirenboardHistoryManagerGrain : Grain, IWirenboardHistoryManagerGrain, IRemindable
{
    private readonly ILogger<WirenboardHistoryManagerGrain> _logger;
    private readonly IoTModule.WirenboardSettings _settings;
    private readonly IHostApplicationLifetime _applicationLifetime;
    private readonly ConcurrentDictionary<string, Guid> _activeHistoryGrains = new();
    private IGrainReminder? _keepAliveReminder;
    private const string _keepAliveReminderName = "WirenboardHistoryManagerKeepAliveReminder";
    private readonly TimeSpan _keepAliveReminderPeriod = TimeSpan.FromMinutes(5);
    private bool _isRunning = false;

    public WirenboardHistoryManagerGrain(ILogger<WirenboardHistoryManagerGrain> logger,
                                         IOptions<IoTModule.WirenboardSettings> settings,
                                         IHostApplicationLifetime applicationLifetime)
    {
        _logger = logger;
        _settings = settings.Value;
        _applicationLifetime = applicationLifetime;
    }

    public override async Task OnActivateAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("WirenboardHistoryManagerGrain activated with ID: {GrainId}", this.GetPrimaryKeyString());

        // Регистрируем напоминание для предотвращения деактивации зерна
        try
        {
            _keepAliveReminder = await this.RegisterOrUpdateReminder(
                _keepAliveReminderName,
                TimeSpan.FromMinutes(1), // Начальная задержка
                _keepAliveReminderPeriod // Период между вызовами
            );

            _logger.LogInformation("Registered keep-alive reminder for WirenboardHistoryManagerGrain with ID: {GrainId}",
                this.GetPrimaryKeyString());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to register keep-alive reminder for WirenboardHistoryManagerGrain with ID: {GrainId}",
                this.GetPrimaryKeyString());
        }

        await base.OnActivateAsync(cancellationToken);
    }

    public override async Task OnDeactivateAsync(DeactivationReason reason, CancellationToken cancellationToken)
    {
        _logger.LogInformation("WirenboardHistoryManagerGrain deactivating with ID: {GrainId}, reason: {Reason}",
            this.GetPrimaryKeyString(), reason);

        // Удаляем напоминание
        if (_keepAliveReminder is not null)
        {
            // Проверяем, не находится ли приложение в процессе завершения работы
            if (!_applicationLifetime.ApplicationStopping.IsCancellationRequested)
            {
                try
                {
                    await this.UnregisterReminder(_keepAliveReminder);
                    _logger.LogInformation("Unregistered keep-alive reminder for WirenboardHistoryManagerGrain with ID: {GrainId}",
                        this.GetPrimaryKeyString());
                }
                catch (OperationCanceledException)
                {
                    _logger.LogWarning("ReminderService has been stopped while unregistering reminder for WirenboardHistoryManagerGrain with ID: {GrainId}",
                        this.GetPrimaryKeyString());
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to unregister keep-alive reminder for WirenboardHistoryManagerGrain with ID: {GrainId}",
                        this.GetPrimaryKeyString());
                }
            }
            else
            {
                _logger.LogInformation("Skipping reminder unregistration for WirenboardHistoryManagerGrain with ID: {GrainId} as application is stopping",
                    this.GetPrimaryKeyString());
            }
        }

        // Останавливаем все активные грейны истории при деактивации
        // await StopAllHistoryGrainsAsync();
        _isRunning = false;

        _logger.LogInformation("WirenboardHistoryManagerGrain resources cleaned up for ID: {GrainId}",
            this.GetPrimaryKeyString());

        await base.OnDeactivateAsync(reason, cancellationToken);
    }

    /// <summary>
    /// Метод, вызываемый системой Orleans при срабатывании напоминания
    /// </summary>
    /// <param name="reminderName">Имя напоминания</param>
    /// <param name="status">Статус напоминания</param>
    /// <returns>Task</returns>
    public Task ReceiveReminder(string reminderName, TickStatus status)
    {
        if (reminderName == _keepAliveReminderName)
        {
            _logger.LogDebug("Keep-alive reminder received for WirenboardHistoryManagerGrain with ID: {GrainId}",
                this.GetPrimaryKeyString());

            // Отправляем пинг всем активным грейнам истории, чтобы они не деактивировались
            if (_isRunning && !_activeHistoryGrains.IsEmpty)
            {
                _logger.LogDebug("Sending keep-alive pings to {Count} history grains", _activeHistoryGrains.Count);

                foreach (var (topic, grainId) in _activeHistoryGrains)
                {
                    try
                    {
                        // Получаем ссылку на грейн и вызываем его метод, чтобы предотвратить деактивацию
                        var grain = GrainFactory.GetGrain<IWirenboardSensorDataHistoryGrain>(grainId);

                        // Простой вызов любого метода грейна предотвратит его деактивацию
                        // Используем неблокирующий вызов, чтобы не ждать ответа
                        _ = grain.PingAsync();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error sending keep-alive ping to history grain for topic: {Topic}", topic);
                    }
                }
            }
        }

        return Task.CompletedTask;
    }

    /// <summary>
    /// Настраивает сбор истории для списка топиков
    /// При первом вызове происходит запуск сервиса
    /// </summary>
    /// <param name="request">Запрос с информацией о топиках</param>
    public async Task ConfigureHistoryCollectionAsync(IWirenboardHistoryManagerGrain.ConfigureHistoryCollectionRequest request)
    {
        if (_settings.BrokerAddress is null || _settings.Port is null)
        {
            _logger.LogError("Broker address or port is not configured");
            return;
        }

        // Если грейн еще не запущен, запускаем его
        if (!_isRunning)
        {
            _logger.LogInformation("Starting WirenboardHistoryManagerGrain");
            _isRunning = true;
        }

        var topicInfos = request.TopicInfos;

        _logger.LogInformation("Configuring history collection for WirenboardHistoryManagerGrain, count: {Count}", topicInfos.Count);

        // Получаем текущие активные топики
        var currentTopics = _activeHistoryGrains.Keys.ToList();

        // Определяем новые топики, которые нужно добавить
        var newTopics = topicInfos.Select(t => t.Topic).Except(currentTopics).ToList();

        // Определяем топики, которые нужно удалить
        var topicsToRemove = currentTopics.Except(topicInfos.Select(t => t.Topic)).ToList();

        // Останавливаем грейны для удаляемых топиков
        foreach (var topic in topicsToRemove)
        {
            if (_activeHistoryGrains.TryGetValue(topic, out var grainId))
            {
                var grain = GrainFactory.GetGrain<IWirenboardSensorDataHistoryGrain>(grainId);
                await grain.StopAsync();
                _activeHistoryGrains.TryRemove(topic, out _);
                _logger.LogInformation("Stopped history grain for topic: {Topic}", topic);
            }
        }

        // Запускаем грейны для новых топиков
        foreach (var topic in newTopics)
        {
            var topicInfo = topicInfos.First(t => t.Topic == topic);
            var grainId = GenerateGrainId(topic);

            var grain = GrainFactory.GetGrain<IWirenboardSensorDataHistoryGrain>(grainId);
            await grain.StartAsync(new IWirenboardSensorDataHistoryGrain.StartRequest(_settings.BrokerAddress!, _settings.Port!.Value, topicInfo));

            _activeHistoryGrains[topic] = grainId;
            _logger.LogInformation("Started history grain for topic: {Topic}", topic);
        }
    }

    /// <summary>
    /// Останавливает все активные грейны истории
    /// </summary>
    private async Task StopAllHistoryGrainsAsync()
    {
        _logger.LogInformation("Stopping all active history grains, count: {Count}", _activeHistoryGrains.Count);

        foreach (var (topic, grainId) in _activeHistoryGrains)
        {
            try
            {
                var grain = GrainFactory.GetGrain<IWirenboardSensorDataHistoryGrain>(grainId);
                await grain.StopAsync();
                _logger.LogInformation("Stopped history grain for topic: {Topic}", topic);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error stopping history grain for topic: {Topic}", topic);
            }
        }

        _activeHistoryGrains.Clear();
    }

    /// <summary>
    /// Генерирует уникальный ID для грейна на основе топика
    /// </summary>
    /// <param name="topic">Топик</param>
    /// <returns>ID грейна</returns>
    private static Guid GenerateGrainId(string topic)
    {
        // Создаем детерминированный GUID на основе топика
        return GuidUtility.Create(Guid.Parse("A4ED63A9-698B-4B11-A0A6-E13118E3C34D"), topic);
    }
}