﻿@using Teslametrics.App.Web.Features.Main.Devices.Diagrams.Nodes.FridgeNode.Sensors

@inherits BaseNodeComponent<FridgeElement>

@if (_isHovering && _fridgeData?.IsSuccess == true)
{
    <div class="plan_fridge absolute fridge-hover"
         data-id="@Element.Id"
         style="@($"left: {ToCssValue(Element.X)}px; top: {ToCssValue(Element.Y)}px;")"
         @onclick="SelectAsync"
         @onmouseenter="OnMouseEnterAsync"
         @onmouseleave="OnMouseLeave">
        <MudTooltip Text="@Element.Title">
            <div class="fridge-content">
                <div class="sensor-grid">
                    @foreach (var sensor in _temperature ?? [])
                    {
                        <SensorDataComponent TopicName="@sensor.Name"
                                             Name="@sensor.DisplayName"
                                             ValueProcessor="@((object value) => value is double doubleValue ? Math.Round(doubleValue, 2) + "°C" : value.ToString())"
                                             Error="@(sensor.Incident is not null)"
                                             Icon="@TeslaIcons.Sensors.Temperature" />
                    }
                    @foreach (var item in _humidity ?? [])
                    {
                        <SensorDataComponent TopicName="@item.Name"
                                             Name="@item.DisplayName"
                                             Error="@(item.Incident is not null)"
                                             ValueProcessor="@((object value) => value.ToString() + "%")"
                                             Icon="@TeslaIcons.Sensors.Humidity" />
                    }
                    @foreach (var item in _door ?? [])
                    {
                        <SensorDataComponent TopicName="@item.Name"
                                             Name="@item.DisplayName"
                                             Error="@(item.Incident is not null)"
                                             ValueProcessor="@((object value) => value is bool boolValue ? (boolValue ? "Открыта" : "Закрыта") : value.ToString())"
                                             Icon="@TeslaIcons.Sensors.Door" />
                    }
                    @foreach (var item in _power ?? [])
                    {
                        <SensorDataComponent TopicName="@item.Name"
                                             Name="@item.DisplayName"
                                             Error="@(item.Incident is not null)"
                                             ValueProcessor="@((object value) => value is bool boolValue ? (boolValue ? "Подключен" : "Не подключен") : value.ToString())"
                                             Icon="@TeslaIcons.Sensors.Power" />
                    }
                    @foreach (var item in _leak ?? [])
                    {
                        <SensorDataComponent TopicName="@item.Name"
                                             Name="@item.DisplayName"
                                             Error="@(item.Incident is not null)"
                                             ValueProcessor="@((object value) => value is bool boolValue ? (boolValue ? "Есть протечка" : "Нет протечек") : value.ToString())"
                                             Icon="@TeslaIcons.Sensors.Leak" />
                    }
                </div>
            </div>
        </MudTooltip>
    </div>
}
else
{
    <div class="plan_fridge absolute"
         data-id="@Element.Id"
         style="@($"left: {ToCssValue(Element.X)}px; top: {ToCssValue(Element.Y)}px;")"
         @onclick="SelectAsync"
         @onmouseenter="OnMouseEnterAsync"
         @onmouseleave="OnMouseLeave">
        <MudIcon Icon="@TeslaIcons.Devices.Fridge"
                 Class="@($"{(IsSelected ? "selected" : "")}")" />
    </div>
}
