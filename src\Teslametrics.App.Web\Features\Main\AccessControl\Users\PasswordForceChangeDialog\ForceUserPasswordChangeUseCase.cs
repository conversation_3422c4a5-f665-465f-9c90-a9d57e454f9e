using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.Core.Domain.AccessControl.Users;
using Teslametrics.Core.Services.TransactionManager;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Users.PasswordForceChangeDialog;

public static class ForceUserPasswordChangeUseCase
{
    public record Command(Guid Id) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public Guid Id { get; init; }

        public Result Result { get; init; }
        public bool IsSuccess => Result == Result.Success;

        public Response(Guid id)
        {
            Id = id;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Id = Guid.Empty;
            Result = result;
        }
    }

    public enum Result
    {
        Unknown = 0,
        ValidationError,
        Success,
        UserNotFound
    }

    public class Validator : AbstractValidator<Command>
    {
        public Validator()
        {
            RuleFor(c => c.Id).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Command, Response>
    {
        private readonly IValidator<Command> _validator;
        private readonly IUserRepository _userRepository;
        private readonly ITransactionManager _transactionManager;

        public Handler(IValidator<Command> validator,
                       IUserRepository userRepository,
                       ITransactionManager transactionManager)
        {
            _validator = validator;
            _userRepository = userRepository;
            _transactionManager = transactionManager;
        }

        public async Task<Response> Handle(Command request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            using var transaction = await _transactionManager.CreateTransactionAsync();

            var user = await _userRepository.FindAsync(request.Id, cancellationToken);
            if (user is null)
            {
                return new Response(Result.UserNotFound);
            }

            user.SetForcePasswordChange();

            await _userRepository.SaveChangesAsync(cancellationToken);

            await transaction.CommitAsync();

            return new Response(user.Id);
        }
    }
}