﻿::deep .mud-table table {
	border-collapse: separate;
	border-spacing: 0 8px;
}

::deep .mud-table thead tr th {
	border: none;
}

::deep .mud-table tbody tr {
	border-radius: 12px;
	margin-bottom: 8px;
	border-top: 1px solid var(--mud-palette-table-lines);
}

::deep .mud-table tbody tr td {
	border-top: 1px solid var(--mud-palette-table-lines);
}

::deep .mud-table tbody tr td:first-child {
	border-top-left-radius: 12px;
	border-bottom-left-radius: 12px;
	border-left: 1px solid var(--mud-palette-table-lines);
}

::deep .mud-table tbody tr td:last-child {
	border-top-right-radius: 12px;
	border-bottom-right-radius: 12px;
	border-right: 1px solid var(--mud-palette-table-lines);
}

::deep .mud-table tbody tr {
	display: table-row;
}

::deep .mud-table tbody {
	display: table-row-group;
}

::deep .mud-table {
	border-collapse: separate;
	border-spacing: 0 8px;
}

::deep .mud-table .selected td {
	background-color: rgba(248, 252, 255, 1);
}

::deep .mud-table .error td {
	border-color: rgba(255, 243, 244, 1) !important;
}

::deep .unread::after {
	content: " ";
	display: block;
	background: var(--mud-palette-primary);
	height: 4px;
	width: 4px;
	border-radius: 50%;
	align-self: center;
	justify-self: end;
}