﻿@using Teslametrics.App.Web.Features.DetailView.ViewFrame
@attribute [Route("/camera_views_detail")]
@attribute [AppAuthorize(AppPermissions.Main.Cameras.Read)]
@layout DetailViewLayout
@inherits InteractiveBaseComponent
<PageTitle>Multimonitor | Виды</PageTitle>
<MudContainer MaxWidth="MaxWidth.False"
              Class="mud-height-full pa-4 ">
    <MudStack Spacing="2"
              Class="mud-height-full">
        @if (OrganizationId.HasValue && ViewId.HasValue)
        {
            <AuthorizeView Policy="@AppPermissions.Main.CameraViews.Read.GetEnumPermissionString()"
                           Resource="new PolicyRequirementResource(OrganizationId.Value, ViewId.Value)"
                           Context="innerContext">
                <Authorized Context="innerContext">
                    <ViewFrameComponent OrganizationId="@OrganizationId.Value"
                                        ViewId="@ViewId.Value" />
                </Authorized>
                <NotAuthorized>
                    <NotAuthorizedComponent Class="mud-height-full" />
                </NotAuthorized>
            </AuthorizeView>
        }
        else
        {
            <MudStack AlignItems="AlignItems.Center"
                      Justify="Justify.Center"
                      Class="mud-height-full">
                <div class="view">
                    <div class="left">
                        <MudIcon Icon="@Icons.Material.Filled.Folder"
                                 Style="font-size: 16rem;"
                                 Class="icon" />
                    </div>
                    <div class="divider"></div>
                    <div class="right">
                        <MudIcon Icon="@Icons.Material.Filled.Business"
                                 Style="font-size: 16rem;"
                                 Class="icon" />
                    </div>
                </div>
                <MudText Typo="Typo.h1">Вид не выбран</MudText>
                <MudText Typo="Typo.subtitle1">Для просмотра списка камер выберите вид</MudText>
            </MudStack>
        }
    </MudStack>
</MudContainer>