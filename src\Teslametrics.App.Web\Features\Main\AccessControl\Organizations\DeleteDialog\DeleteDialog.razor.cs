using MudBlazor;
using System.Reactive;
using System.Text;
using Teslametrics.App.Web.Events.Organization;
using Teslametrics.App.Web.Extensions;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Organizations.DeleteDialog;

public partial class DeleteDialog
{
	private Guid _id;
	private bool _subscribing;
	private string _confirmationMessage => new StringBuilder("Удалить ").AppendIf(_model?.Name ?? string.Empty, _model is not null).ToString();
	private DialogOptions _dialogOptions = new() { CloseOnEscapeKey = true, FullWidth = true, MaxWidth = MaxWidth.Medium, CloseButton = true, NoHeader = true };
	private SubscribeOrganizationUseCase.Response? _subscriptionResult;
	private GetOrganizationUseCase.Response? _model;
	private string _input = string.Empty;
	private bool _isVisible;

	protected override void OnInitialized()
	{
		CompositeDisposable.Add(EventSystem.Subscribe<OrganizationDeleteEto>(OnDeleteHandler));

		base.OnInitialized();
	}

	private async Task FetchAsync(Guid id)
	{
		if (IsLoading) return;

		try
		{
			await SetLoadingAsync();
			_model = await ScopeFactory.MediatorSend(new GetOrganizationUseCase.Query(id));
			switch (_model.Result)
			{
				case GetOrganizationUseCase.Result.Success:
					await SubscribeAsync();
					break;
				case GetOrganizationUseCase.Result.ValidationError:
					Snackbar.Add("Ошибка валидации данных", Severity.Error);
					break;
				case GetOrganizationUseCase.Result.OrganizationNotFound:
					Snackbar.Add("Организация не найдена", Severity.Error);
					break;
				case GetOrganizationUseCase.Result.Unknown:
				default:
					throw new Exception($"Unexpected result in {nameof(GetOrganizationUseCase)}: {_model.Result}");
			}
		}
		catch (Exception ex)
		{
			Snackbar.Add("Не удалось получить организацию из-за непредвиденной ошибки. Повторите попытку", Severity.Error);
			Logger.LogError(ex, ex.Message);
		}
		finally
		{
			await SetLoadingAsync(false);
		}
	}

	private async Task SubscribeAsync()
	{
		try
		{
			if (_model is null) return;
			await SetSubscribingAsync(true);
			Unsubscribe();
			_subscriptionResult = await ScopeFactory.MediatorSend(new SubscribeOrganizationUseCase.Request(Observer.Create<object>(OnAppEventHandler, OnError), _model.Id));

			switch (_subscriptionResult.Result)
			{
				case SubscribeOrganizationUseCase.Result.Success:
					CompositeDisposable.Add(_subscriptionResult.Subscription!);
					break;
				case SubscribeOrganizationUseCase.Result.ValidationError:
					Snackbar.Add("Ошибка валидации при подписке на события", Severity.Error);
					break;

				case SubscribeOrganizationUseCase.Result.Unknown:
				default:
					throw new Exception($"Unexpected result in {nameof(SubscribeOrganizationUseCase)}: {_subscriptionResult.Result}");
			}
		}
		catch (Exception exc)
		{
			Snackbar.Add("Не удалось получить подписку на события с организацией. Повторите попытку", Severity.Error);
			Logger.LogError(exc, exc.Message);
		}
		finally
		{
			await SetSubscribingAsync(false);
		}
	}
	private void Unsubscribe()
	{
		if (_subscriptionResult is not null && _subscriptionResult.Subscription is not null)
		{
			CompositeDisposable.Remove(_subscriptionResult.Subscription);
			_subscriptionResult.Subscription.Dispose();
		}
	}
	protected Task SetSubscribingAsync(bool isLoading = true) => UpdateViewAsync(() =>
	{
		_subscribing = isLoading;
	});

	#region [Actions]
	private Task RefreshAsync() => FetchAsync(_id);

	private Task CancelAsync() => UpdateViewAsync(() =>
	{
		Unsubscribe();
		_isVisible = false;
		_model = null;
	});

	private async Task SubmitAsync()
	{
		if (IsLoading || _model is null || !_model.IsSuccess) return;
		try
		{
			await SetLoadingAsync();
			Unsubscribe();
			var response = await ScopeFactory.MediatorSend(new DeleteOrganizationUseCase.Command(_model.Id));
			switch (response.Result)
			{
				case DeleteOrganizationUseCase.Result.Success:
					Snackbar.Add("Организация успешно удалена", Severity.Success);
					await CancelAsync();
					break;
				case DeleteOrganizationUseCase.Result.ValidationError:
					await SubscribeAsync();
					Snackbar.Add("Произошла ошибка при валидации запроса. Повторите попытку позднее.", Severity.Error);
					break;
				case DeleteOrganizationUseCase.Result.CannotDeleteSystemOrganization:
					await SubscribeAsync();
					Snackbar.Add("Нельзя удалить системную организацию", Severity.Error);
					break;
				case DeleteOrganizationUseCase.Result.Unknown:
				default:
					await SubscribeAsync();
					throw new Exception($"Unexpected result in {nameof(DeleteOrganizationUseCase)}: {response.Result}");
			}
		}
		catch (Exception ex)
		{
			Snackbar.Add("Произошла ошибка при удалении организации", Severity.Error);
			Logger.LogError(ex, ex.Message);
		}
		finally
		{
			await SetLoadingAsync(false);
		}
	}
	#endregion

	#region [Event Handlers]
	private async void OnAppEventHandler(object appEvent)
	{
		switch (appEvent)
		{
			case SubscribeOrganizationUseCase.UpdatedEvent updatedEto:
				await FetchAsync(updatedEto.Id);
				await UpdateViewAsync();
				break;

			case SubscribeOrganizationUseCase.DeletedEvent deleteEto:
				Snackbar.Add("Организация была удалена.", Severity.Error);
				await CancelAsync();
				break;

			default:
				break;
		}
	}

	private void OnError(Exception exc)
	{
		Logger.LogError(exc, exc.Message);
		Snackbar.Add("Ошибка валидации при подписке на события", Severity.Error);
	}

	private void VisibilityChanged(bool isVisible)
	{
		_isVisible = isVisible;
		if (!isVisible)
		{
			Unsubscribe();
			_model = null;
		}
	}

	private async Task OnDeleteHandler(OrganizationDeleteEto eto)
	{
		_input = string.Empty;
		if (_model is not null && eto.OrganizationId == _model.Id)
		{
			return;
		}

		await UpdateViewAsync(() =>
		{
			_id = eto.OrganizationId;
			_isVisible = true;
		});

		await FetchAsync(eto.OrganizationId);
	}
	#endregion
}
