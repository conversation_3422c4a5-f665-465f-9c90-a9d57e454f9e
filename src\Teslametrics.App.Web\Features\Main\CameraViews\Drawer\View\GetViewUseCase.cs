using Dapper;
using FluentValidation;
using MediatR;
using System.Data;
using System.Text.Json;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Data.Sql;
using Teslametrics.App.Web.Services.Persistence;
using Teslametrics.App.Web.Shared;

namespace Teslametrics.App.Web.Features.Main.CameraViews.Drawer.View;

public static class GetViewUseCase
{
    public record Query(Guid Id) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public Guid Id { get; init; }

        public string Name { get; init; }

        public short ColumnCount { get; init; } // 8x8, 6x6, 4x4 и т.д.

        public short RowCount { get; init; }

        public GridType GridType { get; init; }

        public List<Cell> Cells { get; init; } // Список камер

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(Guid id, string name, short columnCount, short rowCount, GridType gridType, List<Cell> cells)
        {
            Id = id;
            Name = name;
            ColumnCount = columnCount;
            RowCount = rowCount;
            GridType = gridType;
            Cells = cells;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Result = result;

            Id = Guid.Empty;
            Name = string.Empty;
            Cells = [];
        }

        public record Cell(Guid CameraId, string Name, short CellIndex);
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        ViewNotFound
    }

    public class Validator : AbstractValidator<Query>
    {
        public Validator()
        {
            RuleFor(q => q.Id).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Query, Response>
    {
        private readonly IValidator<Query> _validator;
        private readonly IDbConnection _dbConnection;

        public Handler(IValidator<Query> validator,
                       IDbConnection dbConnection)
        {
            _validator = validator;
            _dbConnection = dbConnection;
        }

        public async Task<Response> Handle(Query request, CancellationToken cancellationToken = default)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            var template = SqlQueryBuilder.Create()
                .Select(Db.CameraViews.Props.Id)
                .Select(Db.CameraViews.Props.Name)
                .Select(Db.CameraViews.Props.ColumnCount)
                .Select(Db.CameraViews.Props.RowCount)
                .Select(Db.CameraViews.Props.GridType)
                .Select(Db.CameraViews.Props.Cells)
                .Where(Db.CameraViews.Props.Id, ":Id", SqlOperator.Equals, new { request.Id })
                .Build(QueryType.Standard, Db.CameraViews.Table, RowSelection.AllRows);

            var view = await _dbConnection.QuerySingleOrDefaultAsync<CameraViewModel>(template.RawSql, template.Parameters);
            if (view is null)
            {
                return new Response(Result.ViewNotFound);
            }

            List<CellModel> cells = JsonSerializer.Deserialize<List<CellModel>>(view.Cells) ?? [];

            var cameraTemplate = SqlQueryBuilder.Create()
                .Select(Db.Cameras.Props.Id)
                .Select(Db.Cameras.Props.Name)
                .Where(Db.Cameras.Props.Id, ":CameraIds", SqlOperator.Any, new { CameraIds = cells.Select(c => c.CameraId).ToList() })
                .Build(QueryType.Standard, Db.Cameras.Table, RowSelection.AllRows);

            var cameras = await _dbConnection.QueryAsync<CameraModel>(cameraTemplate.RawSql, cameraTemplate.Parameters);

            return new Response(view.Id, view.Name, view.ColumnCount, view.RowCount, view.GridType, cells.Select(c => new Response.Cell(c.CameraId, cameras.First(ca => ca.Id == c.CameraId).Name, c.CellIndex)).ToList());
        }
    }

    public record CameraViewModel(Guid Id, string Name, short ColumnCount, short RowCount, GridType GridType, string Cells);
    public record CellModel(Guid CameraId, short CellIndex);
    public record CameraModel(Guid Id, string Name);
}
