using System;
using System.Linq;
using FluentValidation;
using Microsoft.AspNetCore.Components;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Components.Drawer;
using Teslametrics.App.Web.Extensions;
using Teslametrics.App.Web.Features.Main.Cameras.CameraDrawer.Create.PresetField;
using Teslametrics.App.Web.Shared;

namespace Teslametrics.App.Web.Features.Main.Cameras.CameraDrawer.Create;

public partial class CameraCreateComponent
{
	private class Model
	{
		public string Name { get; set; } = string.Empty;
		public PresetFieldComponent.Preset? Preset { get; set; }

		public string ArchiveUri { get; set; } = string.Empty;
		public string PublicUri { get; set; } = string.Empty;
		public string ViewUri { get; set; } = string.Empty;
		public bool AutoStart { get; set; }
		public bool StartOnCreate { get; set; }
		public TimeSpan TimeZone { get; set; } = TimeSpan.FromHours(3);
		public Coordinates? Coordinates { get; set; }

		public bool OnvifEnabled { get; set; }

		public Onvif.OnvifComponent.OnvifSettings OnvifSettings { get; set; } = new();
	}

	private class Validator : BaseFluentValidator<Model>
	{
		private static string? ExtractHostAndPort(string uri)
		{
			try
			{
				if (string.IsNullOrWhiteSpace(uri)) return null;

				var rtspUri = new Uri(uri);
				if (rtspUri.Scheme != "rtsp") return null;

				return rtspUri.Authority;
			}
			catch
			{
				return null;
			}
		}

		private static bool ValidateUriHostMatch(string currentUri, Model model)
		{
			if (string.IsNullOrWhiteSpace(currentUri))
				return true;

			var currentHost = ExtractHostAndPort(currentUri);
			if (currentHost == null)
				return true;

			var allUris = new[] { model.ArchiveUri, model.PublicUri, model.ViewUri };
			var allHosts = allUris
				.Where(uri => !string.IsNullOrWhiteSpace(uri))
				.Select(ExtractHostAndPort)
				.Where(h => h != null);

			return allHosts.All(h => h == currentHost);
		}

		public Validator()
		{
			RuleFor(model => model.Name)
				.Length(3, 60).WithMessage("наименование должно быть длиной от 3 до 60 символов");

			RuleFor(model => model.ArchiveUri)
				.NotEmpty().WithMessage("Поле должно быть заполнено")
				.Must(uri => Uri.TryCreate(uri, UriKind.Absolute, out var parsed) && parsed.Scheme == "rtsp")
				.WithMessage("Некорректный формат RTSP URI")
				.Must((model, uri) => ValidateUriHostMatch(uri, model))
				.WithMessage("Все RTSP URI должны иметь одинаковый хост и порт");

			RuleFor(model => model.ViewUri)
				.NotEmpty().WithMessage("Поле должно быть заполнено")
				.Must(uri => Uri.TryCreate(uri, UriKind.Absolute, out var parsed) && parsed.Scheme == "rtsp")
				.WithMessage("Некорректный формат RTSP URI")
				.Must((model, uri) => ValidateUriHostMatch(uri, model))
				.WithMessage("Все RTSP URI должны иметь одинаковый хост и порт");

			RuleFor(model => model.PublicUri)
				.NotEmpty().WithMessage("Поле должно быть заполнено")
				.Must(uri => Uri.TryCreate(uri, UriKind.Absolute, out var parsed) && parsed.Scheme == "rtsp")
				.WithMessage("Некорректный формат RTSP URI")
				.Must((model, uri) => ValidateUriHostMatch(uri, model))
				.WithMessage("Все RTSP URI должны иметь одинаковый хост и порт");

			RuleFor(model => model.Preset)
				.NotEmpty().WithMessage("Поле должно быть заполнено");

			RuleFor(model => model.TimeZone)
				.NotNull().WithMessage("Поле должно быть заполнено")
				.NotEmpty().WithMessage("Поле должно быть заполнено");

			RuleFor(model => model.Coordinates)
				.NotNull().WithMessage("Поле должно быть заполнено")
				.NotEmpty().WithMessage("Поле должно быть заполнено");
		}
	}


	private MudBlazor.MudTextField<string>? _archiveUriRef;
	private MudBlazor.MudTextField<string>? _publicUriRef;
	private MudBlazor.MudTextField<string>? _viewUriRef;

	private bool _isValid;
	private Model _model = new();
	private Validator _validator = new();

	#region Parameters
	[Parameter]
	[EditorRequired]
	public Guid OrganizationId { get; set; }

	[Parameter]
	[EditorRequired]
	public Guid FolderId { get; set; }

	[CascadingParameter(Name = DrawerConsts.InstanceName)]
	private DrawerComponent Drawer { get; set; } = null!;
	#endregion

	#region [Action]
	private async Task SubmitAsync()
	{
		CreateCameraUseCase.Response? response = null;
		try
		{
			CreateCameraUseCase.Command.OnvifSettings? onvifSettings = _model.OnvifEnabled
				? new CreateCameraUseCase.Command.OnvifSettings(_model.OnvifSettings.Host, _model.OnvifSettings.Port, _model.OnvifSettings.Username, _model.OnvifSettings.Password)
				: null;
			response = await ScopeFactory.MediatorSend(new CreateCameraUseCase.Command(
				OrganizationId,
				FolderId,
				_model.Preset!.Id,
				_model.Name,
				_model.TimeZone,
				_model.Coordinates,
				_model.ArchiveUri,
				_model.PublicUri,
				_model.ViewUri,
				_model.AutoStart,
				_model.StartOnCreate,
				_model.OnvifEnabled,
				onvifSettings
			));
		}
		catch (Exception exc)
		{
			response = null;
			Logger.LogError(exc, exc.Message);
			Snackbar.Add($"Не удалось создать камеру из-за ошибки сообщения с сервером. Повторите попытку позже.", MudBlazor.Severity.Error);
		}

		if (response is not null)
		{
			switch (response.Result)
			{
				case CreateCameraUseCase.Result.Success:
					Snackbar.Add("Камера успешно создана.", MudBlazor.Severity.Success);
					await Drawer.HideAsync();
					break;
				case CreateCameraUseCase.Result.ValidationError:
					Snackbar.Add("Ошибка валидации, проверьте правильность заполнения полей", MudBlazor.Severity.Error);
					break;
				case CreateCameraUseCase.Result.CameraQuotaLimitReached:
					Snackbar.Add("Невозможно создать камеру. Лимит камер на квоту превышен", MudBlazor.Severity.Error);
					break;
				case CreateCameraUseCase.Result.OrganizationNotFound:
					Snackbar.Add("Невозможно создать камеру. Организация не найдена", MudBlazor.Severity.Error);
					break;
				case CreateCameraUseCase.Result.FolderNotFound:
					Snackbar.Add("Невозможно создать камеру. Директория не найдена", MudBlazor.Severity.Error);
					break;
				case CreateCameraUseCase.Result.CameraQuotaNotFound:
					Snackbar.Add("Невозможно создать камеру. Квота не найдена. Выберите другую квоту", MudBlazor.Severity.Error);
					break;

				case CreateCameraUseCase.Result.Unknown:
					Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(CameraCreateComponent), nameof(CreateCameraUseCase));
					Snackbar.Add($"Не удалось создать камеру из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
					break;

				default:
					Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(CameraCreateComponent), nameof(CreateCameraUseCase), response.Result);
					Snackbar.Add($"Не удалось создать камеру из-за ошибки: {response.Result}", MudBlazor.Severity.Error);
					break;
			}
		}
	}

	private Task CancelAsync() => Drawer.HideAsync();

	private void OnViewUriChanged(string uri)
	{
		if (_model is not null)
		{
			_model.ViewUri = uri;
			_archiveUriRef?.Validate();
			_publicUriRef?.Validate();
		}
	}

	private void OnArchiveUriChanged(string uri)
	{
		if (_model is not null)
		{
			_model.ArchiveUri = uri;
			_viewUriRef?.Validate();
			_publicUriRef?.Validate();
		}
	}

	private void OnPublicUriChanged(string uri)
	{
		if (_model is not null)
		{
			_model.PublicUri = uri;
			_viewUriRef?.Validate();
			_archiveUriRef?.Validate();
		}
	}
	#endregion
}
