@typeparam T
@inherits InteractiveBaseComponent
<div class="@ElementClass"
	 draggable="@_draggable.ToString()"
	 @ondragstart="OnDragStartHandler"
	 @ondragover:stopPropagation="true"
	 @ondragover:preventDefault="true"
	 @ondragover="OnDragOverHandler"
	 @ondragleave:stopPropagation="true"
	 @ondragleave:preventDefault="true"
	 @ondragleave="OnDragLeaveHandler"
	 @ondrop="OnDropHandler"
	 @ondrop:stopPropagation="true"
	 @ondrop:preventDefault="true">
	@ChildContent
</div>