// Функции для работы с изображениями
window.imageHelper = {
    // Создает временный URL для изображения из массива байтов
    createObjectURL: function (imageBytes, contentType) {
        // Преобразуем массив байтов в Blob
        const blob = new Blob([new Uint8Array(imageBytes)], { type: contentType });
        
        // Создаем временный URL
        return URL.createObjectURL(blob);
    },
    
    // Освобождает временный URL
    revokeObjectURL: function (url) {
        URL.revokeObjectURL(url);
    }
};
