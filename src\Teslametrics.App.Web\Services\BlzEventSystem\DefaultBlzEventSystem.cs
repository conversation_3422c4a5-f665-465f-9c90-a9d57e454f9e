﻿using System.Reactive.Linq;
using System.Reactive.Subjects;

namespace Teslametrics.App.Web.Services.BlzEventSystem;

public class DefaultBlzEventSystem : IBlzEventSystem
{
    private readonly ISubject<object> _dtoEvents;

    public DefaultBlzEventSystem()
    {
        _dtoEvents = new Subject<object>();
    }

    public void Publish(object obj)
    {
        _dtoEvents.OnNext(obj);
    }

    public void Publish<T>() where T : new()
    {
        Publish(new T());
    }

    public IDisposable Subscribe<T>(Action action)
    {
        return _dtoEvents.OfType<T>().Subscribe(e => action.Invoke());
    }

    public IDisposable Subscribe<T>(Action<T> action)
    {
        return _dtoEvents.OfType<T>().Subscribe(e => action.Invoke(e));
    }

    public IDisposable Subscribe<T>(Func<T, Task> func)
    {
        return _dtoEvents.OfType<T>().Subscribe(async e => await func.Invoke(e));
    }

    public IDisposable Subscribe<T>(Func<Task> func)
    {
        return _dtoEvents.OfType<T>().Subscribe(async e => await func.Invoke());
    }
}
