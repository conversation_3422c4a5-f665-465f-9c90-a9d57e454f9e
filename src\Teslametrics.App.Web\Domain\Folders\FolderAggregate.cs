﻿using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Domain.Folders.Events;

namespace Teslametrics.App.Web.Domain.Folders;

public class FolderAggregate : IEntity
{
    public Guid Id { get; private set; }

    public Guid OrganizationId { get; private set; }

    public Guid? ParentId { get; private set; }

    public string Name { get; private set; }

    public static (FolderAggregate folder, List<object> events) Create(Guid id, Guid organizationId, Guid? parentId, string name)
    {
        return (new FolderAggregate(id, organizationId, parentId, name), [new FolderCreatedEvent(id, organizationId)]);
    }

    private FolderAggregate()
    {
        Name = string.Empty;
    }

    private FolderAggregate(Guid id, Guid organizationId, Guid? parentId, string name)
    {
        Id = id;
        OrganizationId = organizationId;
        ParentId = parentId;
        Name = name;
    }

    public List<object> ChangeParentFolder(Guid parentId)
    {
        if (ParentId != parentId)
        {
            ParentId = parentId;
            return [new FolderParentChangedEvent(Id, OrganizationId)];
        }

        return [];
    }

    public List<object> ChangeName(string name)
    {
        if (Name != name)
        {
            Name = name;
            return [new FolderUpdatedEvent(Id, OrganizationId)];
        }

        return [];
    }
}