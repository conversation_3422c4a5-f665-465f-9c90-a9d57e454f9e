using FluentValidation;
using Microsoft.AspNetCore.Components;
using MudBlazor;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Components.Form;
using Teslametrics.App.Web.Events.Users;
using Teslametrics.App.Web.Extensions;
using Teslametrics.App.Web.Features.Main.Cameras.TreeView;

namespace Teslametrics.App.Web.Features.Authentication.Account.LoginForceChangePassword;

public partial class LoginForceChangePassword
{
	private class PasswordChangeModel
	{
		public string UserName { get; set; } = string.Empty;

		public string Password { get; set; } = string.Empty;

		public string NewPassword { get; set; } = string.Empty;
		public string NewPasswordVerify { get; set; } = string.Empty;
	}

	private class Validator : BaseFluentValidator<PasswordChangeModel>
	{
		public Validator()
		{
			RuleFor(model => model.UserName)
				.NotEmpty().WithMessage("Поле должно быть заполнено")
				.Length(3, 60).WithMessage("Логин должен быть длиной от 3 до 60 символов");

			RuleFor(model => model.Password)
				.NotEmpty().WithMessage("Поле должно быть заполнено")
				.Length(3, 60).WithMessage("Пароль должен быть длиной от 3 до 60 символов");

			RuleFor(model => model.NewPassword)
				.NotEmpty().WithMessage("Поле должно быть заполнено");

			When(model => !string.IsNullOrWhiteSpace(model.NewPassword) && !string.IsNullOrWhiteSpace(model.NewPasswordVerify), () =>
			{
				RuleFor(model => model.NewPassword).Equal(model => model.NewPasswordVerify).WithMessage("Пароли не совпадают");
				RuleFor(model => model.NewPasswordVerify).Equal(model => model.NewPassword).WithMessage("Пароли не совпадают");
			});

			RuleFor(model => model.NewPasswordVerify)
				.NotEmpty().WithMessage("Поле должно быть заполнено");
		}
	}

	private MudForm? _formRef;
	private PasswordFieldComponent? _pwdFieldRef;
	private PasswordFieldComponent? _pwdConfirmFieldRef;

	private bool _isValid;

	private PasswordChangeModel PwdChangeModel { get; set; } = new();
	private Validator _validator = new();


	[Inject]
	protected NavigationManager NavigationManager { get; set; } = null!;

	[Parameter]
	[EditorRequired]
	public string UserName { get; set; } = null!;

	[Parameter]
	[EditorRequired]
	public string Password { get; set; } = null!;

	protected override void OnParametersSet()
	{
		PwdChangeModel = new()
		{
			UserName = UserName,
			Password = Password,
			NewPassword = string.Empty,
			NewPasswordVerify = string.Empty,
		};
		base.OnParametersSet();
	}

	private async Task SubmitAsync()
	{
		try
		{
			var response = await ScopeFactory.MediatorSend(new UserLoginChangePasswordUseCase.Command(PwdChangeModel.UserName, PwdChangeModel.Password, PwdChangeModel.NewPassword));
			switch (response.Result)
			{
				case UserLoginChangePasswordUseCase.Result.Success:
					EventSystem.Publish(new ShowLoginFormEto());
					Snackbar.Add("Пароль успешно изменён", MudBlazor.Severity.Success);
					break;

				case UserLoginChangePasswordUseCase.Result.UserLockedout:
					EventSystem.Publish(new ShowLoginFormEto());
					EventSystem.Publish(new UserBannedEto());
					break;
				case UserLoginChangePasswordUseCase.Result.NewPasswordEqualsOld:
					Snackbar.Add("Новый пароль не может совпадать со старым", MudBlazor.Severity.Error);
					await _pwdFieldRef.SetErrorAsync("Новый пароль не может совпадать со старым");
					break;
				case UserLoginChangePasswordUseCase.Result.ForceChangePasswordNotSet:
					EventSystem.Publish(new ShowLoginFormEto());
					Snackbar.Add("Пользователю нельзя принудительно сменить пароль", MudBlazor.Severity.Error);
					break;
				case UserLoginChangePasswordUseCase.Result.ValidationError:
					Snackbar.Add("Ошибка валидации. Выберите другой пароль", MudBlazor.Severity.Error);
					await _pwdFieldRef.SetErrorAsync("Ошибка валидации. Выберите другой пароль");
					break;
				case UserLoginChangePasswordUseCase.Result.UserNotFound:
					EventSystem.Publish(new ShowLoginFormEto());
					Snackbar.Add("Пользователь не найден", MudBlazor.Severity.Error);
					break;
				case UserLoginChangePasswordUseCase.Result.WrongPassword:
					EventSystem.Publish(new ShowLoginFormEto());
					Snackbar.Add("Неверный пароль пользователя. Введите текущий пароль ещё раз.", MudBlazor.Severity.Error);
					break;
				case UserLoginChangePasswordUseCase.Result.Unknown:
				default:
					throw new Exception($"Unexpected result in {nameof(UpdateFolderUseCase)}: {response.Result}");
			}
		}
		catch (Exception ex)
		{
			Snackbar.Add("Произошла ошибка при смене пароля", MudBlazor.Severity.Error);
			Logger.LogError(ex, ex.Message);
		}
	}

	private async Task OnPwdChange(string pwd)
	{
		PwdChangeModel.NewPassword = pwd;
		if (!string.IsNullOrWhiteSpace(PwdChangeModel.NewPasswordVerify))
		{
			_pwdConfirmFieldRef?.ResetValidation();
		}
		if (PwdChangeModel.NewPasswordVerify == PwdChangeModel.NewPassword && _formRef is not null)
		{
			await _formRef.Validate();
		}
	}

	private async Task OnPwdConfirmChange(string pwd)
	{
		PwdChangeModel.NewPasswordVerify = pwd;
		if (!string.IsNullOrWhiteSpace(PwdChangeModel.NewPassword))
		{
			_pwdFieldRef?.ResetValidation();
		}
		if (PwdChangeModel.NewPasswordVerify == PwdChangeModel.NewPassword && _formRef is not null)
		{
			await _formRef.Validate();
		}
	}
}
