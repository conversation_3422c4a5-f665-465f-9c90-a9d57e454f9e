﻿@inherits InteractiveBaseComponent
<MudAutocomplete T="GetCityListUseCase.Response.Item"
                 SearchFunc="@SearchCityAsync"
                 ToStringFunc="@(e => e == null ? null : e.Name)"
                 Value="@_selectedCity"
                 ValueChanged="@CityValueChanged"
                 Label="Город"
                 Margin="Margin.Dense"
                 Clearable="true"
                 ResetValueOnEmptyText="true"
                 Variant="Variant.Outlined" />

@code {
    private GetCityListUseCase.Response.Item? _selectedCity;

    [Parameter]
    public Guid? City { get; set; }
    [Parameter]
    public EventCallback<Guid?> CityChanged { get; set; }

    private async Task CityValueChanged(GetCityListUseCase.Response.Item? city)
    {
        _selectedCity = city;
        await CityChanged.InvokeAsync(city?.Id);
    }

    private async Task<IEnumerable<GetCityListUseCase.Response.Item>> SearchCityAsync(string value, CancellationToken token)
    {
        GetCityListUseCase.Response? response = null;
        try
        {
            response = await ScopeFactory.MediatorSend(new GetCityListUseCase.Query(value), token);
        }
        catch (TaskCanceledException)
        {
            // Search was canceled, just return empty result
            return Enumerable.Empty<GetCityListUseCase.Response.Item>();
        }
        catch (Exception exc)
        {
            Logger.LogError(exc, "Error searching cities");
            Snackbar.Add("Не удалось получить список городов из-за непредвиденной ошибки.", MudBlazor.Severity.Error);
            return Enumerable.Empty<GetCityListUseCase.Response.Item>();
        }

        if (response.Result == GetCityListUseCase.Result.Success)
            return response.Items;

        if (response.Result == GetCityListUseCase.Result.ValidationError)
            Snackbar.Add("Ошибка валидации при получении списка городов", MudBlazor.Severity.Error);
        else
            Snackbar.Add("Не удалось получить список городов из-за непредвиденной ошибки.", MudBlazor.Severity.Error);

        return Enumerable.Empty<GetCityListUseCase.Response.Item>();
    }
}