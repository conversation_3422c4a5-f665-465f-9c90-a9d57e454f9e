using Microsoft.AspNetCore.Components;
using System.Reflection;
using Teslametrics.App.Web.Domain.AccessControl;
using Teslametrics.App.Web.Extensions;
using Teslametrics.App.Web.Shared;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Roles.Drawer.Create;

public partial class WildcardPermissionComponents<TEnum> where TEnum : struct, Enum
{
	private bool _showAdminTaggedValues;
	private record InheritStatus(bool Inherited, IEnumerable<string> InheritedFrom);
	private IEnumerable<ResourcePermission> _wildcardPermissions => Selected.Where(x => x.ResourceId.IsWildcard && x.Permission.Value.Contains(_namespace));
	private string _namespace;
	private IEnumerable<TEnum> _values;

	[Parameter]
	public bool ShowAdminTaggedValues { get; set; }

	[Parameter]
	[EditorRequired]
	public string Title { get; set; } = string.Empty!;

	[Parameter]
	[EditorRequired]
	public IEnumerable<ResourcePermission> Selected { get; set; } = [];

	[Parameter]
	public EventCallback<IEnumerable<ResourcePermission>> SelectedChanged { get; set; }

	public WildcardPermissionComponents()
	{
		if (ShowAdminTaggedValues)
		{
			_values = ((TEnum[])Enum.GetValues(typeof(TEnum))).Where(item => Convert.ToUInt64(item) != 0);
		}
		else
		{
			_values = typeof(TEnum).IsAdminTagged() ? [] : ((TEnum[])Enum.GetValues(typeof(TEnum))).Where(item => Convert.ToUInt64(item) != 0 && !item.IsAdminTagged());
		}
		_namespace = GetNameSpace();
	}

	protected override void OnParametersSet()
	{
		base.OnParametersSet();
		if (ShowAdminTaggedValues != _showAdminTaggedValues)
		{
			_showAdminTaggedValues = ShowAdminTaggedValues;
			if (ShowAdminTaggedValues)
			{
				_values = ((TEnum[])Enum.GetValues(typeof(TEnum))).Where(item => Convert.ToUInt64(item) != 0);
			}
			else
			{
				_values = ((TEnum[])Enum.GetValues(typeof(TEnum))).Where(item => Convert.ToUInt64(item) != 0 && !item.IsAdminTagged());
			}
		}
	}

	private async Task OnChangedHandler(bool isChecked, TEnum value)
	{
		if (isChecked)
		{
			var inherited = _values.Where(x => value.HasFlag(x));
			List<ResourcePermission> toBeAdded = [];
			foreach (var inheritedValue in inherited)
			{
				string permissionName = GetValueString(inheritedValue);
				if (_wildcardPermissions.Any(x => x.Permission.Value == permissionName))
					continue;

				var newPermission = new ResourcePermission(ResourceId.Wildcard, new Permission(permissionName));
				toBeAdded.Add(newPermission);
			}
			Selected = Selected.Concat(toBeAdded);
		}
		else
		{
			string permissionName = GetValueString(value);
			Selected = Selected.Where(x => x.Permission.Value != permissionName); ;
		}

		if (SelectedChanged.HasDelegate)
			await SelectedChanged.InvokeAsync(Selected);
	}

	private string GetValueString(TEnum value) => $"{_namespace}{value}";

	private InheritStatus GetInheritStatus(TEnum permission)
	{
		List<string> _inheritedFrom = [];
		var enumType = typeof(TEnum);
		foreach (var perm in _wildcardPermissions)
		{
			TEnum enumValue = (TEnum)Enum.Parse(enumType, perm.Permission.Value.Substring(perm.Permission.Value.LastIndexOf('.') + 1));

			bool hasReadPermission = enumValue.HasFlag(permission);
			if (hasReadPermission)
			{
				_inheritedFrom.Add(GetValueString(enumValue));
			}
		}

		if (_inheritedFrom.Count == 1)
			return new(false, []);

		_inheritedFrom.Remove(GetValueString(permission));

		return new(_inheritedFrom.Count > 0, _inheritedFrom);
	}

	private string GetNameSpace()
	{
		var type = typeof(TEnum);
		var namespaceParts = new Stack<string>();

		while (type != null)
		{
			namespaceParts.Push(type.Name);

			if (type.GetCustomAttribute<FqdnRootAttribute>() != null)
			{
				break;
			}
			type = type.DeclaringType!;
		}

		return string.Join(".", namespaceParts) + ".";
	}
}
