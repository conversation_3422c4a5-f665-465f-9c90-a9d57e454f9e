@using Teslametrics.App.Web.Features.Main.Presets.DeleteDialog
@attribute [Route(RouteConstants.CameraPresets)]
@attribute [AppAuthorize(AppPermissions.Main.CameraPresets.Read)]
@inherits InteractiveBaseComponent
<PageTitle>Multimonitor | Пресеты</PageTitle>
<MudContainer MaxWidth="MaxWidth.False"
              Class="mud-height-full pt-4 ">
    <MudStack Spacing="2"
              Class="mud-height-full">
        <Teslametrics.App.Web.Features.Main.Presets.List.PresetListComponent />
    </MudStack>
</MudContainer>
<DeletePresetDialog />
<Teslametrics.App.Web.Features.Main.Presets.Drawer.PresetDrawerComponent />