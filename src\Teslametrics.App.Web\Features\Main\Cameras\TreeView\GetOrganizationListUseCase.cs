using Dapper;
using FluentValidation;
using MediatR;
using System.Data;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Services.Persistence;
using Teslametrics.MediaServer.Orleans.Camera;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Features.Main.Cameras.TreeView;

public static class GetOrganizationListUseCase
{
    public record Query(Guid UserId, int Offset, int Limit, string OrderBy, OrderDirection OrderDirection) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public List<Item> Items { get; init; }

        public int TotalCount { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(List<Item> items, int totalCount)
        {
            Items = items;
            TotalCount = totalCount;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Result = result;

            Items = [];
            TotalCount = 0;
        }

        public record Item(Guid Id, string Name, List<Folder> Folders, int CameraCount);

        public record Folder(Guid Id, string Name, List<Camera> Cameras, int CameraCount);

        public record Camera(Guid Id, string Name, CameraStatus Status);
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError
    }

    public class Validator : AbstractValidator<Query>
    {
        public Validator()
        {
            RuleFor(q => q.UserId).NotEmpty();
            RuleFor(q => q.Offset).GreaterThanOrEqualTo(0);
            RuleFor(q => q.Limit).GreaterThan(0);
        }
    }

    public class Handler : IRequestHandler<Query, Response>
    {
        private readonly IValidator<Query> _validator;
        private readonly IDbConnection _dbConnection;
        private readonly IClusterClient _clusterClient;

        public Handler(IValidator<Query> validator,
                       IDbConnection dbConnection,
                       IClusterClient clusterClient)
        {
            _validator = validator;
            _dbConnection = dbConnection;
            _clusterClient = clusterClient;
        }

        public async Task<Response> Handle(Query request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            var allowedResources = request.UserId != SystemConsts.RootUserId
                ? await GetAllowedResourcesAsync(request.UserId)
                : [];

            var orgainzationsBuilder = SqlQueryBuilder.Create()
                .WhereIf(request.UserId != SystemConsts.RootUserId, $"({Db.Organizations.Props.OwnerId} = :UserId OR :Wildcard = ANY(:ResourceIds) OR {Db.Organizations.Props.Id} = ANY(:ResourceIds))", new
                {
                    request.UserId,
                    Wildcard = SystemConsts.ResourceWildcardId,
                    ResourceIds = allowedResources.Where(r => r.Permission == Fqdn<AppPermissions>.GetName(AppPermissions.Main.AccessControl.Organizations.Read))
                        .Select(r => r.ResourceId).ToList()
                });

            var countTemplate = orgainzationsBuilder.Build(QueryType.Standard, Db.Organizations.Table, RowSelection.AllRows, [$"COUNT(DISTINCT {Db.Organizations.Props.Id})"]);
            var totalCount = await _dbConnection.ExecuteScalarAsync<int>(countTemplate.RawSql, countTemplate.Parameters);

            var selectTemplate = orgainzationsBuilder
                .OrderByIf(!string.IsNullOrWhiteSpace(request.OrderBy), request.OrderBy, request.OrderDirection)
                .Build(QueryType.Paginated,
                       Db.Organizations.Table,
                       RowSelection.UniqueRows,
                       [
                           Db.Organizations.Props.Id,
                           Db.Organizations.Props.Name
                       ],
                       new { request.Limit, request.Offset });

            var organizationModels = await _dbConnection.QueryAsync<OrganizationModel>(selectTemplate.RawSql, selectTemplate.Parameters);

            var organizationIds = organizationModels.Select(o => o.Id).ToList();

            var foldersTemplate = SqlQueryBuilder.Create()
                .Select(Db.Folders.Props.Id)
                .Select(Db.Folders.Props.OrganizationId)
                .Select(Db.Folders.Props.Name)
                .InnerJoin(Db.Organizations.Table, Db.Organizations.Props.Id, Db.Folders.Props.OrganizationId, SqlOperator.Equals)
                .Where(Db.Folders.Props.OrganizationId, ":OrganizationIds", SqlOperator.Any, new { organizationIds })
                .WhereIf(request.UserId != SystemConsts.RootUserId, $"({Db.Organizations.Props.OwnerId} = :UserId OR :Wildcard = ANY(:ResourceIds) OR {Db.Folders.Props.Id} = ANY(:ResourceIds))", new
                {
                    request.UserId,
                    Wildcard = SystemConsts.ResourceWildcardId,
                    ResourceIds = allowedResources.Where(r => r.Permission == Fqdn<AppPermissions>.GetName(AppPermissions.Main.Folders.Read))
                        .Select(r => r.ResourceId).ToList()
                })
                .OrderByIf(!string.IsNullOrWhiteSpace(request.OrderBy), request.OrderBy, request.OrderDirection)
                .Build(QueryType.Standard, Db.Folders.Table, RowSelection.AllRows);

            var folderModels = await _dbConnection.QueryAsync<FolderModel>(foldersTemplate.RawSql, foldersTemplate.Parameters);

            var camerasTemplate = SqlQueryBuilder.Create()
                .Select(Db.Cameras.Props.Id)
                .Select(Db.Cameras.Props.FolderId)
                .Select(Db.Cameras.Props.Name)
                .InnerJoin(Db.Organizations.Table, Db.Organizations.Props.Id, Db.Cameras.Props.OrganizationId, SqlOperator.Equals)
                .Where(Db.Cameras.Props.OrganizationId, ":OrganizationIds", SqlOperator.Any, new { organizationIds })
                .WhereIf(request.UserId != SystemConsts.RootUserId, $"({Db.Organizations.Props.OwnerId} = :UserId OR :Wildcard = ANY(:ResourceIds) OR {Db.Cameras.Props.Id} = ANY(:ResourceIds))", new
                {
                    request.UserId,
                    Wildcard = SystemConsts.ResourceWildcardId,
                    ResourceIds = allowedResources.Where(r => r.Permission == Fqdn<AppPermissions>.GetName(AppPermissions.Main.Cameras.Read))
                        .Select(r => r.ResourceId).ToList()
                })
                .OrderByIf(!string.IsNullOrWhiteSpace(request.OrderBy), request.OrderBy, request.OrderDirection)
                .Build(QueryType.Standard, Db.Cameras.Table, RowSelection.AllRows);

            var cameraModels = await _dbConnection.QueryAsync<CameraModel>(camerasTemplate.RawSql, camerasTemplate.Parameters);

            var mediaServerGrain = _clusterClient.GetGrain<IMediaServerGrain>(Guid.Empty);

            List<Response.Item> organizations = [];

            foreach (var organization in organizationModels)
            {
                var folders = folderModels.Where(f => f.OrganizationId == organization.Id);
                List<Response.Folder> folderResponse = [];

                foreach (var folder in folders)
                {
                    var cameras = cameraModels.Where(c => c.FolderId == folder.Id);
                    List<Response.Camera> cameraResponse = [];

                    foreach (var camera in cameras)
                    {
                        var status = await mediaServerGrain.GetStatusAsync(new IMediaServerGrain.GetCameraStatusRequest(camera.Id));
                        cameraResponse.Add(new Response.Camera(camera.Id, camera.Name, status));
                    }

                    folderResponse.Add(new Response.Folder(folder.Id, folder.Name, cameraResponse, cameraResponse.Count));
                }

                organizations.Add(new Response.Item(organization.Id, organization.Name, folderResponse, folderResponse.Sum(f => f.CameraCount)));
            }

            return new Response(organizations, totalCount);
        }

        private async Task<IEnumerable<PermissionModel>> GetAllowedResourcesAsync(Guid userId)
        {
            var template = SqlQueryBuilder.Create()
                .Select(Db.RolePermissions.Props.Permission)
                .Select(Db.RolePermissions.Props.ResourceId)
                .InnerJoin(Db.UserRoles.Table, Db.UserRoles.Props.RoleId, Db.RolePermissions.Props.RoleId, SqlOperator.Equals)
                .Where($"({Db.RolePermissions.Props.Permission} = :OrganizationPermission " +
                    $"OR {Db.RolePermissions.Props.Permission} = :FolderPermission " +
                    $"OR {Db.RolePermissions.Props.Permission} = :CameraPermission)", new
                    {
                        OrganizationPermission = Fqdn<AppPermissions>.GetName(AppPermissions.Main.AccessControl.Organizations.Read),
                        FolderPermission = Fqdn<AppPermissions>.GetName(AppPermissions.Main.Folders.Read),
                        CameraPermission = Fqdn<AppPermissions>.GetName(AppPermissions.Main.Cameras.Read),
                    })
                .Where(Db.UserRoles.Props.UserId, ":UserId", SqlOperator.Equals, new { userId })
                .Build(QueryType.Standard, Db.RolePermissions.Table, RowSelection.AllRows);

            return await _dbConnection.QueryAsync<PermissionModel>(template.RawSql, template.Parameters);
        }
    }

    public record OrganizationModel(Guid Id, string Name);

    public record FolderModel(Guid Id, Guid OrganizationId, string Name);

    public record CameraModel(Guid Id, Guid FolderId, string Name);

    public record PermissionModel(string Permission, Guid ResourceId);
}