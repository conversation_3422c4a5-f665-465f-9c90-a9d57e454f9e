﻿using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.WebUtilities;

namespace Teslametrics.App.Web.Features.Main.Organization;

// <summary>
// Имя организации и владелец
// </summary>
public partial class OrganizationPage
{
	#region [Injectables]
	[Inject]
	protected NavigationManager NavigationManager { get; set; } = null!;
	#endregion

	#region [Parameters]
	[Parameter]
	[SupplyParameterFromQuery(Name = "offset")]
	public int Offset { get; set; } = 1;

	[Parameter]
	[SupplyParameterFromQuery(Name = "limit")]
	public int Limit { get; set; } = 25;

	[Parameter]
	[SupplyParameterFromQuery(Name = "search")]
	public string Search { get; set; } = string.Empty;
	#endregion

	#region [EventHandlers]
	private void OnOffsetChanged(int offset)
	{
		Offset = offset;
		UpdateUrn();
	}

	private void OnLimitChanged(int limit)
	{
		Limit = limit;
		UpdateUrn();
	}

	private void OnSearchChanged(string search)
	{
		Search = search;
		UpdateUrn();
	}
	#endregion

	private void UpdateUrn()
	{
		Dictionary<string, string?> parameters = [];
		if (Offset > 0)
		{
			parameters.Add("offset", Offset.ToString());
		}
		if (Limit > 0)
		{
			parameters.Add("limit", Limit.ToString());
		}
		if (!string.IsNullOrWhiteSpace(Search))
		{
			parameters.Add("search", Search);
		}
		var url = QueryHelpers.AddQueryString(RouteConstants.Organizations, parameters);
		NavigationManager.NavigateTo(url);
	}
}
