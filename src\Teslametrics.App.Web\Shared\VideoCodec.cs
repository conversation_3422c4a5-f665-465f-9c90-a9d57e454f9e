using System.ComponentModel.DataAnnotations;

namespace Teslametrics.App.Web.Shared;

public enum VideoCodec
{
    [Display(Name = "H.264 Baseline")]
    H264Baseline = 0,

    [Display(Name = "H.264 Main")]
    H264Main = 1,

    [Display(Name = "H.264 High")]
    H264High = 2,

    [Display(Name = "H.264+ Baseline")]
    H264PlusBaseline = 3,

    [Display(Name = "H.264+ Main")]
    H264PlusMain = 4,

    [Display(Name = "H.264+ High")]
    H264PlusHigh = 5
}