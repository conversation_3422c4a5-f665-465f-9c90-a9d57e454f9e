using System.Reactive;
using System.Text;
using Microsoft.AspNetCore.Components;
using Teslametrics.App.Web.Components.Drawer;
using Teslametrics.App.Web.Events.CameraView;
using Teslametrics.App.Web.Extensions;
using Teslametrics.App.Web.Shared;

namespace Teslametrics.App.Web.Features.Main.CameraViews.Drawer.View;

public partial class DrawerViewComponent
{
	[CascadingParameter(Name = DrawerConsts.InstanceName)]
	private DrawerComponent Drawer { get; set; } = null!;

	private Guid _organizationId;
	private Guid _viewId;

	private string _contextMenuAuthPolicyString => new StringBuilder()
		.Append("," + AppPermissions.Main.CameraViews.Update.GetEnumPermissionString())
		.Append("," + AppPermissions.Main.CameraViews.Delete.GetEnumPermissionString())
		.ToString();// Есть вероятность, что спереди будет запятая.

	private GetViewUseCase.Response? _viewResponse;
	private List<GetViewUseCase.Response.Cell> _cells = new();
	private DateTime _lastRefreshTime = DateTime.Now;
	private bool _subscribing = false;
	private SubscribeViewUseCase.Response _subscriptionResult = null!;

	#region [Parameters]
	[Parameter]
	[EditorRequired]
	public Guid ViewId { get; set; }

	[Parameter]
	[EditorRequired]
	public Guid OrganizationId { get; set; }
	#endregion

	protected override async Task OnParametersSetAsync()
	{
		await base.OnParametersSetAsync();

		if (OrganizationId != _organizationId || ViewId != _viewId)
		{
			_organizationId = OrganizationId;
			_viewId = ViewId;

			await SubscribeAsync();
			await FetchAsync();
		}
	}

	private async Task FetchAsync()
	{
		await SetLoadingAsync(true);
		_lastRefreshTime = DateTime.Now;
		try
		{
			_viewResponse = await ScopeFactory.MediatorSend(new GetViewUseCase.Query(ViewId));
		}
		catch (Exception exc)
		{
			_viewResponse = null;
			Logger.LogError(exc, exc.Message);
			Snackbar.Add("Не удалось получить список камер из-за непредвиденной ошибки.", MudBlazor.Severity.Error);
		}

		await SetLoadingAsync(false);
		if (_viewResponse is null) return;
		switch (_viewResponse.Result)
		{
			case GetViewUseCase.Result.Success:
				FillEmptyCells();
				break;
			case GetViewUseCase.Result.ViewNotFound:
				Snackbar.Add("Не удалось получить вид. Вид не существует.", MudBlazor.Severity.Error);
				break;
			case GetViewUseCase.Result.Unknown:
				Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(DrawerViewComponent), nameof(GetViewUseCase));
				Snackbar.Add($"Не удалось получить вид из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
				break;
			default:
				Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(DrawerViewComponent), nameof(GetViewUseCase), _viewResponse.Result);
				Snackbar.Add($"Не удалось получить вид из-за непредвиденной ошибки: {_viewResponse.Result}. Обратитесь к администратору.", MudBlazor.Severity.Error);
				break;
		}
	}

	private void FillEmptyCells()
	{
		if (_viewResponse == null) return;

		int cellCount = _viewResponse.GridType switch
		{
			GridType.Grid1Plus5 => 6,
			GridType.Grid1Plus7 => 8,
			GridType.Grid1Plus12 => 13,
			GridType.Grid2Plus8 => 10,
			GridType.Grid3Plus4 => 7,
			_ => _viewResponse.RowCount * _viewResponse.ColumnCount
		};
		_cells.Clear();
		_cells.Capacity = cellCount;

		// Initialize all cells as empty
		for (short i = 0; i < cellCount; i++)
		{
			_cells.Add(new GetViewUseCase.Response.Cell(Guid.Empty, string.Empty, i));
		}

		// Fill in the actual cells from _viewResponse
		foreach (var cell in _viewResponse.Cells)
		{
			if (cell.CellIndex < cellCount)
			{
				_cells[(int)cell.CellIndex] = cell;
			}
		}
	}

	private async Task SubscribeAsync()
	{
		try
		{
			Unsubscribe();

			await SetSubscribingAsync(true);
			_subscriptionResult = await ScopeFactory.MediatorSend(new SubscribeViewUseCase.Request(Observer.Create<object>(OnAppEventHandler, OnError), ViewId));
			await SetSubscribingAsync(false);
			switch (_subscriptionResult.Result)
			{
				case SubscribeViewUseCase.Result.Success:
					CompositeDisposable.Add(_subscriptionResult.Subscription!);
					break;
				case SubscribeViewUseCase.Result.ValidationError:
					Snackbar.Add("Ошибка валидации при подписке на события", MudBlazor.Severity.Error);
					break;
				case SubscribeViewUseCase.Result.Unknown:
				default:
					throw new Exception($"Unexpected result in {nameof(SubscribeViewUseCase)}: {_subscriptionResult.Result}");
			}
		}
		catch (Exception ex)
		{
			await SetSubscribingAsync(false);
			Snackbar.Add("Не удалось получить подписку на события камеры из-за непредвиденной ошибки. Повторите попытку", MudBlazor.Severity.Error);
			Logger.LogError(ex, ex.Message);
		}
	}

	private void Unsubscribe()
	{
		if (_subscriptionResult?.Subscription is not null)
		{
			CompositeDisposable.Remove(_subscriptionResult.Subscription);
			_subscriptionResult.Subscription.Dispose();
		}
	}

	protected Task SetSubscribingAsync(bool isLoading = true) => UpdateViewAsync(() =>
	{
		_subscribing = isLoading;
	});

	#region [Actions]
	private Task CancelAsync() => Drawer.HideAsync();
	private void Edit() => EventSystem.Publish(new CameraViewEditEto(OrganizationId, ViewId));
	private Task RefreshAsync() => FetchAsync();
	private void Delete() => EventSystem.Publish(new CameraViewDeleteEto(OrganizationId, ViewId));
	#endregion

	#region [Event Handlers]
	private async void OnAppEventHandler(object appEvent)
	{
		await FetchAsync();
		await UpdateViewAsync();
	}

	private void OnError(Exception exc)
	{
		Snackbar.Add("Ошибка при подписке на события", MudBlazor.Severity.Error);
		Logger.LogError(exc, exc.Message);
	}
	#endregion [Event Handlers]
}
