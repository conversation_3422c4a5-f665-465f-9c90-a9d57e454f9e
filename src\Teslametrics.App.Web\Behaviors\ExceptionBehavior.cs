using MediatR;
using Teslametrics.App.Web.Abstractions;

namespace Teslametrics.App.Web.Behaviors;

public class ExceptionBehavior<TRequest, TResponse> : IPipelineBehavior<TRequest, TResponse>
    where TRequest : BaseRequest<TResponse>
    where TResponse : BaseResponse
{
    private readonly ILogger _logger;

    public ExceptionBehavior(ILogger<ExceptionBehavior<TRequest, TResponse>> logger)
    {
        _logger = logger;
    }

    public async Task<TResponse> Handle(TRequest request, RequestHandlerDelegate<TResponse> next, CancellationToken cancellationToken)
    {
        using var scope = _logger.BeginScope("{CorrelationId}", request.CorrelationId);
        try
        {
            _logger.LogTrace("Processing {RequestType}", typeof(TRequest).FullName);
            var response = await next();
            _logger.LogTrace("Processed {RequestType}", typeof(TRequest).FullName);
            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "An error occurred while processing {RequestType}: {Message}", typeof(TRequest).FullName, ex.Message);
            throw;
        }
    }
}