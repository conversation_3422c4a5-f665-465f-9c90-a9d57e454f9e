namespace Teslametrics.App.Web.Orleans.Wirenboard;

[<PERSON><PERSON>("Teslametrics.App.Web.Orleans.ISensorObserver")]
public interface ISensorObserver : IGrainObserver
{
    [<PERSON><PERSON>("ReceiveData")]
    Task ReceiveData(ISensorData SensorData);
}

public interface ISensorData
{
}

[GenerateSerializer]
[<PERSON><PERSON>("Teslametrics.App.Web.Orleans.SensorIntData")]
public record SensorIntData(string Topic, int Value) : ISensorData;

[GenerateSerializer]
[<PERSON><PERSON>("Teslametrics.App.Web.Orleans.SensorStringData")]
public record SensorStringData(string Topic, string Value) : ISensorData;

[GenerateSerializer]
[<PERSON><PERSON>("Teslametrics.App.Web.Orleans.SensorDoubleData")]
public record SensorDoubleData(string Topic, double Value) : ISensorData;

[GenerateSerializer]
[<PERSON><PERSON>("Teslametrics.App.Web.Orleans.SensorBoolData")]
public record SensorBoolData(string Topic, bool Value) : ISensorData;