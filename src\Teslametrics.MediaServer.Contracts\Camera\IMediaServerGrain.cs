using Orleans.Concurrency;

namespace Teslametrics.MediaServer.Orleans.Camera;

public interface IMediaServerGrain : IGrainWithGuidKey
{
    [OneWay]
    public Task ConnectRtspAsync(CameraConnectRtspRequest request);

    [OneWay]
    public Task ConnectOnvifAsync(CameraConnectOnvifRequest request);

    [OneWay]
    public Task DisconnectAsync(CameraDisconnectRequest request);

    [OneWay]
    public Task StopAllAsync();

    public Task<CameraStatus> GetStatusAsync(GetCameraStatusRequest request);

    [GenerateSerializer]
    public record CameraConnectRtspRequest(Guid CameraId, string ArchiveUri, string ViewUri, string PublicUri);

    [GenerateSerializer]
    public record CameraConnectOnvifRequest(Guid CameraId, string Host, int Port, string Username, string Password);

    [GenerateSerializer]
    public record CameraDisconnectRequest(Guid CameraId);

    [GenerateSerializer]
    public record GetCameraStatusRequest(Guid CameraId);
}