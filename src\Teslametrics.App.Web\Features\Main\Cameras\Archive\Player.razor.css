/* Стили для кастомного прогресс-бара */
::deep
{
	overflow: hidden;
}

::deep>div:first-child
{
	height: 100% !important;
}

::deep.video-js
{
	height: 100vh !important;
	/* overflow: hidden; */
	/* display: grid
; */
	/* grid-template-rows: 1fr auto; */
	/* align-items: center; */
	/* justify-items: center; */
	/* justify-content: center; */
	/* align-content: center; */
	padding: 0 !important;
}

::deep .vjs-custom-progress-control
{
	position: relative;
	width: 100%;
}

::deep .vjs-custom-seek-bar
{
	position: relative;
	width: 100%;
	height: 5px;
	/* Высота прогресс-бара */
	background-color: #555;
	/* Цвет фона прогресс-бара */
	cursor: pointer;
}

::deep .vjs-segments-container
{
	position: absolute;
	width: 100%;
	height: 100%;
}

::deep .vjs-segment
{
	position: absolute;
	height: 100%;
	background-color: rgba(255, 255, 255, 0.3);
	/* Цвет доступных сегментов */
}

::deep .vjs-custom-progress
{
	position: absolute;
	height: 100%;
	background-color: #2196F3;
	/* Цвет прогресса воспроизведения */
	width: 0%;
}

::deep .vjs-progress-holder
{
	position: relative;
	/* Для позиционирования gap-сегментов */
}

::deep .vjs-gap-segment
{
	position: absolute;
	display: block;
	height: 4px;
	z-index: 10000;
	background: var(--mud-palette-error);
	transition: all 0.1s;
	top: calc(50% - 2px);
	z-index: 4;
}

::deep .vjs-gap-segment.gap-future
{
	background: var(--mud-palette-dark);
}

::deep .video-js .vjs-control:hover .vjs-gap-segment
{
	height: 5px;
}

::deep #timeline-container
{
	position: relative;
	width: 100%;
	height: 40px;
	overflow-x: auto;
	background: black;
	margin-top: 10px;
	display: flex;
	align-items: center;
	cursor: grab;
}

::deep .vjs-time-labels-wrapper
{
	position: absolute;
	bottom: 0;
	width: 100%;
	height: fit-content;
	display: flex;
	align-items: center;
	z-index: 5;
}

::deep .vjs-time-labels-container
{
	position: relative;
	flex: 1;
	height: 60px;
	/* overflow-x: auto; */
	/* overflow-y: hidden; */
	white-space: nowrap;
	scrollbar-width: none;
	/* Firefox */
	-ms-overflow-style: none;
	/* IE and Edge */
	overflow: visible;
}

::deep .vjs-volume-panel.vjs-control.vjs-volume-panel-horizontal
{
	display: flex;
	justify-content: flex-start;
	margin-left: -10px;
}

::deep .vjs-time-labels-container::-webkit-scrollbar
{
	display: none;
	/* Chrome, Safari, Opera */
}

/* Закомментированные стили кнопок прокрутки
::deep .vjs-time-scroll-button {
	width: 20px;
	height: 20px;
	background: rgba(0, 0, 0, 0.7);
	border: none;
	color: white;
	cursor: pointer;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 12px;
	border-radius: 3px;
	transition: background-color 0.2s;
	z-index: 1;
}

::deep .vjs-time-scroll-button:hover {
	background: rgba(0, 0, 0, 0.9);
}

::deep .vjs-time-scroll-button.scroll-left {
	margin-right: 5px;
}

::deep .vjs-time-scroll-button.scroll-right {
	margin-left: 5px;
}
*/

::deep.vjs-time-label
{
	position: absolute;
	transform: translateX(-50%);
	color: white;
	font-size: 14px;
	background-color: rgba(0, 0, 0, 0.7);
	padding: 2px 5px;
	border-radius: 3px;
	pointer-events: none;
	overflow: visible;
}

::deep.vjs-time-label::after
{
	content: " ";
	/* Используем data-time для отображения метки времени */
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
	font-size: 12px;
	color: white;
	width: 1px;
	height: 40px;
	background: var(--mud-palette-secondary-darken);
	margin-top: 20px;
	z-index: 5;
}

::deep .vjs-time-label.date-change-label::after
{
	width: 2px;
}

::deep .vjs-progress-holder
{
	position: relative;
}

::deep .vjs-progress-holder.vjs-slider.vjs-slider-horizontal
{
	margin: 0;
}

::deep .video-js .vjs-control
{
	margin: 0 10px;
}

::deep .vjs-my-full-bar
{
	height: 100%;
	position: relative;
	align-content: center;
	cursor: pointer;
	flex: auto;
	display: flex;
	flex-wrap: wrap;
	align-items: center;
	min-width: 4em;
	touch-action: none;
	overflow: hidden;
}

::deep .vjs-my-buffer-bar
{
	height: 4px;
	background: var(--mud-palette-gray-dark);
	position: absolute;
	z-index: 2;
	display: none;
}

::deep .vjs-my-played-bar
{
	background: var(--mud-palette-primary);
	height: 4px;
	z-index: 3;
}

::deep .vjs-my-handle
{
	height: 10px;
	width: 10px;
	position: absolute;
	background: var(--mud-palette-primary);
	border-radius: 5px;
	margin-left: -5px;
	z-index: 6;
}

::deep .vjs-my-progress
{
	height: 4px;
	background: white;
	width: 100%;
	flex: 1;
	position: absolute;
}

::deep .vjs-my-progress-bar
{
	width: 100%;
	position: relative;
	margin: 0 10px;
}

::deep .vjs_progressbar_tooltip_container
{
	position: absolute;
	bottom: 40px;
	transform: translateX(-50%);
	display: none;
	z-index: 6;
	pointer-events: none;
	display: flex;
	gap: 8px;
	pointer-events: none;
	flex-direction: column;
	visibility: hidden;
}

::deep .vjs-thumbnail-preview
{
	background: rgba(28, 28, 28, 0.9);
	border-radius: 4px;
	padding: 8px;
	border: 2px solid var(--mud-palette-action-default);
}

::deep .vjs-thumbnail-preview.viewed
{
	border-color: var(--mud-palette-primary) !important;
}

::deep .vjs-thumbnail-preview.viewed::after
{
	background: var(--mud-palette-primary);
}

::deep .vjs-thumbnail-preview.in_gap
{
	border-color: var(--mud-palette-error) !important;
}

::deep .vjs-thumbnail-preview.in_gap::after
{
	background: var(--mud-palette-error);
}

::deep .vjs-thumbnail-preview.future
{
	border-color: var(--mud-palette-dark-darken) !important;
}

::deep .vjs-thumbnail-preview.future::after
{
	background: var(--mud-palette-dark-darken);
}

::deep .vjs-thumbnail-preview::after
{
	content: attr(data-time);
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
	font-size: 12px;
	color: white;
	width: 1px;
	height: 40px;
	background: var(--mud-palette-secondary-darken);
	margin-top: 8px;
}

::deep .vjs-preview-time
{
	color: white;
	font-size: 16px;
	text-align: center;
	margin-bottom: 4px;
	padding-top: 8px;
}

::deep .vjs-preview-image
{
	width: 160px;
	height: 90px;
	background: #000;
	border-radius: 2px;
}

::deep .vjs-current-bitrate-container
{
	top: 12px;
	position: absolute;
	left: 12px;
	padding: 12px;
	background: var(--mud-palette-background-gray);
	border-radius: 4px;
	font-size: 12px;
}

::deep.player-event
{
	position: absolute;
	height: 30px;
	background-color: rgba(255, 165, 0, 0.3);
	border-left: 2px solid orange;
	border-right: 2px solid orange;
	bottom: 0px;
	overflow: visible;
}

::deep.event-label
{
	position: absolute;
	top: -20px;
	font-size: 12px;
	white-space: nowrap;
}

::deep.event-tooltip
{
	background-color: rgba(0, 0, 0, 0.9);
	color: white;
	padding: 8px 12px;
	border-radius: 4px;
	font-size: 12px;
	white-space: nowrap;
	z-index: 1000;
	margin-bottom: 8px;
	pointer-events: none;
	border: 1px solid var(--mud-palette-divider);
	display: flex;
	flex-direction: column;
	gap: 4px;
}

::deep.event-tooltip::after
{
	content: '';
	position: absolute;
	bottom: -4px;
	left: 50%;
	transform: translateX(-50%);
	border-width: 4px 4px 0;
	border-style: solid;
	border-color: rgba(0, 0, 0, 0.9) transparent transparent;
}

::deep.event-tooltip .event-name
{
	font-weight: bold;
	margin-bottom: 4px;
}

::deep.event-tooltip .event-time
{
	font-size: 11px;
	color: rgba(255, 255, 255, 0.8);
}

.player_container
{
	display: grid;
	grid-template-rows: 1fr auto;
}

.player_container .player_controlbar
{
	width: 100%;
	height: 3em;
}

.player_container .player_controlbar .player_timeline
{
	display: flex;
	width: 100%;
	height: 100%;
}

::deep .vjs-current-time
{
	display: flex !important;
	font-size: var(--mud-typography-body1-size);
	align-items: center;
}

.player_to_current_time
{
	display: flex;
	align-items: center;
	justify-content: center;
}