using MudBlazor;
using MudBlazor.Services;
using MudExtensions.Services;
using System.Web;

namespace Teslametrics.App.Web.Extensions;

public static class MudBlazorThemingExtensions
{
	public static void AddMudBlazor(this IServiceCollection services)
	{
		services.AddMudExtensions();

		services.AddMudServices(config =>
		{
			config.SnackbarConfiguration.PreventDuplicates = true;
			config.SnackbarConfiguration.PositionClass = MudBlazor.Defaults.Classes.Position.BottomCenter;
			config.SnackbarConfiguration.ShowCloseIcon = true;

			config.SnackbarConfiguration.SnackbarVariant = MudBlazor.Variant.Outlined;
			config.SnackbarConfiguration.VisibleStateDuration = 4000;
			config.SnackbarConfiguration.HideTransitionDuration = 500;
			config.SnackbarConfiguration.ShowTransitionDuration = 500;
		});

		MudTheme CustomTheme = new()
		{
			Typography = new Typography()
			{
				Default = new Default()
				{
					FontFamily = ["Inter", "sans-serif"]
				},
				H1 = new H1()
				{
					FontFamily = ["Inter", "sans-serif"],
					FontSize = "40px",
					FontWeight = 400,
					LineHeight = 1.1,
				},
				H2 = new H2()
				{
					FontFamily = ["Inter", "sans-serif"],
					FontSize = "24px", //"1.75rem",
					FontWeight = 400,
					LineHeight = 1.1, // 1.3,
				},
				H3 = new H3()
				{
					FontFamily = ["Inter", "sans-serif"],
					FontSize = "36px",
					FontWeight = 500,
					LineHeight = 1.2,
				},
				H4 = new H4()
				{
					FontFamily = ["Inter", "sans-serif"],
					FontSize = "23px",
					FontWeight = 700,
					LineHeight = 1.45,
				},
				H5 = new H5()
				{
					FontFamily = ["Inter", "sans-serif"],
					FontSize = "16px",
					FontWeight = 400,
					LineHeight = 1.1,
				},
				H6 = new H6()
				{
					FontFamily = ["Inter", "sans-serif"],
					FontSize = "14px",
					FontWeight = 400,
					LineHeight = 1.1,
				},
				Body1 = new Body1()
				{
					FontFamily = ["Inter", "sans-serif"],
					FontSize = "12px", //"14px",
					FontWeight = 500,//400,
					LineHeight = 1.1//1.45,
				},
				Body2 = new Body2
				{
					FontFamily = ["Inter", "sans-serif"],
					FontSize = "10px", //"14px",
					FontWeight = 400, //350,
					LineHeight = 1.1 //1.5,
				},
				Subtitle1 = new Subtitle1
				{
					FontFamily = ["Inter", "sans-serif"],
					FontSize = "16px",
					FontWeight = 500,
					LineHeight = 1.1,
				},
				Subtitle2 = new Subtitle2
				{
					FontFamily = ["Inter", "sans-serif"],
					FontSize = "14px",//"0.875rem",
					LineHeight = 1.1,
					FontWeight = 500,
				},
				Input = new Input()
				{
					FontFamily = ["Inter", "sans-serif"],
					FontWeight = 500,
					FontSize = "12px",
					LineHeight = 1.1,
					LetterSpacing = "0"
				},
				Caption = new Caption()
				{
					FontFamily = ["Inter", "sans-serif"],
					FontWeight = 500,
					FontSize = "12px",
					LineHeight = 1.1,
					LetterSpacing = "0"
				}
			},
			LayoutProperties = new LayoutProperties()
			{
				AppbarHeight = "55px"
			},
			PaletteLight = new PaletteLight()
			{
				Primary = new MudBlazor.Utilities.MudColor("rgba(253, 117, 89, 1)"),
				Background = new MudBlazor.Utilities.MudColor(241, 243, 249, 255),
				Error = new MudBlazor.Utilities.MudColor("#FF1323")
			},
			PaletteDark = new PaletteDark()
			{
				Primary = new MudBlazor.Utilities.MudColor("rgba(253, 117, 89, 1)"),
				TableStriped = new MudBlazor.Utilities.MudColor("rgba(255,255,255,0.05)"),
				Error = new MudBlazor.Utilities.MudColor("#FF1323"),
			}
		};

		services.AddSingleton(CustomTheme);
	}
}