using System.Data;
using Dapper;
using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.Shared;
using Teslametrics.App.Web.Services.Persistence;

namespace Teslametrics.App.Web.Features.Main.IncidentsDashboard.IncidentsCount;

public static class GetIncidentsCountUseCase
{
    public record Query(DateTimeOffset DateFrom, DateTimeOffset DateTo, Guid? CityId, Guid? BuildingId, Guid? FloorId, Guid? RoomId, Guid? FridgeId) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public List<Incident> Incidents { get; init; }
        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(List<Incident> incidents)
        {
            Incidents = incidents;

            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Incidents = [];
            Result = result;
        }

        /// <summary>
        /// Представляет количество инцидентов за определенный день
        /// </summary>
        /// <param name="Date">Дата (без времени)</param>
        /// <param name="Count">Количество инцидентов за этот день</param>
        public record Incident(DateTime Date, int Count);
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError
    }

    public class Validator : AbstractValidator<Query>
    {
        public Validator()
        {
        }
    }

    public class Handler : IRequestHandler<Query, Response>
    {
        private readonly IValidator<Query> _validator;
        private readonly IDbConnection _dbConnection;

        public Handler(IValidator<Query> validator,
                       IDbConnection dbConnection)
        {
            _validator = validator;
            _dbConnection = dbConnection;
        }

        public async Task<Response> Handle(Query request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            var template = SqlQueryBuilder.Create()
                .Select(Db.Incidents.Props.CreatedAt)
                .Where(Db.Incidents.Props.CreatedAt, ":DateFrom", SqlOperator.GreaterThanOrEqual, new { request.DateFrom })
                .Where(Db.Incidents.Props.CreatedAt, ":DateTo", SqlOperator.LessThanOrEqual, new { request.DateTo })
                .WhereIf(request.CityId is not null, Db.Incidents.Props.CityId, ":CityId", SqlOperator.Equals, new { request.CityId })
                .WhereIf(request.BuildingId is not null, Db.Incidents.Props.BuildingId, ":BuildingId", SqlOperator.Equals, new { request.BuildingId })
                .WhereIf(request.FloorId is not null, Db.Incidents.Props.FloorId, ":FloorId", SqlOperator.Equals, new { request.FloorId })
                .WhereIf(request.RoomId is not null, Db.Incidents.Props.RoomId, ":RoomId", SqlOperator.Equals, new { request.RoomId })
                .WhereIf(request.FridgeId is not null, Db.Incidents.Props.DeviceId, ":FridgeId", SqlOperator.Equals, new { request.FridgeId })
                .Build(QueryType.Standard, Db.Incidents.Table, RowSelection.AllRows);

            var incidentModels = await _dbConnection.QueryAsync<IncidentModel>(template.RawSql, template.Parameters);

            // Группируем инциденты по дате (без времени) и считаем количество
            var incidents = incidentModels
                .GroupBy(i => i.CreatedAt.ToLocalTime().Date)
                .Select(g => new Response.Incident(g.Key, g.Count()))
                .OrderBy(i => i.Date)
                .ToList();

            return new Response(incidents);
        }
    }
}

public record IncidentModel(DateTimeOffset CreatedAt);