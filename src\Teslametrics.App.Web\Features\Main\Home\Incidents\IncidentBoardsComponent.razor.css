::deep .incident_time {
    color: #B8C2CC;
}

::deep .dotted {
    display: flex;
    align-items: center;
    gap: 8px;
}

::deep .dotted::before {
    border-radius: 50%;
    content: " ";
    display: block;
    width: 9px;
    height: 9px;
}

.mud_theme_light div ::deep .dotted::before {
    background: rgba(184, 194, 204, 1);
}

.mud_theme_dark div ::deep .dotted::before {
    background: rgb(83 88 93);
}

div ::deep .dotted.primary::before {
    background: var(--mud-palette-primary);
}


@media screen and (max-width: 767px) {
    ::deep .incidents {
        flex-direction: column !important;
	}
}