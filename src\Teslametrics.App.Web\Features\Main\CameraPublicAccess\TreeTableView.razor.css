::deep .table {
    height: 100%;
    display: grid;
    grid-template-rows: auto 1fr auto;
    overflow: hidden;
}

::deep .tree-table-container {
    width: 100%;
    overflow: auto;
}

.tree-indent {
    display: flex;
    align-items: center;
    position: relative;
}

::deep.tree-indent button {
    z-index: 1;
}

.tree-leaf-indent {
    width: 26px;
}

.tree-line-container {
    display: flex;
    align-items: center;
    position: absolute;
    left: -24px;
    height: 100%;
    z-index: 0;
}

.tree-line {
    width: 24px;
    position: relative;
}

.tree-line:not(.tree-line-horizontal) {
    top: -31px;
    height: 62px;
}

.tree-line::before {
    content: '';
    position: absolute;
    left: 50%;
    top: 0;
    bottom: 0;
    width: 1px;
    background-color: var(--mud-palette-lines-default);
    opacity: 0;
}

.tree-line-visible::before {
    opacity: 1;
}

.tree-line-horizontal::after {
    content: '';
    position: absolute;
    left: -12px;
    top: 50%;
    width: 20px;
    height: 1px;
    background-color: var(--mud-palette-lines-default);
    z-index: 0;
}

.tree-table-container {
    width: 100%;
    overflow: auto;
}