﻿using ArchUnitNET.Domain;
using ArchUnitNET.Loader;
using ArchUnitNET.xUnit;
using MediatR;
using ArchUnitNET.Domain.Extensions;
using Teslametrics.App.Web.Abstractions;
using static ArchUnitNET.Fluent.ArchRuleDefinition;

namespace ArchTests;

public class UseCaseTests
{
    private static readonly Architecture Architecture;

    private static readonly IObjectProvider<Class> UseCases;
    private static readonly IObjectProvider<Class> UseCaseRequests;
    private static readonly IObjectProvider<Class> UseCaseResponses;
    private static readonly IObjectProvider<Class> UseCaseHandlers;

    private static readonly IObjectProvider<Class> Functions;
    public static readonly IObjectProvider<Class> FunctionHandlers;

    private static readonly Class BaseRequestType;
    private static readonly Class BaseResponseType;
    private static readonly Interface MediatRRequestHandlerType;

    static UseCaseTests()
    {
        Architecture = new ArchLoader().LoadAssemblies(
            System.Reflection.Assembly.Load("Teslametrics.App.Web")
        ).Build();

        BaseRequestType = Architecture.GetClassOfType(typeof(BaseRequest<>));

        BaseResponseType = Architecture.GetClassOfType(typeof(BaseResponse));

        MediatRRequestHandlerType = Architecture.GetInterfaceOfType(typeof(IRequestHandler<,>));

        UseCases = Classes()
            .That()
            .AreAbstract()
            .And()
            .AreSealed()
            .And()
            .HaveNameEndingWith("UseCase")
            .As("Use Cases");

        UseCaseRequests = Classes()
            .That()
            .AreRecord()
            .And()
            .AreAssignableTo(BaseRequestType)
            .And()
            .AreNestedIn(UseCases)
            .As("Use Case Requests");

        UseCaseResponses = Classes()
            .That()
            .AreRecord()
            .And()
            .AreAssignableTo(BaseResponseType)
            .And()
            .AreNestedIn(UseCases)
            .As("Use Case Responses");

        UseCaseHandlers = Classes()
            .That()
            .ImplementInterface(MediatRRequestHandlerType)
            .And()
            .AreNestedIn(UseCases)
            .As("Use Case Handlers");

        Functions = Classes()
            .That()
            .AreAbstract()
            .And()
            .AreSealed()
            .And()
            .HaveNameEndingWith("Function")
            .As("Functions");

        FunctionHandlers = Classes()
            .That()
            .ImplementInterface(MediatRRequestHandlerType)
            .And()
            .AreNestedIn(UseCases)
            .As("Use Case Handlers");
    }

    [Fact]
    public void RequestShouldBeRecord() =>
        Classes()
        .That()
        .AreAssignableTo(BaseRequestType)
        .Should()
        .BeRecord()
        .Check(Architecture);

    [Fact]
    public void ResponseShouldBeRecord() =>
        Classes()
        .That()
        .AreAssignableTo(BaseResponseType)
        .Should()
        .BeRecord()
        .Check(Architecture);

    [Fact]
    public void UseCaseShouldBeStatic() =>
        Classes()
        .That()
        .HaveNameEndingWith("UseCase")
        .Should()
        .BeAbstract()
        .AndShould()
        .BeSealed()
        .Check(Architecture);

    [Fact]
    public void RequestShouldBeNestedInUseCaseOrInFunction() =>
        Classes()
        .That()
        .AreAssignableTo(BaseRequestType)
        .And()
        .AreNot(BaseRequestType)
        .Should()
        .BeNestedIn(UseCases)
        .OrShould()
        .BeNestedIn(Functions)
        .Check(Architecture);

    [Fact]
    public void RequestShouldFollowNamingConvention() =>
        Classes()
        .That()
        .Are(UseCaseRequests)
        .Should()
        .HaveName("Request")
        .OrShould()
        .HaveName("Query")
        .OrShould()
        .HaveName("Command")
        .Check(Architecture);

    [Fact]
    public void ResponseShouldBeNestedInUseCaseOrInFunction() =>
        Classes()
        .That()
        .AreAssignableTo(BaseResponseType)
        .And()
        .AreNot(BaseResponseType)
        .Should()
        .BeNestedIn(UseCases)
        .OrShould()
        .BeNestedIn(Functions)
        .Check(Architecture);

    [Fact]
    public void ResponseShouldFollowNamingConvention() =>
        Classes()
        .That()
        .Are(UseCaseResponses)
        .Should()
        .HaveName("Response")
        .Check(Architecture);

    [Fact]
    public void HandlerShouldBeNestedInUseCaseOrInFunction() =>
        Classes()
        .That()
        .AreAssignableTo(MediatRRequestHandlerType)
        .Should()
        .BeNestedIn(UseCases)
        .OrShould()
        .BeNestedIn(Functions)
        .Check(Architecture);

    [Fact]
    public void HandlerShouldFollowNamingConvention() =>
        Classes()
        .That()
        .Are(UseCaseHandlers)
        .Or()
        .Are(FunctionHandlers)
        .Should()
        .HaveName("Handler")
        .Check(Architecture);
}
