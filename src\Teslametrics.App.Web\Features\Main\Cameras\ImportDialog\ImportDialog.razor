@using CsvHelper.Configuration
@using MudExtensions
@using MudExtensions.Utilities
@using Teslametrics.App.Web.Events.Cameras
@using Teslametrics.App.Web.Features.Main.Cameras.ImportDialog.QoutaSelect
@using Teslametrics.App.Web.Features.Main.Cameras.ImportDialog.CoordinatesSelector
@using Teslametrics.Shared
@inherits InteractiveBaseComponent
<MudDialog Visible="_isVisible"
           VisibleChanged="VisibilityChanged"
           ActionsClass="mx-2"
           ContentClass="px-0 mx-0"
           Options="_dialogOptions">
    <DialogContent>
        <MudTabs Elevation="0"
                 ApplyEffectsToContainer="true"
                 PanelClass="pt-6"
                 KeepPanelsAlive="true"
                 @bind-ActivePanelIndex="_activePanelIndex">
            <MudTabPanel Text="Маппинг модели">
                <div class="px-4">
                    <CsvMapperComponent TImportModel="CameraImportDto"
                                        TImportMap="ImportMap"
                                        Fields="@_expectedFields"
                                        OnParseException="OnError"
                                        GetImportMap="GetImportMap"
                                        OnImportCanceled="OnImportCanceled"
                                        OnImport="OnImport" />
                </div>
            </MudTabPanel>
            <MudTabPanel Text="Проверка сущностей"
                         Disabled="_importModels is null || !_importModels.Any()">
                @if (_hasValidationErrors)
                {
                    <MudAlert Severity="Severity.Warning"
                              Class="mb-4 mx-4">
                        <MudText>Обнаружены ошибки валидации в данных камер. Пожалуйста, исправьте их перед импортом.</MudText>
                    </MudAlert>
                }
                <div class="d_contents">
                    <MudDataGrid T="CameraImportDto"
                                 Items="@_importModels"
                                 ReadOnly="@(_importModels is null)"
                                 EditMode="@DataGridEditMode.Cell"
                                 SortMode="SortMode.None"
                                 Elevation="0"
                                 Virtualize="true"
                                 FixedHeader="true"
                                 Height="650px"
                                 HorizontalScrollbar="true"
                                 Hover="true"
                                 Striped="true"
                                 Class="overflow-hidden table"
                                 ItemSize="52.68f"
                                 ColumnResizeMode="ResizeMode.Container">
                        <Columns>
                            <PropertyColumn Property="x => x.Name"
                                            Title="Название">
                                <EditTemplate>
                                    <div class="field-with-error-icon">
                                        <MudTextField Value="context.Item.Name"
                                                      Placeholder="Введите название камеры *"
                                                      ValueChanged="async (string value) => { context.Item.Name = value; await ValidateAllModelsAsync(); }"
                                                      Immediate="true" />
                                        <ErrorTooltipComponent HasError="HasFieldError(context.Item, nameof(CameraImportDto.Name))"
                                                               ErrorMessages="GetFieldErrorMessages(context.Item, nameof(CameraImportDto.Name))" />
                                    </div>
                                </EditTemplate>
                            </PropertyColumn>
                            <PropertyColumn Property="x => x.Directory"
                                            Title="Директория">
                                <EditTemplate>
                                    <div class="field-with-error-icon">
                                        <MudTextField Value="context.Item.Directory"
                                                      Placeholder="Введите название директории *"
                                                      ValueChanged="async (string value) => { context.Item.Directory = value; await ValidateAllModelsAsync(); }"
                                                      Immediate="true" />
                                        <ErrorTooltipComponent HasError="HasFieldError(context.Item, nameof(CameraImportDto.Directory))"
                                                               ErrorMessages="GetFieldErrorMessages(context.Item, nameof(CameraImportDto.Directory))" />
                                    </div>
                                </EditTemplate>
                            </PropertyColumn>
                            <PropertyColumn Property="x => x.Quota"
                                            Title="Квота"
                                            CellStyle="width: 450px">
                                <EditTemplate>
                                    <QuotaFieldComponent Value="@context.Item.Quota"
                                                         ValueChanged="async (string value) => { context.Item.Quota = value; await ValidateAllModelsAsync(); }"
                                                         OrganizationId="_organizationId"
                                                         Error="@(context.Item.HasQuotaError || HasFieldError(context.Item, nameof(CameraImportDto.Quota)))"
                                                         ErrorText="@(context.Item.QuotaErrorMessage ?? (HasFieldError(context.Item, nameof(CameraImportDto.Quota)) ? "Поле должно быть заполнено" : null))" />
                                </EditTemplate>
                            </PropertyColumn>
                            <PropertyColumn Property="x => x.TimeZone"
                                            Title="Часовой пояс">
                                <EditTemplate>
                                    <TimeZoneSelector @bind-TimeZone="@context.Item.TimeZone" />
                                </EditTemplate>
                            </PropertyColumn>

                            <PropertyColumn Property="x => x.Coordinates"
                                            Title="Координаты">
                                <EditTemplate>
                                    <CoordinatesSelector Value="context.Item.Coordinates"
                                                         ValueChanged="async (Coordinates? value) => { context.Item.Coordinates = value; await ValidateAllModelsAsync(); }"
                                                         HasValidationError="@HasFieldError(context.Item, nameof(CameraImportDto.Coordinates))" />
                                </EditTemplate>
                            </PropertyColumn>
                            <PropertyColumn Property="x => x.ArchiveUri"
                                            Title="Ссылка на архивный поток"
                                            Required="true">
                                <EditTemplate>
                                    <div class="field-with-error-icon">
                                        <MudTextField Value="context.Item.ArchiveUri"
                                                      Placeholder="Введите ссылку на архивный поток *"
                                                      Validation="(object x) => _validator.ValidateValueAsync(context.Item, nameof(CameraImportDto.ArchiveUri))"
                                                      ValueChanged="async (string value) => { context.Item.ArchiveUri = value; await ValidateAllModelsAsync(); }"
                                                      Immediate="true" />
                                        <ErrorTooltipComponent HasError="HasFieldError(context.Item, nameof(CameraImportDto.ArchiveUri))"
                                                               ErrorMessages="GetFieldErrorMessages(context.Item, nameof(CameraImportDto.ArchiveUri))" />
                                    </div>
                                </EditTemplate>
                            </PropertyColumn>
                            <PropertyColumn Property="x => x.ViewUri"
                                            Title="Ссылка на поток для видов"
                                            Required="true">
                                <EditTemplate>
                                    <div class="field-with-error-icon">
                                        <MudTextField Value="context.Item.ViewUri"
                                                      Placeholder="Введите ссылку на поток для видов *"
                                                      Validation="(object x) => _validator.ValidateValueAsync(context.Item, nameof(CameraImportDto.ViewUri))"
                                                      ValueChanged="async (string value) => { context.Item.ViewUri = value; await ValidateAllModelsAsync(); }"
                                                      Immediate="true" />
                                        <ErrorTooltipComponent HasError="HasFieldError(context.Item, nameof(CameraImportDto.ViewUri))"
                                                               ErrorMessages="GetFieldErrorMessages(context.Item, nameof(CameraImportDto.ViewUri))" />
                                    </div>
                                </EditTemplate>
                            </PropertyColumn>
                            <PropertyColumn Property="x => x.PublicUri"
                                            Title="Ссылка на публичный поток"
                                            Required="true">
                                <EditTemplate>
                                    <div class="field-with-error-icon">
                                        <MudTextField Value="context.Item.PublicUri"
                                                      Placeholder="Введите ссылку на публичный поток *"
                                                      Validation="(object x) => _validator.ValidateValueAsync(context.Item, nameof(CameraImportDto.PublicUri))"
                                                      ValueChanged="async (string value) => { context.Item.PublicUri = value; await ValidateAllModelsAsync(); }"
                                                      Immediate="true" />
                                        <ErrorTooltipComponent HasError="HasFieldError(context.Item, nameof(CameraImportDto.PublicUri))"
                                                               ErrorMessages="GetFieldErrorMessages(context.Item, nameof(CameraImportDto.PublicUri))" />
                                    </div>
                                </EditTemplate>
                            </PropertyColumn>
                            <PropertyColumn Property="x => x.AutoStart"
                                            Title="Автозапуск при перезапуске системы">
                                <HeaderTemplate>
                                    <MudCheckBox T="bool"
                                                 Value="@(_importModels?.All(x => x.AutoStart) ?? false)"
                                                 ValueChanged="(bool isChecked) => OnAutoStartChanged(isChecked)"
                                                 Label="Автозапуск при перезапуске системы" />
                                </HeaderTemplate>
                                <EditTemplate>
                                    <MudCheckBox @bind-Value="context.Item.AutoStart" />
                                </EditTemplate>
                            </PropertyColumn>
                            <PropertyColumn Property="x => x.StartOnCreate"
                                            Title="Запуск при создании">
                                <HeaderTemplate>
                                    <MudCheckBox T="bool"
                                                 Value="@(_importModels?.All(x => x.StartOnCreate) ?? false)"
                                                 ValueChanged="(bool isChecked) => OnStartOnCreateChanged(isChecked)"
                                                 Label="Запуск при создании" />
                                </HeaderTemplate>
                                <EditTemplate>
                                    <MudCheckBox @bind-Value="context.Item.StartOnCreate" />
                                </EditTemplate>
                            </PropertyColumn>
                        </Columns>
                        <PagerContent>
                            <div class="pager">
                                <MudDataGridPager T="CameraImportDto"
                                                  Class="sticky"
                                                  RowsPerPageString="Строк на странице:"
                                                  InfoFormat="{first_item}-{last_item} из {all_items}" />
                            </div>
                        </PagerContent>
                    </MudDataGrid>
                </div>
            </MudTabPanel>
        </MudTabs>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="Cancel"
                   Variant="Variant.Text">Отменить</MudButton>
        <MudButton OnClick="SubmitAsync"
                   Variant="Variant.Outlined"
                   Disabled="@(!_importModels?.Any() ?? true || IsLoading || _hasValidationErrors)"
                   Color="Color.Secondary">Импорт
        </MudButton>
    </DialogActions>
</MudDialog>
