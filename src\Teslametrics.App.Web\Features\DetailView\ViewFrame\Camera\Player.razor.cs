using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using MudBlazor;


namespace Teslametrics.App.Web.Features.DetailView.ViewFrame.Camera;

public partial class Player : IAsyncDisposable
{
	private IJSObjectReference? _playerJsModule;
	private DotNetObjectReference<Player>? _objRef;

	#region [Injects]
	[Inject]
	private ILogger<Player> _logger { get; set; } = null!;

	[Inject]
	public IJSRuntime Js { get; set; } = null!;

	[Inject]
	public ISnackbar Snackbar { get; set; } = null!;
	#endregion

	#region [Parameter]
	[Parameter]
	[EditorRequired]
	public string Id { get; set; } = null!;

	[Parameter]
	[EditorRequired]
	public Guid CameraId { get; set; }

	[Parameter]
	public string PathToFile { get; set; } = null!;
	#endregion

	[JSInvokable]
	public void ShowError(string message)
	{
		Snackbar.Add(message, MudBlazor.Severity.Error);
	}

	public async ValueTask DisposeAsync()
	{
		try
		{
			if (_playerJsModule is not null)
			{
				await _playerJsModule.InvokeVoidAsync($"stopPlayer", Id, CameraId);
				await _playerJsModule.DisposeAsync();
			}
		}
		catch (JSDisconnectedException) // А всё. Соединения нету. https://learn.microsoft.com/en-us/aspnet/core/blazor/javascript-interoperability/?view=aspnetcore-9.0
		{
		}
	}
	protected override void OnInitialized()
	{
		_objRef = DotNetObjectReference.Create(this);
	}

	protected override async Task OnAfterRenderAsync(bool firstRender)
	{
		await base.OnAfterRenderAsync(firstRender);
		if (firstRender)
		{
			try
			{
				_playerJsModule = await Js.InvokeAsync<IJSObjectReference>("import", "./Features/DetailView/ViewFrame/Camera/Player.razor.js");
				if (_playerJsModule is not null)
				{
					await _playerJsModule.InvokeVoidAsync($"initializePlayer", _objRef, Id, CameraId);
				}
			}
			catch (Exception exc)
			{
				_logger.LogError(exc, exc.Message);
				Snackbar.Add("Не удалось загрузить необходимые файлы скриптов, плеер будет недоступен!", MudBlazor.Severity.Error);
			}
		}
	}

	protected override async Task OnParametersSetAsync()
	{
		await base.OnParametersSetAsync();
		if (_playerJsModule is not null)
		{
			await _playerJsModule.InvokeVoidAsync($"updatePlayer", Id, CameraId);
		}
	}
}