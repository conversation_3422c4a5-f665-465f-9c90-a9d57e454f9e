﻿@using Teslametrics.Shared
<div class="d_contents">
    <MudDrawer Open="Open"
               OpenChanged="OpenChanged"
               Elevation="1"
               Variant="@(_mobile ? DrawerVariant.Temporary : DrawerVariant.Persistent)"
               Overlay="@_mobile"
               OverlayAutoClose="true"
               UserAttributes="@(new Dictionary<string, object>() { { "id", "layout_sidebar" } })">
        <MudNavMenu Rounded="true"
                    Margin="Margin.Normal"
                    Color="Color.Primary"
                    Class="nav_menu pt-6">
            <MudNavLink Href="/"
                        Icon="@TeslaIcons.PageIcons.Dashboard"
                        Match="NavLinkMatch.All"
                        Class="nav_link px-2"
                        IconColor="Color.Inherit"
                        title="Главная">
                Главная
            </MudNavLink>
            <AuthorizeView Policy="@(Fqdn<AppPermissions>.GetNames(AppPermissions.Main.AccessControl.Organizations.Read).Last())">
                <MudNavLink Href="/system-settings"
                            Icon="@Icons.Material.Outlined.Map"
                            Match="NavLinkMatch.All"
                            Class="nav_link px-2"
                            IconColor="Color.Inherit"
                            title="Главная">
                    Планы
                </MudNavLink>
            </AuthorizeView>
            <MudNavLink Href="/devices"
                        Icon="@TeslaIcons.Devices.Fridge"
                        Match="NavLinkMatch.Prefix"
                        Class="nav_link px-2"
                        IconColor="Color.Inherit"
                        title="Устройства">
                Оборудование
            </MudNavLink>
            <MudNavLink Href="/incidents"
                        Icon="@TeslaIcons.State.Warning"
                        Match="NavLinkMatch.Prefix"
                        Class="nav_link px-2"
                        IconColor="Color.Inherit"
                        title="Происшествия">
                Происшествия
            </MudNavLink>
            <MudNavLink Href="/incidents-dashboard"
                        Icon="@TeslaIcons.PageIcons.Analytics"
                        Match="NavLinkMatch.Prefix"
                        Class="nav_link px-2"
                        IconColor="Color.Inherit"
                        title="Аналитика">
                Аналитика
            </MudNavLink>
            <AuthorizeView Policy="@AppPermissions.Main.CameraViews.Read.GetEnumPermissionString()">
                <MudNavLink Href="@RouteConstants.CameraViews"
                            Icon="@Icons.Material.Filled.ViewModule"
                            Match="NavLinkMatch.Prefix"
                            Class="nav_link px-2"
                            IconColor="Color.Inherit">
                    Виды
                </MudNavLink>
            </AuthorizeView>
            <AuthorizeView Policy="@($"{AppPermissions.Main.Cameras.Read.GetEnumPermissionString()},{AppPermissions.Main.Folders.Read.GetEnumPermissionString()}")">
                <MudNavLink Href="@RouteConstants.Cameras"
                            Icon="@Icons.Material.Outlined.Camera"
                            Match="NavLinkMatch.Prefix"
                            Class="nav_link px-2"
                            IconColor="Color.Inherit"
                            title="Камеры">
                    Камеры
                </MudNavLink>
            </AuthorizeView>
            @* <AuthorizeView Policy="@(Fqdn<AppPermissions>.GetNames(AppPermissions.Main.AccessControl.Organizations.Read).Last())">
								<MudNavLink Href="@RouteConstants.Organizations" Icon="@Icons.Material.Filled.Business" Match="NavLinkMatch.Prefix"
									Class="nav_link px-2" IconColor="Color.Inherit" title="Организации">
									Организации
								</MudNavLink>
							</AuthorizeView> *@
            <AuthorizeView Policy="@(Fqdn<AppPermissions>.GetNames(AppPermissions.Main.AccessControl.Organizations.Read).Last())">
                <MudNavLink Href="@RouteConstants.AccessControl"
                            Icon="@Icons.Material.Outlined.Key"
                            Match="NavLinkMatch.Prefix"
                            Class="nav_link px-2"
                            IconColor="Color.Inherit"
                            title="Контроль доступа">
                    Контроль доступа
                </MudNavLink>
            </AuthorizeView>
            <AuthorizeView Policy="@AppPermissions.Main.CameraPresets.Read.GetEnumPermissionString()">
                <MudNavLink Href="@RouteConstants.CameraPresets"
                            Icon="@Icons.Material.Filled.Pattern"
                            Match="NavLinkMatch.Prefix"
                            Class="nav_link px-2"
                            IconColor="Color.Inherit"
                            title="Пресеты">
                    Пресеты
                </MudNavLink>
            </AuthorizeView>
            <AuthorizeView Policy="@AppPermissions.Main.CameraPublicAccess.Read.GetEnumPermissionString()">
                <MudNavLink Href="@RouteConstants.CameraPublicAccess"
                            Icon="@Icons.Material.Filled.Public"
                            Match="NavLinkMatch.Prefix"
                            Class="nav_link px-2"
                            IconColor="Color.Inherit"
                            title="Публичный доступ">
                    Публичный доступ
                </MudNavLink>
            </AuthorizeView>
        </MudNavMenu>
    </MudDrawer>
</div>