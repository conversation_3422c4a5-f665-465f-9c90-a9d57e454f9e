using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.MediaServer.Orleans.Camera;

namespace Teslametrics.App.Web.Features.Main.Cameras.TreeView.Camera;

public static class DisconnectCameraUseCase
{
	public record Command(Guid Id) : BaseRequest<Response>;

	public record Response : BaseResponse
	{
		public Result Result { get; init; }

		public bool IsSuccess => Result == Result.Success;

		public Response(Result result)
		{
			Result = result;
		}
	}

	public enum Result
	{
		Unknown = 0,
		Success,
		ValidationError,
		CameraNotFound
	}

	public class Validator : AbstractValidator<Command>
	{
		public Validator()
		{
			RuleFor(c => c.Id).NotEmpty();
		}
	}

	public class Handler : IRequestHandler<Command, Response>
	{
		private readonly IValidator<Command> _validator;
		private readonly IClusterClient _clusterClient;

		public Handler(IValidator<Command> validator,
					   IClusterClient clusterClient)
		{
			_validator = validator;
			_clusterClient = clusterClient;
		}

		public async Task<Response> Handle(Command request, CancellationToken cancellationToken)
		{
			if (!_validator.Validate(request).IsValid)
			{
				return new Response(Result.ValidationError);
			}

			var mediaServerGrain = _clusterClient.GetGrain<IMediaServerGrain>(Guid.Empty);
			await mediaServerGrain.DisconnectAsync(new IMediaServerGrain.CameraDisconnectRequest(request.Id));


			return new Response(Result.Success);
		}
	}
}