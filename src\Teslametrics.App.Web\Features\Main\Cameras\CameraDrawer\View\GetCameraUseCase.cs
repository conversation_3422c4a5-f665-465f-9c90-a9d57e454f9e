using Dapper;
using FluentValidation;
using MediatR;
using System.Data;
using System.Text.Json;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.MediaServer.Orleans.Camera;
using Teslametrics.Core.Services.Persistence;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Features.Main.Cameras.CameraDrawer.View;

public static class GetCameraUseCase
{
    public record Query(Guid Id) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public Guid Id { get; init; }

        public string Name { get; init; }

        public TimeSpan TimeZone { get; init; }

        public Coordinates? Coordinates { get; init; }

        public string ArchiveUri { get; init; }

        public string ViewUri { get; init; }

        public string PublicUri { get; init; }

        public bool AutoStart { get; init; }

        public CameraStatus CameraStatus { get; init; }

        public string QuotaName { get; init; }

        public StreamConfig? ArchiveStreamConfig { get; init; }

        public StreamConfig? ViewStreamConfig { get; init; }

        public StreamConfig? PublicStreamConfig { get; init; }

        public bool IsBlocked { get; init; }

        public bool OnvifEnabled { get; init; }

        public OnvifSettings? OnvifOptions { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(Guid id,
                        string name,
                        TimeSpan timeZone,
                        Coordinates? coordinates,
                        string archiveUri,
                        string viewUri,
                        string publicUri,
                        bool autoStart,
                        CameraStatus cameraStatus,
                        string quotaName,
                        StreamConfig? archiveStreamConfig,
                        StreamConfig? viewStreamConfig,
                        StreamConfig? publicStreamConfig,
                        bool isBlocked,
                        bool onvifEnabled,
                        OnvifSettings? onvifOptions)
        {
            Id = id;
            Name = name;
            TimeZone = timeZone;
            Coordinates = coordinates;
            ArchiveUri = archiveUri;
            ViewUri = viewUri;
            PublicUri = publicUri;
            AutoStart = autoStart;
            CameraStatus = cameraStatus;
            QuotaName = quotaName;
            ArchiveStreamConfig = archiveStreamConfig;
            ViewStreamConfig = viewStreamConfig;
            PublicStreamConfig = publicStreamConfig;
            IsBlocked = isBlocked;
            OnvifEnabled = onvifEnabled;
            OnvifOptions = onvifOptions;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Result = result;

            Id = Guid.Empty;
            Name = string.Empty;
            ArchiveUri = string.Empty;
            ViewUri = string.Empty;
            PublicUri = string.Empty;
            QuotaName = string.Empty;
            ArchiveStreamConfig = null;
            ViewStreamConfig = null;
            PublicStreamConfig = null;
            IsBlocked = false;
            OnvifEnabled = false;
            OnvifOptions = null;
        }

        public record StreamConfig(Resolution Resolution, VideoCodec VideoCodec, FrameRate FrameRate, SceneDynamic SceneDynamic, AudioCodec AudioCodec);
        public record OnvifSettings(string Host, int Port, string Username, string Password);
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        CameraNotFound
    }

    public class Validator : AbstractValidator<Query>
    {
        public Validator()
        {
            RuleFor(q => q.Id).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Query, Response>
    {
        private readonly IValidator<Query> _validator;
        private readonly IDbConnection _dbConnection;
        private readonly IClusterClient _clusterClient;

        public Handler(IValidator<Query> validator,
                       IDbConnection dbConnection,
                       IClusterClient clusterClient)
        {
            _validator = validator;
            _dbConnection = dbConnection;
            _clusterClient = clusterClient;
        }

        public async Task<Response> Handle(Query request, CancellationToken cancellationToken = default)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            var template = SqlQueryBuilder.Create()
                .Select(Db.Cameras.Props.Id)
                .Select(Db.Cameras.Props.Name)
                .Select(Db.Cameras.Props.TimeZone)
                .Select(Db.Cameras.Props.Latitude)
                .Select(Db.Cameras.Props.Longitude)
                .Select(Db.Cameras.Props.ArchiveUri)
                .Select(Db.Cameras.Props.ViewUri)
                .Select(Db.Cameras.Props.PublicUri)
                .Select(Db.Cameras.Props.AutoStart)
                .Select(Db.CameraQuotas.Props.Name, "quota_name")
                .Select(Db.Presets.Props.ArchiveStreamConfig)
                .Select(Db.Presets.Props.ViewStreamConfig)
                .Select(Db.Presets.Props.PublicStreamConfig)
                .Select(Db.Cameras.Props.IsBlocked)
                .Select(Db.Cameras.Props.OnvifEnabled)
                .Select(Db.Cameras.Props.OnvifSettings)
                .LeftJoin(Db.CameraQuotas.Table, Db.CameraQuotas.Props.Id, Db.Cameras.Props.QuotaId, SqlOperator.Equals)
                .LeftJoin(Db.Presets.Table, Db.Presets.Props.Id, Db.CameraQuotas.Props.PresetId, SqlOperator.Equals)
                .Where(Db.Cameras.Props.Id, ":Id", SqlOperator.Equals, new { request.Id })
                .Build(QueryType.Standard, Db.Cameras.Table, RowSelection.AllRows);

            var camera = await _dbConnection.QuerySingleOrDefaultAsync<CameraModel>(template.RawSql, template.Parameters);

            if (camera is null)
            {
                return new Response(Result.CameraNotFound);
            }

            var grain = _clusterClient.GetGrain<IMediaServerGrain>(Guid.Empty);
            var statusResponse = await grain.GetStatusesAsync(new IMediaServerGrain.GetCameraStatusesRequest([request.Id]));
            var status = statusResponse.Statuses[request.Id];

            return new Response(camera.Id,
                                camera.Name,
                                camera.TimeZone,
                                camera.Latitude.HasValue && camera.Longitude.HasValue
                                    ? new Coordinates(camera.Latitude.Value, camera.Longitude.Value)
                                    : null,
                                camera.ArchiveUri,
                                camera.ViewUri,
                                camera.PublicUri,
                                camera.AutoStart,
                                status,
                                camera.QuotaName,
                                ToStreamConfigResponse(camera.ArchiveStreamConfig),
                                ToStreamConfigResponse(camera.ViewStreamConfig),
                                ToStreamConfigResponse(camera.PublicStreamConfig),
                                camera.IsBlocked,
                                camera.OnvifEnabled,
                                camera.OnvifSettings is not null ? JsonSerializer.Deserialize<Response.OnvifSettings>(camera.OnvifSettings) : null);
        }

        private static Response.StreamConfig? ToStreamConfigResponse(string streamConfig)
        {
            if (string.IsNullOrEmpty(streamConfig))
            {
                return null;
            }

            var streamConfigModel = JsonSerializer.Deserialize<Response.StreamConfig>(streamConfig);
            return new Response.StreamConfig(streamConfigModel!.Resolution, streamConfigModel.VideoCodec, streamConfigModel.FrameRate, streamConfigModel.SceneDynamic, streamConfigModel.AudioCodec);
        }
    }

    public record CameraModel(Guid Id,
                              string Name,
                              TimeSpan TimeZone,
                              double? Latitude,
                              double? Longitude,
                              string ArchiveUri,
                              string ViewUri,
                              string PublicUri,
                              bool AutoStart,
                              string QuotaName,
                              string ArchiveStreamConfig,
                              string ViewStreamConfig,
                              string PublicStreamConfig,
                              bool IsBlocked,
                              bool OnvifEnabled,
                              string? OnvifSettings);
}