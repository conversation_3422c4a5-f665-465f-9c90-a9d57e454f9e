﻿using FluentValidation;

namespace Teslametrics.App.Web.Abstractions;

public interface IBaseFluentValidator<T> : IValidator<T>
{
    Func<object, string, Task<IEnumerable<string>>> ValidateValue { get; }

    /// <summary>
    /// Данный метод незаменим для валидации значений в inline/dialog формах компонентов MudTable и MudDataGrid
    /// </summary>
    /// <example>
    ///		<code>
    ///			<PropertyColumn Property="x=>x.Field">
    ///				<EditTemplate>
    ///					<MudTextField @bind-Value="context.Item.Field" Validation=@(async (object value) => await Validator.ValidateValueAsync(context.Item, nameof(Model.Field))) />
    ///				</EditTemplate>
    ///			</PropertyColumn>
    ///		</code>
    /// </example>
    /// <param name="model"></param>
    /// <param name="propertyName"></param>
    /// <returns></returns>
    Task<IEnumerable<string>> ValidateValueAsync(object model, string propertyName);
}
