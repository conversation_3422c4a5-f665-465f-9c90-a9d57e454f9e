using System.Reactive;
using Microsoft.AspNetCore.Components;
using Teslametrics.App.Web.Exceptions;
using Teslametrics.App.Web.Extensions;

namespace Teslametrics.App.Web.Features.Main.Notifications;

public partial class NotificationsPage
{
    private SubscribeNotificationUseCase.Response? _subscriptionResult;
    private GetNotificationListUseCase.Response? _response;


    [Inject]
    private NavigationManager _navigationManager { get; set; } = null!;

    protected override async Task OnInitializedAsync()
    {
        await FetchDataAsync();
        await SubscribeAsync();
        await base.OnInitializedAsync();
    }

    private async Task MarkAllReadAsync()
    {
        MarkAllNotificationsAsReadedUseCase.Response? response = null;
        try
        {
            response = await ScopeFactory.MediatorSend(new MarkAllNotificationsAsReadedUseCase.Command(await GetCurrentUserIdAsync() ?? throw new NotAuthorizedException()));
        }
        catch (Exception ex)
        {
            response = null;
            Logger.LogError(ex, ex.Message);
            Snackbar.Add("Не удалось отметить все уведомления как прочитанные из-за ошибки на сервере. Обратитесь к администратору", MudBlazor.Severity.Error);
        }

        if (response is null) return;

        switch (response.Result)
        {
            case MarkAllNotificationsAsReadedUseCase.Result.Success:
                await FetchDataAsync();
                await UpdateViewAsync();
                break;
            case MarkAllNotificationsAsReadedUseCase.Result.ValidationError:
                Snackbar.Add("Ошибка валидации при отметке всех уведомлений как прочитанных", MudBlazor.Severity.Error);
                break;
            case MarkAllNotificationsAsReadedUseCase.Result.Unknown:
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(NotificationsPage), nameof(MarkAllNotificationsAsReadedUseCase));
                Snackbar.Add($"Не удалось отметить все уведомления как прочитанные из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
                break;
            default:
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(NotificationsPage), nameof(MarkAllNotificationsAsReadedUseCase), response.Result);
                Snackbar.Add($"Не удалось отметить все уведомления как прочитанные из-за ошибки: {response.Result}", MudBlazor.Severity.Error);
                break;
        }
    }


    private async Task SubscribeAsync()
    {
        Unsubscribe();

        try
        {
            var userId = await GetCurrentUserIdAsync() ?? throw new NotAuthorizedException();
            _subscriptionResult = await ScopeFactory.MediatorSend(new SubscribeNotificationUseCase.Request(Observer.Create<object>(OnAppEventHandler, OnError), userId));
        }
        catch (Exception ex)
        {
            _subscriptionResult = null;
            Snackbar.Add($"Не удалось получить подписку на события уведомлений из-за ошибки сообщения с сервером. Повторите попытку позже.", MudBlazor.Severity.Error);
            Logger.LogError(ex, ex.Message);
        }

        if (_subscriptionResult is null) return;
        switch (_subscriptionResult.Result)
        {
            case SubscribeNotificationUseCase.Result.Success:
                CompositeDisposable.Add(_subscriptionResult.Subscription!);
                break;
            case SubscribeNotificationUseCase.Result.ValidationError:
                Snackbar.Add("Ошибка валидации при подписке на события уведомлений", MudBlazor.Severity.Error);
                break;
            case SubscribeNotificationUseCase.Result.Unknown:
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(NotificationsPage), nameof(SubscribeNotificationUseCase));
                Snackbar.Add($"Не удалось получить подписку на события уведомлений из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
                break;
            default:
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(NotificationsPage), nameof(SubscribeNotificationUseCase), _subscriptionResult.Result);
                Snackbar.Add($"Не удалось получить подписку на события уведомлений из-за ошибки: {_subscriptionResult.Result}", MudBlazor.Severity.Error);
                break;
        }
    }

    private void Unsubscribe()
    {
        if (_subscriptionResult?.Subscription is not null)
        {
            CompositeDisposable.Remove(_subscriptionResult.Subscription);
            _subscriptionResult.Subscription.Dispose();
        }
    }

    private void ShowDetails(GetNotificationListUseCase.Response.Incident n)
    {
        _navigationManager.NavigateTo("/incidents/?IncidentId=" + n.Id);
    }


    private async Task FetchDataAsync()
    {

        try
        {
            _response = await ScopeFactory.MediatorSend(new GetNotificationListUseCase.Query(await GetCurrentUserIdAsync() ?? throw new NotAuthorizedException()));
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, ex.Message);
            Snackbar.Add("Не удалось получить список уведомлений из-за ошибки на сервере. Обратитесь к администратору", MudBlazor.Severity.Error);
        }

        switch (_response?.Result)
        {
            case GetNotificationListUseCase.Result.Success:
                break;
            case GetNotificationListUseCase.Result.ValidationError:
                Snackbar.Add("Ошибка валидации при получении списка уведомлений", MudBlazor.Severity.Error);
                break;
            case GetNotificationListUseCase.Result.Unknown:
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(NotificationsPage), nameof(GetNotificationListUseCase));
                Snackbar.Add($"Не удалось получить список уведомлений из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
                break;
            default:
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(NotificationsPage), nameof(GetNotificationListUseCase), _response?.Result);
                Snackbar.Add($"Не удалось получить список уведомлений из-за ошибки: {_response?.Result}", MudBlazor.Severity.Error);
                break;
        }
    }

    private async void OnAppEventHandler(object appEvent)
    {
        await FetchDataAsync();
        await UpdateViewAsync();
    }

    private void OnError(Exception exc)
    {
        Snackbar.Add("Ошибка при подписке на события", MudBlazor.Severity.Error);
        Logger.LogError(exc, exc.Message);
    }
}
