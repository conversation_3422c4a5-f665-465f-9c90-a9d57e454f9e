using MediatR;

namespace Teslametrics.App.Web.Extensions;

public static class ScopeFactoryExtensions
{
	public static async Task<TResponse> MediatorSend<TResponse>(this IServiceScopeFactory scopeFactory, IRequest<TResponse> request, CancellationToken cancellationToken = default)
	{
		var scope = scopeFactory.CreateScope();
		var response = await scope.ServiceProvider.GetRequiredService<IMediator>().Send(request, cancellationToken);
		scope.Dispose();
		return response;
	}
}
