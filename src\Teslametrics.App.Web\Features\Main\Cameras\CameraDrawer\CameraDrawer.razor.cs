using Teslametrics.App.Web.Events;
using Teslametrics.App.Web.Events.Cameras;

namespace Teslametrics.App.Web.Features.Main.Cameras.CameraDrawer;

public partial class CameraDrawer
{
	private Guid? _folderId;
	private Guid? _cameraId;

	private Guid _organizationId;
	private DrawerMode _mode;

	public bool IsOpened => _mode != DrawerMode.Hidden;

	public enum DrawerMode
	{
		Hidden,
		Create,
		Edit,
		View
	}

	protected override void OnInitialized()
	{
		CompositeDisposable.Add(EventSystem.Subscribe<CameraCreateEto>(OnEventHandler));
		CompositeDisposable.Add(EventSystem.Subscribe<CameraSelectEto>(OnEventHandler));
		CompositeDisposable.Add(EventSystem.Subscribe<CameraEditEto>(OnEventHandler));
		base.OnInitialized();
	}

	public void ShowCreate(Guid organizationId, Guid? folderId)
	{
		_organizationId = organizationId;
		_folderId = folderId;
		_mode = DrawerMode.Create;
		StateHasChanged();
	}

	public void ShowEdit(Guid organizationId, Guid cameraId)
	{
		_organizationId = organizationId;
		_cameraId = cameraId;
		_mode = DrawerMode.Edit;
		StateHasChanged();
	}
	public void ShowView(Guid organizationId, Guid cameraId)
	{
		_organizationId = organizationId;
		_cameraId = cameraId;
		_mode = DrawerMode.View;
		StateHasChanged();
	}

	public void Show(DrawerMode mode, Guid userId)
	{
		if (mode == DrawerMode.Hidden || mode == DrawerMode.Create)
		{
			throw new ArgumentException($"DrawerMode Edit or View expected {mode.ToString()} provided");
		}

		_mode = mode;
		_cameraId = userId;
	}

	public void Close()
	{
		_mode = DrawerMode.Hidden;
		_cameraId = null;
		StateHasChanged();
	}

	private void Show()
	{
		_mode = DrawerMode.Create;
		StateHasChanged();
	}

	#region [Event Handlers]
	private void OnEventHandler(BaseEto eto)
	{
		switch (eto)
		{
			case CameraCreateEto createEto:
				ShowCreate(createEto.OrganizationId, createEto.GroupId);
				break;
			case CameraEditEto editEto:
				ShowEdit(editEto.OrganizationId, editEto.CameraId);
				break;
			case CameraSelectEto selectEto:
				ShowView(selectEto.OrganizationId, selectEto.CameraId);
				break;
			default:
				throw new ArgumentException("Unknown event type", nameof(eto));
		}
	}

	private void OnOpenChanged(bool opened)
	{
		if (opened)
		{
			if (_mode != DrawerMode.Hidden && _cameraId.HasValue)
			{
				Show(_mode, _cameraId.Value);
			}
			else
			{
				Show();
			}
		}
		else
		{
			Close();
		}
	}
	#endregion [Event Handlers]
}
