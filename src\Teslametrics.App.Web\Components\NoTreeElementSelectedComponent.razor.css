::deep.content
{
	display: grid;
	grid-template-rows: auto 1fr;
}

::deep.view
{
	width: calc(16rem * 1.3);
	height: 16rem;
	transform: skew(-5deg);
	margin-bottom: 48px;
}

::deep.view .left .icon,
::deep.view .right .icon
{
	bottom: -5%;
	left: -5%;
	position: absolute;
	right: -5%;
	top: -5%;
	transform: skew(5deg);
}

::deep.view .right .icon
{
	left: -30%;
}

::deep.left,
::deep.right
{
	bottom: 0;
	overflow: hidden;
	position: absolute;
	top: 0;
}

::deep.left
{
	left: -5%;
	right: 50%;
}

::deep.divider
{
	bottom: -5%;
	left: calc(16rem * 0.65);
	position: absolute;
	top: -5%;
	z-index: 1;
	background-color: var(--mud-palette-primary);
	width: 6px;
}

::deep.right
{
	left: 50%;
	right: -10%;
}

::deep.sun,
::deep.moon
{
	bottom: -5%;
	left: -5%;
	position: absolute;
	right: -5%;
	top: -5%;
	transform: skew(5deg);
}