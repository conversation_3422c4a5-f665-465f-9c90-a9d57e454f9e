﻿@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Components.Forms
@inherits InteractiveBaseComponent
<MudStack Row="true"
		  AlignItems="AlignItems.Center"
		  Justify="Justify.SpaceBetween"
		  Class="mud-width-full pa-2">
	<MudText Inline="true"
			 Color="Color.Secondary">
		@_user?.Username
	</MudText>
	<MudAvatar Color="Color.Primary"
			   Variant="Variant.Outlined">
		<MudIcon Icon="@Icons.Material.Outlined.Person" />
	</MudAvatar>
</MudStack>