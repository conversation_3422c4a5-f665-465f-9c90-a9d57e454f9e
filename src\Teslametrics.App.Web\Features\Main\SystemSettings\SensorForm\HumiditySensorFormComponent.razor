@if (_model is not null)
{
	<MudTextField @bind-Value="_model.DisplayName" Label="Наименование датчика" Clearable="true" />
	<MudTextField @bind-Value="_model.Name" Label="Наименование топика" />
	<MudTextField @bind-Value="_model.MinHumidity" Label="Минимальная влажность" Clearable="true" />
	<MudTextField @bind-Value="_model.MaxHumidity" Label="Максимальная влажность" Clearable="true" />
}

@code {
	private HumidityModel? _model => SensorModel as HumidityModel;

	[Parameter]
	public ISensorModel? SensorModel { get; set; }
}
