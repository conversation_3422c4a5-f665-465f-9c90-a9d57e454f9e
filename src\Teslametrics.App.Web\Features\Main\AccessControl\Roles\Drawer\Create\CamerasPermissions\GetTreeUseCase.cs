using System.Data;
using Dapper;
using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.Core.Services.Persistence;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Roles.Drawer.Create.CamerasPermissions;

public static class GetTreeUseCase
{
    public record Query(Guid OrganizationId, Guid UserId) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public List<Folder> Items { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(List<Folder> items)
        {
            Items = items;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Result = result;

            Items = [];
        }

        public record Folder(Guid Id, string Name, List<Camera> Cameras);

        public record Camera(Guid Id, string Name);
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError
    }

    public class Validator : AbstractValidator<Query>
    {
        public Validator()
        {
            RuleFor(q => q.OrganizationId).NotEmpty();
            RuleFor(q => q.UserId).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Query, Response>
    {
        private readonly IValidator<Query> _validator;
        private readonly IDbConnection _dbConnection;

        public Handler(IValidator<Query> validator,
                       IDbConnection dbConnection)
        {
            _validator = validator;
            _dbConnection = dbConnection;
        }

        public async Task<Response> Handle(Query request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            var allowedResources = request.UserId != SystemConsts.RootUserId
                ? await GetAllowedResourcesAsync(request.UserId)
                : [];

            var foldersTemplate = SqlQueryBuilder.Create()
                .Select(Db.Folders.Props.Id)
                .Select(Db.Folders.Props.Name)
                .InnerJoin(Db.Organizations.Table, Db.Organizations.Props.Id, Db.Folders.Props.OrganizationId, SqlOperator.Equals)
                .Where(Db.Folders.Props.OrganizationId, ":OrganizationId", SqlOperator.Equals, new { request.OrganizationId })
                .WhereIf(request.UserId != SystemConsts.RootUserId,
                         $"({Db.Organizations.Props.OwnerId} = :UserId OR :Wildcard = ANY(:ResourceIds) OR {Db.Folders.Props.Id} = ANY(:ResourceIds))",
                         new
                         {
                             request.UserId,
                             Wildcard = SystemConsts.ResourceWildcardId,
                             ResourceIds = allowedResources.Where(r => r.Permission == Fqdn<AppPermissions>.GetName(AppPermissions.Main.Folders.Read))
                                 .Select(r => r.ResourceId).ToList()
                         })
                .Build(QueryType.Standard, Db.Folders.Table, RowSelection.UniqueRows);

            var folderModels = await _dbConnection.QueryAsync<FolderModel>(foldersTemplate.RawSql, foldersTemplate.Parameters);

            var camerasTemplate = SqlQueryBuilder.Create()
                .Select(Db.Cameras.Props.Id)
                .Select(Db.Cameras.Props.FolderId)
                .Select(Db.Cameras.Props.Name)
                .InnerJoin(Db.Organizations.Table, Db.Organizations.Props.Id, Db.Cameras.Props.OrganizationId, SqlOperator.Equals)
                .Where(Db.Cameras.Props.OrganizationId, ":OrganizationId", SqlOperator.Equals, new { request.OrganizationId })
                .WhereIf(request.UserId != SystemConsts.RootUserId,
                         $"({Db.Organizations.Props.OwnerId} = :UserId OR :Wildcard = ANY(:ResourceIds) OR {Db.Cameras.Props.Id} = ANY(:ResourceIds))",
                         new
                         {
                             request.UserId,
                             Wildcard = SystemConsts.ResourceWildcardId,
                             ResourceIds = allowedResources.Where(r => r.Permission == Fqdn<AppPermissions>.GetName(AppPermissions.Main.Cameras.Read))
                                 .Select(r => r.ResourceId).ToList()
                         })
                .Build(QueryType.Standard, Db.Cameras.Table, RowSelection.UniqueRows);

            var cameraModels = await _dbConnection.QueryAsync<CameraModel>(camerasTemplate.RawSql, camerasTemplate.Parameters);

            var folders = folderModels.Select(f =>
            {
                var cameras = cameraModels.Where(c => c.FolderId == f.Id)
                    .Select(c => new Response.Camera(c.Id, c.Name)).ToList();

                return new Response.Folder(f.Id, f.Name, cameras);
            }).ToList();

            return new Response(folders);
        }

        private async Task<IEnumerable<PermissionModel>> GetAllowedResourcesAsync(Guid userId)
        {
            var template = SqlQueryBuilder.Create()
                .Select(Db.RolePermissions.Props.Permission)
                .Select(Db.RolePermissions.Props.ResourceId)
                .InnerJoin(Db.UserRoles.Table, Db.UserRoles.Props.RoleId, Db.RolePermissions.Props.RoleId, SqlOperator.Equals)
                .Where($"({Db.RolePermissions.Props.Permission} = :FolderPermission OR {Db.RolePermissions.Props.Permission} = :CameraPermission)",
                    new
                    {
                        FolderPermission = Fqdn<AppPermissions>.GetName(AppPermissions.Main.Folders.Read),
                        CameraPermission = Fqdn<AppPermissions>.GetName(AppPermissions.Main.Cameras.Read)
                    })
                .Where(Db.UserRoles.Props.UserId, ":UserId", SqlOperator.Equals, new { userId })
                .Build(QueryType.Standard, Db.RolePermissions.Table, RowSelection.AllRows);

            return await _dbConnection.QueryAsync<PermissionModel>(template.RawSql, template.Parameters);
        }
    }

    public record FolderModel(Guid Id, string Name);

    public record CameraModel(Guid Id, Guid FolderId, string Name);

    public record PermissionModel(string Permission, Guid ResourceId);
}
