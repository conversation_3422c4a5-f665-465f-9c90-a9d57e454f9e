﻿using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Domain.AccessControl.Users.Events;
using Teslametrics.App.Web.Shared;

namespace Teslametrics.App.Web.Domain.AccessControl.Users;

public class UserAggregate : IEntity
{
    private readonly List<UserOrganizationEntity> _organizations;
    private readonly List<UserRoleEntity> _roles;

    public Guid Id { get; private set; }

    public string Name { get; private set; }

    public string Password { get; private set; }

    public bool IsSystem => Id == SystemConsts.RootUserId;

    // TODO: Заменить на свой ValueType
    public DateTimeOffset? LastLogInTime { get; private set; }

    public bool ForcePasswordChange { get; private set; }

    public bool LockoutEnabled { get; private set; }

    // Ключ для 2FA OTP
    public string SecretKey { get; private set; }

    // Флаг, указывающий, что 2FA включена
    public bool Is2faEnabled { get; private set; }

    //Флаг, указывающий, что OTP настроен
    public bool Setup2FAIsCompleted { get; private set; }

    public IReadOnlyCollection<UserOrganizationEntity> Organizations => _organizations;

    public IReadOnlyCollection<UserRoleEntity> Roles => _roles;

    public static (UserAggregate User, List<object> Events) Create(Guid id, string name, string password, Guid organizationId, IEnumerable<Guid> roles)
    {
        return (new UserAggregate(id, name, password, organizationId, roles), [new UserCreatedEvent(id, organizationId)]);
    }

    private UserAggregate()
    {
        Name = string.Empty;
        Password = string.Empty;
        SecretKey = string.Empty;
        Is2faEnabled = true;
        _organizations = [];
        _roles = [];
    }

    private UserAggregate(Guid id, string name, string password, Guid organizationId, IEnumerable<Guid> roles)
    {
        Id = id;
        Name = name;
        Password = password;
        SecretKey = string.Empty;
        Is2faEnabled = true;
        ForcePasswordChange = true;
        _organizations = [new UserOrganizationEntity(id, organizationId)];
        _roles = roles.Select(r => new UserRoleEntity(Id, r)).ToList();
    }

    public List<object> Lock()
    {
        List<object> events = [];

        if (!LockoutEnabled)
        {
            LockoutEnabled = true;
            events.Add(new UserLockedEvent(Id));
        }

        return events;
    }

    public List<object> Unlock()
    {
        List<object> events = [];

        if (LockoutEnabled)
        {
            LockoutEnabled = false;
            events.Add(new UserUnlockedEvent(Id));
        }

        return events;
    }

    public List<object> LogIn(DateTimeOffset? logInTime)
    {
        List<object> events = [];

        LastLogInTime = logInTime;
        events.Add(new UserLoggedInEvent(Id));

        return events;
    }

    public void SetForcePasswordChange()
    {
        if (ForcePasswordChange)
        {
            return;
        }

        ForcePasswordChange = true;
    }

    public void ResetForcePasswordChange()
    {
        if (!ForcePasswordChange)
        {
            return;
        }

        ForcePasswordChange = false;
    }

    //public void UpdatePermissions<TPermissions>(ResourceId resourceId, TPermissions permissions)
    //    where TPermissions : Enum
    //{
    //    var existingPermissions = _permissions.FirstOrDefault(p => p.ResourceId == resourceId && p.TypeName == typeof(TPermissions).FullName);
    //    if (existingPermissions is null)
    //    {
    //        _permissions.Add(UserPermissionEntity.Create(resourceId, permissions));
    //    }
    //    else
    //    {
    //        if (existingPermissions.Update(permissions))
    //        {

    //        }
    //    }

    //    UserPermissionEntity.Create(resourceId, permissions);
    //}

    public void RemoveOrganization(Guid organizationId)
    {
        var organization = _organizations.FirstOrDefault(o => o.OrganizationId == organizationId);

        if (organization is not null)
        {
            _organizations.Remove(organization);
        }
    }

    public void ChangePassword(string password)
    {
        Password = password;
    }

    public void Enable2FA()
    {
        Is2faEnabled = true;
    }

    public void Disable2FA()
    {
        Is2faEnabled = false;
    }

    public void UpdateSecretKey(string newSecretKey)
    {
        SecretKey = newSecretKey;
        Setup2FAIsCompleted = false;
    }

    public void CompleteSetup2FA()
    {
        Setup2FAIsCompleted = true;
    }

    public void Reset2FA()
    {  
        SecretKey = string.Empty;
        Setup2FAIsCompleted = false;
    }

    public IEnumerable<object> UpdateRoles(IEnumerable<Guid> removedRoles, IEnumerable<Guid> addedRoles)
    {
        if (!removedRoles.Any() && !addedRoles.Any())
        {
            return [];
        }

        _roles.RemoveAll(r => removedRoles.Contains(r.RoleId));
        _roles.AddRange(addedRoles.Select(r => new UserRoleEntity(Id, r)));

        return [new UserUpdatedEvent(Id, Organizations.Select(o => o.OrganizationId).ToList())];
    }
}
