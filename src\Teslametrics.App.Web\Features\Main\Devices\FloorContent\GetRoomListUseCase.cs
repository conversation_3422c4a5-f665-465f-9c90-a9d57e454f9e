using System.Data;
using System.Text.Json;
using Dapper;
using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Services.Persistence;
using Teslametrics.Shared;

namespace Teslametrics.App.Web.Features.Main.Devices.FloorContent;

public static class GetRoomListUseCase
{
    public record Query(Guid FloorId) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public class Room
        {
            public Guid Id { get; set; }
            public string Name { get; set; }
            public int ActiveIncidentCount { get; init; }
            public int CameraCount { get; init; }
            public int FridgeCount { get; init; }
            public int SensorCount { get; init; }

            public Room(Guid id, string name, int activeIncidentCount, int cameraCount, int fridgeCount, int sensorCount)
            {
                Id = id;
                Name = name;
                ActiveIncidentCount = activeIncidentCount;
                CameraCount = cameraCount;
                FridgeCount = fridgeCount;
                SensorCount = sensorCount;
            }
        }

        public int Number { get; init; }

        public List<Room> Rooms { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(int number, List<Room> rooms)
        {
            Number = number;
            Rooms = rooms;

            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Rooms = [];
            Result = result;
        }
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        PlanNotFound,
        FloorNotFound
    }

    public class Validator : AbstractValidator<Query>
    {
        public Validator()
        {
            RuleFor(r => r.FloorId).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Query, Response>
    {
        private readonly IValidator<Query> _validator;
        private readonly IDbConnection _dbConnection;

        public Handler(IValidator<Query> validator,
                       IDbConnection dbConnection)
        {
            _validator = validator;
            _dbConnection = dbConnection;
        }

        public async Task<Response> Handle(Query request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            if (!await CheckTableExistsAsync())
            {
                return new Response(Result.PlanNotFound);
            }

            var template = SqlQueryBuilder.Create()
                .Select(Db.Plans.Props.Page)
                .Build(QueryType.Standard, Db.Plans.Table, RowSelection.AllRows);

            var pageJson = await _dbConnection.ExecuteScalarAsync<string?>(template.RawSql);

            if (string.IsNullOrEmpty(pageJson))
            {
                return new Response(Result.PlanNotFound);
            }

            var page = JsonSerializer.Deserialize<PageModel>(pageJson)!;

            var floor = page.GetFloor(request.FloorId);

            if (floor is null)
            {
                return new Response(Result.FloorNotFound);
            }

            template = SqlQueryBuilder.Create()
                .Select(Db.Incidents.Props.RoomId)
                .Where(Db.Incidents.Props.FloorId, ":FloorId", SqlOperator.Equals, new { request.FloorId })
                .Where(Db.Incidents.Props.ResolvedAt, SqlOperator.IsNull)
                .Build(QueryType.Standard, Db.Incidents.Table, RowSelection.AllRows);

            var incidents = await _dbConnection.QueryAsync<IncidentModel>(template.RawSql, template.Parameters);

            var rooms = new List<Response.Room>();

            foreach (var room in floor.Rooms)
            {
                rooms.Add(new Response.Room(room.Id, room.Name,
                            incidents.Count(i => i.RoomId == room.Id),
                                            room.Camera is not null ? 1 : 0,
                                            room.Fridges.Count,
                                            room.Sensors.Count + room.Fridges.Sum(f => f.Sensors.Count)));
            }

            return new Response(floor.Number, rooms);
        }

        private async Task<bool> CheckTableExistsAsync()
        {
            // Check if table exists
            var tableExists = await _dbConnection.ExecuteScalarAsync<int>(
                "SELECT COUNT(*) FROM information_schema.tables " +
                "WHERE table_schema = 'public' AND table_name = @TableName",
                new { TableName = Db.Plans.Table });

            return tableExists > 0;
        }
    }

    public record IncidentModel(Guid RoomId);
}
