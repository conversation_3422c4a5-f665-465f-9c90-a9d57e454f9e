@using Teslametrics.App.Web.Features.Main.CameraViews.Drawer.View.Preview;
@inherits InteractiveBaseComponent
<DrawerHeader>
	<MudStack Spacing="0">
		<MudText Typo="Typo.h1">Просмотр настроек Вида</MudText>
		@if (IsLoading)
		{
			<MudSkeleton Width="60%"
						 Height="calc(var(--mud-typography-subtitle1-lineheight) * var(--mud-typography-subtitle1-size))" />
		}
		@if (!IsLoading && _viewResponse is not null && _viewResponse.IsSuccess)
		{
			<MudText Typo="Typo.subtitle1">@_viewResponse.Name</MudText>
		}
		@if (!IsLoading && (_viewResponse is null || !_viewResponse.IsSuccess))
		{
			<MudText Typo="Typo.subtitle1">Не удалось получить настройки вида</MudText>
		}
	</MudStack>
	<MudSpacer />
	@if (!_subscribing && (_subscriptionResult is null || !_subscriptionResult.IsSuccess))
	{
		<MudTooltip Arrow="true"
					Placement="Placement.Start"
					Text="Ошибка подписки на события">
			<MudIconButton OnClick="SubscribeAsync"
						   Icon="@Icons.Material.Filled.ErrorOutline"
						   Color="Color.Error" />
		</MudTooltip>
	}
	<MudTooltip Text="@($"Время последнего обновления: {_lastRefreshTime.ToLocalTime()}")"
				Arrow="true"
				Placement="Placement.Start">
		<MudIconButton OnClick="RefreshAsync"
					   Icon="@Icons.Material.Filled.Refresh"
					   Color="Color.Primary" />
	</MudTooltip>
	@if (!IsLoading && _viewResponse is not null && _viewResponse.IsSuccess)
	{
		<AuthorizeView Policy="@_contextMenuAuthPolicyString"
					   Resource="new PolicyRequirementResource(OrganizationId, ViewId)"
					   Context="menuContext">
			<MudMenu Icon="@Icons.Material.Filled.MoreVert"
					 Color="Color.Primary"
					 Variant="Variant.Outlined">
				<AuthorizeView Policy="@AppPermissions.Main.CameraViews.Update.GetEnumPermissionString()"
							   Resource="new PolicyRequirementResource(OrganizationId, ViewId)"
							   Context="editContext">
					<MudMenuItem OnClick="Edit"
								 Icon="@Icons.Material.Outlined.Edit">Редактировать</MudMenuItem>
				</AuthorizeView>
				<AuthorizeView Policy="@AppPermissions.Main.CameraViews.Delete.GetEnumPermissionString()"
							   Resource="new PolicyRequirementResource(OrganizationId, ViewId)"
							   Context="deleteContext">
					<MudDivider Class="my-4" />
					<MudMenuItem OnClick="Delete"
								 Icon="@Icons.Material.Outlined.Delete"
								 IconColor="Color.Warning">Удалить</MudMenuItem>
				</AuthorizeView>
			</MudMenu>
		</AuthorizeView>
	}
</DrawerHeader>
@if (IsLoading)
{
	<MudProgressLinear Indeterminate="true" />
}
else
{
	<div style="height: 4px;"></div>
}
<div class="px-4 py-4">
	<MudStack Spacing="8">
		@if (_viewResponse is not null && _viewResponse.IsSuccess)
		{
			<FormSectionComponent title="Описания вида">
				<MudTextField Value="_viewResponse.Name"
							  Label="Наименование"
							  ReadOnly="true" />
			</FormSectionComponent>

			@if (_viewResponse.GridType == GridType.GridCustom)
			{
				<FormSectionComponent title="Настройки отображения">
					<MudGrid>
						<MudItem xs="6">
							<MudNumericField T="short"
											 Value="_viewResponse.RowCount"
											 Label="Количество строк"
											 ReadOnly="true" />
						</MudItem>

						<MudItem xs="6">
							<MudNumericField T="short"
											 Value="_viewResponse.ColumnCount"
											 Label="Количество колонок"
											 ReadOnly="true" />
						</MudItem>
					</MudGrid>
				</FormSectionComponent>
			}
			else
			{
				<MudText Typo="Typo.h6"
						 Class="mb-n4">Настройки отображения</MudText>
			}

			<div class="@($"grid {_viewResponse.GridType.ToString()}")"
				 style="@($"--cols: {_viewResponse.ColumnCount}; --rows: {_viewResponse.RowCount};")">
				@foreach (var cell in _cells)
				{
					<Preview CameraId="@cell.CameraId"
							 @key="cell.CameraId" />
				}
			</div>

		}
		<FormLoadingComponent IsLoading="IsLoading && (_viewResponse is null || !_viewResponse.IsSuccess)" />
		<NoItemsFoundComponent HasItems="_viewResponse is not null && _viewResponse.IsSuccess"
							   LastRefreshTime="_lastRefreshTime"
							   RefreshAsync="RefreshAsync" />
	</MudStack>
</div>
<DrawerActions>
	<MudSpacer />
	<MudButton OnClick="CancelAsync"
			   Variant="Variant.Outlined"
			   StartIcon="@Icons.Material.Outlined.Close">Закрыть</MudButton>
	<AuthorizeView Policy="@AppPermissions.Main.CameraViews.Update.GetEnumPermissionString()"
				   Resource="new PolicyRequirementResource(OrganizationId, ViewId)"
				   Context="editContext">
		@if (!IsLoading)
		{
			<MudButton OnClick="Edit"
					   Color="Color.Secondary"
					   Variant="Variant.Outlined">Редактировать</MudButton>
		}
		else
		{
			<MudSkeleton Width="150px"
						 Height="36.5px" />
		}
	</AuthorizeView>
</DrawerActions>