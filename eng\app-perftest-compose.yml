name: teslametrics
services:
  app:
    container_name: teslametrics-app-web
    build:
      context: ..
      dockerfile: eng/Dockerfile
    restart: always
    user: root
    ports:
      - "80:80"
      - "443:443"
      - "22022:22"
    environment:
      ASPNETCORE_ENVIRONMENT: Perftest
      #DOTNET_DiagnosticPorts: /diag/dp.socket,nosuspend
      DOTNET_EnableDiagnostics: 1
    #volumes:
      #- dotnet_diagnostics:/diag

#volumes:
  #dotnet_diagnostics: