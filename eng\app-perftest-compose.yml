name: teslametrics
services:
  app-web:
    container_name: app-web
    build:
      context: ..
      dockerfile: eng/dockerfile-app-web
    restart: always
    user: root
    ports:
      - "80:80"
      - "443:443"
      - "22022:22"
    environment:
      ASPNETCORE_ENVIRONMENT: Perftest
      #DOTNET_DiagnosticPorts: /diag/dp.socket,nosuspend
      DOTNET_EnableDiagnostics: 1
    #volumes:
      #- dotnet_diagnostics:/diag

  media-server:
    container_name: media-server
    build:
      context: ..
      dockerfile: eng/dockerfile-media-server
    restart: always
    user: root
    ports:
      - "127.0.0.1:30000:30000"
    environment:
      ASPNETCORE_ENVIRONMENT: Perftest

#volumes:
  #dotnet_diagnostics: