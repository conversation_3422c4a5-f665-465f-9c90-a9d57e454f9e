﻿:root {
	--color-neutral-10: #17181A;
	--color-neutral-40: #5C6166;
	--color-neutral-70: #A1AAB2;
	--color-neutral-80: #B8C2CC;
	--color-neutral-90: #CFDAE5;
	--color-stroke-blue: #B2C2D2;
	--color-bg-blue: #ECF4FC;
}

@media (min-width: 600px) {
	:root {
		--mud-appbar-height: 112px !important;
	}
}

.color_neutral_40 {
	color: var(--color-neutral-40);
}

.color_neutral_70 {
	color: var(--color-neutral-70);
}

.color_neutral_90 {
	color: var(--color-neutral-90);
}

.bg_light_blue_surface {
	background-color: rgba(230, 233, 244, 1) !important;
}

.mud_theme_dark .bg_light_blue_surface {
	background-color: #27272f !important;
}

/* .mud-input-outlined .mud-input-control-input-container,
.mud-input-outlined-with-label .mud-input-control-input-container {
	height: 40px;
} */

.mud_theme_dark .bg_light_blue_surface {
	background-color: #27272f !important;
}

.mud_theme_light .mud-typography-subtitle2 {
	color: var(--color-neutral-40);
}