using System.Data;
using Dapper;
using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Data.Sql;
using Teslametrics.App.Web.Services.Persistence;
using Teslametrics.App.Web.Shared;

namespace Teslametrics.App.Web.Features.Main.Notifications;

public static class GetNotificationListUseCase
{
    public record Query(Guid UserId) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public List<Incident> Incidents { get; init; }
        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(List<Incident> incidents)
        {
            Incidents = incidents;

            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Incidents = [];
            Result = result;
        }

        public record Incident(Guid Id,
                               DateTimeOffset Date,
                               IncidentType IncidentType,
                               IncidentBreadcrumbModel? City,
                               IncidentBreadcrumbModel? Building,
                               IncidentBreadcrumbModel? Floor,
                               IncidentBreadcrumbModel? Room,
                               IncidentBreadcrumbModel? Device);

        public record IncidentBreadcrumbModel(Guid Id, string Name); // Представление пути к обьекту инцидента
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError
    }

    public class Validator : AbstractValidator<Query>
    {
        public Validator()
        {
            RuleFor(q => q.UserId).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Query, Response>
    {
        private readonly IValidator<Query> _validator;
        private readonly IDbConnection _dbConnection;

        public Handler(IValidator<Query> validator,
                       IDbConnection dbConnection)
        {
            _validator = validator;
            _dbConnection = dbConnection;
        }

        public async Task<Response> Handle(Query request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            var template = SqlQueryBuilder.Create()
                .Select(Db.Incidents.Props.Id)
                .Select(Db.Incidents.Props.CreatedAt)
                .Select(Db.Incidents.Props.IncidentType)
                .Select(Db.Incidents.Props.CityId)
                .Select(Db.Incidents.Props.City)
                .Select(Db.Incidents.Props.BuildingId)
                .Select(Db.Incidents.Props.Building)
                .Select(Db.Incidents.Props.FloorId)
                .Select(Db.Incidents.Props.Floor)
                .Select(Db.Incidents.Props.RoomId)
                .Select(Db.Incidents.Props.Room)
                .Select(Db.Incidents.Props.DeviceId)
                .Select(Db.Incidents.Props.Device)
                .InnerJoin(Db.Incidents.Table, Db.Incidents.Props.Id, Db.IncidentNotifications.Props.IncidentId, SqlOperator.Equals)
                .Where(Db.IncidentNotifications.Props.UserId, ":UserId", SqlOperator.Equals, new { request.UserId })
                .Build(QueryType.Standard, Db.IncidentNotifications.Table, RowSelection.AllRows);

            var incidentModels = await _dbConnection.QueryAsync<IncidentModel>(template.RawSql, template.Parameters);

            var incidents = incidentModels.Select(i => new Response.Incident(
                i.Id,
                i.CreatedAt,
                i.IncidentType,
                new Response.IncidentBreadcrumbModel(i.CityId, i.City),
                new Response.IncidentBreadcrumbModel(i.BuildingId, i.Building),
                new Response.IncidentBreadcrumbModel(i.FloorId, i.Floor.ToString()),
                new Response.IncidentBreadcrumbModel(i.RoomId, i.Room),
                i.DeviceId is not null ? new Response.IncidentBreadcrumbModel(i.DeviceId.Value, i.Device!) : null
            )).ToList();

            return new Response(incidents);
        }
    }

    public record IncidentModel(Guid Id, DateTimeOffset CreatedAt, IncidentType IncidentType, Guid CityId, string City, Guid BuildingId, string Building, Guid FloorId, int Floor, Guid RoomId, string Room, Guid? DeviceId, string? Device);
}