using Microsoft.AspNetCore.Components;
using Teslametrics.App.Web.Events.Cameras;

namespace Teslametrics.App.Web.Features.Main.Cameras;

public partial class ListPage
{
	[SupplyParameterFromQuery(Name = "Group")]
	public Guid? Folder { get; set; }

	[SupplyParameterFromQuery(Name = "OrganizationId")]
	public Guid? OrganizationId { get; set; }

	protected override void OnInitialized()
	{
		base.OnInitialized();

		CompositeDisposable.Add(EventSystem.Subscribe<CameraGroupSelectEto>(CameraGroupSelectHandler));
	}

	#region [EventHandlers]
	private void CameraGroupSelectHandler(CameraGroupSelectEto eto)
	{
		OrganizationId = eto.OrganizationId;
		Folder = eto.GroupId;
		StateHasChanged();
	}
	#endregion
}