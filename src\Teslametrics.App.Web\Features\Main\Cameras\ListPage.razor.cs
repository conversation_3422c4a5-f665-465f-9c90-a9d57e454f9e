using Microsoft.AspNetCore.Components;
using MudBlazor;
using MudBlazor.Services;
using Teslametrics.App.Web.Components;
using Teslametrics.App.Web.Events.Cameras;
using Teslametrics.App.Web.Services.UserDevice;

namespace Teslametrics.App.Web.Features.Main.Cameras;

public partial class ListPage : IAsyncDisposable, IBrowserViewportObserver
{
    private bool _drawerOpen = true;
    private ResizerOrientation _orientation = ResizerOrientation.Horizontal;

    ResizeOptions IBrowserViewportObserver.ResizeOptions { get; } = new()
    {
        ReportRate = 250,
        NotifyOnBreakpointOnly = true
    };

    [Inject]
    private IBrowserViewportService BrowserViewportService { get; set; } = null!;

    [Inject]
    private IUserDeviceService UserDeviceService { get; set; } = null!;

    [SupplyParameterFromQuery(Name = "Group")]
    public Guid? Folder { get; set; }

    [SupplyParameterFromQuery(Name = "OrganizationId")]
    public Guid? OrganizationId { get; set; }

    public readonly IEnumerable<Breakpoint> Markers = [Breakpoint.Xs, Breakpoint.Sm, Breakpoint.Md, Breakpoint.SmAndDown, Breakpoint.MdAndDown];

    public Guid Id { get; } = Guid.NewGuid();

    public Task NotifyBrowserViewportChangeAsync(BrowserViewportEventArgs browserViewportEventArgs)
    {
        _orientation = Markers.Contains(browserViewportEventArgs.Breakpoint) ? ResizerOrientation.Vertical : ResizerOrientation.Horizontal;

        StateHasChanged();

        return Task.CompletedTask;
    }

    public async ValueTask DisposeAsync() => await BrowserViewportService.UnsubscribeAsync(this);

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            // Отложить подписку на viewport service, чтобы избежать конфликта с инициализацией drawer
            await Task.Delay(100);
            await BrowserViewportService.SubscribeAsync(this, fireImmediately: true);

            // Защита от автоматического закрытия drawer после инициализации
            if (UserDeviceService.IsMobile && !_drawerOpen)
            {
                _drawerOpen = true;
                StateHasChanged();
            }
        }

        await base.OnAfterRenderAsync(firstRender);
    }

    protected override void OnInitialized()
    {
        if (UserDeviceService.IsMobile)
        {
            _drawerOpen = true;
            _orientation = ResizerOrientation.Vertical;
        }

        CompositeDisposable.Add(EventSystem.Subscribe<CameraGroupSelectEto>(CameraGroupSelectHandler));

        base.OnInitialized();
    }

    #region [EventHandlers]
    private void OnSwipeEnd(SwipeEventArgs e)
    {
        if (e.SwipeDirection == SwipeDirection.LeftToRight && !_drawerOpen)
        {
            _drawerOpen = true;
            StateHasChanged();
        }
        else if (e.SwipeDirection == SwipeDirection.RightToLeft && _drawerOpen)
        {
            _drawerOpen = false;
            StateHasChanged();
        }
    }

    private void CameraGroupSelectHandler(CameraGroupSelectEto eto)
    {
        OrganizationId = eto.OrganizationId;
        Folder = eto.GroupId;
        StateHasChanged();
    }

    private void OnOrganizationIdChanged(Guid? organizationId)
    {
        OrganizationId = organizationId;
        _drawerOpen = false;
    }
    #endregion
}