﻿using MediatR;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using MudBlazor;
using Teslametrics.App.Web.Events.Users;
using Teslametrics.App.Web.Extensions;

namespace Teslametrics.App.Web.Features.Main.AuthToolbarComponent;

public partial class AuthToolbarComponent
{
    private class User(Guid id, string username)
    {
        public Guid UserId { get; init; } = id;
        public string Username { get; init; } = username;
    }

    private User? _user;

    #region [Injectables]
    [Inject]
    private NavigationManager _navigationManager { get; set; } = null!;
    #endregion

    protected override async Task OnInitializedAsync()
    {
        try
        {
            var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
            if (authState.User.Identity?.IsAuthenticated ?? false)
            {
                var userId = authState.User.GetUserId()!.Value;
                await FetchAsync(userId);
            }
        }
        catch (Exception exc)
        {
            _navigationManager.NavigateTo(RouteConstants.LogoutPage, true);
            Logger.LogError(exc, exc.Message);
        }
    }

    private async Task FetchAsync(Guid id)
    {
        if (IsLoading) return;
        try
        {
            await SetLoadingAsync();
            var result = await ScopeFactory.CreateScope().ServiceProvider.GetRequiredService<IMediator>().Send(new GetUserUseCase.Query(id));
            if (result.IsSuccess)
            {
                _user = new(result.Id, result.Username);
                return;
            }

            switch (result.Result)
            {
                case GetUserUseCase.Result.ValidationError:
                    _navigationManager.NavigateTo(RouteConstants.LogoutPage, true);
                    Snackbar.Add("Ошибка валидации данных", Severity.Error);
                    break;
                case GetUserUseCase.Result.UserNotFound:
                    _navigationManager.NavigateTo(RouteConstants.LogoutPage, true);
                    Snackbar.Add("Пользователь не найден", Severity.Error);
                    break;
                case GetUserUseCase.Result.Unknown:
                default:
                    _navigationManager.NavigateTo(RouteConstants.LogoutPage, true);
                    Snackbar.Add("Не удалось получить пользователя из-за непредвиденной ошибки:" + result.Result.ToString(), Severity.Error);
                    break;
            }
        }
        catch (Exception ex)
        {
            _navigationManager.NavigateTo(RouteConstants.LogoutPage, true);
            Snackbar.Add("Не удалось получить выбранного пользователя. Повторите попытку", Severity.Error);
            Logger.LogError(ex, ex.Message);
        }
        finally
        {
            await SetLoadingAsync(false);
        }
    }

    private void SignOut()
    {
        _navigationManager.NavigateTo(RouteConstants.LogoutPage, true);
    }

    private void ChangePasswordAsync() => EventSystem.Publish(new ChangeCurrentUserPassword());
}
