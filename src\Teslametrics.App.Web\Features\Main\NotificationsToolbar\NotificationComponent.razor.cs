using System.Reactive;
using Microsoft.AspNetCore.Components;
using Teslametrics.App.Web.Exceptions;
using Teslametrics.App.Web.Extensions;

namespace Teslametrics.App.Web.Features.Main.NotificationsToolbar;

public partial class NotificationComponent
{
    private bool _subscribing;

    private SubscribeNotificationUseCase.Response? _subscriptionResult;
    private bool _visible => _response is not null && _response.Incidents.Count > 0;
    private GetNotificationListUseCase.Response? _response;

    [Inject]
    private NavigationManager NavigationManager { get; set; } = null!;

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        await FetchDataAsync();
    }

    private async Task FetchDataAsync()
    {
        await SetLoadingAsync(true);
        try
        {
            var userId = await GetCurrentUserIdAsync() ?? throw new NotAuthorizedException();
            _response = await ScopeFactory.MediatorSend(new GetNotificationListUseCase.Query(userId));
        }
        catch (Exception ex)
        {
            _response = null;
            Logger.LogError(ex, ex.Message);
        }
        await SetLoadingAsync(false);
        if (_response is null) return;

        switch (_response.Result)
        {
            case GetNotificationListUseCase.Result.Success:
                await SubscribeAsync();
                break;
            case GetNotificationListUseCase.Result.ValidationError:
                Snackbar.Add("Ошибка валидации при получении списка уведомлений", MudBlazor.Severity.Error);
                break;
            case GetNotificationListUseCase.Result.Unknown:
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(NotificationComponent), nameof(GetNotificationListUseCase));
                Snackbar.Add($"Не удалось получить список уведомлений из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
                break;
        }
    }

    private async Task SubscribeAsync()
    {
        Unsubscribe();
        await SetSubscribingAsync(true);

        try
        {
            var userId = await GetCurrentUserIdAsync() ?? throw new NotAuthorizedException();
            _subscriptionResult = await ScopeFactory.MediatorSend(new SubscribeNotificationUseCase.Request(Observer.Create<object>(OnAppEventHandler, OnError), userId));
        }
        catch (Exception ex)
        {
            _subscriptionResult = null;
            Snackbar.Add($"Не удалось получить подписку на события уведомлений из-за ошибки сообщения с сервером. Повторите попытку позже.", MudBlazor.Severity.Error);
            Logger.LogError(ex, ex.Message);
        }

        await SetSubscribingAsync(false);
        if (_subscriptionResult is null) return;

        switch (_subscriptionResult.Result)
        {
            case SubscribeNotificationUseCase.Result.Success:
                CompositeDisposable.Add(_subscriptionResult.Subscription!);
                break;
            case SubscribeNotificationUseCase.Result.ValidationError:
                Snackbar.Add("Ошибка валидации при подписке на события уведомлений", MudBlazor.Severity.Error);
                break;
            case SubscribeNotificationUseCase.Result.Unknown:
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(NotificationComponent), nameof(SubscribeNotificationUseCase));
                Snackbar.Add($"Не удалось получить подписку на события уведомлений из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
                break;

            default:
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(NotificationComponent), nameof(SubscribeNotificationUseCase), _subscriptionResult.Result);
                Snackbar.Add($"Не удалось получить подписку на события уведомлений из-за ошибки: {_subscriptionResult.Result}", MudBlazor.Severity.Error);
                break;
        }
    }
    private void Unsubscribe()
    {
        if (_subscriptionResult?.Subscription is not null)
        {
            CompositeDisposable.Remove(_subscriptionResult.Subscription);
            _subscriptionResult.Subscription.Dispose();
        }
    }

    protected Task SetSubscribingAsync(bool isLoading = true) => UpdateViewAsync(() =>
    {
        _subscribing = isLoading;
    });

    private void ShowDetails(GetNotificationListUseCase.Response.Incident item)
    {
        NavigationManager.NavigateTo("/incidents/?IncidentId=" + item.Id);
    }

    #region [Event Handlers]
    private async void OnAppEventHandler(object appEvent)
    {
        await FetchDataAsync();
        await UpdateViewAsync();
    }

    private void OnError(Exception exc)
    {
        Snackbar.Add("Ошибка при подписке на события", MudBlazor.Severity.Error);
        Logger.LogError(exc, exc.Message);
    }
    #endregion [Event Handlers]
}
