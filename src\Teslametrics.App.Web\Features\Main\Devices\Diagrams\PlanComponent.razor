@using Teslametrics.App.Web.Features.Main.Devices.Diagrams.Nodes
@using Teslametrics.App.Web.Features.Main.Devices.Diagrams.Nodes.PlanNode
@using Teslametrics.App.Web.Features.Main.Devices.Diagrams.Nodes.FridgeNode
@using Teslametrics.App.Web.Features.Main.Devices.Diagrams.Nodes.CameraNode
@using Teslametrics.App.Web.Features.Main.Devices.Diagrams.Nodes.RoomNode
@using Microsoft.Extensions.Logging
@using Teslametrics.App.Web.Shared

@inherits InteractiveBaseComponent

<div class="plan-component-container relative">
    @if (IsLoading)
    {
        <MudProgressCircular Color="Color.Primary"
                             Indeterminate="true"
                             Class="absolute" />
    }
    else if (_response == null || !_response.IsSuccess)
    {
        <MudAlert Severity="Severity.Error"
                  Class="absolute">
            Не удалось загрузить план этажа
        </MudAlert>
    }
    <div class="@(_response is not null && _response.IsSuccess ? "plan-container" : "plan-container d-none")"
         id="@_containerId">
        <div class="plan-content"
             id="@_contentId">
            @if (_elements != null)
            {
                @foreach (var element in _elements)
                {
                    @switch (element)
                    {
                        case ImageElement imgElement:
                            <ImgNodeComponent Element="imgElement" ReadOnly="true" @key="element" />
                            break;
                        case FridgeElement fridgeElement:
                            <FridgeNodeComponent Element="@fridgeElement" ReadOnly="true" @key="element" />
                            break;
                        case CameraElement cameraElement:
                            <CameraNodeComponent Element="@cameraElement" ReadOnly="true" @key="element" />
                            break;
                        case RoomElement roomElement:
                            <RoomNodeComponent Element="@roomElement" IsSelected="@(_roomId == roomElement.Id)" OnSelect="SelectRoomAsync" @key="element" />
                            break;
                    }
                }
            }
        </div>
    </div>
</div>
