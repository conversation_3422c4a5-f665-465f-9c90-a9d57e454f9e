using Dapper;
using FluentValidation;
using MediatR;
using System.Data;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Data.Sql;
using Teslametrics.App.Web.Services.Persistence;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Roles.Drawer.View.OrganizationsConcretePermissionsComponent;

public static class GetOrganizationListUseCase
{
    public record Query(Guid RoleId) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public List<Item> Items { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(List<Item> items)
        {
            Items = items;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Result = result;
            Items = [];
        }

        public record Item(Guid Id, string Name);
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError
    }

    public class Validator : AbstractValidator<Query>
    {
        public Validator()
        {
            RuleFor(q => q.RoleId).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Query, Response>
    {
        private readonly IValidator<Query> _validator;
        private readonly IDbConnection _dbConnection;

        public Handler(IValidator<Query> validator,
                       IDbConnection dbConnection)
        {
            _validator = validator;
            _dbConnection = dbConnection;
        }

        public async Task<Response> Handle(Query request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            var template = SqlQueryBuilder.Create()
                .Select(Db.Organizations.Props.Id)
                .Select(Db.Organizations.Props.Name)
                .InnerJoin(Db.RolePermissions.Table, Db.RolePermissions.Props.ResourceId, Db.Organizations.Props.Id, SqlOperator.Equals)
                .Where(Db.RolePermissions.Props.RoleId, ":RoleId", SqlOperator.Equals, new { request.RoleId })
                .Build(QueryType.Standard, Db.Organizations.Table, RowSelection.UniqueRows);

            var organizations = await _dbConnection.QueryAsync<OrganizationModel>(template.RawSql, template.Parameters);

            return new Response(organizations.Select(o => new Response.Item(o.Id, o.Name)).ToList());
        }
    }

    public record OrganizationModel(Guid Id, string Name);
}