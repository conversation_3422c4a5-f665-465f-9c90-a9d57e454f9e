@using System.Reactive
@inherits InteractiveBaseComponent
<div class="container">
	@if (_selectedDate.HasValue && _camera?.IsSuccess == true)
	{
		<Player CameraId="CameraId"
				@bind-SelectedDate="@_selectedDate"
				TimeLineChanged="TimeLineChanged"
				UTCOffset="@_camera.TimeZone"
				@ref="_playerRef" />
	}
	else
	{
		<MudStack AlignItems="AlignItems.Center"
				  Justify="Justify.Center"
				  Spacing="0"
				  Class="mud-height-full">
			<MudIcon Icon="@Icons.Material.Outlined.DateRange"
					 Color="Color.Info"
					 Style="font-size: 8rem;"
					 Class="mb-2" />
			<MudText Typo="Typo.subtitle1"
					 Color="Color.Warning">Выберите дату</MudText>
		</MudStack>
	}
	<MudStack Spacing="0"
			  Class="sidebar">
		<MudStack Row=true
				  Class="pl-4 pt-4 header">
			<MudIcon Icon="@Icons.Material.Filled.Archive"
					 Class="mt-1" />
			<MudStack Spacing="0">
				<MudText Typo="Typo.h6">Архив камеры</MudText>
				@if (IsLoading)
				{
					<MudSkeleton Width="30%"
								 Height="42px" />
				}
				@if (!IsLoading && _camera is null)
				{
					<MudText Typo="Typo.subtitle2">Камера не найдена</MudText>
				}
				@if (!IsLoading && _camera is not null)
				{
					<MudText Typo="Typo.subtitle2">@_camera?.Name</MudText>
				}
			</MudStack>
			<MudSpacer />
			<div>
				@if (!_subscribing && (_subscriptionResult is null || !_subscriptionResult.IsSuccess))
				{
					<MudTooltip Arrow="true"
								Placement="Placement.Start"
								Text="Ошибка подписки на события">
						<MudIconButton OnClick="SubscribeAsync"
									   Icon="@Icons.Material.Filled.ErrorOutline"
									   Color="Color.Error" />
					</MudTooltip>
					<MudIconButton OnClick="RefreshAsync"
								   Icon="@Icons.Material.Filled.Refresh"
								   Color="Color.Primary" />
				}
			</div>
		</MudStack>
		<MudDatePicker PickerVariant="PickerVariant.Static"
					   @bind-Date="@_selectedDate"
					   IsDateDisabledFunc="IsDateDisabledFunc"
					   PickerMonthChanged="FetchMonthAvaliableDates"
					   Class="mud-width-full" />
		@if ((_camera?.IsSuccess ?? false) && _selectedDate is not null)
		{
			<EventListComponent CameraId="CameraId"
								Class="pa-2 overflow-hidden"
								SelectedDate="@_selectedDate!.Value"
								TimeLine="@_currentTimeLineRange"
								EventSelected="OnEventSelected" />
		}
		else
		{
			<div></div>
		}
		<div class="px-2">
			<MudButton OnClick="Cancel"
					   Variant="Variant.Outlined">Закрыть</MudButton>
		</div>
	</MudStack>
</div>