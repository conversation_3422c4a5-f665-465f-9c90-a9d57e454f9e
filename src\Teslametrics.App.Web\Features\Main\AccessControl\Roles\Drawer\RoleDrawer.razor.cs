using Teslametrics.App.Web.Eto.Roles;
using Teslametrics.App.Web.Events;
using Teslametrics.App.Web.Components.Drawer;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Roles.Drawer;

public partial class RoleDrawer
{
	private DrawerOptions _options = new DrawerOptions() { Width = "calc(100% - var(--mud-drawer-width-left))" };
	private DrawerMode _mode = DrawerMode.Hidden;
	private Guid? _resourceId;
	public Guid _organizationId;

	public bool IsOpened => _mode != DrawerMode.Hidden;

	public enum DrawerMode
	{
		Hidden,
		Create,
		Edit,
		View,
	}

	protected override void OnInitialized()
	{
		CompositeDisposable.Add(EventSystem.Subscribe<RoleCreateEto>(OnRoleHandler));
		CompositeDisposable.Add(EventSystem.Subscribe<RoleSelectEto>(OnRoleHandler));
		CompositeDisposable.Add(EventSystem.Subscribe<RoleEditEto>(OnRoleHandler));

		base.OnInitialized();
	}
	public Task ShowCreateAsync(Guid organizationId) => InvokeAsync(() =>
	{
		_organizationId = organizationId;
		_resourceId = null;
		_mode = DrawerMode.Create;
		StateHasChanged();
	});

	public Task ShowEditAsync(Guid organizationId, Guid id) => InvokeAsync(() =>
	{
		_organizationId = organizationId;
		_resourceId = id;
		_mode = DrawerMode.Edit;
		StateHasChanged();
	});

	public Task ShowViewAsync(Guid organizationId, Guid id) => InvokeAsync(() =>
	{
		_organizationId = organizationId;
		_resourceId = id;
		_mode = DrawerMode.View;
		StateHasChanged();
	});

	public Task CloseAsync() => InvokeAsync(() =>
	{
		try
		{
			_mode = DrawerMode.Hidden;
			_resourceId = null;
			StateHasChanged();
		}
		catch (Exception ex)
		{
			Console.WriteLine($"Error in CloseAsync: {ex.Message}");
			throw;
		}
	});

	#region [Event Handlers]
	private async void OnRoleHandler(BaseEto eto)
	{
		switch (eto)
		{
			case RoleEditEto editEto:
				await ShowEditAsync(editEto.OrganizationId, editEto.RoleId);
				break;

			case RoleCreateEto createEto:
				await ShowCreateAsync(createEto.OrganizationId);
				break;

			case RoleSelectEto selectEto:
				await ShowViewAsync(selectEto.OrganizationId, selectEto.RoleId);
				break;

			default:
				await CloseAsync();
				return;
		}
	}

	private Task OnOpenChanged(bool opened) => InvokeAsync(() =>
	{
		_mode = DrawerMode.Hidden;
		_resourceId = null;
		StateHasChanged();
	});
	#endregion [Event Handlers]
}
