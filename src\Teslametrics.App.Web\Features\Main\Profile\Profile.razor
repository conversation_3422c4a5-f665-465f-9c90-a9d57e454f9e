﻿@rendermode InteractiveServer
@attribute [Authorize]
@attribute [Route(RouteConstants.Profile)]
@inherits InteractiveBaseComponent

<PageTitle>Multimonitor | Камеры</PageTitle>

@if (IsLoading)
{
	<ProfileLoadingComponent />
}

@if (!IsLoading && Model is null)
{
	<MudStack AlignItems="AlignItems.Center"
			  Justify="Justify.Center"
			  Class="mud-height-full">
		<MudIcon Icon="@Icons.Material.Filled.PersonOff"
				 Style="font-size: 8rem;" />
		<MudText Typo="Typo.body1">Ничего не найдено</MudText>
		<MudText Typo="Typo.subtitle1">Попробуйте снова позднее</MudText>
	</MudStack>
}

@if (!IsLoading && Model is not null)
{
	<MudContainer MaxWidth="MaxWidth.Medium"
				  Class="mud-height-full">
		<MudStack Spacing="2"
				  Class="py-3 mud-width-full mud-height-full">
			<MudStack Spacing="4">
				<div>
					<MudStack Spacing="0"
							  Class="mb-2">
						<MudText Typo="Typo.h6">Описание пользователя</MudText>
						<MudText Typo="Typo.subtitle2">Настройки, которые влияют только на восприятие человеком</MudText>
					</MudStack>
					<MudCard>
						<MudCardContent>
							<MudStack Spacing="6">
								<MudTextField Value="Model.Username"
											  Disabled="true"
											  ReadOnly="true"
											  InputType="InputType.Text"
											  Label="Логин" />
							</MudStack>
						</MudCardContent>
					</MudCard>
				</div>

				<div>
					<MudStack Spacing="0"
							  Class="mb-2 mt-6">
						<MudText Typo="Typo.h6">Параметры пользователя</MudText>
						<MudText Typo="Typo.subtitle2">Данные настройки важны для работы в системе</MudText>
					</MudStack>
					<MudCard>
						@if (Model.IsSystem)
						{
							<MudStack AlignItems="AlignItems.Center"
									  Justify="Justify.Center"
									  Class="pa-8">
								<MudIcon Icon="@Icons.Material.Filled.WarningAmber"
										 Style="font-size: 8rem;" />
								<MudText Typo="Typo.body1">Системный пользователь</MudText>
								<MudText Typo="Typo.subtitle1">Доступны все элементы</MudText>
							</MudStack>
						}
						else
						{
							<MudList T="string"
									 ReadOnly="true"
									 Class="list">
								<MudListSubheader Class="pb-1">
									<b>Роли пользователя</b>
								</MudListSubheader>
								@foreach (var item in Model.Roles)
								{
									<MudListItem Text="@item.Name"
												 @key="item"
												 Class="item" />
								}
							</MudList>
							@*
					<MudDivider Class="my-6" />

							<MudList T="string" ReadOnly="true" Class="list">
								<MudListSubheader Class="pb-1">
									<b>Права пользователя</b>
								</MudListSubheader>
								@{
									var permissionTuples = Model.Roles
									.SelectMany(role => role.Permissions, (role, permission) => new { RoleName = role.Name, Permission = permission })
									.GroupBy(rp => rp.Permission)
									.Select(group => (Permission: group.Key, Roles: group.Select(rp => rp.RoleName).Distinct()));
									if (permissionTuples.Any())
									{
										@foreach (var item in permissionTuples)
										{
											<MudListItem Text="@item.Permission" SecondaryText="@("Источник(и): " + string.Join(", ", item.Roles))" @key="item" Class="item" />
										}
									}
								}
					</MudList> *@
						}
					</MudCard>
				</div>
			</MudStack>
		</MudStack>
	</MudContainer>
}