using Microsoft.AspNetCore.Authorization;
using Teslametrics.App.Web.Extensions;

namespace Teslametrics.App.Web.Shared;

[AttributeUsage(AttributeTargets.All)]
public class AppAuthorizeAttribute : AuthorizeAttribute
{
    public AppAuthorizeAttribute(params object[] permissions)
	{
		List<string> policy = [];
		foreach (var permission in permissions)
		{
			if (permission is not Enum value)
			{
				throw new ArgumentException("All permissions must be enum values.", nameof(permissions));
			}

			policy.Add(value.GetEnumPermissionString());

			// Your implementation here
			// For example, you might store them in a list:
		}
		Policy = policy.Join(",");
	}
}