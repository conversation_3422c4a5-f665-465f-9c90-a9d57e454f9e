﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Teslametrics.DbMigrator.Migrations
{
    /// <inheritdoc />
    public partial class TwoFactorAuth : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "is2fa_enabled",
                table: "users",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "secret_key",
                table: "users",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<bool>(
                name: "setup2fa_is_completed",
                table: "users",
                type: "boolean",
                nullable: false,
                defaultValue: false);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "is2fa_enabled",
                table: "users");

            migrationBuilder.DropColumn(
                name: "secret_key",
                table: "users");

            migrationBuilder.DropColumn(
                name: "setup2fa_is_completed",
                table: "users");
        }
    }
}
