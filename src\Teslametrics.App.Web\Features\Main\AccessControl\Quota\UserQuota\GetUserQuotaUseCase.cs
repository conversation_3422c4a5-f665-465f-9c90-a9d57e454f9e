using Teslametrics.App.Web.Abstractions;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Quota.UserQuota;

public static class GetUserQuotaUseCase
{
    public record Query(Guid OrganizationId);//: BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public int UsedQuota { get; init; }

        public int TotalQuota { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(int usedQuota, int totalQuota, Result result)
        {
            UsedQuota = usedQuota;
            TotalQuota = totalQuota;
            Result = result;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Result = result;
            UsedQuota = 0;
            TotalQuota = 0;
        }
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError
    }
}
