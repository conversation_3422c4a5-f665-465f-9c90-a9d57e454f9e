using System.Reactive;
using Microsoft.AspNetCore.Components;
using MudBlazor;
using Teslametrics.App.Web.Components;
using Teslametrics.App.Web.Exceptions;
using Teslametrics.App.Web.Extensions;
using Teslametrics.App.Web.Shared;

namespace Teslametrics.App.Web.Features.Main.Incidents.IncidentsList;

public partial class IncidentsListComponent
{
	private Filter _prevFilter;          // «снимок» параметров с прошлого рендера
	private readonly record struct Filter(
		DateTime DateFrom,
		DateTime DateTo,
		Guid? CityId,
		Guid? BuildingId,
		Guid? FloorId,
		Guid? RoomId,
		Guid? FridgeId,
		IncidentType? IncidentType,
		bool? IsResolved);

	private bool _subscribing;
	private SubscribeIncidentListUseCase.Response? _subscribeResponse;

	private GetIncidentListUseCase.Response? _response;
	private GetIncidentListUseCase.Response.Incident? _selectedIncident => _response?.Incidents.Find(x => x.Id == IncidentId);

	[Parameter]
	public DateTime DateFrom { get; set; } = DateTime.Today;

	[Parameter]
	public DateTime DateTo { get; set; } = DateTime.Today.AddDays(1).AddMilliseconds(-1);
	[Parameter]
	public Guid? CityId { get; set; }
	[Parameter]
	public Guid? BuildingId { get; set; }
	[Parameter]
	public Guid? FloorId { get; set; }
	[Parameter]
	public Guid? RoomId { get; set; }
	[Parameter]
	public Guid? FridgeId { get; set; }
	[Parameter]
	public IncidentType? IncidentType { get; set; }
	[Parameter]
	public bool? IsResolved { get; set; }

	[Parameter]
	public Guid? IncidentId { get; set; }

	[Parameter]
	public EventCallback<Guid?> IncidentIdChanged { get; set; }

	string GetIncidentIcon(IncidentType type) => type switch
	{
		Shared.IncidentType.Door => TeslaIcons.Sensors.Door,
		Shared.IncidentType.Temperature => TeslaIcons.Sensors.Temperature,
		Shared.IncidentType.Humidity => TeslaIcons.Sensors.Humidity,
		Shared.IncidentType.Leak => TeslaIcons.Sensors.Leak,
		Shared.IncidentType.Power => TeslaIcons.Sensors.Power,
		_ => MudBlazor.Icons.Material.Outlined.ReportProblem
	};

	protected override async Task OnParametersSetAsync()
	{
		// формируем текущий «снимок»
		var current = new Filter(DateFrom, DateTo, CityId, BuildingId, FloorId, RoomId, FridgeId, IncidentType, IsResolved);

		// если фильтры изменились (первый проход или реальное отличие) – берём данные заново
		if (!current.Equals(_prevFilter))
		{
			await LoadDataAsync();
			_prevFilter = current;          // запоминаем актуальное состояние
		}

		await base.OnParametersSetAsync();   // прочий жизненный цикл
	}

	private async Task LoadDataAsync()
	{
		await SetLoadingAsync(true);
		try
		{
			var userId = await GetCurrentUserIdAsync() ?? throw new NotAuthorizedException();
			_response = await ScopeFactory.MediatorSend(new GetIncidentListUseCase.Query(userId, new DateTimeOffset(DateFrom.ToUniversalTime()), new DateTimeOffset(DateTo.ToUniversalTime()), CityId, BuildingId, FloorId, RoomId, FridgeId, IncidentType, IsResolved));
		}
		catch (Exception ex)
		{
			_response = null;
			Logger.LogError(ex, ex.Message);
		}
		await SetLoadingAsync(false);
		if (_response is null) return;

		switch (_response.Result)
		{
			case GetIncidentListUseCase.Result.Success:
				await SubscribeAsync();
				break;
			case GetIncidentListUseCase.Result.ValidationError:
				Snackbar.Add("Ошибка валидации при получении списка просишествий", MudBlazor.Severity.Error);
				break;
			case GetIncidentListUseCase.Result.Unknown:
				Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(IncidentsListComponent), nameof(GetIncidentListUseCase));
				Snackbar.Add($"Не удалось получить список просишествий из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
				break;
			default:
				Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(IncidentsListComponent), nameof(GetIncidentListUseCase), _response.Result);
				Snackbar.Add($"Не удалось получить список просишествий из-за ошибки: {_response.Result}", MudBlazor.Severity.Error);
				break; ;
		}
	}

	private async Task OnSelectedIncidentChanged(DataGridRowClickEventArgs<GetIncidentListUseCase.Response.Incident> args)
	{
		IncidentId = IncidentId == args.Item.Id ? null : args.Item.Id;
		if (IncidentIdChanged.HasDelegate)
		{
			await IncidentIdChanged.InvokeAsync(IncidentId);
		}
	}

	private string GetRowClass(GetIncidentListUseCase.Response.Incident item, int index)
	{
		return string.Join(' ', [
			item.IsResolved ? "resolved-row" : "unresolved-row",
			item.Id == _selectedIncident?.Id ? "selected" : null
		]);
	}

	private async Task SubscribeAsync()
	{
		if (_response is null || !_response.IsSuccess || _subscribing) return;
		try
		{
			Unsubscribe();
			await SetSubscribingAsync(true);
			var userId = await GetCurrentUserIdAsync() ?? throw new NotAuthorizedException();
			_subscribeResponse = await ScopeFactory.MediatorSend(new SubscribeIncidentListUseCase.Request(Observer.Create<object>(OnAppEventHandler, OnError), userId, new DateTimeOffset(DateFrom.ToUniversalTime()), new DateTimeOffset(DateTo.ToUniversalTime()), CityId, BuildingId, FloorId, RoomId, FridgeId, IncidentType, IsResolved));
		}
		catch (Exception ex)
		{
			_subscribeResponse = null;
			Snackbar.Add($"Не удалось подписаться на события списка просишествий из-за ошибки сообщения с сервером. Повторите попытку позже.", MudBlazor.Severity.Error);
			Logger.LogError(ex, ex.Message);
		}
		await SetSubscribingAsync(false);
		if (_subscribeResponse is null) return;

		switch (_subscribeResponse.Result)
		{
			case SubscribeIncidentListUseCase.Result.Success:
				CompositeDisposable.Add(_subscribeResponse.Subscription!);
				break;
			case SubscribeIncidentListUseCase.Result.ValidationError:
				Snackbar.Add("Ошибка валидации при подписке на события списка просишествий", MudBlazor.Severity.Error);
				break;
			case SubscribeIncidentListUseCase.Result.Unknown:
				Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(IncidentsListComponent), nameof(SubscribeIncidentListUseCase));
				Snackbar.Add($"Не удалось подписаться на события списка просишествий из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
				break;
			default:
				Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(IncidentsListComponent), nameof(SubscribeIncidentListUseCase), _subscribeResponse.Result);
				Snackbar.Add($"Не удалось подписаться на события списка просишествий из-за ошибки: {_subscribeResponse.Result}", MudBlazor.Severity.Error);
				break;
		}
	}
	private void Unsubscribe()
	{
		if (_subscribeResponse?.Subscription is not null)
		{
			CompositeDisposable.Remove(_subscribeResponse.Subscription);
			_subscribeResponse.Subscription.Dispose();
		}
	}

	protected Task SetSubscribingAsync(bool isLoading = true) => UpdateViewAsync(() =>
	{
		_subscribing = isLoading;
	});

	#region [Event Handlers]
	private async void OnAppEventHandler(object appEvent)
	{
		await LoadDataAsync();
		await UpdateViewAsync();
	}

	private void OnError(Exception exc)
	{
		Snackbar.Add("Ошибка при подписке на события", MudBlazor.Severity.Error);
		Logger.LogError(exc, exc.Message);
	}
	#endregion [Event Handlers]
}
