@using Teslametrics.App.Web.Features.Main.Cameras.CameraDrawer.Create
@using Teslametrics.App.Web.Features.Main.Cameras.CameraDrawer.Edit
@using Teslametrics.App.Web.Features.Main.Cameras.CameraDrawer.View
@inherits InteractiveBaseComponent
<DrawerComponent Open="IsOpened"
				 OpenChanged="OnOpenChanged">
	<CascadingValue IsFixed="true"
					Value="this">
		@switch (_mode)
		{
			case DrawerMode.View:
				@if (_cameraId.HasValue)
				{
					<AuthorizeView Policy="@(Fqdn<AppPermissions>.GetNames(AppPermissions.Main.Cameras.Read).Last())"
								   Resource="new PolicyRequirementResource(_organizationId, _cameraId.Value)"
								   Context="innerContext">
						<Authorized>
							<CameraViewComponent CameraId="@_cameraId.Value"
												 OrganizationId="@_organizationId" />
						</Authorized>
						<NotAuthorized>
							<NotAuthorizedComponent Class="mud-height-full" />
						</NotAuthorized>
					</AuthorizeView>
				}
				break;

			case DrawerMode.Edit:
				@if (_cameraId.HasValue)
				{
					<AuthorizeView Policy="@(Fqdn<AppPermissions>.GetNames(AppPermissions.Main.Cameras.Update).Last())"
								   Resource="new PolicyRequirementResource(_organizationId, _cameraId.Value)"
								   Context="innerContext">
						<Authorized Context="innerContext">
							<CameraEditComponent CameraId="_cameraId.Value"
												 OrganizationId="@_organizationId" />
						</Authorized>
						<NotAuthorized>
							<NotAuthorizedComponent Class="mud-height-full" />
						</NotAuthorized>
					</AuthorizeView>
				}
				break;

			case DrawerMode.Create:
				<AuthorizeView Policy="@AppPermissions.Main.Cameras.Create.GetEnumPermissionString()"
							   Resource="new PolicyRequirementResource(_organizationId, _folderId ?? _organizationId)"
							   Context="innerContext">
					<Authorized>
						<CameraCreateComponent FolderId="_folderId!.Value"
											   OrganizationId="@_organizationId" />
					</Authorized>
					<NotAuthorized>
						<NotAuthorizedComponent Class="mud-height-full" />
					</NotAuthorized>
				</AuthorizeView>
				break;
		}
	</CascadingValue>
</DrawerComponent>