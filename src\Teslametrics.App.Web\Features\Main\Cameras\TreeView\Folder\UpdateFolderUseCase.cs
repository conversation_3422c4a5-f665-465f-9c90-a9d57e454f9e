using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.Core.Domain.Folders;
using Teslametrics.Core.Services.Outbox;
using Teslametrics.Core.Services.TransactionManager;

namespace Teslametrics.App.Web.Features.Main.Cameras.TreeView;

public static class UpdateFolderUseCase
{
    public record Command(Guid Id, string Name) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public Guid Id { get; init; }

        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(Guid id)
        {
            Id = id;
            Result = Result.Success;
        }

        public Response(Result result)
        {
            if (result == Result.Success)
            {
                throw new ArgumentException("Expected an error, but provided a successful result", nameof(result));
            }

            Id = Guid.Empty;
            Result = result;
        }
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        FolderNotFound,
        FolderNameAlreadyExists
    }

    public class Validator : AbstractValidator<Command>
    {
        public Validator()
        {
            RuleFor(c => c.Id).NotEmpty();
            RuleFor(c => c.Name).Length(3, 120);
        }
    }

    public class Handler : IRequestHandler<Command, Response>
    {
        private readonly IValidator<Command> _validator;
        private readonly IFolderRepository _folderRepository;
        private readonly ITransactionManager _transactionManager;
        private readonly IPublisher _publisher;
        private readonly IOutbox _outbox;

        public Handler(IValidator<Command> validator,
                       IFolderRepository folderRepository,
                       ITransactionManager transactionManager,
                       IPublisher publisher,
                       IOutbox outbox)
        {
            _validator = validator;
            _folderRepository = folderRepository;
            _transactionManager = transactionManager;
            _publisher = publisher;
            _outbox = outbox;
        }

        public async Task<Response> Handle(Command request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            using var transaction = await _transactionManager.CreateTransactionAsync();

            var folder = await _folderRepository.FindAsync(request.Id, cancellationToken);
            if (folder is null)
            {
                return new Response(Result.FolderNotFound);
            }

            if (await _folderRepository.IsFolderNameExistsAsync(request.Name, folder.OrganizationId, cancellationToken) && folder.Name != request.Name)
            {
                return new Response(Result.FolderNameAlreadyExists);
            }

            var events = folder.ChangeName(request.Name);
            await _folderRepository.SaveChangesAsync(cancellationToken);

            foreach (var @event in events)
            {
                await _publisher.Publish(@event, cancellationToken);
            }

            await _outbox.AddRangeAsync(events);
            await transaction.CommitAsync();

            return new Response(folder.Id);
        }
    }
}