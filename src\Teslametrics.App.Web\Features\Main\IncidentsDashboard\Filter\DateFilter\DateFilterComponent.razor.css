::deep .quick-range {
    width: fit-content;
}

::deep .toggle_group {
    gap: 4px;
}

::deep .range-btn,
::deep .toggle_group>button {
    border-radius: 6px;
}

::deep .range-btn.active,
::deep .toggle_group>button[aria-checked="true"] {
    background: var(--mud-palette-surface);
}

/* Quick date range panel */
::deep .quick-range {
    border-radius: 8px;
    background: var(--mud-palette-background);
}

.mud_theme_dark div ::deep .quick-range {
    border: 1px solid var(--mud-palette-lines-inputs);
}

.mud_theme_dark div ::deep .range-btn.active,
.mud_theme_dark div ::deep .toggle_group>button[aria-checked="true"] {
    background: (--mud-palette-background-gray);
}