﻿::deep .time_picker_inputs {
}

::deep .input_fields input.mud-input-root-outlined {
	text-align: center;
	padding: 0 !important;
	font-size: 57px !important;
	width: 96px !important;
	height: 80px !important;
	background-color: rgba(0,0,0,.09);
	border-radius: 8px;
	color: var(--mud-palette-secondary);
}

::deep > .ds_toolbar {
	height: auto;
}

::deep .ds_timepicker_separator {
	font-size: 57px !important;
}

::deep .mud-input > .mud-input-outlined-border {
	border-radius: 8px !important;
}

::deep .input_fields input.mud-input-root-outlined:focus {
	background-color: var(--mud-palette-secondary-hover);
}

::deep .input_fields input.mud-input-root-outlined:hover {
	background-color: var(--mud-palette-primary-hover);
}

::deep .input_fields input.mud-input-root-outlined:not(:focus):not(:hover) ~ .mud-input-outlined-border {
	border-color: transparent !important;
}

::deep .input_fields > .mud-input-control-input-container > label {
	background: transparent;
	transform: none !important;
	top: initial;
	bottom: -24px;
	padding: 0 !important;
}