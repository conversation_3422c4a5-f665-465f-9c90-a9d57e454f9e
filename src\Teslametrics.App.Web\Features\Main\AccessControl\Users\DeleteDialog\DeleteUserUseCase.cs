﻿using FluentValidation;
using MediatR;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Domain.AccessControl.Organizations;
using Teslametrics.App.Web.Domain.AccessControl.Users;
using Teslametrics.App.Web.Domain.AccessControl.Users.Events;
using Teslametrics.App.Web.Services.Outbox;
using Teslametrics.App.Web.Services.TransactionManager;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Users.DeleteDialog;

public static class DeleteUserUseCase
{
    public record Command(Guid UserId, Guid OrganizationId) : BaseRequest<Response>;

    public record Response : BaseResponse
    {
        public Result Result { get; init; }

        public bool IsSuccess => Result == Result.Success;

        public Response(Result result)
        {
            Result = result;
        }
    }

    public enum Result
    {
        Unknown = 0,
        Success,
        ValidationError,
        CannotDeleteSystemUser,
        OrganizationNotFound,
        CannotDeleteOwner
    }

    public class Validator : AbstractValidator<Command>
    {
        public Validator()
        {
            RuleFor(c => c.UserId).NotEmpty();
        }
    }

    public class Handler : IRequestHandler<Command, Response>
    {
        private readonly IValidator<Command> _validator;
        private readonly IUserRepository _userRepository;
        private readonly IOrganizationRepository _organizationRepository;
        private readonly ITransactionManager _transactionManager;
        private readonly IPublisher _publisher;
        private readonly IOutbox _outbox;

        public Handler(IValidator<Command> validator,
                       IUserRepository userRepository,
                       IOrganizationRepository organizationRepository,
                       ITransactionManager transactionManager,
                       IPublisher publisher,
                       IOutbox outbox)
        {
            _validator = validator;
            _userRepository = userRepository;
            _organizationRepository = organizationRepository;
            _transactionManager = transactionManager;
            _publisher = publisher;
            _outbox = outbox;
        }

        public async Task<Response> Handle(Command request, CancellationToken cancellationToken)
        {
            if (!_validator.Validate(request).IsValid)
            {
                return new Response(Result.ValidationError);
            }

            using var transaction = await _transactionManager.CreateTransactionAsync();

            var user = await _userRepository.FindAsync(request.UserId, cancellationToken);
            if (user is not null)
            {
                if (user.IsSystem)
                {
                    return new Response(Result.CannotDeleteSystemUser);
                }

                var organization = await _organizationRepository.FindAsync(request.OrganizationId, cancellationToken);
                if (organization is null)
                {
                    return new Response(Result.OrganizationNotFound);
                }

                var organizationsOwner = await _organizationRepository.FindByOwnerId(user.Id, cancellationToken);

                if (organizationsOwner.Any())
                {
                    return new Response(Result.CannotDeleteOwner);
                }

                user.RemoveOrganization(request.OrganizationId);

                if (user.Organizations.Count == 0)
                {
                    await _userRepository.DeleteAsync(request.UserId, cancellationToken);
                }

                await _userRepository.SaveChangesAsync(cancellationToken);

                List<object> events = [new UserDeletedEvent(user.Id, organization.Id)];

                foreach (var @event in events)
                {
                    await _publisher.Publish(@event, cancellationToken);
                }

                await _outbox.AddRangeAsync(events);

                await transaction.CommitAsync();
            }

            return new Response(Result.Success);
        }
    }
}