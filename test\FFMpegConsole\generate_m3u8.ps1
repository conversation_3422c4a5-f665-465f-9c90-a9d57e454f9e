$ffprobe = ""
$streamDir = ""
$files = Get-ChildItem "$streamDir\*.ts"

@"
#EXTM3U
#EXT-X-VERSION:3
#EXT-X-TARGETDURATION:4
#EXT-X-MEDIA-SEQUENCE:0
"@ | Out-File "$streamDir\playlist.m3u8" -Encoding UTF8

$maxDuration = 0

foreach ($file in $files | Sort-Object Name) {
    $json = & $ffprobe -v quiet -print_format json -show_format $file.FullName
    $format = $json | ConvertFrom-Json
    $duration = [double]$format.format.duration
    $durationStr = $duration.ToString("F3", [System.Globalization.CultureInfo]::InvariantCulture)
    if ($duration -gt $maxDuration) {
        $maxDuration = $duration
    }
    @"
#EXTINF:$durationStr,
$($file.Name)
"@ | Out-File "$streamDir\playlist.m3u8" -Append -Encoding UTF8
}

"#EXT-X-ENDLIST" | Out-File "$streamDir\playlist.m3u8" -Append -Encoding UTF8

Write-Host "Max duration: $maxDuration"
