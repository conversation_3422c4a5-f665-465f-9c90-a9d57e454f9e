@using Teslametrics.App.Web.Extensions
@using Teslametrics.Shared
@inherits InteractiveBaseComponent<Teslametrics.App.Web.Locales.Features.Main.AccessControl.Roles.Drawer.Create.CamerasPermissions.CamerasConcretePermissionsComponent>
<div class="d_contents">
	<MudTreeView T="Guid"
				 Items="@_items"
				 Class="tree overflow-auto"
				 ReadOnly="true">
		<ItemTemplate>
			@{
				var presenter = (TreeItemPresenter)context;
				<MudTreeViewItem @bind-Expanded="@presenter.Expanded"
								 CanExpand="@presenter.Expandable"
								 Items="@presenter.Children"
								 Icon="@presenter.Icon"
								 Class="tree_item"
								 Value="@presenter.Value">
					<BodyContent Context="body">
						<MudText>@presenter.Text</MudText>
						<MudSpacer />
						<div class="checkboxes">
							@if (presenter.IsFolder)
							{
								@foreach (var permission in _folderValues)
								{
									InheritStatus status = GetInheritStatus(permission, presenter);
									if (status.Inherited)
									{
										<MudTooltip Arrow="true"
													Placement="Placement.Start"
													@key="permission"
													Class="pa-1"
													Style="background:var(--mud-palette-gray-dark);box-shadow: var(--mud-elevation-2);">
											<ChildContent>
												<MudCheckBox Value="true"
															 Class="input_checkbox"
															 Label="@Localizer[permission.GetEnumPermissionString()]"
															 Disabled="true"
															 Color="Color.Primary" />
											</ChildContent>
											<TooltipContent>
												<MudList T="string"
														 ReadOnly="true">
													<MudListSubheader Class="align-left"
																	  Style="color: var(--mud-palette-dark-text)">
														Данное право было наследовано от другого и не может быть снято напрямую
													</MudListSubheader>
													@foreach (var item in status.InheritedFrom)
													{
														<MudListItem Icon="@Icons.Material.Filled.Check"
																	 @key="item">
															@Localizer[item]
														</MudListItem>
													}
												</MudList>
											</TooltipContent>
										</MudTooltip>
									}
									else
									{
										<MudCheckBox T="bool"
													 Class="input_checkbox"
													 Value="ContainsPermission(permission, context.Value)"
													 ValueChanged="(isChecked) => OnChangedHandler(isChecked, permission, presenter)"
													 Label="@Localizer[permission.GetEnumPermissionString()]"
													 Color="Color.Primary"
													 @key="permission" />
									}
								}
								if (_cameraValues.Contains(AppPermissions.Main.Cameras.Create))
								{
									<MudCheckBox T="bool"
												 Class="input_checkbox"
												 Value="ContainsPermission(AppPermissions.Main.Cameras.Create, context.Value)"
												 ValueChanged="(isChecked) => OnChangedHandler(isChecked, AppPermissions.Main.Cameras.Create, presenter)"
												 Label="@Localizer[AppPermissions.Main.Cameras.Create.GetEnumPermissionString()]"
												 Color="Color.Primary"
												 @key="AppPermissions.Main.Cameras.Create" />
								}
							}
							@if (presenter.IsCamera)
							{
								@foreach (var permission in _cameraValues.Where(item => item != AppPermissions.Main.Cameras.Create))
								{
									InheritStatus status = GetInheritStatus(permission, presenter);
									if (status.Inherited)
									{
										<MudTooltip Arrow="true"
													Placement="Placement.Start"
													@key="permission"
													Class="pa-1"
													Style="background:var(--mud-palette-gray-dark);box-shadow: var(--mud-elevation-2);">
											<ChildContent>
												<MudCheckBox Value="true"
															 Label="@Localizer[permission.GetEnumPermissionString()]"
															 Disabled="true"
															 Color="Color.Primary" />
											</ChildContent>
											<TooltipContent>
												<MudList T="string"
														 ReadOnly="true">
													<MudListSubheader Class="align-left"
																	  Style="color: var(--mud-palette-dark-text)">
														Данное право было наследовано от другого и не может быть снято напрямую
													</MudListSubheader>
													@foreach (var item in status.InheritedFrom)
													{
														<MudListItem Icon="@Icons.Material.Filled.Check"
																	 @key="item">
															@Localizer[item]
														</MudListItem>
													}
												</MudList>
											</TooltipContent>
										</MudTooltip>
									}
									else
									{
										<MudCheckBox T="bool"
													 Class="input_checkbox"
													 Value="ContainsPermission(permission, context.Value)"
													 ValueChanged="(isChecked) => OnChangedHandler(isChecked, permission, presenter)"
													 Label="@Localizer[permission.GetEnumPermissionString()]"
													 Color="Color.Primary"
													 @key="permission" />
									}
								}
							}
						</div>
					</BodyContent>
				</MudTreeViewItem>
			}
		</ItemTemplate>
	</MudTreeView>
</div>