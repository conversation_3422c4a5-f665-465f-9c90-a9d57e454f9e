/* Chart container styles */
::deep .apexcharts-canvas {
    margin: 0 auto;
}

/* Filter styles */
::deep .mud-button-group {
    overflow: hidden;
    border-radius: 4px;
}

::deep .mud-button-group .mud-button {
    min-width: 80px;
}

/* Chart title styles */
::deep .mud-typography.mud-typography-subtitle1 {
    font-weight: 500;
}

/* Responsive adjustments */
@media (max-width: 960px) {
    ::deep .mud-grid-item {
        flex-basis: 100%;
        max-width: 100%;
    }
}