<?xml version="1.0" encoding="utf-8"?>
<root>
	<resheader name="resmimetype">
		<value>text/microsoft-resx</value>
	</resheader>
	<resheader name="version">
		<value>2.0</value>
	</resheader>
	<resheader name="reader">
		<value>System.Resources.ResXResourceReader, System.Windows.Forms</value>
	</resheader>
	<resheader name="writer">
		<value>System.Resources.ResXResourceWriter, System.Windows.Forms</value>
	</resheader>
  <data name="Title" xml:space="preserve">
    <value>Create role</value>
  </data>
  <data name="RoleDescription" xml:space="preserve">
    <value>Role description</value>
  </data>
  <data name="RoleName" xml:space="preserve">
    <value>Role name</value>
  </data>
  <data name="RoleNameRequired" xml:space="preserve">
    <value>This field is required</value>
  </data>
  <data name="RoleNameHelper" xml:space="preserve">
    <value>Specify the role name in Latin characters</value>
  </data>
  <data name="AdminRole" xml:space="preserve">
    <value>Administrator role</value>
  </data>
  <data name="AccessRights" xml:space="preserve">
    <value>Access rights</value>
  </data>
  <data name="AccessRightsDescription" xml:space="preserve">
    <value>User access rights are of two types:</value>
  </data>
  <data name="GlobalRightsRoot" xml:space="preserve">
    <value>1. Global - to all resources of all organizations</value>
  </data>
  <data name="ResourceRightsRoot" xml:space="preserve">
    <value>2. Resource - to a specific object in a certain area</value>
  </data>
  <data name="GlobalRightsOrg" xml:space="preserve">
    <value>1. Common - to all organization resources</value>
  </data>
  <data name="ResourceRightsOrg" xml:space="preserve">
    <value>2. Resource - to a specific object in the organization</value>
  </data>
  <data name="TabAccessControl" xml:space="preserve">
    <value>Access control</value>
  </data>
  <data name="TabCameras" xml:space="preserve">
    <value>Cameras</value>
  </data>
  <data name="GlobalRights" xml:space="preserve">
    <value>Global</value>
  </data>
  <data name="CommonRights" xml:space="preserve">
    <value>Common</value>
  </data>
  <data name="ResourceRights" xml:space="preserve">
    <value>Resource</value>
  </data>
  <data name="Organizations" xml:space="preserve">
    <value>Organizations</value>
  </data>
  <data name="Users" xml:space="preserve">
    <value>Users</value>
  </data>
  <data name="Roles" xml:space="preserve">
    <value>Roles</value>
  </data>
  <data name="Cameras" xml:space="preserve">
    <value>Cameras</value>
  </data>
  <data name="Folders" xml:space="preserve">
    <value>Folders</value>
  </data>
  <data name="TabPresets" xml:space="preserve">
    <value>Presets</value>
  </data>
  <data name="CameraPresets" xml:space="preserve">
    <value>Camera presets</value>
  </data>
  <data name="TabQuotas" xml:space="preserve">
    <value>Quota</value>
  </data>
  <data name="CameraQuotas" xml:space="preserve">
    <value>Camera quotas</value>
  </data>
  <data name="TabPublicAccess" xml:space="preserve">
    <value>Camera public access</value>
  </data>
  <data name="CameraPublicAccess" xml:space="preserve">
    <value>Camera public access</value>
  </data>
  <data name="TabViews" xml:space="preserve">
    <value>Views</value>
  </data>
  <data name="Save" xml:space="preserve">
    <value>Save</value>
  </data>
</root>
