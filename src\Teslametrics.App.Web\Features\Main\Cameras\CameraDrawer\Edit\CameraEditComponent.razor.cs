using System.Reactive;
using FluentValidation;
using Microsoft.AspNetCore.Components;
using Teslametrics.App.Web.Abstractions;
using Teslametrics.App.Web.Events.Cameras;
using Teslametrics.App.Web.Extensions;
using Teslametrics.App.Web.Features.Main.Cameras.CameraDrawer.Edit.PresetField;
using Teslametrics.App.Web.Shared;

namespace Teslametrics.App.Web.Features.Main.Cameras.CameraDrawer.Edit;

public partial class CameraEditComponent
{
	private class Model(Guid id, string name, PresetFieldComponent.Preset? preset, string archiveUri, string publicUri, string viewUri, TimeSpan timeZone, Coordinates? coordinates, bool autoStart, bool onvifEnabled, Onvif.OnvifComponent.OnvifSettings onvifSettings)
	{
		public Guid Id { get; set; } = id;
		public string Name { get; set; } = name;
		public PresetFieldComponent.Preset? Preset { get; set; } = preset;
		public string ArchiveUri { get; set; } = archiveUri;
		public string PublicUri { get; set; } = publicUri;
		public string ViewUri { get; set; } = viewUri;
		public TimeSpan TimeZone { get; set; } = timeZone;
		public Coordinates? Coordinates { get; set; } = coordinates;
		public bool AutoStart { get; set; } = autoStart;

		public bool OnvifEnabled { get; set; } = onvifEnabled;

		public Onvif.OnvifComponent.OnvifSettings OnvifSettings { get; set; } = onvifSettings;
	}

	private MudBlazor.MudTextField<string>? _archiveUriRef;
	private MudBlazor.MudTextField<string>? _publicUriRef;
	private MudBlazor.MudTextField<string>? _viewUriRef;

	private class Validator : BaseFluentValidator<Model>
	{
		private static string? ExtractHostAndPort(string uri)
		{
			try
			{
				if (string.IsNullOrWhiteSpace(uri)) return null;

				var rtspUri = new Uri(uri);
				if (rtspUri.Scheme != "rtsp") return null;

				return rtspUri.Authority;
			}
			catch
			{
				return null;
			}
		}

		private static bool ValidateUriHostMatch(string currentUri, Model model)
		{
			if (string.IsNullOrWhiteSpace(currentUri))
				return true;

			var currentHost = ExtractHostAndPort(currentUri);
			if (currentHost == null)
				return true;

			var allUris = new[] { model.ArchiveUri, model.PublicUri, model.ViewUri };
			var allHosts = allUris
				.Where(uri => !string.IsNullOrWhiteSpace(uri))
				.Select(ExtractHostAndPort)
				.Where(h => h != null);

			return allHosts.All(h => h == currentHost);
		}

		public Validator()
		{
			RuleFor(model => model.Name)
				.Length(3, 60).WithMessage("наименование должно быть длиной от 3 до 60 символов");

			RuleFor(model => model.ArchiveUri)
				.NotEmpty().WithMessage("Поле должно быть заполнено")
				.Must(uri => Uri.TryCreate(uri, UriKind.Absolute, out var parsed) && parsed.Scheme == "rtsp")
				.WithMessage("Некорректный формат RTSP URI")
				.Must((model, uri) => ValidateUriHostMatch(uri, model))
				.WithMessage("Все RTSP URI должны иметь одинаковый хост и порт");

			RuleFor(model => model.ViewUri)
				.NotEmpty().WithMessage("Поле должно быть заполнено")
				.Must(uri => Uri.TryCreate(uri, UriKind.Absolute, out var parsed) && parsed.Scheme == "rtsp")
				.WithMessage("Некорректный формат RTSP URI")
				.Must((model, uri) => ValidateUriHostMatch(uri, model))
				.WithMessage("Все RTSP URI должны иметь одинаковый хост и порт");

			RuleFor(model => model.PublicUri)
				.NotEmpty().WithMessage("Поле должно быть заполнено")
				.Must(uri => Uri.TryCreate(uri, UriKind.Absolute, out var parsed) && parsed.Scheme == "rtsp")
				.WithMessage("Некорректный формат RTSP URI")
				.Must((model, uri) => ValidateUriHostMatch(uri, model))
				.WithMessage("Все RTSP URI должны иметь одинаковый хост и порт");

			RuleFor(model => model.Preset)
				.NotEmpty().WithMessage("Поле должно быть заполнено");

			RuleFor(model => model.TimeZone)
				.NotNull().WithMessage("Поле должно быть заполнено")
				.NotEmpty().WithMessage("Поле должно быть заполнено");

			RuleFor(model => model.Coordinates)
				.NotNull().WithMessage("Поле должно быть заполнено")
				.NotEmpty().WithMessage("Поле должно быть заполнено");
		}
	}

	private bool _subscribing;
	private bool _isValid;
	private Model? _model = new(Guid.Empty, string.Empty, null, string.Empty, string.Empty, string.Empty, TimeSpan.Zero, null, false, false, new());
	private GetCameraUseCase.Response? _camera;
	private SubscribeCameraUseCase.Response? _subscriptionResult;
	private Validator _validator = new();

	#region Parameters
	[Parameter]
	[EditorRequired]
	public Guid OrganizationId { get; set; }

	[Parameter]
	[EditorRequired]
	public Guid CameraId { get; set; }

	[CascadingParameter]
	public CameraDrawer CameraDrawer { get; set; } = null!;
	#endregion

	protected override async Task OnParametersSetAsync()
	{
		await base.OnParametersSetAsync();

		if (_camera is null || CameraId != _camera.Id)
		{
			_model = null;
			await FetchAsync();
			await SubscribeAsync();
		}
	}

	private async Task FetchAsync()
	{
		if (IsLoading) return;
		try
		{
			await SetLoadingAsync(true);
			_camera = await ScopeFactory.MediatorSend(new GetCameraUseCase.Query(CameraId));
		}
		catch (Exception ex)
		{
			_camera = null;
			Logger.LogError(ex, ex.Message);
			Snackbar.Add($"Не удалось получить камеру из-за ошибки сообщения с сервером. Повторите попытку позже.", MudBlazor.Severity.Error);
		}

		await SetLoadingAsync(false);
		if (_camera is null) return;

		switch (_camera.Result)
		{
			case GetCameraUseCase.Result.Success:
				PresetFieldComponent.Preset? preset = null;
				if (_camera.QuotaId.HasValue)
				{
					preset = new PresetFieldComponent.Preset(_camera.QuotaId.Value, _camera.QuotaName);
				}
				Onvif.OnvifComponent.OnvifSettings onvifSettings = _camera.OnvifEnabled && _camera.OnvifOptions is not null ? new(_camera.OnvifOptions.Host, _camera.OnvifOptions.Port, _camera.OnvifOptions.Username, _camera.OnvifOptions.Password) : new();
				_model ??= new Model(_camera.Id, _camera.Name, preset, _camera.ArchiveUri, _camera.PublicUri, _camera.ViewUri, _camera.TimeZone, _camera.Coordinates, _camera.AutoStart, _camera.OnvifEnabled, onvifSettings);
				break;
			case GetCameraUseCase.Result.CameraNotFound:
				Snackbar.Add("Не удалось получить камеру. Возможно камера уже удалена.", MudBlazor.Severity.Warning);
				Cancel();
				break;
			case GetCameraUseCase.Result.ValidationError:
				Snackbar.Add("Не удалось получить камеру из-за ошибки валидации.", MudBlazor.Severity.Error);
				Cancel();
				break;
			case GetCameraUseCase.Result.Unknown:
				Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(CameraEditComponent), nameof(GetCameraUseCase));
				Snackbar.Add($"Не удалось получить камеру из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
				break;
			default:
				Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(CameraEditComponent), nameof(GetCameraUseCase), _camera.Result);
				Snackbar.Add($"Не удалось получить камеру из-за ошибки: {_camera.Result}", MudBlazor.Severity.Error);
				break;
		}
	}

	private async Task SubscribeAsync()
	{
		try
		{
			Unsubscribe();
			await SetSubscribingAsync(true);
			_subscriptionResult = await ScopeFactory.MediatorSend(new SubscribeCameraUseCase.Request(Observer.Create<object>(OnAppEventHandler, OnError), CameraId));
		}
		catch (Exception ex)
		{
			_subscriptionResult = null;
			Snackbar.Add($"Не удалось подписаться на события камеры из-за ошибки сообщения с сервером. Повторите попытку позже.", MudBlazor.Severity.Error);
			Logger.LogError(ex, ex.Message);
		}
		finally
		{
			await SetSubscribingAsync(false);
		}

		if (_subscriptionResult is null) return;

		switch (_subscriptionResult.Result)
		{
			case SubscribeCameraUseCase.Result.Success:
				CompositeDisposable.Add(_subscriptionResult.Subscription!);
				break;
			case SubscribeCameraUseCase.Result.ValidationError:
				Snackbar.Add("Ошибка валидации при подписке на события", MudBlazor.Severity.Error);
				break;
			case SubscribeCameraUseCase.Result.CameraNotFound:
				Snackbar.Add("Ошибка подписки на события камеры", MudBlazor.Severity.Error);
				break;
			case SubscribeCameraUseCase.Result.Unknown:
				Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(CameraEditComponent), nameof(SubscribeCameraUseCase));
				Snackbar.Add($"Не удалось подписаться на события камеры из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
				break;
			default:
				Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(CameraEditComponent), nameof(SubscribeCameraUseCase), _subscriptionResult.Result);
				Snackbar.Add($"Не удалось подписаться на события камеры из-за ошибки: {_subscriptionResult.Result}", MudBlazor.Severity.Error);
				break;
		}
	}
	private void Unsubscribe()
	{
		if (_subscriptionResult?.Subscription is not null)
		{
			CompositeDisposable.Remove(_subscriptionResult.Subscription);
			_subscriptionResult.Subscription.Dispose();
		}
	}

	protected Task SetSubscribingAsync(bool isLoading = true) => UpdateViewAsync(() =>
	{
		_subscribing = isLoading;
	});

	#region [Actions]
	private async Task SubmitAsync()
	{
		if (_model is null)
		{
			Snackbar.Add("Камера не найдена. Невозможно применить изменения!", MudBlazor.Severity.Error);
			return;
		}

		UpdateCameraUseCase.Response? response = null;
		try
		{
			await SetLoadingAsync(true);
			UpdateCameraUseCase.Command.OnvifSettings? onvifSettings = _model.OnvifEnabled
				? new(_model.OnvifSettings.Host, _model.OnvifSettings.Port, _model.OnvifSettings.Username, _model.OnvifSettings.Password)
				: null;
			response = await ScopeFactory.MediatorSend(new UpdateCameraUseCase.Command(_model.Id, _model.Preset!.id, _model.Name, _model.TimeZone, _model.Coordinates, _model.ArchiveUri, _model.ViewUri, _model.PublicUri, _model.AutoStart, _model.OnvifEnabled, onvifSettings));
		}
		catch (Exception ex)
		{
			response = null;
			Logger.LogError(ex, ex.Message);
			Snackbar.Add($"Не удалось сохранить изменения из-за ошибки сообщения с сервером. Повторите попытку позже.", MudBlazor.Severity.Error);
		}
		finally
		{
			//await SetLoadingAsync(false);
		}

		if (response is null) return;
		switch (response.Result)
		{
			case UpdateCameraUseCase.Result.Success:
				Snackbar.Add("Камера успешно сохранена", MudBlazor.Severity.Success);
				EventSystem.Publish(new CameraSelectEto(OrganizationId, _model.Id));
				break;
			case UpdateCameraUseCase.Result.ValidationError:
				Snackbar.Add("Не удалось сохранить изменения из-за ошибки валидации. Проверьте правильность заполнения полей", MudBlazor.Severity.Error);
				break;
			case UpdateCameraUseCase.Result.CameraNotFound:
				Snackbar.Add("Не удалось сохранить изменения, так как камера не найдена. Возможно камера уже удалена.", MudBlazor.Severity.Warning);
				Cancel();
				break;
			case UpdateCameraUseCase.Result.CameraPresetNotFound:
				Snackbar.Add("Не удалось сохранить изменения. Пресет не найден.", MudBlazor.Severity.Error);
				Cancel();
				break;
			case UpdateCameraUseCase.Result.CameraQuotaNotFound:
				Snackbar.Add("Не удалось сохранить изменения. Квота не найдена. Выберите другую квоту", MudBlazor.Severity.Error);
				Cancel();
				break;

			case UpdateCameraUseCase.Result.Unknown:
				Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(CameraEditComponent), nameof(UpdateCameraUseCase));
				Snackbar.Add($"Не удалось сохранить изменения из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
				break;

			default:
				Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(CameraEditComponent), nameof(UpdateCameraUseCase), response.Result);
				Snackbar.Add($"Не удалось сохранить изменения из-за ошибки: {response.Result}", MudBlazor.Severity.Error);
				break;
		}
	}
	private Task RefreshAsync() => FetchAsync();
	private void Cancel() => EventSystem.Publish(new CameraSelectEto(OrganizationId, CameraId));
	#endregion

	#region [Event Handlers]
	private void OnViewUriChanged(string uri)
	{
		if (_model is not null)
		{
			_model.ViewUri = uri;
			_archiveUriRef?.Validate();
			_publicUriRef?.Validate();
		}
	}

	private void OnArchiveUriChanged(string uri)
	{
		if (_model is not null)
		{
			_model.ArchiveUri = uri;
			_viewUriRef?.Validate();
			_publicUriRef?.Validate();
		}
	}

	private void OnPublicUriChanged(string uri)
	{
		if (_model is not null)
		{
			_model.PublicUri = uri;
			_viewUriRef?.Validate();
			_archiveUriRef?.Validate();
		}
	}

	private async void OnAppEventHandler(object appEvent)
	{
		switch (appEvent)
		{
			case SubscribeCameraUseCase.UpdatedEvent updatedEto:
				await FetchAsync();
				await UpdateViewAsync();
				break;

			case SubscribeCameraUseCase.DeletedEvent deletedEto:
				Snackbar.Add("Просматриваемая вами камеры была удалена", MudBlazor.Severity.Warning);
				CameraDrawer.Close();
				break;

			default:
				Snackbar.Add("Было получено непредвиденное событие.", MudBlazor.Severity.Warning);
				await FetchAsync();
				await UpdateViewAsync();
				Logger.LogWarning("Unexpected event in {UseCase}: {Event}", nameof(SubscribeCameraUseCase), nameof(appEvent));
				break;
		}
	}

	private void OnError(Exception exc)
	{
		Snackbar.Add("Ошибка при подписке на события", MudBlazor.Severity.Error);
		Logger.LogError(exc, exc.Message);
	}
	#endregion [Event Handlers]
}
