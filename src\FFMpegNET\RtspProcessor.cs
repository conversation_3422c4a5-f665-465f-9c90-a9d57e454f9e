using System.Buffers;
using System.Runtime.InteropServices;
using System.Threading.Channels;
using FFmpeg.AutoGen;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace FFMpegNET;

//TODO Заменить пуллинг буферов и потоков на стандартный .NET (но это не точно) или этот https://github.com/Microsoft/Microsoft.IO.RecyclableMemoryStream
//TODO Добавить возможность перезапуска обработки
//TODO Проверить устойчивость при возникновении ошибок

/// <summary>
/// Класс для обработки RTSP видеопотока с использованием FFmpeg.
/// Позволяет принимать RTSP поток и разбивать его на сегменты MPEGTS.
/// </summary>
public unsafe sealed class RtspProcessor : IDisposable
{
    public enum State
    {
        Initialized,
        Running,
        Stopped,
        Faulted,
        Disposed
    }

    public record Options
    {
        /// <summary>
        /// Тип транспорта RTSP
        /// </summary>
        public RtspTransportType RtspTransport { get; set; } = RtspTransportType.Tcp;

        /// <summary>
        /// Размер буфера в байтах
        /// </summary>
        public int BufferSize { get; set; } = 8192000;

        /// <summary>
        /// Максимальная задержка в микросекундах
        /// </summary>
        public int MaxDelay { get; set; } = 5000000;

        /// <summary>
        /// Размер очереди для переупорядочивания пакетов
        /// </summary>
        public int ReorderQueueSize { get; set; } = 10000;

        /// <summary>
        /// Таймаут сокета в микросекундах
        /// </summary>
        public int SocketTimeout { get; set; } = 10000000;

        /// <summary>
        /// Длительность сегмента в секундах
        /// </summary>
        public int SegmentDuration { get; set; } = 2;

        /// <summary>
        /// Минимальное количество кадров для расчета допуска разрыва
        /// </summary>
        public int MinFramesForGapCalc { get; set; } = 100;

        /// <summary>
        /// Максимальный допуск разрыва как доля от длительности сегмента
        /// </summary>
        public double MaxGapToleranceRatio { get; set; } = 0.1;

        /// <summary>
        /// Интервал между генерацией превью в количестве I-кадров
        /// </summary>
        public int PreviewFrameInterval { get; set; } = 10;

        /// <summary>
        /// Ширина превью (0 - использовать оригинальную ширину)
        /// </summary>
        public int PreviewWidth { get; set; } = 0;

        /// <summary>
        /// Высота превью (0 - использовать оригинальную высоту)
        /// </summary>
        public int PreviewHeight { get; set; } = 0;

        /// <summary>
        /// Качество JPEG для превью (1-100)
        /// </summary>
        public int PreviewQuality { get; set; } = 100;

        public int FifoSize { get; set; } = 10000000;

        public static Options Default => new Options();
    }

    #region Константы
    /// <summary>
    /// Максимальное количество пропущенных кадров для расчета допуска
    /// </summary>
    private const int _maxSkippedFrames = 3;

    /// <summary>
    /// Количество стандартных отклонений для адаптивного расчета допуска
    /// </summary>
    private const int _adaptiveStdDevMultiplier = 3;

    /// <summary>
    /// Максимальная задержка для выходного контекста (в микросекундах)
    /// </summary>
    private const int _outputMaxDelay = 500000;
    #endregion

    #region Настройки и зависимости
    private readonly ILogger<RtspProcessor> _logger;
    private readonly Options _options;
    private State _state;
    #endregion

    #region FFmpeg контексты и потоки
    // Основной контекст FFmpeg для входного потока
    private AVFormatContext* _inputFormatContext;
    // Контекст FFmpeg для выходного потока
    private AVFormatContext* _outputFormatContext;
    // Поток для записи выходных данных
    private AVStream* _outputVideoStream;
    // Буфер в памяти для хранения текущего сегмента
    private MemoryOutputBuffer? _currentSegmentBuffer;
    #endregion

    #region Индексы и параметры потоков
    // Индекс видеопотока среди всех потоков
    private int _videoStreamIndex;
    // Временная база для видео
    private AVRational _videoTimeBase;
    // Индекс аудио потока
    private int _audioStreamIndex;
    // Временная база для аудио
    private AVRational _audioTimeBase;
    // Частота кадров
    private double _videoFrameRate;
    #endregion

    #region Управление сегментами
    // Временная метка начала текущего сегмента
    private long _segmentStartPts;
    // Порядковый номер сегмента
    private int _segmentNumber;
    private long _audioSegmentStartPts;
    #endregion

    #region Временные метки и синхронизация
    private DateTimeOffset _streamStartTime;
    private long _firstPts;
    private long _lastPts;
    private DateTimeOffset? _lastSegmentEndTime;
    private readonly Queue<double> _frameIntervals;
    private long _lastFramePts = -1;
    #endregion


    #region Управление асинхронной обработкой
    private CancellationTokenSource? _runCts;
    private Task? _inputTask;
    private Task? _outputTask;
    private Channel<(MemoryOutputBuffer, DateTimeOffset, double)>? _streamChannel;
    private Channel<byte[]>? _previewChannel;
    private int _keyFrameCounter;
    private Task? _previewTask;

    private readonly EventWaitHandle _stoppedEvent;
    #endregion

    private class StreamStats
    {
        private const int _windowSize = 30;
        private const int _bitsInKilobit = 1024;
        private readonly Queue<(long bytes, long ticks)> _segments = new();
        private readonly object _lock = new();
        private long _windowBytes;
        private long _windowTicks;

        public void AddSegment(long bytes, double durationSeconds)
        {
            var durationTicks = (long)(durationSeconds * TimeSpan.TicksPerSecond);
            var now = DateTime.UtcNow.Ticks;

            lock (_lock)
            {
                // Удаляем устаревшие сегменты
                while (_segments.Count > 0)
                {
                    var oldest = _segments.Peek();
                    var age = (double)(now - oldest.ticks) / TimeSpan.TicksPerSecond;
                    if (age <= _windowSize)
                        break;

                    _segments.Dequeue();
                    _windowBytes -= oldest.bytes;
                    _windowTicks -= now - oldest.ticks;
                }

                // Добавляем новый сегмент
                _segments.Enqueue((bytes, now));
                _windowBytes += bytes;
                _windowTicks += durationTicks;
            }
        }

        public long GetBitRate()
        {
            lock (_lock)
            {
                if (_segments.Count == 0)
                    return 0;

                var durationSeconds = (double)_windowTicks / TimeSpan.TicksPerSecond;
                if (durationSeconds <= 0)
                    return 0;

                var bitRate = (long)(_windowBytes * 8 / _bitsInKilobit / durationSeconds);
                return bitRate;
            }
        }

        public void Reset()
        {
            lock (_lock)
            {
                _segments.Clear();
                _windowBytes = 0;
                _windowTicks = 0;
            }
        }
    }

    private readonly StreamStats _videoStats;
    private readonly StreamStats _audioStats;

    private long _videoTotalBytes;
    private long _audioTotalBytes;
    private bool _hasAudio;
    private bool _needNewSegment;

    #region Декодирование для превью
    private AVCodecContext* _decoderContext;
    private AVCodecContext* _encoderContext;
    private SwsContext* _swsContext;
    #endregion

    /// <summary>
    /// Инициализирует новый экземпляр процессора RTSP потока
    /// </summary>
    public RtspProcessor(ILogger<RtspProcessor> logger, IOptions<Options>? options = null)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _options = options?.Value ?? Options.Default;

        _frameIntervals = new Queue<double>();
        _videoStats = new StreamStats();
        _audioStats = new StreamStats();
        _stoppedEvent = new EventWaitHandle(false, EventResetMode.ManualReset);

        _state = State.Initialized;
    }

    public void ResetFields()
    {
        _currentSegmentBuffer = null;

        _frameIntervals.Clear();
        _videoStats.Reset();
        _audioStats.Reset();

        _videoTimeBase = new AVRational();
        _audioTimeBase = new AVRational();

        _videoStreamIndex = -1;
        _audioStreamIndex = -1;

        _segmentNumber = 0;
        _videoFrameRate = 0;

        _streamStartTime = DateTimeOffset.MinValue;

        _segmentStartPts = -1;
        _audioSegmentStartPts = -1;
        _firstPts = -1;
        _lastPts = -1;
        _lastFramePts = -1;
        _lastSegmentEndTime = null;

        _videoTotalBytes = 0;
        _audioTotalBytes = 0;
        _hasAudio = false;
        _needNewSegment = false;
        _keyFrameCounter = 0;

        _stoppedEvent.Reset();

        // Освобождаем ресурсы декодера, если они были выделены
        CleanupDecoderResources();
    }

    /// <summary>
    /// Основной метод сегментации видеопотока.
    /// Читает пакеты из входного потока, обрабатывает их и записывает в сегменты.
    /// Сегментация происходит на ключевых кадрах (I-frames) с минимальным интервалом в 2 секунды.
    /// </summary>
    public Task Run(string rtspUrl, bool enableAudio, Func<Segment, Task> onNextSegment, Func<byte[], Task> onNextPreview, CancellationToken cancellationToken)
    {
        ThrowIfDisposed();

        if (_state is State.Running)
        {
            throw new InvalidOperationException("Segmentation already running");
        }

        if (string.IsNullOrEmpty(rtspUrl))
        {
            throw new ArgumentNullException(nameof(rtspUrl));
        }

        ResetFields();

        CreateInputContext();

        ConfigureAndOpenInput(rtspUrl);

        LoadStreamInfo();

        FindAndConfigureStreams(rtspUrl, enableAudio);

        _hasAudio = enableAudio && _audioStreamIndex != -1;

        InitializeDecoder();

        _runCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);

        _streamChannel = Channel.CreateUnbounded<(MemoryOutputBuffer, DateTimeOffset, double)>(new UnboundedChannelOptions()
        {
            SingleWriter = true,
            SingleReader = true
        });

        _previewChannel = Channel.CreateUnbounded<byte[]>(new UnboundedChannelOptions()
        {
            SingleWriter = true,
            SingleReader = true
        });

        _inputTask = Task.Run(() =>
        {
            try
            {
                ProcessInputStream(_runCts.Token);
            }
            finally
            {
                CloseInputContext();

                if (!_runCts.IsCancellationRequested)
                {
                    _runCts.Cancel();
                }
            }
        }, cancellationToken);

        _outputTask = RtspProcessorHelper.ProcessOutputStream(_streamChannel, onNextSegment, _runCts.Token);

        _previewTask = RtspProcessorHelper.ProcessPreviewStream(_previewChannel, onNextPreview, _runCts.Token);

        _state = State.Running;

        return Task.WhenAll(_inputTask, _outputTask, _previewTask)
            .ContinueWith(task =>
            {
                _inputTask.Dispose();
                _outputTask.Dispose();
                _previewTask?.Dispose();
                _runCts.Dispose();
                _runCts = null;

                _state = task.IsFaulted ? State.Faulted : State.Stopped;

                _stoppedEvent.Set();
            }, cancellationToken);
    }

    /// <summary>
    /// Создает контекст для входного потока
    /// </summary>
    private void CreateInputContext()
    {
        _inputFormatContext = ffmpeg.avformat_alloc_context();
        if (_inputFormatContext == null)
        {
            throw new ApplicationException("Failed to allocate memory for input context.");
        }
    }

    /// <summary>
    /// Настраивает и открывает входной поток
    /// </summary>
    private void ConfigureAndOpenInput(string rtspUrl)
    {
        // Устанавливаем параметры буферизации для RTSP
        AVDictionary* dict = null;

        try
        {
            SetDictionaryValue(&dict, "rtsp_transport", _options.RtspTransport.ToString().ToLowerInvariant());
            SetDictionaryValue(&dict, "buffer_size", _options.BufferSize.ToString());
            SetDictionaryValue(&dict, "max_delay", _options.MaxDelay.ToString());
            SetDictionaryValue(&dict, "reorder_queue_size", _options.ReorderQueueSize.ToString());
            SetDictionaryValue(&dict, "timeout", _options.SocketTimeout.ToString());
            SetDictionaryValue(&dict, "fifo_size", _options.FifoSize.ToString());
            SetDictionaryValue(&dict, "fflags", "nobuffer");
            SetDictionaryValue(&dict, "analyzeduration", "2000000");
            SetDictionaryValue(&dict, "rtsp_flags", "prefer_tcp");

            fixed (AVFormatContext** inputFormatContext = &_inputFormatContext)
            {
                if (ffmpeg.avformat_open_input(inputFormatContext, rtspUrl, null, &dict) < 0)
                {
                    throw new ApplicationException($"Failed to open stream by URL: {rtspUrl}");
                }
            }

            //TODO учесть задержку сети?
            _streamStartTime = DateTimeOffset.UtcNow;
        }
        finally
        {
            if (dict != null)
            {
                ffmpeg.av_dict_free(&dict);
            }
        }
    }

    private void CloseInputContext()
    {
        fixed (AVFormatContext** ppFormatContext = &_inputFormatContext)
        {
            if (_inputFormatContext != null)
            {
                ffmpeg.avformat_close_input(ppFormatContext);
                _inputFormatContext = null;
            }
        }
    }

    /// <summary>
    /// Устанавливает значение в словаре с проверкой результата
    /// </summary>
    private static void SetDictionaryValue(AVDictionary** dict, string key, string value)
    {
        int result = ffmpeg.av_dict_set(dict, key, value, 0);
        if (result < 0)
        {
            throw new ApplicationException($"Failed to set {key}: {result}");
        }
    }

    /// <summary>
    /// Загружает информацию о потоке
    /// </summary>
    private void LoadStreamInfo()
    {
        if (ffmpeg.avformat_find_stream_info(_inputFormatContext, null) < 0)
        {
            throw new ApplicationException("Failed to retrieve stream information.");
        }
    }

    /// <summary>
    /// Ищет и настраивает видео и аудио потоки
    /// </summary>
    private void FindAndConfigureStreams(string rtspUrl, bool enableAudio)
    {
        _videoStreamIndex = -1;
        _audioStreamIndex = -1;

        for (int i = 0; i < _inputFormatContext->nb_streams; i++)
        {
            var stream = _inputFormatContext->streams[i];
            var codecType = stream->codecpar->codec_type;

            if (codecType == AVMediaType.AVMEDIA_TYPE_VIDEO)
            {
                ConfigureVideoStream(i, stream);
            }
            else if (enableAudio && codecType == AVMediaType.AVMEDIA_TYPE_AUDIO)
            {
                ConfigureAudioStream(i, stream);
            }

            if (ShouldStopStreamSearch(enableAudio))
            {
                break;
            }
        }

        ValidateStreams(enableAudio);
    }

    /// <summary>
    /// Настраивает видеопоток
    /// </summary>
    private void ConfigureVideoStream(int index, AVStream* videoStream)
    {
        _videoStreamIndex = index;
        _videoTimeBase = videoStream->time_base;

        _logger.LogInformation("Input stream parameters: time_base={TimeBase}, avg_frame_rate={AvgFrameRate}, r_frame_rate={RFrameRate}",
            $"{videoStream->time_base.num}/{videoStream->time_base.den}",
            $"{videoStream->avg_frame_rate.num}/{videoStream->avg_frame_rate.den}",
            $"{videoStream->r_frame_rate.num}/{videoStream->r_frame_rate.den}");

        ConfigureFrameRate(videoStream);
    }

    /// <summary>
    /// Настраивает частоту кадров
    /// </summary>
    private void ConfigureFrameRate(AVStream* videoStream)
    {
        if (videoStream->avg_frame_rate.den != 0)
        {
            _videoFrameRate = (double)videoStream->avg_frame_rate.num / videoStream->avg_frame_rate.den;
            _logger.LogInformation("Frame rate: {FrameRate:F2} fps", _videoFrameRate);
        }
        else if (videoStream->r_frame_rate.den != 0)
        {
            _videoFrameRate = (double)videoStream->r_frame_rate.num / videoStream->r_frame_rate.den;
            _logger.LogInformation("Frame rate (real): {FrameRate:F2} fps", _videoFrameRate);
        }
        else
        {
            _logger.LogInformation("Unable to determine frame rate, using adaptive calculation");
            _videoFrameRate = 0;
        }
    }

    /// <summary>
    /// Настраивает аудиопоток
    /// </summary>
    private void ConfigureAudioStream(int index, AVStream* stream)
    {
        _audioStreamIndex = index;
        _audioTimeBase = stream->time_base;
        _logger.LogInformation("Found audio stream with index {Index}", index);
    }

    /// <summary>
    /// Проверяет, нужно ли прекратить поиск потоков
    /// </summary>
    private bool ShouldStopStreamSearch(bool enableAudio)
    {
        if (_videoStreamIndex != -1)
        {
            if (!enableAudio || _audioStreamIndex != -1)
            {
                return true;
            }
        }
        return false;
    }

    /// <summary>
    /// Проверяет корректность найденных потоков
    /// </summary>
    private void ValidateStreams(bool enableAudio)
    {
        if (_videoStreamIndex == -1)
        {
            throw new ApplicationException("Video stream not found.");
        }

        if (enableAudio && _audioStreamIndex == -1)
        {
            _logger.LogWarning("Audio processing was enabled but no audio stream found");
        }
    }

    #region Создание и настройка выходного потока

    /// <summary>
    /// Создаёт новый сегмент для записи части видеопотока в формате MPEGTS
    /// </summary>
    private void CreateOutputSegment()
    {
        ThrowIfDisposed();

        _logger.LogInformation("Creating segment {SegmentNumber:D3}", _segmentNumber);

        CreateOutputFormatContext();
        CreateAndSetupVideoStream();

        if (_audioStreamIndex != -1)
        {
            CreateAndSetupAudioStream();
        }

        SetupOutputFormatFlags();
        InitializeSegmentBuffer();
        InitializeSegmentPts();
    }

    /// <summary>
    /// Создает контекст формата для выходного потока
    /// </summary>
    private void CreateOutputFormatContext()
    {
        fixed (AVFormatContext** ppOutputContext = &_outputFormatContext)
        {
            if (ffmpeg.avformat_alloc_output_context2(ppOutputContext, null,
                OutputStreamFormat.MpegTs.ToString().ToLowerInvariant(), null) < 0)
            {
                throw new ApplicationException("Failed to create output segment context.");
            }
        }
    }

    /// <summary>
    /// Создает и настраивает видеопоток для выходного сегмента
    /// </summary>
    private void CreateAndSetupVideoStream()
    {
        _outputVideoStream = ffmpeg.avformat_new_stream(_outputFormatContext, null);
        if (_outputVideoStream == null)
        {
            throw new ApplicationException("Failed to create stream for segment.");
        }

        // Копируем параметры кодека из входного видеопотока в выходной
        if (ffmpeg.avcodec_parameters_copy(_outputVideoStream->codecpar,
            _inputFormatContext->streams[_videoStreamIndex]->codecpar) < 0)
        {
            throw new ApplicationException("Error copying codec parameters.");
        }

        // Устанавливаем параметры тайминга и фреймрейта
        _outputVideoStream->time_base = _inputFormatContext->streams[_videoStreamIndex]->time_base;
    }

    /// <summary>
    /// Создает и настраивает аудиопоток для выходного сегмента
    /// </summary>
    private void CreateAndSetupAudioStream()
    {
        var outputAudioStream = ffmpeg.avformat_new_stream(_outputFormatContext, null);
        if (outputAudioStream == null)
        {
            throw new ApplicationException("Failed to create audio stream for segment.");
        }

        // Копируем параметры аудио кодека
        if (ffmpeg.avcodec_parameters_copy(outputAudioStream->codecpar,
                                           _inputFormatContext->streams[_audioStreamIndex]->codecpar) < 0)
        {
            throw new ApplicationException("Error copying audio codec parameters.");
        }

        outputAudioStream->time_base = _inputFormatContext->streams[_audioStreamIndex]->time_base;
        outputAudioStream->codecpar->codec_tag = 0;
    }

    /// <summary>
    /// Устанавливает флаги для выходного формата
    /// </summary>
    private void SetupOutputFormatFlags()
    {
        _outputFormatContext->flags |= ffmpeg.AVFMT_FLAG_IGNDTS;
        _outputFormatContext->flags |= ffmpeg.AVFMT_FLAG_GENPTS;
        _outputFormatContext->flags &= ~ffmpeg.AVFMT_FLAG_NOBUFFER;

        // Устанавливаем максимальное время задержки
        _outputFormatContext->max_delay = _outputMaxDelay;
    }

    /// <summary>
    /// Инициализирует буфер для сегмента и записывает заголовок
    /// </summary>
    private void InitializeSegmentBuffer()
    {
        // Инициализируем буфер в памяти для хранения данных сегмента
        _currentSegmentBuffer = new MemoryOutputBuffer();
        _outputFormatContext->pb = _currentSegmentBuffer.AvioContext;

        // Записываем заголовок MPEGTS
        if (ffmpeg.avformat_write_header(_outputFormatContext, null) < 0)
        {
            throw new ApplicationException("Error writing segment header.");
        }
    }

    /// <summary>
    /// Инициализирует временные метки для нового сегмента
    /// </summary>
    private void InitializeSegmentPts()
    {
        // Инициализируем временную метку только для первого сегмента
        if (_segmentNumber == 0)
        {
            _segmentStartPts = -1;
        }

        _audioSegmentStartPts = -1;
    }

    #endregion

    /// <summary>
    /// Закрывает текущий сегмент и подготавливает данные для обработки
    /// </summary>
    private void CloseOutputSegment()
    {
        ThrowIfDisposed();

        if (_outputFormatContext != null && _currentSegmentBuffer != null)
        {
            // Записываем завершающие данные сегмента
            ffmpeg.av_write_trailer(_outputFormatContext);

            _currentSegmentBuffer.Stream.Position = 0;

            WriteSegment(_currentSegmentBuffer);

            ffmpeg.avformat_free_context(_outputFormatContext);
            _outputFormatContext = null;
            _segmentNumber++;
        }
    }

    /// <summary>
    /// Обработка готового сегмента видео
    /// </summary>
    /// <param name="outputBuffer">Буфер памяти с данными сегмента в формате MPEGTS</param>
    private void WriteSegment(MemoryOutputBuffer outputBuffer)
    {
        ThrowIfDisposed();

        var duration = (_lastPts - _segmentStartPts) * _videoTimeBase.num / (double)_videoTimeBase.den;

        // Считаем что в сегменте видео и аудио распределены пропорционально их общему размеру

        //TODO надо считать более точно для каждого
        if (_videoTotalBytes + _audioTotalBytes > 0)
        {
            var videoRatio = (double)_videoTotalBytes / (_videoTotalBytes + _audioTotalBytes);
            var videoBytes = (long)(outputBuffer.Stream.Length * videoRatio);
            var audioBytes = outputBuffer.Stream.Length - videoBytes;

            _videoStats.AddSegment(videoBytes, duration);

            if (_hasAudio)
            {
                _audioStats.AddSegment(audioBytes, duration);
            }
        }

        var currentTime = _firstPts >= 0
            ? _streamStartTime.AddSeconds((double)_segmentStartPts * _videoTimeBase.num / _videoTimeBase.den)
            : _streamStartTime;

        // Проверяем разрыв используя рассчитанный допуск
        var hasGap = false;
        if (_lastSegmentEndTime.HasValue)
        {
            var expectedStartTime = _lastSegmentEndTime.Value;
            var actualStartTime = currentTime;
            var gapTolerance = CalculateGapTolerance();
            hasGap = (actualStartTime - expectedStartTime).TotalSeconds > gapTolerance;
        }

        _logger.LogInformation(
            "Segment {SegmentNumber:D3} | Start time: {Time} | Duration: {Duration:F3} sec | Size: {Size} bytes | Time base: {TimeBaseNum}/{TimeBaseDen}",
            _segmentNumber,
            currentTime.ToString("yyyy-MM-dd HH:mm:ss.fff"),
            duration,
            outputBuffer.Stream.Length,
            _videoTimeBase.num,
            _videoTimeBase.den
        );

        if (hasGap)
        {
            _logger.LogWarning("WARNING: Stream gap detected!");
        }

        outputBuffer.Stream.Position = 0;

        if (!_streamChannel!.Writer.TryWrite((outputBuffer, currentTime, duration)))
        {
            _logger.LogError("Failed to write segment to channel");
        }

        _lastSegmentEndTime = currentTime.AddSeconds(duration);
    }

    private double CalculateGapTolerance()
    {
        ThrowIfDisposed();

        // Если знаем частоту кадров, используем её
        if (_videoFrameRate > 0)
        {
            // Допускаем пропуск до MAX_SKIPPED_FRAMES кадров, но не больше MAX_GAP_TOLERANCE_RATIO
            double expectedFrameInterval = 1.0 / _videoFrameRate;
            var tolerance = Math.Min(expectedFrameInterval * _maxSkippedFrames, _options.SegmentDuration * _options.MaxGapToleranceRatio);
            _logger.LogInformation(
                "Frame rate based tolerance: {Tolerance:F3} sec (frame interval: {ExpectedInterval:F3} sec)",
                tolerance,
                expectedFrameInterval
            );
            return tolerance;
        }

        // Иначе используем адаптивный расчет
        if (_frameIntervals.Count < _options.MinFramesForGapCalc)
            return _options.SegmentDuration * _options.MaxGapToleranceRatio;

        var intervals = _frameIntervals.ToArray();
        var mean = intervals.Average();
        var variance = intervals.Select(x => Math.Pow(x - mean, 2)).Average();
        var stdDev = Math.Sqrt(variance);

        var adaptiveTolerance = Math.Min(mean + _adaptiveStdDevMultiplier * stdDev, _options.SegmentDuration * _options.MaxGapToleranceRatio);
        _logger.LogInformation(
            "Adaptive tolerance: {AdaptiveTolerance:F3} sec (mean: {Mean:F3}, std.dev: {StdDev:F3})",
            adaptiveTolerance,
            mean,
            stdDev
        );
        return adaptiveTolerance;
    }

    private void UpdateFrameInterval(long currentPts)
    {
        ThrowIfDisposed();

        if (_lastFramePts >= 0)
        {
            var interval = (currentPts - _lastFramePts) * _videoTimeBase.num / (double)_videoTimeBase.den;

            // Добавляем интервал в очередь
            _frameIntervals.Enqueue(interval);

            // Держим только последние N интервалов
            if (_frameIntervals.Count > _options.MinFramesForGapCalc)
            {
                _frameIntervals.Dequeue();
            }
        }

        _lastFramePts = currentPts;
    }

    #region Обработка входного потока

    private void ProcessInputStream(CancellationToken cancellationToken)
    {
        ThrowIfDisposed();

        var packet = AllocatePacket();

        try
        {
            CreateOutputSegment();
            ProcessInputFrames(packet, cancellationToken);
            CloseOutputSegment();
            _streamChannel!.Writer.Complete();
            _previewChannel!.Writer.Complete();
        }
        finally
        {
            ffmpeg.av_packet_free(&packet);
        }
    }

    /// <summary>
    /// Выделяет память под пакет данных
    /// </summary>
    private static AVPacket* AllocatePacket()
    {
        var packet = ffmpeg.av_packet_alloc();
        if (packet == null)
        {
            throw new ApplicationException("Failed to allocate memory for packet.");
        }
        return packet;
    }

    /// <summary>
    /// Обрабатывает кадры из входного потока
    /// </summary>
    private void ProcessInputFrames(AVPacket* packet, CancellationToken cancellationToken)
    {
        _needNewSegment = false;

        while (!cancellationToken.IsCancellationRequested &&
               ffmpeg.av_read_frame(_inputFormatContext, packet) >= 0)
        {
            if (packet->stream_index == _videoStreamIndex)
            {
                ProcessVideoPacket(packet);
            }
            else if (packet->stream_index == _audioStreamIndex)
            {
                ProcessAudioPacket(packet);
            }

            ffmpeg.av_packet_unref(packet);
        }
    }

    /// <summary>
    /// Обрабатывает пакет видеоданных
    /// </summary>
    private void ProcessVideoPacket(AVPacket* packet)
    {
        _videoTotalBytes += packet->size;

        UpdatePacketPtsDts(packet);
        UpdateFrameInterval(packet->pts);
        UpdateFirstPts(packet);

        if (_segmentStartPts < 0)
        {
            _segmentStartPts = packet->pts;
        }

        var (elapsedPts, elapsedSec) = CalculateSegmentDuration(packet);
        bool isKeyFrame = (packet->flags & ffmpeg.AV_PKT_FLAG_KEY) != 0;

        // Проверяем, нужно ли создать превью из этого I-фрейма
        if (isKeyFrame)
        {
            // Если это первый I-кадр (счетчик равен 0) или достигнут интервал
            if (_keyFrameCounter == 0 || _keyFrameCounter >= _options.PreviewFrameInterval)
            {
                try
                {
                    GeneratePreviewFromKeyFrame(packet);
                    _keyFrameCounter = 1; // Сбрасываем счетчик на 1 после создания превью
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error generating preview from key frame");
                }
            }
            else
            {
                // Увеличиваем счетчик только если не создавали превью
                _keyFrameCounter++;
            }
        }

        CheckSegmentDuration(elapsedSec);

        // Если нужен новый сегмент и это ключевой кадр, создаем его
        if (HandleKeyFrame(isKeyFrame, elapsedSec, packet->pts))
        {
            return;
        }

        WriteVideoPacket(packet);
    }

    /// <summary>
    /// Обновляет временные метки пакета при необходимости
    /// </summary>
    private void UpdatePacketPtsDts(AVPacket* packet)
    {
        if (packet->pts == ffmpeg.AV_NOPTS_VALUE)
        {
            packet->pts = _lastPts + 1;
        }
        if (packet->dts == ffmpeg.AV_NOPTS_VALUE)
        {
            packet->dts = packet->pts;
        }
    }

    /// <summary>
    /// Обновляет первую временную метку потока
    /// </summary>
    private void UpdateFirstPts(AVPacket* packet)
    {
        if (_firstPts == -1 && packet->pts != ffmpeg.AV_NOPTS_VALUE)
        {
            _firstPts = packet->pts;
            //_streamStartTime = DateTimeOffset.UtcNow;
            _logger.LogInformation("First PTS: {Pts}", _firstPts);
        }
    }

    /// <summary>
    /// Рассчитывает информацию о текущем сегменте
    /// </summary>
    private (long ElapsedPts, double ElapsedSec) CalculateSegmentDuration(AVPacket* packet)
    {
        long elapsedPts = packet->pts - _segmentStartPts;
        double elapsedSec = elapsedPts * _videoTimeBase.num / (double)_videoTimeBase.den;
        return (elapsedPts, elapsedSec);
    }

    /// <summary>
    /// Проверяет длительность сегмента и устанавливает флаг необходимости нового сегмента
    /// </summary>
    private void CheckSegmentDuration(double elapsedSec)
    {
        if (elapsedSec >= _options.SegmentDuration && !_needNewSegment)
        {
            _needNewSegment = true;
            _logger.LogInformation("Segment exceeded {Duration} sec, waiting for keyframe", _options.SegmentDuration);
        }
    }

    /// <summary>
    /// Обрабатывает ключевой кадр и создает новый сегмент при необходимости
    /// </summary>
    private bool HandleKeyFrame(bool isKeyFrame, double elapsedSec, long framePts)
    {
        if (_needNewSegment && isKeyFrame)
        {
            _logger.LogInformation("Closing segment on keyframe, duration: {Duration:F3} sec", elapsedSec);
            _lastPts = framePts;

            CloseOutputSegment();

            // Если запрошена остановка, завершаем после закрытия сегмента
            if (_runCts!.IsCancellationRequested)
            {
                return true;
            }

            CreateOutputSegment();
            _segmentStartPts = framePts;
            _needNewSegment = false;
        }

        return false;
    }

    /// <summary>
    /// Записывает пакет видеоданных в выходной поток
    /// </summary>
    private void WriteVideoPacket(AVPacket* packet)
    {
        AVPacket* outPacket = AllocatePacket();

        try
        {
            if (ffmpeg.av_packet_ref(outPacket, packet) < 0)
            {
                throw new ApplicationException("Error copying packet.");
            }

            outPacket->stream_index = _videoStreamIndex;

            AdjustPacketPtsDts(outPacket,
                _inputFormatContext->streams[_videoStreamIndex]->time_base,
                _outputVideoStream->time_base);

            WritePacketToOutput(outPacket);
        }
        finally
        {
            ffmpeg.av_packet_free(&outPacket);
        }
    }

    /// <summary>
    /// Обрабатывает пакет аудиоданных
    /// </summary>
    private void ProcessAudioPacket(AVPacket* packet)
    {
        _audioTotalBytes += packet->size;

        WriteAudioPacket(packet);
    }

    /// <summary>
    /// Записывает пакет аудиоданных в выходной поток
    /// </summary>
    private void WriteAudioPacket(AVPacket* packet)
    {
        AVPacket* outPacket = ffmpeg.av_packet_alloc();
        if (outPacket == null)
        {
            throw new ApplicationException("Failed to allocate memory for output audio packet.");
        }

        try
        {
            if (ffmpeg.av_packet_ref(outPacket, packet) < 0)
            {
                throw new ApplicationException("Error copying audio packet.");
            }

            outPacket->stream_index = _audioStreamIndex;

            AdjustPacketPtsDts(outPacket,
                _inputFormatContext->streams[_audioStreamIndex]->time_base,
                _outputFormatContext->streams[_audioStreamIndex]->time_base);

            WritePacketToOutput(outPacket);
        }
        finally
        {
            ffmpeg.av_packet_free(&outPacket);
        }
    }

    /// <summary>
    /// Корректирует временные метки пакета относительно начала сегмента
    /// </summary>
    private static void AdjustPacketPtsDts(AVPacket* packet, AVRational sourceTimeBase, AVRational destinationTimeBase)
    {
        // Используем оригинальные временные метки из входного потока
        ffmpeg.av_packet_rescale_ts(packet, sourceTimeBase, destinationTimeBase);
    }

    /// <summary>
    /// Записывает пакет в выходной поток
    /// </summary>
    private void WritePacketToOutput(AVPacket* outPacket)
    {
        if (ffmpeg.av_interleaved_write_frame(_outputFormatContext, outPacket) < 0)
        {
            _logger.LogError("Error writing packet");
        }
    }

    #endregion

    /// <summary>
    /// Запрашивает плавную остановку обработки после завершения текущего сегмента
    /// </summary>
    public void RequestGracefulStop()
    {
        ThrowIfDisposed();

        if (_state is State.Running)
        {
            _runCts!.Cancel();
        }
    }

    ~RtspProcessor()
    {
        Dispose(false);
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    private void Dispose(bool disposing)
    {
        if (_state is State.Disposed)
            return;

        if (disposing)
        {
            if (_state is State.Running)
            {
                _runCts?.Cancel();

                _stoppedEvent.WaitOne();
                _stoppedEvent.Dispose();
            }
        }

        CloseInputContext();
        CleanupDecoderResources();

        _state = State.Disposed;
    }

    /// <summary>
    /// Освобождает ресурсы декодера и энкодера
    /// </summary>
    private void CleanupDecoderResources()
    {
        if (_swsContext != null)
        {
            ffmpeg.sws_freeContext(_swsContext);
            _swsContext = null;
        }

        if (_decoderContext != null)
        {
            fixed (AVCodecContext** ppCodecContext = &_decoderContext)
            {
                ffmpeg.avcodec_free_context(ppCodecContext);
                _decoderContext = null;
            }
        }

        if (_encoderContext != null)
        {
            fixed (AVCodecContext** ppCodecContext = &_encoderContext)
            {
                ffmpeg.avcodec_free_context(ppCodecContext);
                _encoderContext = null;
            }
        }
    }

    private void ThrowIfDisposed()
    {
        ObjectDisposedException.ThrowIf(_state is State.Disposed, nameof(RtspProcessor));
    }

    /// <summary>
    /// Инициализирует декодер для обработки видеокадров
    /// </summary>
    private void InitializeDecoder()
    {
        if (_videoStreamIndex == -1)
            return;

        var videoStream = _inputFormatContext->streams[_videoStreamIndex];
        var decoder = ffmpeg.avcodec_find_decoder(videoStream->codecpar->codec_id);

        if (decoder == null)
        {
            _logger.LogWarning("Failed to find decoder for video stream");
            throw new ApplicationException("Failed to find decoder for video stream");
        }

        _decoderContext = ffmpeg.avcodec_alloc_context3(decoder);
        if (_decoderContext == null)
        {
            _logger.LogError("Failed to allocate memory for decoder context");
            throw new ApplicationException("Failed to allocate memory for decoder context");
        }

        if (ffmpeg.avcodec_parameters_to_context(_decoderContext, videoStream->codecpar) < 0)
        {
            _logger.LogError("Failed to copy codec parameters");
            CleanupDecoderResources();
            throw new ApplicationException("Failed to copy codec parameters");
        }

        if (ffmpeg.avcodec_open2(_decoderContext, decoder, null) < 0)
        {
            _logger.LogError("Failed to open decoder");
            CleanupDecoderResources();
            throw new ApplicationException("Failed to open decoder");
        }

        // Инициализация контекста масштабирования, если нужно изменить размер превью
        int targetWidth = _options.PreviewWidth > 0 ? _options.PreviewWidth : _decoderContext->width;
        int targetHeight = _options.PreviewHeight > 0 ? _options.PreviewHeight : _decoderContext->height;

        // Log source pixel format
        _logger.LogInformation("Source pixel format: {PixFmt}, Width: {Width}, Height: {Height}",
            _decoderContext->pix_fmt, _decoderContext->width, _decoderContext->height);

        // Use a safer approach for pixel format conversion
        AVPixelFormat srcFormat = _decoderContext->pix_fmt;
        AVPixelFormat dstFormat = AVPixelFormat.AV_PIX_FMT_YUV420P;

        // If source format is unknown, use a default format
        if (srcFormat == AVPixelFormat.AV_PIX_FMT_NONE)
        {
            _logger.LogWarning("Source pixel format is AV_PIX_FMT_NONE, using AV_PIX_FMT_YUV420P as fallback");
            srcFormat = AVPixelFormat.AV_PIX_FMT_YUV420P;
        }

        // Create scaling context with explicit flags for better compatibility
        _swsContext = ffmpeg.sws_getContext(
            _decoderContext->width, _decoderContext->height, srcFormat,
            targetWidth, targetHeight, dstFormat,
            ffmpeg.SWS_BILINEAR | ffmpeg.SWS_ACCURATE_RND, null, null, null);

        if (_swsContext == null)
        {
            _logger.LogError("Failed to create scaling context. Source format: {SourceFormat}, Target format: {TargetFormat}",
                _decoderContext->pix_fmt, AVPixelFormat.AV_PIX_FMT_YUV420P);
            CleanupDecoderResources();
            throw new ApplicationException("Failed to create scaling context");
        }

        _logger.LogInformation("Successfully created scaling context. Source format: {SourceFormat}, Target format: {TargetFormat}, Dimensions: {Width}x{Height} -> {TargetWidth}x{TargetHeight}",
            _decoderContext->pix_fmt, AVPixelFormat.AV_PIX_FMT_YUV420P, _decoderContext->width, _decoderContext->height, targetWidth, targetHeight);

        // Инициализация JPEG энкодера для превью
        InitializeJpegEncoder(targetWidth, targetHeight);
    }

    /// <summary>
    /// Инициализирует JPEG энкодер для создания превью
    /// </summary>
    private void InitializeJpegEncoder(int width, int height)
    {
        var encoder = ffmpeg.avcodec_find_encoder(AVCodecID.AV_CODEC_ID_MJPEG);
        if (encoder == null)
        {
            _logger.LogError("Failed to find JPEG encoder");
            throw new ApplicationException("Failed to find JPEG encoder");
        }

        _encoderContext = ffmpeg.avcodec_alloc_context3(encoder);
        if (_encoderContext == null)
        {
            _logger.LogError("Failed to allocate memory for encoder context");
            throw new ApplicationException("Failed to allocate memory for encoder context");
        }

        _encoderContext->width = width;
        _encoderContext->height = height;
        _encoderContext->time_base = new AVRational { num = 1, den = 25 };

        // For MJPEG encoder, use YUVJ420P which is the preferred format for JPEG
        _encoderContext->pix_fmt = AVPixelFormat.AV_PIX_FMT_YUVJ420P;
        _encoderContext->codec_type = AVMediaType.AVMEDIA_TYPE_VIDEO;

        // Set color range explicitly to JPEG (full range)
        _encoderContext->color_range = AVColorRange.AVCOL_RANGE_JPEG;

        // Set additional parameters for better compatibility
        _encoderContext->strict_std_compliance = ffmpeg.FF_COMPLIANCE_NORMAL;
        _encoderContext->flags = ffmpeg.AV_CODEC_FLAG_QSCALE;
        _encoderContext->global_quality = 10 * ffmpeg.FF_QP2LAMBDA; // Higher quality

        // Устанавливаем качество JPEG
        AVDictionary* dict = null;
        try
        {
            SetDictionaryValue(&dict, "quality", _options.PreviewQuality.ToString());

            if (ffmpeg.avcodec_open2(_encoderContext, encoder, &dict) < 0)
            {
                _logger.LogError("Failed to open JPEG encoder");
                fixed (AVCodecContext** ppCodecContext = &_encoderContext)
                {
                    ffmpeg.avcodec_free_context(ppCodecContext);
                    _encoderContext = null;
                }
                throw new ApplicationException("Failed to open JPEG encoder");
            }
        }
        finally
        {
            if (dict != null)
            {
                ffmpeg.av_dict_free(&dict);
            }
        }
    }

    /// <summary>
    /// Генерирует превью из ключевого кадра
    /// </summary>
    private void GeneratePreviewFromKeyFrame(AVPacket* packet)
    {
        if (_decoderContext == null || _swsContext == null || _encoderContext == null)
            return;

        // Декодируем пакет в кадр
        var frame = ffmpeg.av_frame_alloc();
        var scaledFrame = ffmpeg.av_frame_alloc();
        var jpegPacket = ffmpeg.av_packet_alloc();

        try
        {
            // Send packet to decoder
            int ret = ffmpeg.avcodec_send_packet(_decoderContext, packet);
            if (ret < 0)
            {
                _logger.LogError("Error sending packet to decoder: {Error}", ret);
                throw new ApplicationException($"Error sending packet to decoder: {ret}");
            }

            // Receive decoded frame
            ret = ffmpeg.avcodec_receive_frame(_decoderContext, frame);
            if (ret < 0)
            {
                _logger.LogError("Error receiving frame from decoder: {Error}", ret);
                throw new ApplicationException($"Error receiving frame from decoder: {ret}");
            }

            // Prepare frame for scaling
            scaledFrame->width = _encoderContext->width;
            scaledFrame->height = _encoderContext->height;
            scaledFrame->format = (int)_encoderContext->pix_fmt;

            // Set color range to match the encoder
            scaledFrame->color_range = _encoderContext->color_range;

            // Allocate buffer for frame
            ret = ffmpeg.av_frame_get_buffer(scaledFrame, 32);
            if (ret < 0)
            {
                _logger.LogError("Error allocating buffer for frame: {Error}", ret);
                throw new ApplicationException($"Error allocating buffer for frame: {ret}");
            }

            // Log frame information before scaling
            _logger.LogDebug("Scaling frame: Source format={SourceFormat}, Source dimensions={Width}x{Height}, Target format={TargetFormat}, Target dimensions={TargetWidth}x{TargetHeight}",
                frame->format, frame->width, frame->height, scaledFrame->format, scaledFrame->width, scaledFrame->height);

            try
            {
                // Check if we need to recreate the swscaler context for this specific frame
                if (_swsContext == null)
                {
                    _logger.LogWarning("SwsContext is null, creating a new one for this frame");

                    // Create a new swscaler context specifically for this frame
                    _swsContext = ffmpeg.sws_getContext(
                        frame->width, frame->height, (AVPixelFormat)frame->format,
                        scaledFrame->width, scaledFrame->height, (AVPixelFormat)scaledFrame->format,
                        ffmpeg.SWS_BILINEAR | ffmpeg.SWS_ACCURATE_RND, null, null, null);

                    if (_swsContext == null)
                    {
                        _logger.LogError("Failed to create scaling context for frame");
                        throw new ApplicationException("Failed to create scaling context for frame");
                    }
                }

                // Scale the frame with additional error checking
                ret = ffmpeg.sws_scale(_swsContext, frame->data, frame->linesize, 0, frame->height,
                    scaledFrame->data, scaledFrame->linesize);

                if (ret <= 0)
                {
                    _logger.LogError("Error scaling frame: {Error}, Source format: {SrcFormat}, Target format: {DstFormat}",
                        ret, frame->format, scaledFrame->format);
                    throw new ApplicationException($"Error scaling frame: {ret}");
                }

                _logger.LogDebug("Successfully scaled frame, output height: {Height}", ret);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception during frame scaling. Source format: {SrcFormat}, Target format: {DstFormat}, Dimensions: {Width}x{Height} -> {TargetWidth}x{TargetHeight}",
                    frame->format, scaledFrame->format, frame->width, frame->height, scaledFrame->width, scaledFrame->height);
                throw;
            }

            // Send frame to JPEG encoder
            ret = ffmpeg.avcodec_send_frame(_encoderContext, scaledFrame);
            if (ret < 0)
            {
                _logger.LogError("Error sending frame to encoder: {Error}", ret);
                throw new ApplicationException($"Error sending frame to encoder: {ret}");
            }

            // Receive encoded JPEG packet
            ret = ffmpeg.avcodec_receive_packet(_encoderContext, jpegPacket);
            if (ret < 0)
            {
                _logger.LogError("Error receiving JPEG packet: {Error}", ret);
                throw new ApplicationException($"Error receiving JPEG packet: {ret}");
            }

            // Копируем данные JPEG в управляемый буфер
            byte[] jpegData = ArrayPool<byte>.Shared.Rent(jpegPacket->size);
            Marshal.Copy((IntPtr)jpegPacket->data, jpegData, 0, jpegPacket->size);

            // Отправляем превью через канал
            if (_previewChannel != null)
            {
                if (!_previewChannel.Writer.TryWrite(jpegData))
                {
                    _logger.LogWarning("Failed to write preview to channel");
                }
            }
        }
        finally
        {
            ffmpeg.av_frame_free(&frame);
            ffmpeg.av_frame_free(&scaledFrame);
            ffmpeg.av_packet_free(&jpegPacket);
        }
    }

    /// <summary>
    /// Возвращает информацию о текущем потоке
    /// </summary>
    /// <returns>Информация о медиапотоке или null, если поток не открыт</returns>
    public StreamInfo? GetStreamInfo()
    {
        ThrowIfDisposed();

        if (_inputFormatContext == null || _videoStreamIndex < 0)
        {
            return null;
        }

        var videoStream = _inputFormatContext->streams[_videoStreamIndex];
        var videoCodec = ffmpeg.avcodec_find_decoder(videoStream->codecpar->codec_id);

        var videoBitRate = _videoStats.GetBitRate();
        var audioBitRate = _hasAudio ? _audioStats.GetBitRate() : 0;
        var totalBitRate = videoBitRate + audioBitRate;

        _logger.LogInformation(
            "Информация о потоке: {Width}x{Height} @ {FrameRate:F2} fps, Видео: {VideoBitRate} Кбит/с, Аудио: {AudioBitRate} Кбит/с, Всего: {TotalBitRate} Кбит/с",
            videoStream->codecpar->width,
            videoStream->codecpar->height,
            _videoFrameRate,
            videoBitRate,
            audioBitRate,
            totalBitRate
        );

        var info = new StreamInfo
        {
            Width = videoStream->codecpar->width,
            Height = videoStream->codecpar->height,
            FrameRate = _videoFrameRate,
            BitRate = totalBitRate,
            VideoCodec = Marshal.PtrToStringAnsi((IntPtr)videoCodec->name) ?? "unknown",
            HasAudio = _audioStreamIndex >= 0
        };

        if (_audioStreamIndex >= 0)
        {
            var audioStream = _inputFormatContext->streams[_audioStreamIndex];
            var audioCodec = ffmpeg.avcodec_find_decoder(audioStream->codecpar->codec_id);

            info.AudioCodec = Marshal.PtrToStringAnsi((IntPtr)audioCodec->name) ?? "unknown";
            info.AudioSampleRate = audioStream->codecpar->sample_rate;
            info.AudioChannels = audioStream->codecpar->ch_layout.nb_channels;
            info.AudioBitRate = totalBitRate - videoBitRate;
        }

        return info;
    }
}