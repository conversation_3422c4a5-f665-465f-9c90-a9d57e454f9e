using Teslametrics.App.Web.Events.Presets;

namespace Teslametrics.App.Web.Features.Main.AccessControl.Quota.AddQuotaDrawer;

public partial class AddQuotaDrawer
{
    private Guid? _organizationId;
    public bool IsOpened { get; private set; }

    protected override void OnInitialized()
    {
        base.OnInitialized();
        EventSystem.Subscribe<PresetAddToOrganization>(Show);
    }

    private void Show(PresetAddToOrganization eto)
    {
        _organizationId = eto.OrganizationId;
        IsOpened = true;
        StateHasChanged();
    }

    private void OpenChangedHandler(bool isOpened)
    {
        IsOpened = isOpened;
        if (!isOpened)
        {
            _organizationId = null;
        }
    }
}
