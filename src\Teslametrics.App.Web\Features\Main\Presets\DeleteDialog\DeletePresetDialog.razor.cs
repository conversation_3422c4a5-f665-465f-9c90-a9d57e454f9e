using MudBlazor;
using System.Reactive;
using Teslametrics.App.Web.Events.Presets;
using Teslametrics.App.Web.Extensions;

namespace Teslametrics.App.Web.Features.Main.Presets.DeleteDialog;

public partial class DeletePresetDialog
{
	private bool _subscribing;
	private DialogOptions _dialogOptions = new() { CloseOnEscapeKey = true, FullWidth = true, MaxWidth = MaxWidth.Medium, NoHeader = true };
	private bool _isVisible;
	private Guid _id;

	private SubscribeCameraPresetUseCase.Response? _subscriptionResult;
	private GetCameraPresetUseCase.Response? _model;

	protected override void OnInitialized()
	{
		base.OnInitialized();

		CompositeDisposable.Add(EventSystem.Subscribe<PresetDeleteEto>(OnDeleteHandler));
	}

	private async Task FetchAsync()
	{
		try
		{
			if (_model is null) await SetLoadingAsync(true);
			_model = await ScopeFactory.MediatorSend(new GetCameraPresetUseCase.Query(_id));
			await SetLoadingAsync(false);
			switch (_model.Result)
			{
				case GetCameraPresetUseCase.Result.Success:
					await SubscribeAsync();
					break;
				case GetCameraPresetUseCase.Result.CameraPresetNotFound:
					Snackbar.Add("Не удалось получить пресет. Возможно он уже удалён.", Severity.Warning);
					Cancel();
					break;
				case GetCameraPresetUseCase.Result.ValidationError:
					Snackbar.Add("Не удалось получить пресет из-за ошибки валидации.", Severity.Error);
					Cancel();
					break;
				case GetCameraPresetUseCase.Result.Unknown:
				default:
					throw new Exception($"Unexpected result in {nameof(GetCameraPresetUseCase)}: {_model.Result}");
			}
		}
		catch (Exception ex)
		{
			Logger.LogError(ex, ex.Message);
			Snackbar.Add("Не удалось получить пресет.");
		}
		finally
		{
			await SetLoadingAsync(false);
		}
	}

	private async Task SubscribeAsync()
	{
		try
		{
			Unsubscribe();

			await SetSubscribingAsync(true);
			_subscriptionResult = await ScopeFactory.MediatorSend(new SubscribeCameraPresetUseCase.Request(Observer.Create<object>(OnAppEventHandler, OnError), _id));
			await SetSubscribingAsync(false);
			switch (_subscriptionResult.Result)
			{
				case SubscribeCameraPresetUseCase.Result.Success:
					CompositeDisposable.Add(_subscriptionResult.Subscription!);
					StateHasChanged();
					break;
				case SubscribeCameraPresetUseCase.Result.ValidationError:
					Snackbar.Add("Ошибка валидации при подписке на события", Severity.Error);
					break;
				case SubscribeCameraPresetUseCase.Result.Unknown:
				default:
					throw new Exception($"Unexpected result in {nameof(SubscribeCameraPresetUseCase)}: {_subscriptionResult.Result}");
			}
		}
		catch (Exception ex)
		{
			await SetSubscribingAsync(false);
			Snackbar.Add("Не удалось получить подписку на события камеры из-за непредвиденной ошибки. Повторите попытку", Severity.Error);
			Logger.LogError(ex, ex.Message);
		}
	}
	private void Unsubscribe()
	{
		if (_subscriptionResult?.Subscription is not null)
		{
			CompositeDisposable.Remove(_subscriptionResult.Subscription);
			_subscriptionResult.Subscription.Dispose();
		}
	}
	protected Task SetSubscribingAsync(bool isLoading = true) => UpdateViewAsync(() =>
	{
		_subscribing = isLoading;
	});

	#region [Actions]
	private void Cancel()
	{
		_isVisible = false;
		Unsubscribe();
	}
	private Task RefreshAsync() => FetchAsync();
	private async Task SubmitAsync()
	{
		try
		{
			if (_model is null) return;
			Unsubscribe();
			var response = await ScopeFactory.MediatorSend(new DeleteCameraPresetUseCase.Command(_id));
			switch (response.Result)
			{
				case DeleteCameraPresetUseCase.Result.Success:
					Snackbar.Add("Пресет успешно удалён", Severity.Success);
					Cancel();
					break;
				case DeleteCameraPresetUseCase.Result.ValidationError:
					await SubscribeAsync();
					Snackbar.Add("Не удалось удалить пресет из-за ошибки валидации", Severity.Error);
					break;
				case DeleteCameraPresetUseCase.Result.Unknown:
				default:
					await SubscribeAsync();
					throw new Exception($"Unexpected result in {nameof(DeleteCameraPresetUseCase)}: {response.Result}");
			}
		}
		catch (Exception ex)
		{
			Logger.LogError(ex, ex.Message);
			Snackbar.Add("Не удалось удалить группу.");
		}
	}
	#endregion

	#region [Event Handlers]
	private async void OnDeleteHandler(PresetDeleteEto eto)
	{
		_id = eto.PresetId;
		_isVisible = true;
		await FetchAsync();
		await SubscribeAsync();
		StateHasChanged();
	}

	private async void OnAppEventHandler(object appEvent)
	{
		switch (appEvent)
		{
			case SubscribeCameraPresetUseCase.UpdatedEvent updatedEto:
				await FetchAsync();
				await UpdateViewAsync();
				break;

			case SubscribeCameraPresetUseCase.DeletedEvent deletedEto:
				Snackbar.Add("Камера была удалена", Severity.Warning);
				Cancel();
				break;

			default:
				Snackbar.Add("Было получено непредвиденное событие.", Severity.Warning);
				await FetchAsync();
				await UpdateViewAsync();
				Logger.LogWarning("Unexpected event in {UseCase}: {Event}", nameof(SubscribeCameraPresetUseCase), nameof(appEvent));
				break;
		}
	}

	private void OnError(Exception exc)
	{
		Snackbar.Add("Ошибка при подписке на события", Severity.Error);
		Logger.LogError(exc, exc.Message);
	}
	#endregion [Event Handlers]
}