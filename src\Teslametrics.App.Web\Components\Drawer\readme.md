﻿# DrawerService
> Сервис компонента выезжающей панели

Данный компонент, а точнее, набор компонентов является логическим продолжением компонента DrawerComponent, позволяющий
не привзяываться к определённым страницам и давать свободу взаимодействия скозь контексты без дублирования кода

## Словарь

| Вырежение | Определение |
| ------ | ------ |
| <u>_Панели_</u> | Боковая выезжающая панель, появляющаяся в части экрана и служащая для акцентирования пользования на его части с ожиданием от него каких-либо действий  |
| <u>_Шаблон_</u> | Иными словами - Layout, макет, часть страницы, содержащая элементы, расположенные на всех страницах приложения

---

## Мотивация

- Возможность активного взаимодействия (редактирования, просмотра, создания и т.д.) контекстных сущностей в любой точке приложения
- Имеющиеся аналоги (https://github.com/Medtelligent/MT.MudBlazor.Extensions/tree/main) не подходят из-за особенностей вызовов (жёсткая разметка title, недостаток гибкости).
- Возможность вызова <u>_Панели_</u> без объявления её на странице
- Dispose <u>_Панели_</u> по её закрытию

### Ограничения

- При использовании `DrawerHeader`, `DrawerBody`, `DrawerActions` как контента их расположение внутри <u>_Панели_</u> относительно друг-друга фиксировано.

---

## Фишки, отличающие данный компонент от аналога или реализации Dialog в MudBlazor

- Автосортировка секций компонента <u>_Панели_</u> при применении `DrawerHeader`, `DrawerBody`, `DrawerActions`
- Возможность изменения значений параметров, переданных <u>_Панели_</u> через `IDrawerReference.SetParametersAsync`
- Возможность изменения размера <u>_Панели_</u>

---

### Зависимости

- IDrawerService

---

## Применение
1. Регистрируем обязательные зависимости.
2. В <u>_Шаблоне_</u> размещаем компонент ```DrawerProvider```
	> Для примера содержимое `DefaultLayout.razor`:
	```razor
	@using Teslametrics.App.Web.Features._Shared;

	@inherits LayoutComponentBase

	<MudLayout>
		<MudMainContent>
			@Body
		</MudMainContent>
		<DrawerProvider />
	</MudLayout>
	```
3. Создаём компонент, который будет являтся нашей <u>_Панелью_</u>.
	> Для примера назовём его `ExampleDrawer.razor`<br/>
	> Содердимое `ExampleDrawer.razor`:
	```razor
	@using Teslametrics.App.Web.Features._Shared;

	<DrawerHeader>
		Это заголовок тестового компонента
	</DrawerHeader>

	<DrawerBody Class="px-6">
		Здесь невероятно большой контент, который будет размещаться по центру <u>_Панели_</u>
	</DrawerBody>

	<DrawerActions>
		<!-- А тут мы можем разместить кнопочки и прочее -->
		<MudStack AlignItems="AlignItems.End">
			<MudButton OnClick="Cancel">Отмена</MudButton>
			<MudButton OnClick="Submit" Color="Color.Primary">Ok</MudButton>
		</MudStack>
	</DrawerActions>

	@code {
		[CascadingParameter(Name = DrawerConsts.InstanceName)]
		public DrawerInstance DrawerInstance { get; set; } = null!;

		void Submit() => DrawerInstance.Close(DrawerResult.Ok(true));
		void Cancel() => DrawerInstance.Cancel();
	}
	```
	При этом нет жёсткой необходимости размещать `DrawerHeader`, `DrawerBody`, `DrawerActions` в указанном порядке, однако, их конечный вид будет именно в нём последовательности: `DrawerHeader` - сверху, `DrawerBody` - по центру, `DrawerActions` - в самом низу
4. Производим вызов нашей <u>_Панели_</u>
	```cs
	public class ExampleModule : IBlazorModule, IOnInitializedAsync
	{
		private readonly IBlzEventSystem EventSystem;
		private readonly IDrawerService DrawerService;

		public ExampleModule(IBlzEventSystem eventSystem, IDrawerService drawerService)
		{
			EventSystem = eventSystem;
			DrawerService = drawerService;
		}

		public Task OnInitializedAsync()
		{
			EventSystem.Subscribe<ExampleEventEto>(() => DrawerService.Show<ExampleDrawer>());
			return Task.CompletedTask;
		}
	}
	```

## Описание

### DrawerHeader
> Служит для указания контента заголовка <u>_Панели_</u>, по умолачанию имеет кнопку, необходимую для закрытия панели. В зависимости от `Anchor` кнопка меняет своё положение

Параметры компонента:

| Параметр | Тип | Описание |
| ------ | ------ | ------ |
| ChildContent | RenderFragment? | Дочерние компоненты, которые будут обрамлены, как заголовок <u>_Панели_</u> |
| Class | string? | Css класс, который будет дополнительно применён к обрамлению заголовка <u>_Панели_</u> |

### DrawerBody
> Служит для указания основного контента _Панели_, имеет настройки для `overflow` контента, на случай привышения им максимальной высоты контента _Панели_, доступный после отсечения минимально необходимый высоты для `DrawerHeader` и `DrawerActions`

Параметры компонента:

| Параметр | Тип | Описание |
| ------ | ------ | ------ |
| ChildContent | RenderFragment? | Дочерние компоненты, которые будут обрамлены, как контент <u>_Панели_</u> |
| Class | string? | Css класс, который будет дополнительно применён к обрамлению основного контента <u>_Панели_</u> |

### DrawerActions
> Служит для указания контента, ответственного за действия, связанные с <u>_Панелью_</u>

---

## Референсы

Для написания данного пакета компонентов ссылался на следующие источники:

| NuGet | Ссылка на github |
| ------ | ------ |
| MT.MudBlazor.Extensions | [Mud Drawer Extensions][ext-drawer] |
| MudBlazor | [MudBlazor][mud-blazor] |

[//]: # (These are reference links used in the body of this note and get stripped out when the markdown processor does its job. There is no need to format nicely because it shouldn't be seen. Thanks SO - http://stackoverflow.com/questions/4823468/store-comments-in-markdown-syntax)

   [ext-drawer]: <https://github.com/Medtelligent/MT.MudBlazor.Extensions/tree/main>
   [mud-blazor]: <https://github.com/MudBlazor/MudBlazor/tree/dev>
