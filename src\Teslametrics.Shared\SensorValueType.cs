using Orleans;

namespace Teslametrics.Shared;

/// <summary>
/// Определяет типы данных, используемые для хранения и обработки значений
/// </summary>
[GenerateSerializer]
public enum SensorValueType
{
    /// <summary>Строковый тип данных (TEXT в PostgreSQL)</summary>
    String,
    /// <summary>Логический тип данных (BOOLEAN в PostgreSQL)</summary>
    Bool,
    /// <summary>Число с плавающей точкой (DOUBLE PRECISION в PostgreSQL)</summary>
    Double,
    /// <summary>Целочисленный тип данных (BIGINT в PostgreSQL)</summary>
    Integer
}