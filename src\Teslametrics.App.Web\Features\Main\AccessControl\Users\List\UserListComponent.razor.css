﻿::deep .mud-input-input-control:not(:has(.mud-input-control-helper-container))::after
{
	content: none;
}

/* Make the data grid container scrollable */
::deep .mud-table-container
{
	max-height: calc(100vh - 295px);
	/* Adjust this value based on your layout needs */
	overflow-y: auto;
}

/* Ensure the header stays fixed while scrolling */
::deep .mud-table-head
{
	position: sticky;
	top: 0;
	z-index: 1;
	background-color: var(--mud-palette-surface);
}

/* Add a subtle shadow to the header when scrolling */
::deep .mud-table-head::after
{
	content: '';
	position: absolute;
	left: 0;
	right: 0;
	bottom: 0;
	height: 2px;
	background: linear-gradient(180deg, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0) 100%);
}