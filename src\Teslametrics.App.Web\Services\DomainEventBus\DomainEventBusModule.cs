﻿using System.Reactive.Subjects;

namespace Teslametrics.App.Web.Services.DomainEventBus;

public static class DomainEventBusModule
{
    public static void Install(IServiceCollection services)
    {
        services.AddSingleton<IDomainEventBus, DefaultDomainEventBus>();
    }

    public class DefaultDomainEventBus : IDomainEventBus
    {
        Subject<object> _subject;
        private bool _disposedValue;

        public DefaultDomainEventBus()
        {
            _subject = new Subject<object>();
        }

        public Task<IObservable<object>> GetEventStreamAsync()
        {
            return Task.FromResult<IObservable<object>>(_subject);
        }

        public Task PublishAsync(object @event)
        {
            _subject.OnNext(@event);
            return Task.CompletedTask;
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposedValue)
            {
                if (disposing)
                {
                    _subject?.OnCompleted();
                    _subject?.Dispose();
                }

                _disposedValue = true;
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }
    }
}