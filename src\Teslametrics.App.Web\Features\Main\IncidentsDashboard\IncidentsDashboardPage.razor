@using Microsoft.AspNetCore.Authorization
@using Teslametrics.App.Web.Features.Main.IncidentsDashboard.Filter
@using Teslametrics.App.Web.Features.Main.IncidentsDashboard.IncidentsCount
@using Teslametrics.App.Web.Features.Main.IncidentsDashboard.IncidentTypes
@using Teslametrics.App.Web.Features.Main.IncidentsDashboard.DoorOpenings
@page "/incidents-dashboard"
@attribute [Authorize]
@inherits InteractiveBaseComponent
<PageTitle>Multimonitor | Информация по инцидентам и датчикам</PageTitle>
<div class="grid">
    <div class="shadow d-sm-none d-md-flex"></div>
    <MudGrid Spacing="4"
             Class="overflow-auto align-start incidents_dashboard ">
        <MudItem xs="12">
            <MudPaper Elevation="0"
                      Outlined="true"
                      Class="pa-4">
                <MudText Typo="Typo.subtitle1"
                         Class="mb-4">Фильтры</MudText>
                <FilterComponent @bind-DateRange="@DateRange"
                                 @bind-CityId="@CityId" />
            </MudPaper>
        </MudItem>

        <!-- Incidents Chart -->
        <MudItem xs="12"
                 md="6">
            <IncidentsCountComponent DateTo="@DateRange.End!.Value"
                                     DateFrom="@DateRange.Start!.Value"
                                     CityId="@CityId"
                                     BuildingId="@BuildingId"
                                     RoomId="@RoomId" />
        </MudItem>

        <!-- Incident Types Chart -->
        <MudItem xs="12"
                 md="6">
            <IncidentTypesComponent DateTo="@DateRange.End!.Value"
                                    DateFrom="@DateRange.Start!.Value"
                                    CityId="@CityId"
                                    BuildingId="@BuildingId"
                                    RoomId="@RoomId" />
        </MudItem>

        @* <!-- Temperature Chart -->
            <MudItem xs="12"
                     md="6">
                <MudPaper Elevation="0"
                          Outlined="true"
                          Class="pa-4">
                    <MudText Typo="Typo.subtitle1"
                             Class="mb-4">Динамика температуры</MudText>
                    <div id="temperature-chart"
                         style="height: 300px;"></div>
                </MudPaper>
            </MudItem>

            <!-- Humidity Chart -->
            <MudItem xs="12"
                     md="6">
                <MudPaper Elevation="0"
                          Outlined="true"
                          Class="pa-4">
                    <MudText Typo="Typo.subtitle1"
                             Class="mb-4">Динамика влажности</MudText>
                    <div id="humidity-chart"
                         style="height: 300px;"></div>
                </MudPaper>
            </MudItem> *@

        <!-- Door Openings Chart -->
        @* <MudItem xs="12">
                <DoorOpeningsComponent DateTo="@DateRange.End!.Value"
                                       DateFrom="@DateRange.Start!.Value"
                                       CityId="@CityId"
                                       BuildingId="@BuildingId"
                                       RoomId="@RoomId" />
            </MudItem> *@
    </MudGrid>
</div>
