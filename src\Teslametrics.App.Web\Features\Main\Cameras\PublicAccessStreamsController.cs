using Dapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Data;
using Teslametrics.Shared;
using Teslametrics.MediaServer.Orleans.Camera;
using Teslametrics.App.Web.Services;
using Teslametrics.App.Web.Services.Persistence;

namespace Teslametrics.App.Web.Features.Main.Cameras;

[ApiController]
[Route("publicaccess")]
public class PublicAccessStreamsController : ControllerBase
{
    private readonly ILogger<StreamsController> _logger;
    private readonly IDbConnection _dbConnection;
    private readonly StreamCache _streamCache;
    private readonly IAuthorizationService _authorizationService;

    public PublicAccessStreamsController(IAuthorizationService authorizationService,
                                         ILogger<StreamsController> logger,
                                         IDbConnection dbConnection,
                                         StreamCache streamCache)
    {
        _authorizationService = authorizationService;
        _logger = logger;
        _dbConnection = dbConnection;
        _streamCache = streamCache;
    }

    [Route("{accessId}/stream.m3u8")]
    [HttpGet]
    public async Task<string> GetStreamAsync(Guid accessId)
    {
        var cameraId = await GetCameraIdAsync(accessId);

        return await new M3u8Generator(_dbConnection, _streamCache).GetAsync(cameraId, StreamType.Public);
    }

    private async Task<Guid> GetCameraIdAsync(Guid accessId)
    {
        var template = SqlQueryBuilder.Create()
            .Select(Db.PublicLinks.Props.CameraId)
            .Where(Db.PublicLinks.Columns.Id, ":AccessId", SqlOperator.Equals, new { accessId })
            .Build(QueryType.Standard, Db.PublicLinks.Table, RowSelection.AllRows);

        var cameraId = await _dbConnection.QuerySingleAsync<Guid>(template.RawSql, template.Parameters);
        return cameraId;
    }

    [HttpGet]
    [Route("{accessId}/{fileName}")]
    //[Authorize]
    public async Task<IActionResult> GetFile(Guid accessId, string fileName)
    {
        try
        {
            var cameraId = await GetCameraIdAsync(accessId);

            if (string.IsNullOrWhiteSpace(fileName))
            {
                return BadRequest("Invalid segment name.");
            }

            //var authResult = await _authorizationService.AuthorizeAsync(HttpContext.User, new PolicyRequirementResource(), Fqdn<AppPermissions>.GetNames(AppPermissions.Main.AccessControl.Users.Update).Last());
            //if (authResult.Succeeded)
            //{
            //    return Unauthorized();
            //}

            var data = _streamCache.GetSegment(cameraId, StreamType.Public, int.Parse(Path.GetFileNameWithoutExtension(fileName)));

            return File(data, GetContentType(fileName), fileName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, ex.Message);
            return Problem();
        }
    }

    [HttpGet]
    [Route("{accessId}/preview")]
    [Authorize]
    public async Task<IActionResult> GetPreviewFile(Guid accessId)
    {
        try
        {
            var cameraId = await GetCameraIdAsync(accessId);

            // var authResult = await _authorizationService.AuthorizeAsync(HttpContext.User, new PolicyRequirementResource(organizationId, cameraId), AppPermissions.Main.Cameras.Read.GetEnumPermissionString());
            // if (!authResult.Succeeded)
            // {
            //     return Unauthorized();
            // }

            var data = _streamCache.GetPreviewImage(cameraId, StreamType.Public);

            return File(data, "image/jpeg");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, ex.Message);
            return Problem();
        }
    }

    // Метод для определения типа контента (например, .jpg -> image/jpeg)
    private string GetContentType(string fileName)
    {
        var fileExtension = Path.GetExtension(fileName).ToLower();
        return fileExtension switch
        {
            ".jpg" => "image/jpeg",
            ".png" => "image/png",
            ".mp4" => "video/mp4",
            ".m3u8" => "application/vnd.apple.mpegurl",
            ".ts" => "video/mp2t",
            _ => "application/octet-stream", // Стандартный тип для неизвестных файлов
        };
    }

    public record TimelineRange(DateTimeOffset StartTime, DateTimeOffset EndTime);
}