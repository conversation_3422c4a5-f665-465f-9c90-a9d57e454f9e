$ffprobe = "D:\Next\Tesla\teslametrics\test\FFMpegConsole\ffprobe.exe"
$streamDir = "D:\Next\Tesla\teslametrics\test\FFMpegConsole\bin\Debug\net9.0\stream"
$files = Get-ChildItem "$streamDir\*.ts" | Sort-Object Name

foreach ($file in $files) {
    Write-Host "`n=== Analyzing $($file.Name) ===" -ForegroundColor Green

    $json = & $ffprobe -v quiet -print_format json -show_format -show_streams $file.FullName
    $data = $json | ConvertFrom-Json

    # Анализ формата
    $format = $data.format
    Write-Host "Format: $($format.format_name) ($($format.format_long_name))"
    Write-Host "Start: $($format.start_time)"
    Write-Host "Duration: $($format.duration) sec"
    Write-Host "Size: $([math]::Round($format.size/1024, 2)) KB"
    Write-Host "Bitrate: $([math]::Round($format.bit_rate/1024, 2)) Kbit/s"

    # Анализ потоков
    foreach ($stream in $data.streams) {
        Write-Host "`nStream #$($stream.index) - $($stream.codec_type):"
        Write-Host "Codec: $($stream.codec_name) ($($stream.codec_long_name))"

        if ($stream.codec_type -eq "video") {
            Write-Host "Resolution: $($stream.width)x$($stream.height)"
            Write-Host "Pixel Format: $($stream.pix_fmt)"
            Write-Host "Frame Rate: $($stream.r_frame_rate)"
            Write-Host "Average Frame Rate: $($stream.avg_frame_rate)"
            Write-Host "Time Base: $($stream.time_base)"
        }
        elseif ($stream.codec_type -eq "audio") {
            Write-Host "Sample Rate: $($stream.sample_rate) Hz"
            Write-Host "Channels: $($stream.channels)"
            Write-Host "Channel Layout: $($stream.channel_layout)"
            Write-Host "Time Base: $($stream.time_base)"
        }
    }
}
