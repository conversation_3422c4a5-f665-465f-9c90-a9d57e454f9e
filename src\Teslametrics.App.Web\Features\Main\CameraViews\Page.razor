﻿@using Teslametrics.App.Web.Features.Main.CameraViews.TreeView
@using Teslametrics.App.Web.Features.Main.CameraViews.ViewFrame
@using Teslametrics.App.Web.Features.Main.CameraViews.Drawer
@using Teslametrics.App.Web.Features.Main.CameraViews.DeleteDialog
@attribute [Route(RouteConstants.CameraViews)]
@attribute [AppAuthorize(AppPermissions.Main.CameraViews.Read)]
@inherits InteractiveBaseComponent
<PageTitle>Multimonitor | Виды</PageTitle>
<MudContainer MaxWidth="MaxWidth.False"
			  Class="mud-height-full pt-4 ">
	<MudStack Spacing="2"
			  Class="mud-height-full">
		<ResizerComponent Class="mud-height-full"
						  StorageId="cameras_list_size"
						  MinWidth="420px">
			<LeftPanel>
				<MudPaper Class="mud-height-full">
					<TreeViewComponent OrganizationId="@OrganizationId"
									   ViewId="@ViewId"
									   OnParametersChanged="OnTreeParametersChanged" />
				</MudPaper>
			</LeftPanel>
			<RightPanel>
				@if (OrganizationId.HasValue && ViewId.HasValue)
				{
					<AuthorizeView Policy="@AppPermissions.Main.CameraViews.Read.GetEnumPermissionString()"
								   Resource="new PolicyRequirementResource(OrganizationId.Value, ViewId.Value)"
								   Context="innerContext">
						<Authorized Context="innerContext">
							<ViewFrameComponent OrganizationId="@OrganizationId.Value"
												ViewId="@ViewId.Value" />
						</Authorized>
						<NotAuthorized>
							<NotAuthorizedComponent Class="mud-height-full" />
						</NotAuthorized>
					</AuthorizeView>
				}
				<NoTreeElementSelectedComponent Show="!OrganizationId.HasValue || !ViewId.HasValue"
												LeftIcon="@Icons.Material.Filled.Folder"
												RightIcon="@Icons.Material.Filled.ViewModule"
												Title="Выберите вид"
												Subtitle="Для просмотра камер выберите вид в дереве слева" />
			</RightPanel>
		</ResizerComponent>
	</MudStack>
</MudContainer>
<CameraViewDrawerComponent />
<DeleteDialogComponent />