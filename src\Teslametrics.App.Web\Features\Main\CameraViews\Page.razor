﻿@using Teslametrics.App.Web.Features.Main.CameraViews.TreeView
@using Teslametrics.App.Web.Features.Main.CameraViews.ViewFrame
@using Teslametrics.App.Web.Features.Main.CameraViews.Drawer
@using Teslametrics.App.Web.Features.Main.CameraViews.DeleteDialog
@using Teslametrics.Shared
@attribute [Route(RouteConstants.CameraViews)]
@attribute [AppAuthorize(AppPermissions.Main.CameraViews.Read)]
@inherits InteractiveBaseComponent
<PageTitle>Multimonitor | Виды</PageTitle>
@if (!UserDeviceService.IsMobile)
{
    <ResizerComponent Class="mud-height-full"
                      StorageId="cameras_list_size"
                      MinWidth="420px"
                      Orientation="@_orientation">
        <LeftPanel>
            <MudPaper Class="mud-height-full">
                <TreeViewComponent OrganizationId="@OrganizationId"
                                   ViewId="@ViewId"
                                   OnParametersChanged="OnTreeParametersChanged" />
            </MudPaper>
        </LeftPanel>
        <RightPanel>
            @if (OrganizationId.HasValue && ViewId.HasValue)
            {
                <AuthorizeView Policy="@AppPermissions.Main.CameraViews.Read.GetEnumPermissionString()"
                               Resource="new PolicyRequirementResource(OrganizationId.Value, ViewId.Value)"
                               Context="innerContext">
                    <Authorized Context="innerContext">
                        <ViewFrameComponent OrganizationId="@OrganizationId.Value"
                                            ViewId="@ViewId.Value" />
                    </Authorized>
                    <NotAuthorized>
                        <NotAuthorizedComponent Class="mud-height-full" />
                    </NotAuthorized>
                </AuthorizeView>
            }
            <NoTreeElementSelectedComponent Show="!OrganizationId.HasValue || !ViewId.HasValue"
                                            LeftIcon="@Icons.Material.Filled.Folder"
                                            RightIcon="@Icons.Material.Filled.ViewModule"
                                            Title="Выберите вид"
                                            Subtitle="Для просмотра камер выберите вид в дереве слева" />
        </RightPanel>
    </ResizerComponent>
}
else
{
    <MudSwipeArea OnSwipeEnd="@OnSwipeEnd"
                  Class="mud-width-full mud-height-full overflow-hidden">
        <MudDrawer @bind-Open="@_drawerOpen"
                   Breakpoint="Breakpoint.Always"
                   Fixed="true"
                   Width="100%"
                   Elevation="1"
                   Variant="@DrawerVariant.Temporary">
            <TreeViewComponent OrganizationId="@OrganizationId"
                               ViewId="@ViewId"
                               OnParametersChanged="OnTreeParametersChanged" />
        </MudDrawer>
        @if (OrganizationId.HasValue && ViewId.HasValue)
        {
            <AuthorizeView Policy="@AppPermissions.Main.CameraViews.Read.GetEnumPermissionString()"
                           Resource="new PolicyRequirementResource(OrganizationId.Value, ViewId.Value)"
                           Context="innerContext">
                <Authorized Context="innerContext">
                    <ViewFrameComponent OrganizationId="@OrganizationId.Value"
                                        ViewId="@ViewId.Value" />
                </Authorized>
                <NotAuthorized>
                    <NotAuthorizedComponent Class="mud-height-full" />
                </NotAuthorized>
            </AuthorizeView>
        }
        <NoTreeElementSelectedComponent Show="!OrganizationId.HasValue || !ViewId.HasValue"
                                        LeftIcon="@Icons.Material.Filled.Folder"
                                        RightIcon="@Icons.Material.Filled.ViewModule"
                                        Title="Выберите вид"
                                        Subtitle="Для просмотра камер выберите вид в дереве слева" />
    </MudSwipeArea>
}
<CameraViewDrawerComponent />
<DeleteDialogComponent />