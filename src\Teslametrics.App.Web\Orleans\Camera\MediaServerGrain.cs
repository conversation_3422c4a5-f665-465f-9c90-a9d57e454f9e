using static Teslametrics.App.Web.Orleans.Camera.IMediaServerGrain;

namespace Teslametrics.App.Web.Orleans.Camera;

[KeepAlive]
public class MediaServerGrain : Grain, IMediaServerGrain
{
    private readonly ILogger<MediaServerGrain> _logger;
    private readonly IHostApplicationLifetime _applicationLifetime;
    private readonly HashSet<Guid> _cameras;
    private const string _keepAliveReminderName = "MediaServerGrainKeepAliveReminder";
    private readonly TimeSpan _keepAliveReminderPeriod = TimeSpan.FromMinutes(10);

    public MediaServerGrain(ILogger<MediaServerGrain> logger,
                            IHostApplicationLifetime applicationLifetime)
    {
        _cameras = [];
        _logger = logger;
        _applicationLifetime = applicationLifetime;
    }

    public override async Task OnActivateAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("MediaServerGrain activated with ID: {GrainId}", this.GetPrimaryKeyString());

        // Регистрируем напоминание для предотвращения деактивации зерна
        try
        {
            _logger.LogInformation("Registered keep-alive reminder for MqttGrain with ID: {GrainId}",
                this.GetPrimaryKeyString());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to register keep-alive reminder for MqttGrain with ID: {GrainId}",
                this.GetPrimaryKeyString());
        }

        await base.OnActivateAsync(cancellationToken);
    }

    public override async Task OnDeactivateAsync(DeactivationReason reason, CancellationToken cancellationToken)
    {
        _logger.LogInformation("MediaServerGrain deactivating with ID: {GrainId}, reason: {Reason}",
            this.GetPrimaryKeyString(), reason);

        _logger.LogInformation("MediaServerGrain resources cleaned up for ID: {GrainId}",
            this.GetPrimaryKeyString());

        await base.OnDeactivateAsync(reason, cancellationToken);
    }

    public async Task ConnectRtspAsync(CameraConnectRtspRequest request)
    {
        if (!_cameras.Contains(request.CameraId))
        {
            var grain = GrainFactory.GetGrain<ICameraGrain>(request.CameraId);
            await grain.ConnectRtspAsync(new ICameraGrain.ConnectRtspRequest(request.ArchiveUri, request.ViewUri, request.PublicUri));

            _cameras.Add(request.CameraId);
        }
    }

    public async Task ConnectOnvifAsync(CameraConnectOnvifRequest request)
    {
        if (!_cameras.Contains(request.CameraId))
        {
            var grain = GrainFactory.GetGrain<ICameraGrain>(request.CameraId);
            await grain.ConnectOnvifAsync(new ICameraGrain.ConnectOnvifRequest(request.Host, request.Port, request.Username, request.Password));

            _cameras.Add(request.CameraId);
        }
    }

    public async Task DisconnectAsync(CameraDisconnectRequest request)
    {
        if (_cameras.Contains(request.CameraId))
        {
            var grain = GrainFactory.GetGrain<ICameraGrain>(request.CameraId);
            await grain.DisconnectAsync();

            _cameras.Remove(request.CameraId);
        }
    }

    public async Task StopAllAsync()
    {
        foreach (var cameraId in _cameras)
        {
            var grain = GrainFactory.GetGrain<ICameraGrain>(cameraId);
            await grain.DisconnectAsync();
        }

        _cameras.Clear();
    }

    public async Task<CameraStatus> GetStatusAsync(GetCameraStatusRequest request)
    {
        if (!_cameras.Contains(request.CameraId))
        {
            return CameraStatus.Stopped;
        }

        var grain = GrainFactory.GetGrain<ICameraGrain>(request.CameraId);
        return await grain.GetStatusAsync();
    }
}