﻿::deep .cell {
	width: 1%;
	min-width: 120px;
	white-space: nowrap;
}

::deep td:not(.cell),
::deep th:not(.cell) {
	width: 100%;
}

::deep .mud-input-input-control:not(:has(.mud-input-control-helper-container))::after {
	content: none;
}

::deep .input {
	border-right: 1px solid var(--mud-palette-divider);
	padding-right: 12px;
}

::deep .totalQuotaCell {
	min-width: 180px;
	width: fit-content;
}

::deep tr td:last-child {
	display: table-cell !important;
	min-width: 80px !important;
}

::deep .quota_table {
    height: 100%;
    display: grid;
    grid-template-rows: auto 1fr auto;
}