let incidentTypesChart = null;

/**
 * Initialize the incident types pie chart
 * @param {Object} data - The chart data
 */
export function initIncidentTypesChart(data) {
    if (incidentTypesChart) {
        incidentTypesChart.destroy();
    }

    const options = {
        chart: {
            type: 'donut',
            height: 300
        },
        colors: ['#FF5722', '#FF9800', '#FFC107', '#4CAF50'],
        labels: data.labels,
        series: data.series,
        legend: {
            position: 'right',
            formatter: function (seriesName, opts) {
                return [seriesName, ' ', opts.w.globals.series[opts.seriesIndex] + '%'];
            }
        },
        dataLabels: {
            enabled: true,
            formatter: function (val) {
                return val.toFixed(1) + '%';
            }
        },
        plotOptions: {
            pie: {
                donut: {
                    size: '65%'
                }
            }
        }
    };

    incidentTypesChart = new ApexCharts(document.querySelector("#incident-types-chart"), options);
    incidentTypesChart.render();
}